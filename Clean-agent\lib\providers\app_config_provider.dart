import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'dart:convert';
import '../data/estimator_config.dart';

class AppConfigProvider extends ChangeNotifier {
  static const String _configKey = 'clean-agent-app-config';
  static const String _pipeDataKey = 'clean-agent-pipe-data';
  static const String _cylinderSpecsKey = 'clean-agent-cylinder-specs';
  static const String _componentsKey = 'clean-agent-components';

  // Configuration values
  double _shippingExFactor = AppConfig.shippingExFactor;
  double _dollarRateSarUsd = AppConfig.dollarRateSarUsd;
  double _defaultRoomHeightM = AppConfig.defaultRoomHeightM;
  int _agentDischargeTimeSeconds = AppConfig.agentDischargeTimeSeconds;
  int _nozzleSizeThresholdMm = AppConfig.nozzleSizeThresholdMm;
  double _detectorCoverageAreaM2 = AppConfig.detectorCoverageAreaM2;
  bool _no343LCylinder = AppConfig.no343LCylinder;
  double _marginFactor = AppConfig.defaultMarginFactor;
  Map<String, double> _maxFillingRatio = Map.from(AppConfig.maxFillingRatio);

  // Data collections
  List<PipeData> _pipeData = List.from(NozzlePipeData.data);
  Map<String, List<CylinderSpec>> _cylinderSpecs = Map.from(CylinderSpecs.specs);
  List<Component> _components = List.from(MasterComponents.components);

  // Getters
  double get shippingExFactor => _shippingExFactor;
  double get dollarRateSarUsd => _dollarRateSarUsd;
  double get defaultRoomHeightM => _defaultRoomHeightM;
  int get agentDischargeTimeSeconds => _agentDischargeTimeSeconds;
  int get nozzleSizeThresholdMm => _nozzleSizeThresholdMm;
  double get detectorCoverageAreaM2 => _detectorCoverageAreaM2;
  bool get no343LCylinder => _no343LCylinder;
  double get marginFactor => _marginFactor;
  Map<String, double> get maxFillingRatio => Map.from(_maxFillingRatio);
  List<PipeData> get pipeData => List.from(_pipeData);
  Map<String, List<CylinderSpec>> get cylinderSpecs => Map.from(_cylinderSpecs);
  List<Component> get components => List.from(_components);

  AppConfigProvider() {
    _loadConfig();
  }

  Future<void> _loadConfig() async {
    final prefs = await SharedPreferences.getInstance();
    
    // Load main config
    final configJson = prefs.getString(_configKey);
    if (configJson != null) {
      try {
        final config = json.decode(configJson) as Map<String, dynamic>;
        _shippingExFactor = config['shippingExFactor']?.toDouble() ?? AppConfig.shippingExFactor;
        _dollarRateSarUsd = config['dollarRateSarUsd']?.toDouble() ?? AppConfig.dollarRateSarUsd;
        _defaultRoomHeightM = config['defaultRoomHeightM']?.toDouble() ?? AppConfig.defaultRoomHeightM;
        _agentDischargeTimeSeconds = config['agentDischargeTimeSeconds']?.toInt() ?? AppConfig.agentDischargeTimeSeconds;
        _nozzleSizeThresholdMm = config['nozzleSizeThresholdMm']?.toInt() ?? AppConfig.nozzleSizeThresholdMm;
        _detectorCoverageAreaM2 = config['detectorCoverageAreaM2']?.toDouble() ?? AppConfig.detectorCoverageAreaM2;
        _no343LCylinder = config['no343LCylinder'] ?? AppConfig.no343LCylinder;
        _marginFactor = config['marginFactor']?.toDouble() ?? AppConfig.defaultMarginFactor;
        
        if (config['maxFillingRatio'] != null) {
          _maxFillingRatio = Map<String, double>.from(config['maxFillingRatio']);
        }
      } catch (e) {
        print('Error loading config: $e');
      }
    }
    
    // Load pipe data
    final pipeDataJson = prefs.getString(_pipeDataKey);
    if (pipeDataJson != null) {
      try {
        final pipeDataList = json.decode(pipeDataJson) as List;
        _pipeData = pipeDataList.map((item) => PipeData(
          excelRow: item['excelRow'],
          sizeDesc: item['sizeDesc'],
          sizeMm: item['sizeMm'],
          minFlow: item['minFlow'].toDouble(),
          maxFlow: item['maxFlow'].toDouble(),
        )).toList();
      } catch (e) {
        print('Error loading pipe data: $e');
      }
    }
    
    // Load cylinder specs
    final cylinderSpecsJson = prefs.getString(_cylinderSpecsKey);
    if (cylinderSpecsJson != null) {
      try {
        final cylinderSpecsMap = json.decode(cylinderSpecsJson) as Map<String, dynamic>;
        _cylinderSpecs = {};
        cylinderSpecsMap.forEach((key, value) {
          _cylinderSpecs[key] = (value as List).map((item) => CylinderSpec(
            excelRow: item['excelRow'],
            size: item['size'].toDouble(),
            maxKg: item['maxKg'].toDouble(),
            minKg: item['minKg'].toDouble(),
            partNo: item['partNo'],
            bracketPart: item['bracketPart'],
            labelPart: item['labelPart'],
            bracketQtyRule: item['bracketQtyRule'],
          )).toList();
        });
      } catch (e) {
        print('Error loading cylinder specs: $e');
      }
    }
    
    // Load components
    final componentsJson = prefs.getString(_componentsKey);
    if (componentsJson != null) {
      try {
        final componentsList = json.decode(componentsJson) as List;
        _components = componentsList.map((item) => Component(
          partNo: item['partNo'],
          description: item['description'],
          unitCost: item['unitCost'].toDouble(),
          manufacturer: item['manufacturer'],
        )).toList();
      } catch (e) {
        print('Error loading components: $e');
      }
    }
    
    notifyListeners();
  }

  Future<void> saveConfig() async {
    final prefs = await SharedPreferences.getInstance();
    
    // Save main config
    final config = {
      'shippingExFactor': _shippingExFactor,
      'dollarRateSarUsd': _dollarRateSarUsd,
      'defaultRoomHeightM': _defaultRoomHeightM,
      'agentDischargeTimeSeconds': _agentDischargeTimeSeconds,
      'nozzleSizeThresholdMm': _nozzleSizeThresholdMm,
      'detectorCoverageAreaM2': _detectorCoverageAreaM2,
      'no343LCylinder': _no343LCylinder,
      'marginFactor': _marginFactor,
      'maxFillingRatio': _maxFillingRatio,
    };
    await prefs.setString(_configKey, json.encode(config));
    
    // Save pipe data
    final pipeDataList = _pipeData.map((pipe) => {
      'excelRow': pipe.excelRow,
      'sizeDesc': pipe.sizeDesc,
      'sizeMm': pipe.sizeMm,
      'minFlow': pipe.minFlow,
      'maxFlow': pipe.maxFlow,
    }).toList();
    await prefs.setString(_pipeDataKey, json.encode(pipeDataList));
    
    // Save cylinder specs
    final cylinderSpecsMap = <String, dynamic>{};
    _cylinderSpecs.forEach((key, value) {
      cylinderSpecsMap[key] = value.map((spec) => {
        'excelRow': spec.excelRow,
        'size': spec.size,
        'maxKg': spec.maxKg,
        'minKg': spec.minKg,
        'partNo': spec.partNo,
        'bracketPart': spec.bracketPart,
        'labelPart': spec.labelPart,
        'bracketQtyRule': spec.bracketQtyRule,
      }).toList();
    });
    await prefs.setString(_cylinderSpecsKey, json.encode(cylinderSpecsMap));
    
    // Save components
    final componentsList = _components.map((component) => {
      'partNo': component.partNo,
      'description': component.description,
      'unitCost': component.unitCost,
      'manufacturer': component.manufacturer,
    }).toList();
    await prefs.setString(_componentsKey, json.encode(componentsList));
  }

  // Update methods
  void updateMarginFactor(double value) {
    _marginFactor = value;
    notifyListeners();
    saveConfig();
  }

  void updateShippingExFactor(double value) {
    _shippingExFactor = value;
    notifyListeners();
    saveConfig();
  }

  void updateDollarRate(double value) {
    _dollarRateSarUsd = value;
    notifyListeners();
    saveConfig();
  }

  void resetToDefaults() {
    _shippingExFactor = AppConfig.shippingExFactor;
    _dollarRateSarUsd = AppConfig.dollarRateSarUsd;
    _defaultRoomHeightM = AppConfig.defaultRoomHeightM;
    _agentDischargeTimeSeconds = AppConfig.agentDischargeTimeSeconds;
    _nozzleSizeThresholdMm = AppConfig.nozzleSizeThresholdMm;
    _detectorCoverageAreaM2 = AppConfig.detectorCoverageAreaM2;
    _no343LCylinder = AppConfig.no343LCylinder;
    _marginFactor = AppConfig.defaultMarginFactor;
    _maxFillingRatio = Map.from(AppConfig.maxFillingRatio);
    _pipeData = List.from(NozzlePipeData.data);
    _cylinderSpecs = Map.from(CylinderSpecs.specs);
    _components = List.from(MasterComponents.components);
    
    notifyListeners();
    saveConfig();
  }
}
