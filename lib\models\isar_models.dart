import 'package:isar/isar.dart';

part 'isar_models.g.dart';

@collection
class SystemItem {
  Id id = Isar.autoIncrement;

  @Index(unique: true)
  String? itemId;

  String? model;
  String? description;
  String? manufacturer;
  String? approval;
  double? exWorksPrice;
  double? localPrice;
  double? installationPrice;

  @enumerated
  SystemType systemType = SystemType.alarm;

  String? sectionId;
  DateTime? createdAt;

  SystemItem();

  SystemItem.create({
    this.itemId,
    this.model,
    this.description,
    this.manufacturer,
    this.approval,
    this.exWorksPrice,
    this.localPrice,
    this.installationPrice,
    this.systemType = SystemType.alarm,
    this.sectionId,
    this.createdAt,
  });

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'itemId': itemId,
      'model': model,
      'description': description,
      'manufacturer': manufacturer,
      'approval': approval,
      'exWorksPrice': exWorksPrice,
      'localPrice': localPrice,
      'installationPrice': installationPrice,
      'systemType': systemType.name,
      'sectionId': sectionId,
      'createdAt': createdAt?.toIso8601String(),
    };
  }

  factory SystemItem.fromJson(Map<String, dynamic> json) {
    return SystemItem.create(
      itemId: json['itemId'],
      model: json['model'],
      description: json['description'],
      manufacturer: json['manufacturer'],
      approval: json['approval'],
      exWorksPrice: json['exWorksPrice']?.toDouble(),
      localPrice: json['localPrice']?.toDouble(),
      installationPrice: json['installationPrice']?.toDouble(),
      systemType: SystemType.values.firstWhere(
        (e) => e.name == json['systemType'],
        orElse: () => SystemType.alarm,
      ),
      sectionId: json['sectionId'],
      createdAt: json['createdAt'] != null ? DateTime.parse(json['createdAt']) : null,
    );
  }
}

@collection
class Section {
  Id id = Isar.autoIncrement;

  @Index(unique: true)
  String? sectionId;

  String? name;
  String? displayName;
  String? icon;
  String? parentId;
  int orderIndex = 0;
  DateTime? createdAt;
  DateTime? updatedAt;
  String? color;
  bool isCustom = false;

  @enumerated
  SystemType systemType = SystemType.alarm;

  Section();

  Section.create({
    this.sectionId,
    this.name,
    this.displayName,
    this.icon,
    this.parentId,
    this.orderIndex = 0,
    this.createdAt,
    this.updatedAt,
    this.systemType = SystemType.alarm,
    this.color,
    this.isCustom = false,
  });

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'sectionId': sectionId,
      'name': name,
      'displayName': displayName,
      'icon': icon,
      'parentId': parentId,
      'orderIndex': orderIndex,
      'createdAt': createdAt?.toIso8601String(),
      'updatedAt': updatedAt?.toIso8601String(),
      'color': color,
      'isCustom': isCustom,
      'systemType': systemType.name,
    };
  }

  factory Section.fromJson(Map<String, dynamic> json) {
    return Section.create(
      sectionId: json['sectionId'],
      name: json['name'],
      displayName: json['displayName'],
      icon: json['icon'],
      parentId: json['parentId'],
      orderIndex: json['orderIndex'] ?? 0,
      createdAt: json['createdAt'] != null ? DateTime.parse(json['createdAt']) : null,
      updatedAt: json['updatedAt'] != null ? DateTime.parse(json['updatedAt']) : null,
      color: json['color'],
      isCustom: json['isCustom'] ?? false,
      systemType: SystemType.values.firstWhere(
        (e) => e.name == json['systemType'],
        orElse: () => SystemType.alarm,
      ),
    );
  }
}

@collection
class DynamicTable {
  Id id = Isar.autoIncrement;

  @Index(unique: true)
  String? tableId;

  String? sectionId;
  String? name;
  String? displayName;
  String? description;
  DateTime? createdAt;
  DateTime? updatedAt;
  int orderIndex = 0;

  DynamicTable();

  DynamicTable.create({
    this.tableId,
    this.sectionId,
    this.name,
    this.displayName,
    this.description,
    this.createdAt,
    this.updatedAt,
    this.orderIndex = 0,
  });
}

@collection
class TableColumn {
  Id id = Isar.autoIncrement;

  String? tableId;
  String? columnId;
  String? name;
  String? displayName;
  String? dataType; // text, number, currency, date, boolean
  bool isRequired = false;
  String? defaultValue;
  String? validationRules;
  int orderIndex = 0;
  DateTime? createdAt;
  DateTime? updatedAt;

  TableColumn();

  TableColumn.create({
    this.tableId,
    this.columnId,
    this.name,
    this.displayName,
    this.dataType = 'text',
    this.isRequired = false,
    this.defaultValue,
    this.validationRules,
    this.orderIndex = 0,
    this.createdAt,
    this.updatedAt,
  });
}

@collection
class TableRow {
  Id id = Isar.autoIncrement;

  String? tableId;
  String? rowId;
  String? data; // JSON string containing column values
  DateTime? createdAt;
  DateTime? updatedAt;

  TableRow();

  TableRow.create({
    this.tableId,
    this.rowId,
    this.data,
    this.createdAt,
    this.updatedAt,
  });
}

@collection
class Project {
  Id id = Isar.autoIncrement;

  @Index(unique: true)
  String? projectId;

  String? name;
  String? clientName;
  String? projectReference;
  DateTime? createdAt;
  DateTime? updatedAt;
  String? currency;
  double? exchangeRate;
  double? shippingRate; // Shipping rate factor (e.g., 1.15 for 15% shipping)
  double? marginRate; // Margin rate factor (e.g., 1.20 for 20% margin)
  bool? includeInstallation; // Whether to include installation costs (Supply & Install vs Supply Only)

  @enumerated
  SystemType systemType = SystemType.alarm;

  // Store systems and clean agent systems as JSON strings
  String? systemsJson;
  String? cleanAgentSystemsJson;

  Project();

  Project.create({
    this.projectId,
    this.name,
    this.clientName,
    this.projectReference,
    this.createdAt,
    this.updatedAt,
    this.currency,
    this.exchangeRate,
    this.shippingRate,
    this.marginRate,
    this.includeInstallation,
    this.systemType = SystemType.alarm,
    this.systemsJson,
    this.cleanAgentSystemsJson,
  });
}

@collection
class ProjectSystem {
  Id id = Isar.autoIncrement;

  @Index(unique: true)
  String? systemId;

  String? name;
  String? description;

  @enumerated
  SystemType systemType = SystemType.alarm;

  final project = IsarLink<Project>();
  final items = IsarLinks<SystemItemLink>();

  ProjectSystem();
}

@collection
class SystemItemLink {
  Id id = Isar.autoIncrement;

  @Index(unique: true)
  String? linkId;

  double? quantity;
  double? unitCost;
  double? totalCost;

  final system = IsarLink<ProjectSystem>();
  final item = IsarLink<SystemItem>();

  SystemItemLink();
}

enum SystemType {
  alarm,
  water,
  foam,
  cleanAgent,
  co2,
  materials,
  custom,
}

enum ColumnDataType {
  text,
  number,
  currency,
  date,
  boolean,
  dropdown,
}

enum CurrencyType {
  usd('USD', '\$', 'US Dollar'),
  gbp('GBP', '£', 'British Pound'),
  sar('SAR', 'ر.س', 'Saudi Riyal'),
  egp('EGP', 'ج.م', 'Egyptian Pound'),
  aed('AED', 'د.إ', 'UAE Dirham');

  const CurrencyType(this.code, this.symbol, this.name);

  final String code;
  final String symbol;
  final String name;
}

@collection
class SidebarSection {
  Id id = Isar.autoIncrement;

  @Index(unique: true)
  String? sectionId;

  String? name;
  String? icon;
  String? color;
  int? orderIndex;
  String? parentSectionId; // For hierarchical structure

  @enumerated
  SystemType systemType = SystemType.custom;

  DateTime? createdAt;
  DateTime? updatedAt;

  // Link to tables in this section
  final tables = IsarLinks<FlexibleTable>();

  SidebarSection();

  SidebarSection.create({
    this.sectionId,
    this.name,
    this.icon,
    this.color,
    this.orderIndex,
    this.parentSectionId,
    this.systemType = SystemType.custom,
    this.createdAt,
    this.updatedAt,
  });
}

@collection
class FlexibleTable {
  Id id = Isar.autoIncrement;

  @Index(unique: true)
  String? tableId;

  String? name;
  String? description;
  String? sectionId;
  DateTime? createdAt;
  DateTime? updatedAt;

  // Link to columns
  final columns = IsarLinks<FlexibleColumn>();
  final rows = IsarLinks<FlexibleRow>();

  FlexibleTable();

  FlexibleTable.create({
    this.tableId,
    this.name,
    this.description,
    this.sectionId,
    this.createdAt,
    this.updatedAt,
  });
}

@collection
class FlexibleColumn {
  Id id = Isar.autoIncrement;

  @Index(unique: true)
  String? columnId;

  String? name;
  String? tableId;

  @enumerated
  ColumnDataType dataType = ColumnDataType.text;

  @enumerated
  CurrencyType currencyType = CurrencyType.usd;

  bool? isRequired;
  String? defaultValue;
  int? orderIndex;
  String? validationRules; // JSON string for validation rules
  String? dropdownOptions; // JSON string for dropdown options

  DateTime? createdAt;
  DateTime? updatedAt;

  FlexibleColumn();

  FlexibleColumn.create({
    this.columnId,
    this.name,
    this.tableId,
    this.dataType = ColumnDataType.text,
    this.currencyType = CurrencyType.usd,
    this.isRequired,
    this.defaultValue,
    this.orderIndex,
    this.validationRules,
    this.dropdownOptions,
    this.createdAt,
    this.updatedAt,
  });
}

@collection
class FlexibleRow {
  Id id = Isar.autoIncrement;

  @Index(unique: true)
  String? rowId;

  String? tableId;
  String? data; // JSON string containing all column values
  DateTime? createdAt;
  DateTime? updatedAt;

  FlexibleRow();

  FlexibleRow.create({
    this.rowId,
    this.tableId,
    this.data,
    this.createdAt,
    this.updatedAt,
  });
}
