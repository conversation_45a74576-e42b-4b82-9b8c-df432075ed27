import 'dart:convert';
import 'package:uuid/uuid.dart';
import 'clean_agent_system.dart';

class Project {
  final String id;
  String name;
  String clientName;
  String projectReference; // Project reference number/code
  DateTime createdAt;
  DateTime updatedAt;
  String currency; // Default currency for the project
  double exchangeRate; // Exchange rate from USD to local currency
  double shippingRate; // Shipping rate factor (e.g., 1.15 for 15% shipping)
  double marginRate; // Margin rate factor (e.g., 1.20 for 20% margin)
  bool? includeInstallation; // Whether to include installation costs (Supply & Install vs Supply Only)
  Map<String, dynamic> metadata;
  List<SystemEstimate> systems;
  List<CleanAgentSystem> cleanAgentSystems;

  Project({
    String? id,
    required this.name,
    this.clientName = '',
    this.projectReference = '',
    DateTime? createdAt,
    DateTime? updatedAt,
    String? currency,
    double? exchangeRate,
    double? shippingRate,
    double? marginRate,
    bool? includeInstallation,
    Map<String, dynamic>? metadata,
    List<SystemEstimate>? systems,
    List<CleanAgentSystem>? cleanAgentSystems,
  }) :
    id = id ?? const Uuid().v4(),
    createdAt = createdAt ?? DateTime.now(),
    updatedAt = updatedAt ?? DateTime.now(),
    currency = currency ?? 'SAR', // Default to Saudi Riyal
    exchangeRate = exchangeRate ?? 3.75, // Default USD to SAR exchange rate
    shippingRate = shippingRate ?? 1.15, // Default 15% shipping
    marginRate = marginRate ?? 1.0, // Default no margin
    includeInstallation = includeInstallation ?? true, // Default to Supply & Install
    metadata = metadata ?? {},
    systems = systems ?? [],
    cleanAgentSystems = cleanAgentSystems ?? [];

  double get totalExWorksCost {
    return systems.fold(0, (sum, system) => sum + system.totalExWorksCost);
  }

  double get totalLocalCost {
    return systems.fold(0, (sum, system) => sum + system.totalLocalCost);
  }

  double get totalInstallationCost {
    return systems.fold(0, (sum, system) => sum + system.totalInstallationCost);
  }

  double get totalCost {
    // Calculate total from regular systems
    final regularSystemsCost = totalExWorksCost + totalLocalCost + totalInstallationCost;

    // Calculate total from clean agent systems
    final cleanAgentTotalCost = cleanAgentSystems.fold<double>(0, (sum, system) =>
        sum + (system.totalCost * system.quantity));

    return regularSystemsCost + cleanAgentTotalCost;
  }

  // Convert USD to local currency
  double convertToLocalCurrency(double usdAmount) {
    return usdAmount * exchangeRate;
  }

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'name': name,
      'clientName': clientName,
      'projectReference': projectReference,
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt.toIso8601String(),
      'currency': currency,
      'exchangeRate': exchangeRate,
      'shippingRate': shippingRate,
      'marginRate': marginRate,
      'includeInstallation': includeInstallation,
      'metadata': metadata,
      'systems': systems.map((system) => system.toMap()).toList(),
      'cleanAgentSystems': cleanAgentSystems.map((system) => system.toJson()).toList(),
    };
  }

  factory Project.fromMap(Map<String, dynamic> map) {
    return Project(
      id: map['id'],
      name: map['name'],
      clientName: map['clientName'] ?? '',
      projectReference: map['projectReference'] ?? '',
      createdAt: DateTime.parse(map['createdAt']),
      updatedAt: DateTime.parse(map['updatedAt']),
      currency: map['currency'] ?? 'SAR',
      exchangeRate: map['exchangeRate']?.toDouble() ?? 3.75,
      shippingRate: map['shippingRate']?.toDouble() ?? 1.15,
      marginRate: map['marginRate']?.toDouble() ?? 1.0,
      includeInstallation: map['includeInstallation'] ?? true,
      metadata: Map<String, dynamic>.from(map['metadata'] ?? {}),
      systems: List<SystemEstimate>.from(
        map['systems']?.map((x) => SystemEstimate.fromMap(x)) ?? [],
      ),
      cleanAgentSystems: List<CleanAgentSystem>.from(
        map['cleanAgentSystems']?.map((x) => CleanAgentSystem.fromJson(x)) ?? [],
      ),
    );
  }

  String toJson() => json.encode(toMap());

  factory Project.fromJson(String source) {
    try {
      return Project.fromMap(json.decode(source));
    } catch (e) {
      // Return a default project if parsing fails
      return Project(
        name: 'Error Project',
        clientName: 'Error',
      );
    }
  }
}

class SystemEstimate {
  final String id;
  String name;
  String type; // e.g., 'Fire Alarm', 'Water Sprinkler', 'Foam System'
  List<MaterialItem> materials;
  List<EquipmentItem> equipment;
  List<ServiceItem> services; // Renamed from labor to services
  Map<String, dynamic> calculations;

  SystemEstimate({
    String? id,
    required this.name,
    required this.type,
    List<MaterialItem>? materials,
    List<EquipmentItem>? equipment,
    List<ServiceItem>? services, // Updated parameter
    List<LaborItem>? labor, // For backward compatibility
    Map<String, dynamic>? calculations,
  }) :
    id = id ?? const Uuid().v4(),
    materials = materials ?? [],
    equipment = equipment ?? [],
    services = services ?? (labor?.map((l) => l as ServiceItem).toList() ?? []),
    calculations = calculations ?? {};

  // Ex-Works costs (in USD)
  double get materialsExWorksCost {
    return materials.fold(0, (sum, item) => sum + item.exWorksCost);
  }

  double get equipmentExWorksCost {
    return equipment.fold(0, (sum, item) => sum + item.exWorksCost);
  }

  double get totalExWorksCost {
    return materialsExWorksCost + equipmentExWorksCost;
  }

  // Local costs (in local currency)
  double get materialsLocalCost {
    return materials.fold(0, (sum, item) => sum + item.localCost);
  }

  double get equipmentLocalCost {
    return equipment.fold(0, (sum, item) => sum + item.localCost);
  }

  double get totalLocalCost {
    return materialsLocalCost + equipmentLocalCost;
  }

  // Service costs (in local currency)
  double get servicesCost {
    return services.fold(0, (sum, item) => sum + item.totalCost);
  }

  // For backward compatibility
  double get laborCost {
    return servicesCost;
  }

  double get materialsInstallationCost {
    return materials.fold(0, (sum, item) => sum + item.installationCost);
  }

  double get equipmentInstallationCost {
    return equipment.fold(0, (sum, item) => sum + item.installationCost);
  }

  double get totalInstallationCost {
    return laborCost + materialsInstallationCost + equipmentInstallationCost;
  }

  // Total costs
  double get materialsCost {
    return materials.fold(0, (sum, item) => sum + item.totalCost);
  }

  double get equipmentCost {
    return equipment.fold(0, (sum, item) => sum + item.totalCost);
  }

  double get totalCost {
    return materialsCost + equipmentCost + laborCost;
  }

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'name': name,
      'type': type,
      'materials': materials.map((x) => x.toMap()).toList(),
      'equipment': equipment.map((x) => x.toMap()).toList(),
      'services': services.map((x) => x.toMap()).toList(),
      'labor': services.map((x) => x.toMap()).toList(), // For backward compatibility
      'calculations': calculations,
    };
  }

  factory SystemEstimate.fromMap(Map<String, dynamic> map) {
    // Handle services from both 'services' and legacy 'labor' fields
    List<ServiceItem> services = [];

    // Try to load from 'services' field first
    if (map['services'] != null) {
      services = List<ServiceItem>.from(
        map['services']?.map((x) => ServiceItem.fromMap(x)) ?? [],
      );
    }
    // If no services found, try to load from legacy 'labor' field
    else if (map['labor'] != null) {
      services = List<ServiceItem>.from(
        map['labor']?.map((x) => LaborItem.fromMap(x)) ?? [],
      );
    }

    return SystemEstimate(
      id: map['id'],
      name: map['name'],
      type: map['type'],
      materials: List<MaterialItem>.from(
        map['materials']?.map((x) => MaterialItem.fromMap(x)) ?? [],
      ),
      equipment: List<EquipmentItem>.from(
        map['equipment']?.map((x) => EquipmentItem.fromMap(x)) ?? [],
      ),
      services: services,
      calculations: Map<String, dynamic>.from(map['calculations'] ?? {}),
    );
  }
}

class MaterialItem {
  final String id;
  String name;
  String category;
  String description; // Description of the material
  double quantity;
  String unit;
  double exWorksUnitCost; // Cost in USD (Ex-Works)
  double localUnitCost; // Additional local cost in local currency
  double installationUnitCost; // Installation cost per unit in local currency
  bool isImported; // Whether the item is imported (affects exchange rate calculation)
  String vendor; // Manufacturer or vendor name
  String approval; // Approval status or certification

  MaterialItem({
    String? id,
    required this.name,
    required this.category,
    this.description = '',
    required this.quantity,
    required this.unit,
    required this.exWorksUnitCost,
    this.localUnitCost = 0.0,
    this.installationUnitCost = 0.0,
    this.isImported = true,
    this.vendor = '',
    this.approval = '',
  }) : id = id ?? const Uuid().v4();

  // Ex-Works cost in USD
  double get exWorksCost => quantity * exWorksUnitCost;

  // Local cost in local currency
  double get localCost => quantity * localUnitCost;

  // Installation cost in local currency
  double get installationCost => quantity * installationUnitCost;

  // Calculate total unit rate in SAR (with exchange rate applied to ex-works)
  // Round to nearest whole number
  double getTotalUnitRateInSAR(double exchangeRate) {
    final calculatedRate = (exWorksUnitCost * exchangeRate) + localUnitCost + installationUnitCost;
    return calculatedRate.roundToDouble(); // Round to nearest whole number
  }

  // Calculate total unit cost (ex-works + local + installation)
  double get totalUnitCost => exWorksUnitCost + localUnitCost + installationUnitCost;

  // Total cost (total unit rate × quantity)
  double get totalCost {
    // Default exchange rate of 3.75 if not available
    const exchangeRate = 3.75;
    return getTotalUnitRateInSAR(exchangeRate) * quantity;
  }

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'name': name,
      'category': category,
      'description': description,
      'quantity': quantity,
      'unit': unit,
      'exWorksUnitCost': exWorksUnitCost,
      'localUnitCost': localUnitCost,
      'installationUnitCost': installationUnitCost,
      'isImported': isImported,
      'vendor': vendor,
      'approval': approval,
    };
  }

  factory MaterialItem.fromMap(Map<String, dynamic> map) {
    return MaterialItem(
      id: map['id'],
      name: map['name'],
      category: map['category'],
      description: map['description'] ?? '',
      quantity: map['quantity']?.toDouble() ?? 0.0,
      unit: map['unit'],
      exWorksUnitCost: map['exWorksUnitCost']?.toDouble() ?? 0.0,
      localUnitCost: map['localUnitCost']?.toDouble() ?? 0.0,
      installationUnitCost: map['installationUnitCost']?.toDouble() ?? 0.0,
      isImported: map['isImported'] ?? true,
      vendor: map['vendor'] ?? '',
      approval: map['approval'] ?? '',
    );
  }
}

class EquipmentItem {
  final String id;
  String name;
  String category;
  double quantity;
  double exWorksUnitCost; // Cost in USD (Ex-Works)
  double localUnitCost; // Additional local cost in local currency
  double installationUnitCost; // Installation cost per unit in local currency
  bool isImported; // Whether the item is imported (affects exchange rate calculation)
  String vendor; // Manufacturer or vendor name
  String approval; // Approval status or certification

  EquipmentItem({
    String? id,
    required this.name,
    required this.category,
    required this.quantity,
    required this.exWorksUnitCost,
    this.localUnitCost = 0.0,
    this.installationUnitCost = 0.0,
    this.isImported = true,
    this.vendor = '',
    this.approval = '',
  }) : id = id ?? const Uuid().v4();

  // Ex-Works cost in USD
  double get exWorksCost => quantity * exWorksUnitCost;

  // Local cost in local currency
  double get localCost => quantity * localUnitCost;

  // Installation cost in local currency
  double get installationCost => quantity * installationUnitCost;

  // Calculate total unit cost (ex-works + local + installation)
  double get totalUnitCost => exWorksUnitCost + localUnitCost + installationUnitCost;

  // Calculate total unit rate in SAR (with exchange rate applied to ex-works)
  // Round to nearest whole number
  double getTotalUnitRateInSAR(double exchangeRate) {
    final calculatedRate = (exWorksUnitCost * exchangeRate) + localUnitCost + installationUnitCost;
    return calculatedRate.roundToDouble(); // Round to nearest whole number
  }

  // Total cost (total unit rate × quantity)
  double get totalCost {
    // Default exchange rate of 3.75 if not available
    const exchangeRate = 3.75;
    return getTotalUnitRateInSAR(exchangeRate) * quantity;
  }

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'name': name,
      'category': category,
      'quantity': quantity,
      'exWorksUnitCost': exWorksUnitCost,
      'localUnitCost': localUnitCost,
      'installationUnitCost': installationUnitCost,
      'isImported': isImported,
      'vendor': vendor,
      'approval': approval,
    };
  }

  factory EquipmentItem.fromMap(Map<String, dynamic> map) {
    return EquipmentItem(
      id: map['id'],
      name: map['name'],
      category: map['category'],
      quantity: map['quantity']?.toDouble() ?? 0.0,
      exWorksUnitCost: map['exWorksUnitCost']?.toDouble() ?? 0.0,
      localUnitCost: map['localUnitCost']?.toDouble() ?? 0.0,
      installationUnitCost: map['installationUnitCost']?.toDouble() ?? 0.0,
      isImported: map['isImported'] ?? true,
      vendor: map['vendor'] ?? '',
      approval: map['approval'] ?? '',
    );
  }
}

class ServiceItem {
  final String id;
  String description;
  String category; // Engineering, Management, Testing, Programming, etc.
  double quantity;
  String unit; // Hours, Days, Lumpsum, etc.
  double unitRate;

  ServiceItem({
    String? id,
    required this.description,
    this.category = 'Engineering',
    required this.quantity,
    this.unit = 'Days',
    required this.unitRate,
  }) : id = id ?? const Uuid().v4();

  double get totalCost => quantity * unitRate;

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'description': description,
      'category': category,
      'quantity': quantity,
      'unit': unit,
      'unitRate': unitRate,
    };
  }

  factory ServiceItem.fromMap(Map<String, dynamic> map) {
    return ServiceItem(
      id: map['id'],
      description: map['description'],
      category: map['category'] ?? 'Engineering',
      quantity: map['quantity']?.toDouble() ?? 0.0,
      unit: map['unit'] ?? 'Days',
      unitRate: map['unitRate']?.toDouble() ?? 0.0,
    );
  }
}

// For backward compatibility
class LaborItem extends ServiceItem {
  LaborItem({
    super.id,
    required super.description,
    required double hours,
    required double hourlyRate,
  }) : super(
          category: 'Engineering',
          quantity: hours,
          unit: 'Days',
          unitRate: hourlyRate,
        );

  double get hours => quantity;
  double get hourlyRate => unitRate;

  factory LaborItem.fromMap(Map<String, dynamic> map) {
    return LaborItem(
      id: map['id'],
      description: map['description'] ?? 'Engineering Service',
      hours: map['hours']?.toDouble() ?? 0.0,
      hourlyRate: map['hourlyRate']?.toDouble() ?? 0.0,
    );
  }
}
