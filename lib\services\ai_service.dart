import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'app_context_manager.dart';
import 'ai_tool_registry.dart';
import 'project_provider.dart';
import '../repositories/clean_agent_repository.dart';
import '../services/isar_service.dart';
import '../services/dynamic_clean_agent_service.dart';
import 'truly_intelligent_ai.dart';
import '../widgets/ai_interactive_dialog.dart';

/// AI Service for FireTool - Pure intelligence without models
class AIService {
  static final AIService _instance = AIService._internal();
  factory AIService() => _instance;
  AIService._internal();

  final AppContextManager _contextManager = AppContextManager();
  final TrulyIntelligentAI _intelligentAI = TrulyIntelligentAI();
  
  AIToolRegistry? _toolRegistry;
  bool _isInitialized = false;

  /// Initialize the AI service
  Future<bool> initialize(BuildContext context) async {
    if (_isInitialized) return true;

    try {
      // Setup tool registry for real app integration
      final isarService = Provider.of<IsarService>(context, listen: false);
      final projectProvider = Provider.of<ProjectProvider>(context, listen: false);
      
      final isar = await isarService.database;
      final cleanAgentRepo = CleanAgentRepository(isar);
      final dynamicService = DynamicCleanAgentService(isar);

      _toolRegistry = AIToolRegistry(
        cleanAgentRepository: cleanAgentRepo,
        projectProvider: projectProvider,
        dynamicService: dynamicService,
      );

      // Initialize intelligent AI systems
      await _intelligentAI.initialize();

      _isInitialized = true;
      debugPrint('✅ AI Service initialized successfully');
      return true;
    } catch (e) {
      debugPrint('❌ AI Service initialization failed: $e');
      return false;
    }
  }

  /// Check if AI is ready to use
  bool get isReady => _isInitialized;

  /// Process user query with AI
  Future<AIResponse> processQuery(String query, BuildContext context) async {
    if (!isReady) {
      return AIResponse(
        message: _getNotReadyMessage(),
        toolCalls: [],
        contextUsed: [],
        processingTimeMs: 0,
        success: false,
      );
    }

    try {
      final startTime = DateTime.now();
      
      // Use intelligent AI for analysis
      debugPrint('🧠 AI: User message: "$query"');
      
      // Analyze user intent with truly intelligent AI
      final analysis = await _intelligentAI.analyzeUserIntent(query);
      
      if (analysis['intent'] == 'calculate_clean_agent') {
        // Show interactive dialog for clean agent calculations
        debugPrint('🧠 AI: Showing intelligent dialog based on analysis');
        await showInteractiveDialog(context, query);
        
        final processingTime = DateTime.now().difference(startTime).inMilliseconds;
        return AIResponse(
          message: analysis['ai_thinking'] ?? 'I\'ll help you with that calculation.',
          toolCalls: [],
          contextUsed: ['Intelligent Analysis'],
          processingTimeMs: processingTime,
          success: true,
        );
      } else {
        // Process with intelligent AI
        final response = await _intelligentAI.processQuery(query, context);
        final processingTime = DateTime.now().difference(startTime).inMilliseconds;
        
        return AIResponse(
          message: response,
          toolCalls: [],
          contextUsed: ['Intelligent Analysis'],
          processingTimeMs: processingTime,
          success: true,
        );
      }
    } catch (e) {
      debugPrint('❌ AI query processing failed: $e');
      return AIResponse(
        message: 'Sorry, I encountered an error processing your request: $e',
        toolCalls: [],
        contextUsed: [],
        processingTimeMs: 0,
        success: false,
      );
    }
  }

  /// Show interactive dialog for calculations
  Future<void> showInteractiveDialog(BuildContext context, String query) async {
    // Extract data from user input
    final extractedData = _extractDataFromQuery(query);
    
    // Analyze the query to determine what options to show
    final options = _analyzeQueryForOptions(query);

    if (options.isNotEmpty) {
      showDialog(
        context: context,
        builder: (context) => AIInteractiveDialog(
          title: 'Clean Agent Calculator',
          message: 'I need some additional information to provide accurate results:',
          options: options,
          extractedData: extractedData,
          onClose: () => Navigator.of(context).pop(),
        ),
      );
    }
  }

  /// Analyze query to determine what options to show
  List<AIOption> _analyzeQueryForOptions(String query) {
    final queryLower = query.toLowerCase();
    List<AIOption> options = [];

    // Check if agent type is specified
    bool hasAgentType = queryLower.contains('fm200') ||
                       queryLower.contains('fm-200') ||
                       queryLower.contains('novec') ||
                       queryLower.contains('1230');
    if (!hasAgentType) {
      options.add(const AIOption(
        key: 'agent_type',
        title: 'Agent Type',
        description: 'Select the clean agent type',
        choices: [
          AIChoice(value: 'FM200', label: 'FM200', description: 'HFC-227ea clean agent'),
          AIChoice(value: 'NOVEC1230', label: 'NOVEC 1230', description: '3M™ Novec™ 1230 fluid'),
        ],
      ));
    }

    // Check if installation type is specified
    bool hasInstallationType = queryLower.contains('supply only') ||
                              queryLower.contains('supply-only') ||
                              queryLower.contains('supply and install') ||
                              queryLower.contains('supply & install') ||
                              queryLower.contains('installation') ||
                              queryLower.contains('install');
    if (!hasInstallationType) {
      options.add(const AIOption(
        key: 'installation_type',
        title: 'Installation Type',
        description: 'Select the installation scope',
        choices: [
          AIChoice(value: 'supply_only', label: 'Supply Only', description: 'Equipment supply only'),
          AIChoice(value: 'supply_install', label: 'Supply & Install', description: 'Supply and installation'),
        ],
      ));
    }

    // Check if system type is specified - improved detection
    bool hasSystemType = queryLower.contains('main only') ||
                        queryLower.contains('main and reserve') ||
                        queryLower.contains('main + reserve') ||
                        queryLower.contains('reserve') ||
                        queryLower.contains('backup') ||
                        queryLower.contains('single system') ||
                        queryLower.contains('dual system') ||
                        queryLower.contains('redundant') ||
                        queryLower.contains('primary and backup') ||
                        queryLower.contains('main system only') ||
                        (queryLower.contains('main') && queryLower.contains('backup'));

    debugPrint('🧠 AI: System type detection for "$query": hasSystemType = $hasSystemType');

    if (!hasSystemType) {
      options.add(const AIOption(
        key: 'system_type',
        title: 'System Type',
        description: 'Select the system configuration',
        choices: [
          AIChoice(value: 'main_only', label: 'Main System Only', description: 'Single suppression system'),
          AIChoice(value: 'main_reserve', label: 'Main + Reserve', description: 'Primary system with backup reserve'),
        ],
      ));
    }

    return options;
  }

  Map<String, dynamic> _extractDataFromQuery(String query) {
    final queryLower = query.toLowerCase();
    Map<String, dynamic> extractedData = {};

    // Extract agent type
    if (queryLower.contains('fm200') || queryLower.contains('fm-200')) {
      extractedData['agent_type'] = 'FM200';
    } else if (queryLower.contains('novec') || queryLower.contains('1230')) {
      extractedData['agent_type'] = 'NOVEC1230';
    }

    // Extract system type with improved detection
    if (queryLower.contains('main only') || queryLower.contains('main system only') || queryLower.contains('single system')) {
      extractedData['system_type'] = 'main_only';
    } else if (queryLower.contains('main and reserve') || 
               queryLower.contains('main + reserve') || 
               queryLower.contains('reserve') || 
               queryLower.contains('backup') ||
               queryLower.contains('dual system') ||
               queryLower.contains('redundant') ||
               queryLower.contains('primary and backup') ||
               (queryLower.contains('main') && queryLower.contains('backup'))) {
      extractedData['system_type'] = 'main_reserve';
    }

    // Extract installation type
    if (queryLower.contains('supply only') || queryLower.contains('supply-only')) {
      extractedData['installation_type'] = 'supply_only';
    } else if (queryLower.contains('supply and install') || 
               queryLower.contains('supply & install') ||
               queryLower.contains('installation') ||
               queryLower.contains('install')) {
      extractedData['installation_type'] = 'supply_install';
    }

    // Extract agent quantity/weight
    final weightRegex = RegExp(r'(\d+(?:\.\d+)?)\s*kg');
    final weightMatch = weightRegex.firstMatch(queryLower);
    if (weightMatch != null) {
      extractedData['agent_quantity'] = double.parse(weightMatch.group(1)!);
      extractedData['input_type'] = 'agent_quantity';
    }

    return extractedData;
  }

  String _getNotReadyMessage() {
    if (!_isInitialized) {
      return 'AI service not initialized. Please restart the app.';
    }

    return '''🤖 **AI Assistant Ready!**

Your FireTool AI assistant is ready to help with fire suppression calculations and design.

**Try asking:**
- "90 kg FM200"
- "Calculate clean agent for server room"
- "Show me installation options"
- "Main and reserve system for 100 kg NOVEC"

**Features:**
🎤 Voice input (English & Arabic) • 🧠 Smart analysis • 💰 Real pricing''';
  }

  /// Get current status
  AIServiceStatus get status {
    if (!_isInitialized) return AIServiceStatus.notInitialized;
    return AIServiceStatus.ready;
  }
}

/// AI service status
enum AIServiceStatus {
  notInitialized,
  ready,
}

/// AI response wrapper
class AIResponse {
  final String message;
  final List<String> toolCalls;
  final List<String> contextUsed;
  final int processingTimeMs;
  final bool success;

  AIResponse({
    required this.message,
    required this.toolCalls,
    required this.contextUsed,
    required this.processingTimeMs,
    required this.success,
  });
}


