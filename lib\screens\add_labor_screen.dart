import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../constants/app_constants.dart';
import '../models/project.dart';
import '../services/project_provider.dart';

class AddLaborScreen extends StatefulWidget {
  final String systemId;
  
  const AddLaborScreen({super.key, required this.systemId});

  @override
  State<AddLaborScreen> createState() => _AddLaborScreenState();
}

class _AddLaborScreenState extends State<AddLaborScreen> {
  final _formKey = GlobalKey<FormState>();
  final _descriptionController = TextEditingController();
  final _hoursController = TextEditingController();
  final _hourlyRateController = TextEditingController();
  
  String _selectedLaborCategory = AppConstants.laborCategories.first;
  
  @override
  void initState() {
    super.initState();
    // Set default hourly rate based on selected category
    _updateHourlyRate();
  }
  
  @override
  void dispose() {
    _descriptionController.dispose();
    _hoursController.dispose();
    _hourlyRateController.dispose();
    super.dispose();
  }
  
  void _updateHourlyRate() {
    final defaultRate = AppConstants.defaultLaborRates[_selectedLaborCategory] ?? 75.0;
    _hourlyRateController.text = defaultRate.toString();
  }
  
  void _addLabor() {
    if (!_formKey.currentState!.validate()) {
      return;
    }
    
    final labor = LaborItem(
      description: _descriptionController.text.isEmpty 
          ? _selectedLaborCategory 
          : _descriptionController.text.trim(),
      hours: double.tryParse(_hoursController.text) ?? 0,
      hourlyRate: double.tryParse(_hourlyRateController.text) ?? 0,
    );
    
    Provider.of<ProjectProvider>(context, listen: false)
        .addLabor(widget.systemId, labor);
    
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Labor added')),
    );
    
    Navigator.pop(context);
  }
  
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Add Labor'),
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Form(
          key: _formKey,
          child: ListView(
            children: [
              DropdownButtonFormField<String>(
                decoration: const InputDecoration(
                  labelText: 'Labor Category',
                  prefixIcon: Icon(Icons.work),
                ),
                value: _selectedLaborCategory,
                items: AppConstants.laborCategories.map((category) {
                  return DropdownMenuItem<String>(
                    value: category,
                    child: Text(category),
                  );
                }).toList(),
                onChanged: (value) {
                  if (value != null) {
                    setState(() {
                      _selectedLaborCategory = value;
                      _updateHourlyRate();
                    });
                  }
                },
              ),
              const SizedBox(height: 16),
              TextFormField(
                controller: _descriptionController,
                decoration: const InputDecoration(
                  labelText: 'Description (Optional)',
                  hintText: 'Enter custom description or leave blank to use category',
                  prefixIcon: Icon(Icons.description),
                  helperText: 'If left blank, the labor category will be used',
                ),
              ),
              const SizedBox(height: 16),
              TextFormField(
                controller: _hoursController,
                decoration: const InputDecoration(
                  labelText: 'Hours',
                  hintText: 'Enter labor hours',
                  prefixIcon: Icon(Icons.timer),
                ),
                keyboardType: const TextInputType.numberWithOptions(decimal: true),
                validator: (value) {
                  if (value == null || value.trim().isEmpty) {
                    return 'Please enter hours';
                  }
                  if (double.tryParse(value) == null) {
                    return 'Please enter a valid number';
                  }
                  return null;
                },
              ),
              const SizedBox(height: 16),
              TextFormField(
                controller: _hourlyRateController,
                decoration: const InputDecoration(
                  labelText: 'Hourly Rate',
                  hintText: 'Enter hourly rate',
                  prefixIcon: Icon(Icons.attach_money),
                ),
                keyboardType: const TextInputType.numberWithOptions(decimal: true),
                validator: (value) {
                  if (value == null || value.trim().isEmpty) {
                    return 'Please enter an hourly rate';
                  }
                  if (double.tryParse(value) == null) {
                    return 'Please enter a valid number';
                  }
                  return null;
                },
              ),
              const SizedBox(height: 32),
              ElevatedButton(
                onPressed: _addLabor,
                style: ElevatedButton.styleFrom(
                  padding: const EdgeInsets.symmetric(vertical: 16),
                ),
                child: const Text('Add Labor', style: TextStyle(fontSize: 16)),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
