import 'package:flutter/material.dart';
import '../models/isar_models.dart';
import '../services/dynamic_schema_service.dart';

class SidebarProvider extends ChangeNotifier {
  final DynamicSchemaService _schemaService = DynamicSchemaService.instance;
  
  List<SidebarSection> _sections = [];
  SidebarSection? _selectedSection;
  FlexibleTable? _selectedTable;
  bool _isLoading = false;
  String? _error;

  // Getters
  List<SidebarSection> get sections => _sections;
  SidebarSection? get selectedSection => _selectedSection;
  FlexibleTable? get selectedTable => _selectedTable;
  bool get isLoading => _isLoading;
  String? get error => _error;

  // Get root sections (no parent)
  List<SidebarSection> get rootSections => 
      _sections.where((s) => s.parentSectionId == null).toList();

  // Get subsections for a parent
  List<SidebarSection> getSubsections(String parentSectionId) =>
      _sections.where((s) => s.parentSectionId == parentSectionId).toList();

  // Initialize and load sections
  Future<void> initialize() async {
    await loadSections();
  }

  // Load all sections
  Future<void> loadSections() async {
    _setLoading(true);
    try {
      _sections = await _schemaService.getAllSections();
      _error = null;
    } catch (e) {
      _error = e.toString();
    } finally {
      _setLoading(false);
    }
  }

  // Select a section
  Future<void> selectSection(SidebarSection section) async {
    _selectedSection = section;
    _selectedTable = null; // Clear table selection when section changes
    notifyListeners();
  }

  // Select a table
  void selectTable(FlexibleTable table) {
    _selectedTable = table;
    notifyListeners();
  }

  // Clear selection
  void clearSelection() {
    _selectedSection = null;
    _selectedTable = null;
    notifyListeners();
  }

  // Clear only table selection (keep section selected)
  void clearTableSelection() {
    _selectedTable = null;
    notifyListeners();
  }

  // Navigate back one level (table -> section -> main dashboard)
  void navigateBack() {
    if (_selectedTable != null) {
      // From table view -> go back to section view
      _selectedTable = null;
      notifyListeners();
    } else if (_selectedSection != null) {
      // From section view -> go back to main dashboard
      _selectedSection = null;
      notifyListeners();
    }
  }

  // Create a new section
  Future<String?> createSection({
    required String name,
    String? icon,
    String? color,
    String? parentSectionId,
    SystemType systemType = SystemType.custom,
  }) async {
    try {
      final sectionId = await _schemaService.createSection(
        name: name,
        icon: icon,
        color: color,
        parentSectionId: parentSectionId,
        systemType: systemType,
      );
      await loadSections(); // Refresh sections
      return sectionId;
    } catch (e) {
      _error = e.toString();
      notifyListeners();
      return null;
    }
  }

  // Update a section
  Future<bool> updateSection(String sectionId, {
    String? name,
    String? icon,
    String? color,
  }) async {
    try {
      await _schemaService.updateSection(
        sectionId,
        name: name,
        icon: icon,
        color: color,
      );
      await loadSections(); // Refresh sections
      return true;
    } catch (e) {
      _error = e.toString();
      notifyListeners();
      return false;
    }
  }

  // Delete a section
  Future<bool> deleteSection(String sectionId) async {
    try {
      await _schemaService.deleteSection(sectionId);
      
      // Clear selection if deleted section was selected
      if (_selectedSection?.sectionId == sectionId) {
        _selectedSection = null;
        _selectedTable = null;
      }
      
      await loadSections(); // Refresh sections
      return true;
    } catch (e) {
      _error = e.toString();
      notifyListeners();
      return false;
    }
  }

  // Reorder sections
  Future<bool> reorderSections(List<String> sectionIds) async {
    try {
      await _schemaService.reorderSections(sectionIds);
      await loadSections(); // Refresh sections
      return true;
    } catch (e) {
      _error = e.toString();
      notifyListeners();
      return false;
    }
  }

  // Create a new table in the selected section
  Future<String?> createTable({
    required String name,
    String? description,
  }) async {
    if (_selectedSection == null) return null;
    
    try {
      final tableId = await _schemaService.createTable(
        name: name,
        sectionId: _selectedSection!.sectionId!,
        description: description,
      );
      
      // Load the new table and select it
      final table = await _schemaService.getTable(tableId);
      if (table != null) {
        _selectedTable = table;
        notifyListeners();
      }
      
      return tableId;
    } catch (e) {
      _error = e.toString();
      notifyListeners();
      return null;
    }
  }

  // Get tables for the selected section
  Future<List<FlexibleTable>> getTablesForSelectedSection() async {
    if (_selectedSection == null) return [];

    try {
      return await _schemaService.getTablesForSection(_selectedSection!.sectionId!);
    } catch (e) {
      _error = e.toString();
      notifyListeners();
      return [];
    }
  }



  // Update the selected table
  Future<bool> updateSelectedTable({
    String? name,
    String? description,
  }) async {
    if (_selectedTable == null) return false;
    
    try {
      await _schemaService.updateTable(
        _selectedTable!.tableId!,
        name: name,
        description: description,
      );
      
      // Reload the table
      final updatedTable = await _schemaService.getTable(_selectedTable!.tableId!);
      if (updatedTable != null) {
        _selectedTable = updatedTable;
        notifyListeners();
      }
      
      return true;
    } catch (e) {
      _error = e.toString();
      notifyListeners();
      return false;
    }
  }

  // Delete the selected table
  Future<bool> deleteSelectedTable() async {
    if (_selectedTable == null) return false;
    
    try {
      await _schemaService.deleteTable(_selectedTable!.tableId!);
      _selectedTable = null;
      notifyListeners();
      return true;
    } catch (e) {
      _error = e.toString();
      notifyListeners();
      return false;
    }
  }

  // Clear error
  void clearError() {
    _error = null;
    notifyListeners();
  }

  // Private helper methods
  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  // Get section by ID
  SidebarSection? getSectionById(String sectionId) {
    try {
      return _sections.firstWhere((s) => s.sectionId == sectionId);
    } catch (e) {
      return null;
    }
  }

  // Check if section has subsections
  bool hasSubsections(String sectionId) {
    return _sections.any((s) => s.parentSectionId == sectionId);
  }

  // Get section hierarchy path
  List<SidebarSection> getSectionPath(String sectionId) {
    final path = <SidebarSection>[];
    SidebarSection? current = getSectionById(sectionId);
    
    while (current != null) {
      path.insert(0, current);
      current = current.parentSectionId != null 
          ? getSectionById(current.parentSectionId!)
          : null;
    }
    
    return path;
  }
}
