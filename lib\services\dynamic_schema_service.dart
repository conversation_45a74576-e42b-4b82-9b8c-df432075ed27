import 'package:isar/isar.dart';
import 'package:uuid/uuid.dart';
import 'dart:convert';
import '../models/isar_models.dart';
import 'isar_service.dart';

class DynamicSchemaService {
  static DynamicSchemaService? _instance;
  static DynamicSchemaService get instance {
    _instance ??= DynamicSchemaService._();
    return _instance!;
  }
  
  DynamicSchemaService._();
  
  final _uuid = const Uuid();

  Future<Isar> get _isar async => await IsarService.instance.database;

  // Section Management
  Future<List<SidebarSection>> getAllSections() async {
    final isar = await _isar;
    return await isar.sidebarSections.where().sortByOrderIndex().findAll();
  }

  Future<List<SidebarSection>> getRootSections() async {
    final isar = await _isar;
    return await isar.sidebarSections
        .filter()
        .parentSectionIdIsNull()
        .sortByOrderIndex()
        .findAll();
  }

  Future<List<SidebarSection>> getSubsections(String parentSectionId) async {
    final isar = await _isar;
    return await isar.sidebarSections
        .filter()
        .parentSectionIdEqualTo(parentSectionId)
        .sortByOrderIndex()
        .findAll();
  }

  Future<String> createSection({
    required String name,
    String? icon,
    String? color,
    String? parentSectionId,
    SystemType systemType = SystemType.custom,
  }) async {
    final isar = await _isar;
    final sectionId = _uuid.v4();
    final now = DateTime.now();

    // Get next order index
    final existingSections = parentSectionId != null
        ? await getSubsections(parentSectionId)
        : await getRootSections();
    final orderIndex = existingSections.length;

    final section = SidebarSection.create(
      sectionId: sectionId,
      name: name,
      icon: icon ?? 'folder',
      color: color ?? '#2196F3',
      orderIndex: orderIndex,
      parentSectionId: parentSectionId,
      systemType: systemType,
      createdAt: now,
      updatedAt: now,
    );

    await isar.writeTxn(() async {
      await isar.sidebarSections.put(section);
    });

    return sectionId;
  }

  Future<void> updateSection(String sectionId, {
    String? name,
    String? icon,
    String? color,
  }) async {
    final isar = await _isar;
    final section = await isar.sidebarSections
        .filter()
        .sectionIdEqualTo(sectionId)
        .findFirst();

    if (section != null) {
      section.name = name ?? section.name;
      section.icon = icon ?? section.icon;
      section.color = color ?? section.color;
      section.updatedAt = DateTime.now();

      await isar.writeTxn(() async {
        await isar.sidebarSections.put(section);
      });
    }
  }

  Future<void> deleteSection(String sectionId) async {
    final isar = await _isar;
    
    // Delete all tables in this section first
    final tables = await getTablesForSection(sectionId);
    for (final table in tables) {
      await deleteTable(table.tableId!);
    }

    // Delete all subsections
    final subsections = await getSubsections(sectionId);
    for (final subsection in subsections) {
      await deleteSection(subsection.sectionId!);
    }

    // Delete the section itself
    await isar.writeTxn(() async {
      await isar.sidebarSections
          .filter()
          .sectionIdEqualTo(sectionId)
          .deleteAll();
    });
  }

  Future<void> reorderSections(List<String> sectionIds) async {
    final isar = await _isar;
    
    await isar.writeTxn(() async {
      for (int i = 0; i < sectionIds.length; i++) {
        final section = await isar.sidebarSections
            .filter()
            .sectionIdEqualTo(sectionIds[i])
            .findFirst();
        if (section != null) {
          section.orderIndex = i;
          section.updatedAt = DateTime.now();
          await isar.sidebarSections.put(section);
        }
      }
    });
  }

  // Table Management
  Future<List<FlexibleTable>> getTablesForSection(String sectionId) async {
    final isar = await _isar;
    return await isar.flexibleTables
        .filter()
        .sectionIdEqualTo(sectionId)
        .findAll();
  }

  Future<FlexibleTable?> getTable(String tableId) async {
    final isar = await _isar;
    return await isar.flexibleTables
        .filter()
        .tableIdEqualTo(tableId)
        .findFirst();
  }

  // NEW: Get table by name and section name (more robust)
  Future<FlexibleTable?> getTableByName(String tableName, String sectionName) async {
    final isar = await _isar;

    // First find the section by name
    final section = await isar.sidebarSections
        .filter()
        .nameEqualTo(sectionName)
        .findFirst();

    if (section == null) return null;

    // Then find the table by name in that section
    return await isar.flexibleTables
        .filter()
        .nameEqualTo(tableName)
        .and()
        .sectionIdEqualTo(section.sectionId)
        .findFirst();
  }

  // NEW: Get table by name in any section (fallback)
  Future<FlexibleTable?> getTableByNameAny(String tableName) async {
    final isar = await _isar;
    return await isar.flexibleTables
        .filter()
        .nameEqualTo(tableName)
        .findFirst();
  }

  Future<List<FlexibleTable>> getAllTables() async {
    final isar = await _isar;
    return await isar.flexibleTables.where().findAll();
  }

  Future<String> createTable({
    required String name,
    required String sectionId,
    String? description,
  }) async {
    final isar = await _isar;
    final tableId = _uuid.v4();
    final now = DateTime.now();

    final table = FlexibleTable.create(
      tableId: tableId,
      name: name,
      description: description,
      sectionId: sectionId,
      createdAt: now,
      updatedAt: now,
    );

    await isar.writeTxn(() async {
      await isar.flexibleTables.put(table);
    });

    return tableId;
  }

  // NEW: Create table by section name (more robust)
  Future<String?> createTableByName({
    required String tableName,
    required String sectionName,
    String? description,
  }) async {
    final isar = await _isar;

    // Find section by name
    final section = await isar.sidebarSections
        .filter()
        .nameEqualTo(sectionName)
        .findFirst();

    if (section?.sectionId == null) {
      print('ERROR: Section "$sectionName" not found');
      return null;
    }

    // Check if table already exists
    final existingTable = await getTableByName(tableName, sectionName);
    if (existingTable != null) {
      print('Table "$tableName" already exists in section "$sectionName"');
      return existingTable.tableId;
    }

    return await createTable(
      name: tableName,
      sectionId: section!.sectionId!,
      description: description,
    );
  }

  Future<void> updateTable(String tableId, {
    String? name,
    String? description,
  }) async {
    final isar = await _isar;
    final table = await isar.flexibleTables
        .filter()
        .tableIdEqualTo(tableId)
        .findFirst();

    if (table != null) {
      table.name = name ?? table.name;
      table.description = description ?? table.description;
      table.updatedAt = DateTime.now();

      await isar.writeTxn(() async {
        await isar.flexibleTables.put(table);
      });
    }
  }

  /// Move a table to a different section
  Future<void> moveTableToSection(String tableId, String newSectionId) async {
    final isar = await _isar;
    final table = await isar.flexibleTables
        .filter()
        .tableIdEqualTo(tableId)
        .findFirst();

    if (table != null) {
      table.sectionId = newSectionId;
      table.updatedAt = DateTime.now();

      await isar.writeTxn(() async {
        await isar.flexibleTables.put(table);
      });
    }
  }

  Future<void> deleteTable(String tableId) async {
    final isar = await _isar;

    // Delete all rows and columns first
    await isar.writeTxn(() async {
      await isar.flexibleRows
          .filter()
          .tableIdEqualTo(tableId)
          .deleteAll();

      await isar.flexibleColumns
          .filter()
          .tableIdEqualTo(tableId)
          .deleteAll();

      await isar.flexibleTables
          .filter()
          .tableIdEqualTo(tableId)
          .deleteAll();
    });
  }

  // Column Management
  Future<List<FlexibleColumn>> getColumnsForTable(String tableId) async {
    final isar = await _isar;
    return await isar.flexibleColumns
        .filter()
        .tableIdEqualTo(tableId)
        .sortByOrderIndex()
        .findAll();
  }

  // NEW: Get columns by table name and section name
  Future<List<FlexibleColumn>> getColumnsForTableByName(String tableName, String sectionName) async {
    final table = await getTableByName(tableName, sectionName);
    if (table?.tableId == null) return [];
    return await getColumnsForTable(table!.tableId!);
  }

  // NEW: Get specific column by name in a table
  Future<FlexibleColumn?> getColumnByName(String columnName, String tableName, String sectionName) async {
    final columns = await getColumnsForTableByName(tableName, sectionName);
    return columns.where((col) => col.name == columnName).firstOrNull;
  }

  Future<String> createColumn({
    required String name,
    required String tableId,
    ColumnDataType dataType = ColumnDataType.text,
    CurrencyType currencyType = CurrencyType.usd,
    bool isRequired = false,
    String? defaultValue,
    String? validationRules,
    List<String>? dropdownOptions,
  }) async {
    final isar = await _isar;
    final columnId = _uuid.v4();
    final now = DateTime.now();

    // Get next order index
    final existingColumns = await getColumnsForTable(tableId);
    final orderIndex = existingColumns.length;

    final column = FlexibleColumn.create(
      columnId: columnId,
      name: name,
      tableId: tableId,
      dataType: dataType,
      currencyType: currencyType,
      isRequired: isRequired,
      defaultValue: defaultValue,
      orderIndex: orderIndex,
      validationRules: validationRules,
      dropdownOptions: dropdownOptions != null ? jsonEncode(dropdownOptions) : null,
      createdAt: now,
      updatedAt: now,
    );

    await isar.writeTxn(() async {
      await isar.flexibleColumns.put(column);
    });

    return columnId;
  }

  // NEW: Create column by table name and section name
  Future<String?> createColumnByName({
    required String columnName,
    required String tableName,
    required String sectionName,
    ColumnDataType dataType = ColumnDataType.text,
    CurrencyType currencyType = CurrencyType.usd,
    bool isRequired = false,
    String? defaultValue,
    String? validationRules,
    List<String>? dropdownOptions,
  }) async {
    final table = await getTableByName(tableName, sectionName);
    if (table?.tableId == null) {
      print('ERROR: Table "$tableName" not found in section "$sectionName"');
      return null;
    }

    // Check if column already exists
    final existingColumn = await getColumnByName(columnName, tableName, sectionName);
    if (existingColumn != null) {
      print('Column "$columnName" already exists in table "$tableName"');
      return existingColumn.columnId;
    }

    return await createColumn(
      name: columnName,
      tableId: table!.tableId!,
      dataType: dataType,
      currencyType: currencyType,
      isRequired: isRequired,
      defaultValue: defaultValue,
      validationRules: validationRules,
      dropdownOptions: dropdownOptions,
    );
  }

  Future<void> updateColumn(String columnId, {
    String? name,
    ColumnDataType? dataType,
    CurrencyType? currencyType,
    bool? isRequired,
    String? defaultValue,
    String? validationRules,
    List<String>? dropdownOptions,
  }) async {
    final isar = await _isar;
    final column = await isar.flexibleColumns
        .filter()
        .columnIdEqualTo(columnId)
        .findFirst();

    if (column != null) {
      column.name = name ?? column.name;
      column.dataType = dataType ?? column.dataType;
      column.currencyType = currencyType ?? column.currencyType;
      column.isRequired = isRequired ?? column.isRequired;
      column.defaultValue = defaultValue ?? column.defaultValue;
      column.validationRules = validationRules ?? column.validationRules;
      column.dropdownOptions = dropdownOptions != null
          ? jsonEncode(dropdownOptions)
          : column.dropdownOptions;
      column.updatedAt = DateTime.now();

      await isar.writeTxn(() async {
        await isar.flexibleColumns.put(column);
      });
    }
  }

  Future<void> deleteColumn(String columnId) async {
    final isar = await _isar;

    await isar.writeTxn(() async {
      await isar.flexibleColumns
          .filter()
          .columnIdEqualTo(columnId)
          .deleteAll();
    });
  }

  Future<void> reorderColumns(String tableId, List<String> columnIds) async {
    final isar = await _isar;

    await isar.writeTxn(() async {
      for (int i = 0; i < columnIds.length; i++) {
        final column = await isar.flexibleColumns
            .filter()
            .columnIdEqualTo(columnIds[i])
            .findFirst();
        if (column != null) {
          column.orderIndex = i;
          column.updatedAt = DateTime.now();
          await isar.flexibleColumns.put(column);
        }
      }
    });
  }

  // Row Management
  Future<List<FlexibleRow>> getRowsForTable(String tableId) async {
    final isar = await _isar;
    return await isar.flexibleRows
        .filter()
        .tableIdEqualTo(tableId)
        .findAll();
  }

  // NEW: Get rows by table name and section name
  Future<List<FlexibleRow>> getRowsForTableByName(String tableName, String sectionName) async {
    final table = await getTableByName(tableName, sectionName);
    if (table?.tableId == null) return [];
    return await getRowsForTable(table!.tableId!);
  }

  Future<String> createRow(String tableId) async {
    final isar = await _isar;
    final rowId = _uuid.v4();
    final now = DateTime.now();

    // Get columns for default values
    final columns = await getColumnsForTable(tableId);
    final defaultData = <String, dynamic>{};

    for (final column in columns) {
      if (column.defaultValue != null && column.defaultValue!.isNotEmpty) {
        defaultData[column.columnId!] = column.defaultValue;
      }
    }

    final row = FlexibleRow.create(
      rowId: rowId,
      tableId: tableId,
      data: defaultData.isNotEmpty ? jsonEncode(defaultData) : '{}',
      createdAt: now,
      updatedAt: now,
    );

    await isar.writeTxn(() async {
      await isar.flexibleRows.put(row);
    });

    return rowId;
  }

  // NEW: Create row by table name and section name
  Future<String?> createRowByName(String tableName, String sectionName) async {
    final table = await getTableByName(tableName, sectionName);
    if (table?.tableId == null) {
      print('ERROR: Table "$tableName" not found in section "$sectionName"');
      return null;
    }
    return await createRow(table!.tableId!);
  }

  Future<void> updateRowData(String rowId, Map<String, dynamic> data) async {
    final isar = await _isar;
    final row = await isar.flexibleRows
        .filter()
        .rowIdEqualTo(rowId)
        .findFirst();

    if (row != null) {
      row.data = jsonEncode(data);
      row.updatedAt = DateTime.now();

      await isar.writeTxn(() async {
        await isar.flexibleRows.put(row);
      });
    }
  }

  // NEW: Update row data using column names instead of column IDs
  Future<void> updateRowDataByNames(
    String rowId,
    String tableName,
    String sectionName,
    Map<String, dynamic> dataByColumnNames
  ) async {
    final columns = await getColumnsForTableByName(tableName, sectionName);
    if (columns.isEmpty) {
      print('ERROR: No columns found for table "$tableName" in section "$sectionName"');
      return;
    }

    // Convert column names to column IDs
    final dataByColumnIds = <String, dynamic>{};
    for (final entry in dataByColumnNames.entries) {
      final columnName = entry.key;
      final value = entry.value;

      final column = columns.where((col) => col.name == columnName).firstOrNull;
      if (column?.columnId != null) {
        dataByColumnIds[column!.columnId!] = value;
      } else {
        print('WARNING: Column "$columnName" not found in table "$tableName"');
      }
    }

    await updateRowData(rowId, dataByColumnIds);
  }

  Future<void> deleteRow(String rowId) async {
    final isar = await _isar;

    await isar.writeTxn(() async {
      await isar.flexibleRows
          .filter()
          .rowIdEqualTo(rowId)
          .deleteAll();
    });
  }

  Future<void> deleteAllRowsForTable(String tableId) async {
    final isar = await _isar;

    await isar.writeTxn(() async {
      await isar.flexibleRows
          .filter()
          .tableIdEqualTo(tableId)
          .deleteAll();
    });
  }
}
