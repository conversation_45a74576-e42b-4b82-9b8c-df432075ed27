import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/app_config_provider.dart';

class SettingsScreen extends StatefulWidget {
  const SettingsScreen({super.key});

  @override
  State<SettingsScreen> createState() => _SettingsScreenState();
}

class _SettingsScreenState extends State<SettingsScreen> {
  final _marginFactorController = TextEditingController();
  final _shippingFactorController = TextEditingController();
  final _dollarRateController = TextEditingController();

  @override
  void initState() {
    super.initState();
    final config = context.read<AppConfigProvider>();
    _marginFactorController.text = config.marginFactor.toString();
    _shippingFactorController.text = config.shippingExFactor.toString();
    _dollarRateController.text = config.dollarRateSarUsd.toString();
  }

  @override
  void dispose() {
    _marginFactorController.dispose();
    _shippingFactorController.dispose();
    _dollarRateController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Settings'),
        actions: [
          TextButton(
            onPressed: () {
              context.read<AppConfigProvider>().resetToDefaults();
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('Settings reset to defaults'),
                  backgroundColor: Colors.green,
                ),
              );
              Navigator.pop(context);
            },
            child: const Text('Reset to Defaults'),
          ),
        ],
      ),
      body: Consumer<AppConfigProvider>(
        builder: (context, config, child) {
          return Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Application Configuration',
                  style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 24),
                
                // Margin Factor
                Card(
                  child: Padding(
                    padding: const EdgeInsets.all(16),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'Pricing Configuration',
                          style: Theme.of(context).textTheme.titleMedium?.copyWith(
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        const SizedBox(height: 16),
                        
                        TextFormField(
                          controller: _marginFactorController,
                          decoration: const InputDecoration(
                            labelText: 'Margin Factor',
                            helperText: 'Multiplier for profit margin (e.g., 1.2 for 20% margin)',
                            border: OutlineInputBorder(),
                          ),
                          keyboardType: TextInputType.number,
                          onChanged: (value) {
                            final factor = double.tryParse(value);
                            if (factor != null && factor > 0) {
                              config.updateMarginFactor(factor);
                            }
                          },
                        ),
                        const SizedBox(height: 16),
                        
                        TextFormField(
                          controller: _shippingFactorController,
                          decoration: const InputDecoration(
                            labelText: 'Shipping Ex-Works Factor',
                            helperText: 'Multiplier for shipping costs (default: 1.15)',
                            border: OutlineInputBorder(),
                          ),
                          keyboardType: TextInputType.number,
                          onChanged: (value) {
                            final factor = double.tryParse(value);
                            if (factor != null && factor > 0) {
                              config.updateShippingExFactor(factor);
                            }
                          },
                        ),
                        const SizedBox(height: 16),
                        
                        TextFormField(
                          controller: _dollarRateController,
                          decoration: const InputDecoration(
                            labelText: 'USD to SAR Exchange Rate',
                            helperText: 'Current exchange rate (default: 3.75)',
                            border: OutlineInputBorder(),
                          ),
                          keyboardType: TextInputType.number,
                          onChanged: (value) {
                            final rate = double.tryParse(value);
                            if (rate != null && rate > 0) {
                              config.updateDollarRate(rate);
                            }
                          },
                        ),
                      ],
                    ),
                  ),
                ),
                const SizedBox(height: 16),
                
                // System Information
                Card(
                  child: Padding(
                    padding: const EdgeInsets.all(16),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'System Information',
                          style: Theme.of(context).textTheme.titleMedium?.copyWith(
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        const SizedBox(height: 16),
                        
                        _buildInfoRow('Agent Discharge Time', '${config.agentDischargeTimeSeconds} seconds'),
                        _buildInfoRow('Nozzle Size Threshold', '${config.nozzleSizeThresholdMm} mm'),
                        _buildInfoRow('Detector Coverage Area', '${config.detectorCoverageAreaM2} m²'),
                        _buildInfoRow('Default Room Height', '${config.defaultRoomHeightM} m'),
                        _buildInfoRow('343L Cylinder Disabled', config.no343LCylinder ? 'Yes' : 'No'),
                        _buildInfoRow('NOVEC Max Fill Ratio', '${(config.maxFillingRatio['NOVEC1230']! * 100).toInt()}%'),
                        _buildInfoRow('FM200 Max Fill Ratio', '${(config.maxFillingRatio['FM200']! * 100).toInt()}%'),
                      ],
                    ),
                  ),
                ),
                const SizedBox(height: 16),
                
                // Data Information
                Card(
                  child: Padding(
                    padding: const EdgeInsets.all(16),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'Data Information',
                          style: Theme.of(context).textTheme.titleMedium?.copyWith(
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        const SizedBox(height: 16),
                        
                        _buildInfoRow('Pipe Data Entries', '${config.pipeData.length}'),
                        _buildInfoRow('NOVEC Cylinders', '${config.cylinderSpecs['NOVEC1230']?.length ?? 0}'),
                        _buildInfoRow('FM200 Cylinders', '${config.cylinderSpecs['FM200']?.length ?? 0}'),
                        _buildInfoRow('Total Components', '${config.components.length}'),
                      ],
                    ),
                  ),
                ),
                
                const Spacer(),
                
                // Save button
                SizedBox(
                  width: double.infinity,
                  child: ElevatedButton(
                    onPressed: () {
                      config.saveConfig();
                      ScaffoldMessenger.of(context).showSnackBar(
                        const SnackBar(
                          content: Text('Settings saved successfully'),
                          backgroundColor: Colors.green,
                        ),
                      );
                      Navigator.pop(context);
                    },
                    style: ElevatedButton.styleFrom(
                      padding: const EdgeInsets.symmetric(vertical: 16),
                    ),
                    child: const Text(
                      'Save Settings',
                      style: TextStyle(fontSize: 16),
                    ),
                  ),
                ),
              ],
            ),
          );
        },
      ),
    );
  }

  Widget _buildInfoRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: const TextStyle(fontWeight: FontWeight.w500),
          ),
          Text(
            value,
            style: TextStyle(color: Colors.grey[600]),
          ),
        ],
      ),
    );
  }
}
