name: firetool_trae_new
description: "A new Flutter project."
publish_to: 'none'

version: 1.0.0+1

environment:
  sdk: '>=3.4.4 <4.0.0'

dependencies:
  flutter:
    sdk: flutter
  cupertino_icons: ^1.0.6
  sqflite: ^2.3.3+1
  sqflite_common_ffi: ^2.3.3
  path: ^1.9.0
  file_picker: ^8.0.7
  intl: ^0.19.0
  data_table_2: ^2.5.15
  csv: ^6.0.0
  excel: ^4.0.3
  provider: ^6.1.2
  uuid: ^4.4.0
  path_provider: ^2.1.3
  isar: ^3.1.0+1
  isar_flutter_libs: ^3.1.0+1
  reorderables: ^0.6.0
  flutter_colorpicker: ^1.1.0
  shared_preferences: ^2.2.3
  collection: ^1.18.0
  rxdart: ^0.27.7
  printing: ^5.11.1
  pdf: ^3.10.7
  file_selector: ^1.0.2
  syncfusion_flutter_datagrid: ^27.1.48

  # Supabase integration
  supabase_flutter: ^2.3.4
  postgrest: ^2.1.1

  # JSON handling for React app logic
  json_annotation: ^4.8.1

  # Offline AI Agent dependencies (Rust backend in progress)
  # flutter_rust_bridge: ^2.0.0  # Will be enabled after Rust setup
  ffi: ^2.1.0
  http: ^1.1.0
  url_launcher: ^6.2.5

  # Voice input and speech recognition
  speech_to_text: ^7.1.0
  permission_handler: ^11.3.1

dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_lints: ^3.0.0
  build_runner: ^2.4.9
  isar_generator: ^3.1.0+1
  json_serializable: ^6.7.1

flutter:
  uses-material-design: true
