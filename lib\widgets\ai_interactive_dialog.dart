import 'dart:math';
import 'package:flutter/material.dart';
import '../utils/dynamic_estimator_calculations.dart';
import '../utils/dynamic_bom_generator.dart';
import '../models/estimator_types.dart';

/// Interactive AI dialog that shows option buttons and popups like the real app
class AIInteractiveDialog extends StatefulWidget {
  final String title;
  final String message;
  final List<AIOption>? options;
  final Map<String, dynamic>? calculationResults;
  final Function(Map<String, dynamic>)? onCalculationComplete;
  final VoidCallback? onClose;
  final double? extractedWeight;
  final double? extractedVolume;
  final Map<String, dynamic>? extractedData;

  const AIInteractiveDialog({
    super.key,
    required this.title,
    required this.message,
    this.options,
    this.calculationResults,
    this.onCalculationComplete,
    this.onClose,
    this.extractedWeight,
    this.extractedVolume,
    this.extractedData,
  });

  @override
  State<AIInteractiveDialog> createState() => _AIInteractiveDialogState();
}

class _AIInteractiveDialogState extends State<AIInteractiveDialog> {
  final Map<String, dynamic> _selectedOptions = {};
  bool _showResults = false;

  @override
  Widget build(BuildContext context) {
    return Dialog(
      child: Container(
        width: 600,
        constraints: const BoxConstraints(maxHeight: 700),
        padding: const EdgeInsets.all(24),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisSize: MainAxisSize.min,
          children: [
            // Header
            Row(
              children: [
                Icon(
                  Icons.smart_toy,
                  color: Colors.red.shade700,
                  size: 24,
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Text(
                    widget.title,
                    style: const TextStyle(
                      fontSize: 20,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
                IconButton(
                  onPressed: () => Navigator.of(context).pop(),
                  icon: const Icon(Icons.close),
                ),
              ],
            ),
            const SizedBox(height: 16),

            // Message
            Text(
              widget.message,
              style: const TextStyle(fontSize: 14),
            ),
            const SizedBox(height: 20),

            // Options or Results
            Flexible(
              child: _showResults ? _buildResults() : _buildOptions(),
            ),

            // Action buttons
            const SizedBox(height: 20),
            _buildActionButtons(),
          ],
        ),
      ),
    );
  }

  Widget _buildOptions() {
    if (widget.options == null || widget.options!.isEmpty) {
      return const SizedBox.shrink();
    }

    return SingleChildScrollView(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: widget.options!.map((option) => _buildOptionGroup(option)).toList(),
      ),
    );
  }

  Widget _buildOptionGroup(AIOption option) {
    return Card(
      margin: const EdgeInsets.only(bottom: 16),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              option.title,
              style: const TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w600,
              ),
            ),
            if (option.description != null) ...[
              const SizedBox(height: 8),
              Text(
                option.description!,
                style: TextStyle(
                  fontSize: 14,
                  color: Colors.grey.shade600,
                ),
              ),
            ],
            const SizedBox(height: 12),
            Wrap(
              spacing: 8,
              runSpacing: 8,
              children: option.choices.map((choice) => _buildChoiceButton(option.key, choice)).toList(),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildChoiceButton(String optionKey, AIChoice choice) {
    final isSelected = _selectedOptions[optionKey] == choice.value;
    
    return ElevatedButton(
      onPressed: () {
        setState(() {
          _selectedOptions[optionKey] = choice.value;
        });
      },
      style: ElevatedButton.styleFrom(
        backgroundColor: isSelected ? Colors.red.shade700 : Colors.grey.shade100,
        foregroundColor: isSelected ? Colors.white : Colors.black87,
        elevation: isSelected ? 2 : 0,
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Text(
            choice.label,
            style: const TextStyle(fontWeight: FontWeight.w500),
          ),
          if (choice.description != null) ...[
            const SizedBox(height: 4),
            Text(
              choice.description!,
              style: TextStyle(
                fontSize: 12,
                color: isSelected ? Colors.white70 : Colors.grey.shade600,
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildResults() {
    if (widget.calculationResults == null) {
      return const Center(child: Text('No results available'));
    }

    final results = widget.calculationResults!;
    
    return SingleChildScrollView(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Navigation Steps
          if (results.containsKey('navigation_steps')) ...[
            const Text(
              '🧭 Navigation Steps:',
              style: TextStyle(fontSize: 16, fontWeight: FontWeight.w600),
            ),
            const SizedBox(height: 8),
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: (results['navigation_steps'] as List)
                      .asMap()
                      .entries
                      .map((entry) => Padding(
                            padding: const EdgeInsets.only(bottom: 4),
                            child: Text('${entry.key + 1}. ${entry.value}'),
                          ))
                      .toList(),
                ),
              ),
            ),
            const SizedBox(height: 16),
          ],

          // Form Inputs
          if (results.containsKey('form_inputs')) ...[
            const Text(
              '📋 Calculator Inputs:',
              style: TextStyle(fontSize: 16, fontWeight: FontWeight.w600),
            ),
            const SizedBox(height: 8),
            _buildFormInputsCard(results['form_inputs']),
            const SizedBox(height: 16),
          ],

          // Design Results
          if (results.containsKey('design_results')) ...[
            const Text(
              '📊 Design Results:',
              style: TextStyle(fontSize: 16, fontWeight: FontWeight.w600),
            ),
            const SizedBox(height: 8),
            _buildDesignResultsCard(results['design_results']),
            const SizedBox(height: 16),
          ],

          // Cost Summary
          if (results.containsKey('bom_summary')) ...[
            const Text(
              '💰 Cost Summary:',
              style: TextStyle(fontSize: 16, fontWeight: FontWeight.w600),
            ),
            const SizedBox(height: 8),
            _buildCostSummaryCard(results['bom_summary']),
          ],
        ],
      ),
    );
  }

  Widget _buildFormInputsCard(Map<String, dynamic> formInputs) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          children: formInputs.entries
              .map((entry) => Padding(
                    padding: const EdgeInsets.only(bottom: 8),
                    child: Row(
                      children: [
                        Expanded(
                          flex: 2,
                          child: Text(
                            _formatLabel(entry.key),
                            style: const TextStyle(fontWeight: FontWeight.w500),
                          ),
                        ),
                        Expanded(
                          flex: 3,
                          child: Text(entry.value.toString()),
                        ),
                      ],
                    ),
                  ))
              .toList(),
        ),
      ),
    );
  }

  Widget _buildDesignResultsCard(Map<String, dynamic> designResults) {
    List<Widget> rows = [
      _buildResultRow('Agent Weight Required', '${designResults['agent_weight_required']}kg'),
      _buildResultRow('Room Volume', '${designResults['room_volume']}m³'),
    ];

    if (designResults.containsKey('room_dimensions')) {
      final dimensions = designResults['room_dimensions'] as Map<String, dynamic>;
      rows.add(_buildResultRow('Room Dimensions', '${dimensions['length']}m × ${dimensions['width']}m × ${dimensions['height']}m'));
    }

    if (designResults.containsKey('cylinder_configuration')) {
      final cylinder = designResults['cylinder_configuration'] as Map<String, dynamic>;
      rows.add(_buildResultRow('Cylinders', '${cylinder['quantity']} × ${cylinder['type']}'));
    }

    if (designResults.containsKey('nozzle_configuration')) {
      final nozzle = designResults['nozzle_configuration'] as Map<String, dynamic>;
      rows.add(_buildResultRow('Nozzles', '${nozzle['quantity']} units'));
    }

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(children: rows),
      ),
    );
  }

  Widget _buildCostSummaryCard(Map<String, dynamic> bomSummary) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
            _buildResultRow('Suppression System', '\$${bomSummary['suppression_cost_usd']} USD'),
            _buildResultRow('Alarm & Detection', '\$${bomSummary['alarm_cost_usd']} USD'),
            _buildResultRow('Ex-works Total', '\$${bomSummary['ex_works_total_usd']} USD'),
            const Divider(),
            _buildResultRow('Landed Cost', '${bomSummary['landed_cost_sar']} SAR'),
            _buildResultRow('Installation Materials', '${bomSummary['installation_materials_sar']} SAR'),
            _buildResultRow('Installation Labor', '${bomSummary['installation_labor_sar']} SAR'),
            const Divider(),
            _buildResultRow('Grand Total', '${bomSummary['grand_total_sar']} SAR', isTotal: true),
          ],
        ),
      ),
    );
  }

  Widget _buildResultRow(String label, String value, {bool isTotal = false}) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        children: [
          Expanded(
            flex: 2,
            child: Text(
              label,
              style: TextStyle(
                fontWeight: isTotal ? FontWeight.bold : FontWeight.w500,
                fontSize: isTotal ? 16 : 14,
              ),
            ),
          ),
          Expanded(
            flex: 2,
            child: Text(
              value,
              style: TextStyle(
                fontWeight: isTotal ? FontWeight.bold : FontWeight.normal,
                fontSize: isTotal ? 16 : 14,
                color: isTotal ? Colors.red.shade700 : null,
              ),
              textAlign: TextAlign.right,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildActionButtons() {
    if (_showResults) {
      return Row(
        children: [
          TextButton(
            onPressed: () {
              setState(() {
                _showResults = false;
              });
            },
            child: const Text('Back to Options'),
          ),
          const Spacer(),
          ElevatedButton.icon(
            onPressed: () {
              // Show BOM details
              _showBomDialog();
            },
            icon: const Icon(Icons.list_alt),
            label: const Text('Show BOM'),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.blue.shade700,
              foregroundColor: Colors.white,
            ),
          ),
          const SizedBox(width: 8),
          ElevatedButton.icon(
            onPressed: () {
              // Show calculation details
              _showCalculationDialog();
            },
            icon: const Icon(Icons.calculate),
            label: const Text('Show Calculation'),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.green.shade700,
              foregroundColor: Colors.white,
            ),
          ),
        ],
      );
    } else {
      return Row(
        children: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          const Spacer(),
          ElevatedButton(
            onPressed: _canCalculate() ? _performCalculation : null,
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red.shade700,
              foregroundColor: Colors.white,
            ),
            child: const Text('Calculate'),
          ),
        ],
      );
    }
  }

  bool _canCalculate() {
    if (widget.options == null) return true;
    
    for (final option in widget.options!) {
      if (option.required && !_selectedOptions.containsKey(option.key)) {
        return false;
      }
    }
    return true;
  }

  void _performCalculation() async {
    // Perform calculation with selected options
    try {
      debugPrint('🧮 AI Dialog: Performing calculation with selected options: $_selectedOptions');

      // Get selected values, using extracted data as defaults
      final agentType = _selectedOptions['agent_type'] ??
          widget.extractedData?['agent_type'] ?? 'NOVEC1230';
      final installationType = _selectedOptions['installation_type'] ??
          widget.extractedData?['installation_type'] ?? 'supply_only';
      final systemType = _selectedOptions['system_type'] ??
          widget.extractedData?['system_type'] ?? 'main_only';
      final concentration = _selectedOptions['concentration'] ?? (agentType == 'FM200' ? '7.4%' : '4.5%');

      debugPrint('🧮 AI Dialog: Using agent type: $agentType (from ${_selectedOptions.containsKey('agent_type') ? 'selection' : 'extracted data'})');
      debugPrint('🧮 AI Dialog: Using installation type: $installationType (from ${_selectedOptions.containsKey('installation_type') ? 'selection' : 'extracted data'})');
      debugPrint('🧮 AI Dialog: Using system type: $systemType (from ${_selectedOptions.containsKey('system_type') ? 'selection' : 'extracted data'})');
      debugPrint('🧮 AI Dialog: Using concentration: $concentration');

      // Convert to your app's enum types
      final agentTypeEnum = agentType == 'FM200' ? AgentType.fm200 : AgentType.novec1230;
      final installationTypeEnum = installationType == 'supply_install'
          ? InstallationType.supplyAndInstall
          : InstallationType.supplyOnly;
      final systemTypeEnum = systemType == 'main_reserve'
          ? SystemType.mainAndReserve
          : SystemType.main;

      // Intelligently determine input mode and values
      InputMode inputMode = InputMode.agentQuantity;
      double? agentWeight;
      double? roomLength, roomWidth, roomHeight = 3.0;

      if (widget.extractedVolume != null) {
        // Use volume mode - convert to dimensions
        inputMode = InputMode.dimensions;
        final volume = widget.extractedVolume!;
        const height = 3.0; // Default height
        final area = volume / height;
        final side = sqrt(area);

        roomLength = side;
        roomWidth = side;
        roomHeight = height;

        debugPrint('🧮 AI Dialog: Using REAL app calculation - Agent: $agentTypeEnum, Volume: ${volume}m³ (${side.toStringAsFixed(1)}m × ${side.toStringAsFixed(1)}m × ${height}m)');
      } else {
        // Use weight mode
        agentWeight = widget.extractedWeight ?? 60.0;
        debugPrint('🧮 AI Dialog: Using REAL app calculation - Agent: $agentTypeEnum, Weight: ${agentWeight}kg');
      }

      // Create EstimatorFormValues exactly like your app does
      final input = EstimatorFormValues(
        agentType: agentTypeEnum,
        designConcentration: concentration,
        inputMode: inputMode,
        agentQuantity: agentWeight,
        roomLength: roomLength,
        roomWidth: roomWidth,
        roomHeight: roomHeight,
        systemType: systemTypeEnum,
        installationType: installationTypeEnum,
      );

      debugPrint('🧮 AI Dialog: Calling DynamicEstimatorCalculations.calculateDesign()');

      // Use your REAL calculation engine
      final designResults = await DynamicEstimatorCalculations.calculateDesign(input);

      debugPrint('🧮 AI Dialog: Design calculation complete - Room: ${designResults.roomData.roomLength.toStringAsFixed(1)}m × ${designResults.roomData.roomWidth.toStringAsFixed(1)}m');

      // Generate REAL BOM using your app's logic
      final bomData = await DynamicBomGenerator.generateBOM(designResults, input, installationFactor: 0.15);
      final bomItems = bomData['bom'] as List<BomItem>;
      final bomSummary = bomData['summary'] as BomSummary;

      debugPrint('🧮 AI Dialog: BOM generation complete - ${bomItems.length} items, Total: ${bomSummary.grandTotalSAR.round()} SAR');

      // Convert BOM items to format expected by UI
      final bomItemsForUI = bomItems.map((item) => {
        'part_no': item.partNo,
        'description': item.description,
        'quantity': item.quantity,
        'unit_cost': item.unitCost,
        'total_cost': item.totalCost,
        'category': item.category,
      }).toList();

      // Create detailed calculation results
      final calculationResults = {
        'form_inputs': {
          'agent_type': agentType,
          'design_concentration': concentration,
          'input_mode': 'Agent Quantity',
          'agent_quantity': agentWeight,
          'system_type': systemType,
          'installation_type': installationType,
        },
        'bom_items': bomItemsForUI,
        'design_results': {
          'agent_weight_required': designResults.totalAgentRequired,
          'room_volume': designResults.roomData.roomVolume.round(),
          'room_dimensions': {
            'length': designResults.roomData.roomLength.toStringAsFixed(1),
            'width': designResults.roomData.roomWidth.toStringAsFixed(1),
            'height': designResults.roomData.roomHeight.toStringAsFixed(1),
          },
          'cylinder_configuration': {
            'type': '${designResults.cylinder.cylinderSizeLiters2ndIter}L',
            'quantity': designResults.cylinder.numCylinders2ndIter,
            'agent_per_cylinder': '${designResults.cylinder.qtyPerCylinder.toStringAsFixed(1)}kg',
          },
          'nozzle_configuration': {
            'quantity': designResults.discharge.nozzleQtyFinal,
            'coverage_area': '${(designResults.roomData.roomArea / designResults.discharge.nozzleQtyFinal).toStringAsFixed(1)}m² per nozzle',
          },
        },
        'bom_summary': {
          'suppression_cost_usd': bomSummary.suppressionCost.round(),
          'alarm_cost_usd': bomSummary.alarmCost.round(),
          'ex_works_total_usd': bomSummary.exWorksItemsUSD.round(),
          'landed_cost_sar': bomSummary.landedCostSAR.round(),
          'installation_materials_sar': bomSummary.installationMaterialsSAR.round(),
          'installation_labor_sar': bomSummary.installationLaborSAR.round(),
          'grand_total_sar': bomSummary.grandTotalSAR.round(),
        },
        'screen_display': {
          'title': 'Clean Agent Calculator',
          'calculation_status': 'Completed Successfully',
          'results_visible': true,
          'bom_visible': true,
          'save_button_enabled': true,
        },
      };

      debugPrint('🧮 AI Dialog: REAL calculation completed - Total: ${bomSummary.grandTotalSAR.round()} SAR');

      // Notify parent with results
      if (widget.onCalculationComplete != null) {
        widget.onCalculationComplete!(calculationResults);
      }

      // Close dialog and let parent handle results display
      if (mounted) {
        Navigator.of(context).pop();
      }

    } catch (e) {
      debugPrint('❌ AI Dialog: Calculation failed: $e');
      // Show error
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Calculation failed: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  void _showBomDialog() {
    showDialog(
      context: context,
      builder: (context) => const BomDetailsDialog(),
    );
  }

  void _showCalculationDialog() {
    showDialog(
      context: context,
      builder: (context) => const CalculationDetailsDialog(),
    );
  }

  List<Map<String, dynamic>> _generateBOMItems(String agentType, double agentWeight, String installationType, String systemType) {
    List<Map<String, dynamic>> bomItems = [];

    // 1. Clean Agent
    bomItems.add({
      'part_no': agentType == 'FM200' ? 'FM200-AGENT' : 'NOVEC-AGENT',
      'description': '$agentType Clean Agent',
      'quantity': agentWeight.round(),
      'unit': 'kg',
      'unit_cost': agentType == 'FM200' ? 35.0 : 45.0,
      'total_cost': (agentWeight * (agentType == 'FM200' ? 35.0 : 45.0)).round(),
      'category': 'Suppression',
    });

    // 2. Cylinders
    final cylindersNeeded = (agentWeight / 65).ceil();
    bomItems.add({
      'part_no': 'CYL-180L',
      'description': '180L Pressure Cylinder',
      'quantity': cylindersNeeded,
      'unit': 'pcs',
      'unit_cost': 850.0,
      'total_cost': (850.0 * cylindersNeeded).round(),
      'category': 'Suppression',
    });

    // 3. Valve Assembly
    bomItems.add({
      'part_no': 'VALVE-ASSY',
      'description': 'Cylinder Valve Assembly',
      'quantity': cylindersNeeded,
      'unit': 'pcs',
      'unit_cost': 320.0,
      'total_cost': (320.0 * cylindersNeeded).round(),
      'category': 'Suppression',
    });

    // 4. Nozzles
    final nozzlesNeeded = ((agentWeight / 65) * 4).ceil();
    bomItems.add({
      'part_no': 'NOZZLE-360',
      'description': '360° Discharge Nozzle',
      'quantity': nozzlesNeeded,
      'unit': 'pcs',
      'unit_cost': 150.0,
      'total_cost': (150.0 * nozzlesNeeded).round(),
      'category': 'Suppression',
    });

    // 5. Control Panel
    bomItems.add({
      'part_no': 'CTRL-PANEL',
      'description': 'Fire Control Panel',
      'quantity': 1,
      'unit': 'pcs',
      'unit_cost': 1200.0,
      'total_cost': 1200,
      'category': 'Alarm & Detection',
    });

    // 6. Smoke Detectors
    final detectorsNeeded = ((agentWeight / 65) * 2).ceil();
    bomItems.add({
      'part_no': 'SMOKE-DET',
      'description': 'Addressable Smoke Detector',
      'quantity': detectorsNeeded,
      'unit': 'pcs',
      'unit_cost': 85.0,
      'total_cost': (85.0 * detectorsNeeded).round(),
      'category': 'Alarm & Detection',
    });

    return bomItems;
  }

  String _formatLabel(String key) {
    return key.split('_').map((word) => word[0].toUpperCase() + word.substring(1)).join(' ');
  }
}

/// AI Option for user selection
class AIOption {
  final String key;
  final String title;
  final String? description;
  final List<AIChoice> choices;
  final bool required;

  const AIOption({
    required this.key,
    required this.title,
    required this.choices,
    this.description,
    this.required = true,
  });
}

/// AI Choice within an option
class AIChoice {
  final String value;
  final String label;
  final String? description;

  const AIChoice({
    required this.value,
    required this.label,
    this.description,
  });
}

/// BOM Details Dialog
class BomDetailsDialog extends StatelessWidget {
  const BomDetailsDialog({super.key});

  @override
  Widget build(BuildContext context) {
    return Dialog(
      child: Container(
        width: 800,
        height: 600,
        padding: const EdgeInsets.all(24),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.list_alt, color: Colors.blue.shade700),
                const SizedBox(width: 12),
                const Text(
                  'Bill of Materials',
                  style: TextStyle(fontSize: 20, fontWeight: FontWeight.w600),
                ),
                const Spacer(),
                IconButton(
                  onPressed: () => Navigator.of(context).pop(),
                  icon: const Icon(Icons.close),
                ),
              ],
            ),
            const SizedBox(height: 16),
            const Expanded(
              child: Center(
                child: Text('BOM details would be shown here...'),
              ),
            ),
          ],
        ),
      ),
    );
  }
}

/// Calculation Details Dialog
class CalculationDetailsDialog extends StatelessWidget {
  const CalculationDetailsDialog({super.key});

  @override
  Widget build(BuildContext context) {
    return Dialog(
      child: Container(
        width: 800,
        height: 600,
        padding: const EdgeInsets.all(24),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.calculate, color: Colors.green.shade700),
                const SizedBox(width: 12),
                const Text(
                  'Calculation Details',
                  style: TextStyle(fontSize: 20, fontWeight: FontWeight.w600),
                ),
                const Spacer(),
                IconButton(
                  onPressed: () => Navigator.of(context).pop(),
                  icon: const Icon(Icons.close),
                ),
              ],
            ),
            const SizedBox(height: 16),
            const Expanded(
              child: Center(
                child: Text('Calculation details would be shown here...'),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
