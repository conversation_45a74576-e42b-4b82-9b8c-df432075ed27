
enum ActionType {
  cellEdit,
  rowAdd,
  rowDelete,
  columnAdd,
  columnDelete,
  columnEdit,
  tableCreate,
  tableDelete,
  tableEdit,
}

class UndoRedoAction {
  final String id;
  final ActionType type;
  final String description;
  final DateTime timestamp;
  final Map<String, dynamic> data;
  final Map<String, dynamic>? previousData;

  UndoRedoAction({
    required this.id,
    required this.type,
    required this.description,
    required this.timestamp,
    required this.data,
    this.previousData,
  });

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'type': type.name,
      'description': description,
      'timestamp': timestamp.toIso8601String(),
      'data': data,
      'previousData': previousData,
    };
  }

  factory UndoRedoAction.fromJson(Map<String, dynamic> json) {
    return UndoRedoAction(
      id: json['id'],
      type: ActionType.values.firstWhere((e) => e.name == json['type']),
      description: json['description'],
      timestamp: DateTime.parse(json['timestamp']),
      data: json['data'],
      previousData: json['previousData'],
    );
  }
}

class CellEditAction {
  final String tableId;
  final String rowId;
  final String columnId;
  final dynamic newValue;
  final dynamic oldValue;

  CellEditAction({
    required this.tableId,
    required this.rowId,
    required this.columnId,
    required this.newValue,
    required this.oldValue,
  });

  Map<String, dynamic> toJson() {
    return {
      'tableId': tableId,
      'rowId': rowId,
      'columnId': columnId,
      'newValue': newValue,
      'oldValue': oldValue,
    };
  }

  factory CellEditAction.fromJson(Map<String, dynamic> json) {
    return CellEditAction(
      tableId: json['tableId'],
      rowId: json['rowId'],
      columnId: json['columnId'],
      newValue: json['newValue'],
      oldValue: json['oldValue'],
    );
  }
}

class RowAction {
  final String tableId;
  final String rowId;
  final Map<String, dynamic>? rowData;

  RowAction({
    required this.tableId,
    required this.rowId,
    this.rowData,
  });

  Map<String, dynamic> toJson() {
    return {
      'tableId': tableId,
      'rowId': rowId,
      'rowData': rowData,
    };
  }

  factory RowAction.fromJson(Map<String, dynamic> json) {
    return RowAction(
      tableId: json['tableId'],
      rowId: json['rowId'],
      rowData: json['rowData'],
    );
  }
}

class ColumnAction {
  final String tableId;
  final String columnId;
  final Map<String, dynamic>? columnData;
  final int? orderIndex;

  ColumnAction({
    required this.tableId,
    required this.columnId,
    this.columnData,
    this.orderIndex,
  });

  Map<String, dynamic> toJson() {
    return {
      'tableId': tableId,
      'columnId': columnId,
      'columnData': columnData,
      'orderIndex': orderIndex,
    };
  }

  factory ColumnAction.fromJson(Map<String, dynamic> json) {
    return ColumnAction(
      tableId: json['tableId'],
      columnId: json['columnId'],
      columnData: json['columnData'],
      orderIndex: json['orderIndex'],
    );
  }
}

class TableAction {
  final String tableId;
  final String sectionId;
  final Map<String, dynamic>? tableData;

  TableAction({
    required this.tableId,
    required this.sectionId,
    this.tableData,
  });

  Map<String, dynamic> toJson() {
    return {
      'tableId': tableId,
      'sectionId': sectionId,
      'tableData': tableData,
    };
  }

  factory TableAction.fromJson(Map<String, dynamic> json) {
    return TableAction(
      tableId: json['tableId'],
      sectionId: json['sectionId'],
      tableData: json['tableData'],
    );
  }
}
