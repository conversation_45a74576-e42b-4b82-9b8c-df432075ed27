use serde::{Deserialize, Serialize};
use std::collections::HashMap;

/// App context that provides the LLM with current state information
#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct AppContext {
    pub current_screen: Option<String>,
    pub project_name: Option<String>,
    pub project_currency: Option<String>,
    pub available_data: Vec<String>,
    pub user_input: HashMap<String, String>,
    pub database_state: DatabaseState,
    pub calculation_context: CalculationContext,
}

#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct DatabaseState {
    pub sections: Vec<String>,
    pub tables: Vec<String>,
    pub recent_items: Vec<String>,
    pub total_records: u32,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CalculationContext {
    pub last_calculation_type: Option<String>,
    pub current_values: HashMap<String, f64>,
    pub available_formulas: Vec<String>,
    pub recent_results: Vec<CalculationResult>,
}

#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct CalculationResult {
    pub calculation_type: String,
    pub inputs: HashMap<String, f64>,
    pub result: f64,
    pub timestamp: i64,
}

impl AppContext {
    pub fn new() -> Self {
        Self {
            current_screen: None,
            project_name: None,
            project_currency: None,
            available_data: Vec::new(),
            user_input: HashMap::new(),
            database_state: DatabaseState::default(),
            calculation_context: CalculationContext::default(),
        }
    }

    /// Update current screen context
    pub fn set_current_screen(&mut self, screen: String) {
        self.current_screen = Some(screen);
    }

    /// Update project information
    pub fn set_project_info(&mut self, name: String, currency: String) {
        self.project_name = Some(name);
        self.project_currency = Some(currency);
    }

    /// Add user input context
    pub fn add_user_input(&mut self, field: String, value: String) {
        self.user_input.insert(field, value);
    }

    /// Update database state
    pub fn update_database_state(&mut self, state: DatabaseState) {
        self.database_state = state;
    }

    /// Add calculation result
    pub fn add_calculation_result(&mut self, result: CalculationResult) {
        self.calculation_context.recent_results.push(result);
        
        // Keep only last 10 results
        if self.calculation_context.recent_results.len() > 10 {
            self.calculation_context.recent_results.remove(0);
        }
    }

    /// Get context summary for LLM
    pub fn get_context_summary(&self) -> String {
        let mut summary = Vec::new();

        if let Some(screen) = &self.current_screen {
            summary.push(format!("Current screen: {}", screen));
        }

        if let Some(project) = &self.project_name {
            summary.push(format!("Project: {}", project));
        }

        if let Some(currency) = &self.project_currency {
            summary.push(format!("Currency: {}", currency));
        }

        if !self.user_input.is_empty() {
            summary.push(format!("User inputs: {} fields", self.user_input.len()));
        }

        if !self.calculation_context.recent_results.is_empty() {
            summary.push(format!("Recent calculations: {}", self.calculation_context.recent_results.len()));
        }

        summary.join(", ")
    }

    /// Get active context keys for response metadata
    pub fn get_active_context_keys(&self) -> Vec<String> {
        let mut keys = Vec::new();

        if self.current_screen.is_some() {
            keys.push("current_screen".to_string());
        }
        if self.project_name.is_some() {
            keys.push("project_info".to_string());
        }
        if !self.user_input.is_empty() {
            keys.push("user_input".to_string());
        }
        if !self.calculation_context.recent_results.is_empty() {
            keys.push("calculation_history".to_string());
        }

        keys
    }

    /// Check if context is relevant for calculations
    pub fn is_calculation_context(&self) -> bool {
        self.current_screen.as_ref()
            .map(|s| s.contains("calculator") || s.contains("calculation"))
            .unwrap_or(false)
    }

    /// Check if context is relevant for data browsing
    pub fn is_data_context(&self) -> bool {
        self.current_screen.as_ref()
            .map(|s| s.contains("database") || s.contains("table") || s.contains("section"))
            .unwrap_or(false)
    }
}

impl Default for DatabaseState {
    fn default() -> Self {
        Self {
            sections: Vec::new(),
            tables: Vec::new(),
            recent_items: Vec::new(),
            total_records: 0,
        }
    }
}

impl Default for CalculationContext {
    fn default() -> Self {
        Self {
            last_calculation_type: None,
            current_values: HashMap::new(),
            available_formulas: Vec::new(),
            recent_results: Vec::new(),
        }
    }
}
