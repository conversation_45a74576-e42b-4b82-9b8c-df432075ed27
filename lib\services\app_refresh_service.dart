import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'isar_service.dart';
import 'project_provider.dart';
import 'ai_service.dart';

import '../providers/sidebar_provider.dart';

/// Service to handle app-wide refresh functionality
class AppRefreshService {
  static final AppRefreshService _instance = AppRefreshService._internal();
  factory AppRefreshService() => _instance;
  AppRefreshService._internal();

  /// Perform a comprehensive app refresh (similar to hot reload)
  Future<void> refreshApp(BuildContext context) async {
    try {
      // Show loading indicator
      _showRefreshDialog(context);

      // Refresh all major services and providers
      await Future.wait([
        _refreshDatabase(context),
        _refreshProjects(context),
        _refreshAIService(context),
        _refreshSidebar(context),
      ]);

      // Close loading dialog
      if (context.mounted) {
        Navigator.of(context).pop();
        
        // Show success message
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Row(
              children: [
                Icon(Icons.refresh, color: Colors.white),
                SizedBox(width: 8),
                Text('App refreshed successfully!'),
              ],
            ),
            backgroundColor: Colors.green,
            duration: Duration(seconds: 2),
          ),
        );
      }
    } catch (e) {
      // Close loading dialog if still open
      if (context.mounted) {
        Navigator.of(context).pop();
        
        // Show error message
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Row(
              children: [
                const Icon(Icons.error, color: Colors.white),
                const SizedBox(width: 8),
                Expanded(child: Text('Refresh failed: $e')),
              ],
            ),
            backgroundColor: Colors.red,
            duration: const Duration(seconds: 3),
          ),
        );
      }
    }
  }

  /// Show refresh loading dialog
  void _showRefreshDialog(BuildContext context) {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => const AlertDialog(
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            CircularProgressIndicator(),
            SizedBox(height: 16),
            Text('Refreshing app...'),
            SizedBox(height: 8),
            Text(
              'Reloading data and services',
              style: TextStyle(fontSize: 12, color: Colors.grey),
            ),
          ],
        ),
      ),
    );
  }

  /// Refresh database connections and data
  Future<void> _refreshDatabase(BuildContext context) async {
    try {
      final isarService = Provider.of<IsarService>(context, listen: false);
      // Reinitialize database if needed
      await isarService.database;
      debugPrint('✅ Database refreshed');
    } catch (e) {
      debugPrint('❌ Database refresh failed: $e');
    }
  }

  /// Refresh project data
  Future<void> _refreshProjects(BuildContext context) async {
    try {
      final projectProvider = Provider.of<ProjectProvider>(context, listen: false);
      await projectProvider.initialize();
      debugPrint('✅ Projects refreshed');
    } catch (e) {
      debugPrint('❌ Projects refresh failed: $e');
    }
  }

  /// Refresh AI service
  Future<void> _refreshAIService(BuildContext context) async {
    try {
      final aiService = AIService();
      
      // Reinitialize AI service
      await aiService.initialize(context);
      
      // AI service is ready without models
      
      debugPrint('✅ AI Service refreshed');
    } catch (e) {
      debugPrint('❌ AI Service refresh failed: $e');
    }
  }

  /// Refresh sidebar data
  Future<void> _refreshSidebar(BuildContext context) async {
    try {
      final sidebarProvider = Provider.of<SidebarProvider>(context, listen: false);
      sidebarProvider.loadSections();
      debugPrint('✅ Sidebar refreshed');
    } catch (e) {
      debugPrint('❌ Sidebar refresh failed: $e');
    }
  }

  /// Quick refresh for specific components
  Future<void> quickRefresh(BuildContext context, {
    bool refreshProjects = true,
    bool refreshDatabase = false,
    bool refreshAI = false,
    bool refreshSidebar = false,
  }) async {
    try {
      List<Future<void>> refreshTasks = [];

      if (refreshProjects) {
        refreshTasks.add(_refreshProjects(context));
      }
      if (refreshDatabase) {
        refreshTasks.add(_refreshDatabase(context));
      }
      if (refreshAI) {
        refreshTasks.add(_refreshAIService(context));
      }
      if (refreshSidebar) {
        refreshTasks.add(_refreshSidebar(context));
      }

      await Future.wait(refreshTasks);

      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Quick refresh completed'),
            duration: Duration(seconds: 1),
          ),
        );
      }
    } catch (e) {
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Quick refresh failed: $e'),
            backgroundColor: Colors.orange,
          ),
        );
      }
    }
  }

  /// Refresh only AI-related services
  Future<void> refreshAIOnly(BuildContext context) async {
    await quickRefresh(context, 
      refreshProjects: false,
      refreshDatabase: false,
      refreshAI: true,
      refreshSidebar: false,
    );
  }

  /// Refresh only project data
  Future<void> refreshProjectsOnly(BuildContext context) async {
    await quickRefresh(context,
      refreshProjects: true,
      refreshDatabase: false,
      refreshAI: false,
      refreshSidebar: false,
    );
  }

  /// Refresh only database and sidebar
  Future<void> refreshDataOnly(BuildContext context) async {
    await quickRefresh(context,
      refreshProjects: false,
      refreshDatabase: true,
      refreshAI: false,
      refreshSidebar: true,
    );
  }

  /// Force rebuild of widgets (similar to hot reload)
  void forceRebuild(BuildContext context) {
    // This will trigger a rebuild of the current widget tree
    if (context.mounted) {
      // Find the nearest widget that can be rebuilt
      final navigator = Navigator.of(context);
      final currentRoute = ModalRoute.of(context);
      
      if (currentRoute != null) {
        // Trigger a rebuild by updating the route
        navigator.pushReplacementNamed(currentRoute.settings.name ?? '/home');
      }
    }
  }

  /// Get refresh status for UI indicators
  bool isRefreshing = false;

  /// Set refresh status
  void setRefreshStatus(bool status) {
    isRefreshing = status;
  }
}

/// Widget for refresh button
class RefreshButton extends StatefulWidget {
  final VoidCallback? onPressed;
  final String tooltip;
  final IconData icon;
  final Color? color;
  final double size;

  const RefreshButton({
    super.key,
    this.onPressed,
    this.tooltip = 'Refresh App',
    this.icon = Icons.refresh,
    this.color,
    this.size = 24,
  });

  @override
  State<RefreshButton> createState() => _RefreshButtonState();
}

class _RefreshButtonState extends State<RefreshButton>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  final AppRefreshService _refreshService = AppRefreshService();

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 1000),
      vsync: this,
    );
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _animationController,
      builder: (context, child) {
        return Transform.rotate(
          angle: _animationController.value * 2 * 3.14159,
          child: IconButton(
            icon: Icon(widget.icon, size: widget.size),
            color: widget.color,
            tooltip: widget.tooltip,
            onPressed: _refreshService.isRefreshing ? null : () async {
              _animationController.repeat();
              _refreshService.setRefreshStatus(true);
              
              try {
                if (widget.onPressed != null) {
                  widget.onPressed!();
                } else {
                  await _refreshService.refreshApp(context);
                }
              } finally {
                _animationController.stop();
                _animationController.reset();
                _refreshService.setRefreshStatus(false);
              }
            },
          ),
        );
      },
    );
  }
}
