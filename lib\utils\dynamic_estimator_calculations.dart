import 'dart:math';
import '../models/estimator_types.dart';
import '../data/estimator_config.dart';
import '../services/dynamic_clean_agent_service.dart';
import '../services/isar_service.dart';

class DynamicEstimatorCalculations {
  static DynamicCleanAgentService? _dynamicService;
  static Map<String, Map<String, Map<String, dynamic>>>? _cachedDesignFactors;
  static List<CylinderSpec>? _cachedNovecSpecs;
  static List<CylinderSpec>? _cachedFm200Specs;
  static List<PipeData>? _cachedPipeData;

  /// Initialize the dynamic service
  static Future<void> initialize() async {
    if (_dynamicService == null) {
      final isarService = IsarService.instance;
      final isar = await isarService.database;
      _dynamicService = DynamicCleanAgentService(isar);
    }
  }

  /// Get design factor from database
  static Future<double> getDesignFactor(AgentType agentType, String designConcentration) async {
    await initialize();
    
    _cachedDesignFactors ??= await _dynamicService!.getDesignFactors();

    try {
      final agentKey = agentType == AgentType.novec1230 ? 'NOVEC1230' : 'FM200';
      final factor = _cachedDesignFactors![agentKey]?[designConcentration]?['factor'];
      if (factor != null) {
        return factor.toDouble();
      }
    } catch (e) {
      print("Could not find design factor for $agentType $designConcentration in database");
    }
    
    // Return reasonable default if not found in database
    return agentType == AgentType.novec1230 ? 0.656 : 0.583;
  }

  /// Find the smallest cylinder that can hold the target fill quantity from database
  static Future<CylinderSpec?> findCylinderSize(AgentType agentType, double targetFillKg) async {
    await initialize();
    
    final agentKey = agentType == AgentType.novec1230 ? 'NOVEC1230' : 'FM200';
    
    // Get cylinder specs from database
    List<CylinderSpec> cylSpecs;
    if (agentType == AgentType.novec1230) {
      _cachedNovecSpecs ??= await _dynamicService!.getCylinderSpecs(agentKey);
      cylSpecs = _cachedNovecSpecs!;
    } else {
      _cachedFm200Specs ??= await _dynamicService!.getCylinderSpecs(agentKey);
      cylSpecs = _cachedFm200Specs!;
    }
    
    // Filter out 343L cylinder if disabled
    final filteredSpecs = AppConfig.no343LCylinder 
        ? cylSpecs.where((cyl) => cyl.size != 343).toList()
        : cylSpecs;
    
    // Find the first cylinder where the target fill is between min and max capacity
    CylinderSpec? cylinder;
    try {
      cylinder = filteredSpecs.where((cyl) =>
          targetFillKg >= cyl.minKg && targetFillKg <= cyl.maxKg
      ).first;
    } catch (e) {
      cylinder = null;
    }
    
    // If no exact match found, get the smallest cylinder where target fill is above min fill
    if (cylinder == null) {
      // Sort by size (descending to get largest first)
      final sortedCyls = List<CylinderSpec>.from(filteredSpecs)
          ..sort((a, b) => b.size.compareTo(a.size));
      try {
        return sortedCyls.where((c) => targetFillKg >= c.minKg).first;
      } catch (e) {
        return sortedCyls.first;
      }
    }
    
    return cylinder;
  }

  /// Calculate cylinders for the first iteration using database data
  static Future<Map<String, dynamic>> calculateCylinders1stIter(double totalAgentRequired, AgentType agentType) async {
    await initialize();
    
    final agentKey = agentType == AgentType.novec1230 ? 'NOVEC1230' : 'FM200';
    
    // Get cylinder specs from database
    List<CylinderSpec> cylSpecs;
    if (agentType == AgentType.novec1230) {
      _cachedNovecSpecs ??= await _dynamicService!.getCylinderSpecs(agentKey);
      cylSpecs = _cachedNovecSpecs!;
    } else {
      _cachedFm200Specs ??= await _dynamicService!.getCylinderSpecs(agentKey);
      cylSpecs = _cachedFm200Specs!;
    }
    
    final sortedCylinders = AppConfig.no343LCylinder
        ? cylSpecs.where((cyl) => cyl.size != 343).toList()
        : cylSpecs;
    sortedCylinders.sort((a, b) => b.size.compareTo(a.size));
    
    // Default target fill is the total agent required
    double targetFillSingleCyl = totalAgentRequired;
    
    // If total is more than the max of largest available cylinder, use that max
    if (totalAgentRequired > sortedCylinders.first.maxKg) {
      targetFillSingleCyl = sortedCylinders.first.maxKg;
    } else {
      // Check if totalAgentRequired falls in any "gaps" between cylinder sizes
      for (int i = 0; i < sortedCylinders.length - 1; i++) {
        final current = sortedCylinders[i];
        final next = sortedCylinders[i + 1];
        
        // If between max of smaller and min of larger, use min of larger
        if (totalAgentRequired > next.maxKg && totalAgentRequired < current.minKg) {
          targetFillSingleCyl = current.minKg;
          break;
        }
      }
    }
    
    // CEILING to nearest integer
    targetFillSingleCyl = targetFillSingleCyl.ceilToDouble();
    
    // Determine cylinder size for first iteration
    final cylinder1stIter = await findCylinderSize(agentType, targetFillSingleCyl);
    final cylinderSizeLiters1stIter = cylinder1stIter?.size ?? 0;
    
    // Calculate number of cylinders
    int numCylinders1stIter = 1;
    if (!(cylinderSizeLiters1stIter / targetFillSingleCyl > 1 && targetFillSingleCyl > totalAgentRequired)) {
      numCylinders1stIter = (totalAgentRequired / targetFillSingleCyl).ceil();
    }
    
    // Calculate quantity per cylinder
    double qtyPerCylinder1stIter;
    if (targetFillSingleCyl > totalAgentRequired) {
      qtyPerCylinder1stIter = targetFillSingleCyl.ceilToDouble();
    } else {
      qtyPerCylinder1stIter = (totalAgentRequired / numCylinders1stIter).ceilToDouble();
    }
    
    return {
      'targetFillSingleCyl': targetFillSingleCyl,
      'cylinderSizeLiters1stIter': cylinderSizeLiters1stIter,
      'numCylinders1stIter': numCylinders1stIter,
      'qtyPerCylinder1stIter': qtyPerCylinder1stIter,
    };
  }

  /// Calculate cylinder sizing for the second iteration using database data
  static Future<Map<String, dynamic>> calculateCylinders2ndIter(
    double totalAgentRequired,
    double qtyPerCylinder1stIter,
    int numCylinders1stIter,
    double targetFillSingleCyl,
    AgentType agentType,
  ) async {
    await initialize();
    
    // Get max filling ratio from config
    final agentKey = agentType == AgentType.novec1230 ? 'NOVEC1230' : 'FM200';
    final maxFillingRatio = AppConfig.maxFillingRatio[agentKey] ?? 0.88;
    
    // Get cylinder specs from database
    List<CylinderSpec> cylSpecs;
    if (agentType == AgentType.novec1230) {
      _cachedNovecSpecs ??= await _dynamicService!.getCylinderSpecs(agentKey);
      cylSpecs = _cachedNovecSpecs!;
    } else {
      _cachedFm200Specs ??= await _dynamicService!.getCylinderSpecs(agentKey);
      cylSpecs = _cachedFm200Specs!;
    }
    
    // Get all available cylinders for this agent
    final sortedCylinders = cylSpecs
        .where((cyl) => AppConfig.no343LCylinder ? cyl.size != 343 : true)
        .toList()
      ..sort((a, b) => a.size.compareTo(b.size)); // Sort ascending by size
    
    // For each cylinder size, calculate max fill based on filling ratio
    final cylindersWithMaxFill = sortedCylinders.map((cyl) => {
      'spec': cyl,
      'calculatedMaxKg': (cyl.size * maxFillingRatio).floorToDouble(),
    }).toList();
    
    // Find the right cylinder size that can hold qtyPerCylinder1stIter
    var selectedCylinder = cylindersWithMaxFill
        .where((cyl) => (cyl['calculatedMaxKg'] as double) >= qtyPerCylinder1stIter)
        .toList()
      ..sort((a, b) => (a['spec'] as CylinderSpec).size.compareTo((b['spec'] as CylinderSpec).size));
    
    // If no cylinder is big enough for 1st iteration, use the largest available
    if (selectedCylinder.isEmpty) {
      cylindersWithMaxFill.sort((a, b) => (b['spec'] as CylinderSpec).size.compareTo((a['spec'] as CylinderSpec).size));
      selectedCylinder = [cylindersWithMaxFill.first];
    }
    
    final cylinderSpec = selectedCylinder.first['spec'] as CylinderSpec;
    final calculatedMaxKg = selectedCylinder.first['calculatedMaxKg'] as double;
    final cylinderSizeLiters2ndIter = cylinderSpec.size;
    
    // Calculate the final number of cylinders
    int numCylinders2ndIter = (totalAgentRequired / calculatedMaxKg).ceil();
    
    // Calculate the final quantity per cylinder
    double qtyPerCylinder = (totalAgentRequired / numCylinders2ndIter).ceilToDouble();
    
    // Make sure qtyPerCylinder doesn't exceed max filling ratio
    final maxFillKg = (cylinderSizeLiters2ndIter * maxFillingRatio).floorToDouble();
    if (qtyPerCylinder > maxFillKg) {
      qtyPerCylinder = maxFillKg;
      // If we need more cylinders, recalculate
      numCylinders2ndIter = (totalAgentRequired / qtyPerCylinder).ceil();
    }
    
    // Ensure qtyPerCylinder is at least min fill for the selected cylinder
    if (qtyPerCylinder < cylinderSpec.minKg) {
      qtyPerCylinder = cylinderSpec.minKg;
    }
    
    // Final rounding for quantity per cylinder
    qtyPerCylinder = qtyPerCylinder.ceilToDouble();
    
    // Calculate the actual total agent quantity
    final actualTotalKg = qtyPerCylinder * numCylinders2ndIter;
    
    // Calculate the filling ratio
    final fillingRatio = qtyPerCylinder / cylinderSizeLiters2ndIter;
    
    return {
      'cylinderSizeLiters2ndIter': cylinderSizeLiters2ndIter,
      'numCylinders2ndIter': numCylinders2ndIter,
      'qtyPerCylinder': qtyPerCylinder,
      'actualTotalKg': actualTotalKg,
      'fillingRatio': fillingRatio,
    };
  }

  /// Find the appropriate nozzle/pipe size based on flow rate using database data
  static Future<PipeData> findNozzlePipeSize(double flowRateKgPerSecond) async {
    await initialize();
    
    // Get pipe data from database
    _cachedPipeData ??= await _dynamicService!.getPipeData();
    final pipeData = _cachedPipeData!;
    
    // Start from smallest pipe size and find the first one that can handle the flow
    for (final pipe in pipeData) {
      if (flowRateKgPerSecond >= pipe.minFlow && flowRateKgPerSecond <= pipe.maxFlow) {
        return pipe;
      }
    }
    
    // If flow is less than smallest pipe's min flow, use the smallest
    if (flowRateKgPerSecond < pipeData.first.minFlow) {
      return pipeData.first;
    }
    
    // If flow is greater than largest pipe's max flow, use the largest
    if (flowRateKgPerSecond > pipeData.last.maxFlow) {
      return pipeData.last;
    }
    
    // If no exact match but flow is between two pipe sizes, get the next larger pipe
    for (int i = 0; i < pipeData.length - 1; i++) {
      if (flowRateKgPerSecond > pipeData[i].maxFlow && 
          flowRateKgPerSecond < pipeData[i + 1].minFlow) {
        return pipeData[i + 1];
      }
    }
    
    // Fallback to largest pipe if no match found
    return pipeData.last;
  }

  /// Calculate the discharge system using database data
  static Future<Map<String, dynamic>> calculateDischarge(
    double actualTotalKg,
    double roomLength,
    double roomWidth,
    double roomHeight,
  ) async {
    // Calculate total flow rate (kg/sec)
    final totalFlowRate = actualTotalKg / AppConfig.agentDischargeTimeSeconds;
    
    // Improved nozzle coverage calculation based on industry standards
    final roomArea = roomLength * roomWidth;
    
    // Calculate nozzle quantity based on room dimensions and coverage area
    int nozzleQty1stTrial;
    if (totalFlowRate > 25) {
      // For large flow rates, start with fewer nozzles
      nozzleQty1stTrial = max(4, (roomArea / 36).ceil()); // 36m² coverage per nozzle for large systems
    } else if (totalFlowRate > 10) {
      // Medium systems
      nozzleQty1stTrial = max(2, (roomArea / 25).ceil()); // 25m² coverage per nozzle
    } else {
      // Small systems - use the original calculation
      const coverageLength = 6; // meters per nozzle
      const coverageWidth = 6;  // meters per nozzle
      const coverageHeight = 4; // meters per nozzle
      
      nozzleQty1stTrial = (roomLength / coverageLength).ceil() * 
                          (roomWidth / coverageWidth).ceil() * 
                          (roomHeight / coverageHeight).ceil();
    }
    
    // Calculate flow per nozzle first trial
    final flowPerNozzle1stTrial = totalFlowRate / nozzleQty1stTrial;
    
    // Find nozzle size first trial using database data
    final nozzle1stTrial = await findNozzlePipeSize(flowPerNozzle1stTrial);
    final nozzleSize1stTrial = nozzle1stTrial.sizeMm;
    
    // Determine final nozzle quantity
    final nozzleQtyFinal = (nozzleSize1stTrial > AppConfig.nozzleSizeThresholdMm && nozzleQty1stTrial < 6) ?
        nozzleQty1stTrial * 2 : nozzleQty1stTrial;
    
    // Calculate final flow per nozzle
    final flowPerNozzleFinal = totalFlowRate / nozzleQtyFinal;
    
    // Find final nozzle size using database data
    final nozzleFinal = await findNozzlePipeSize(flowPerNozzleFinal);
    final nozzleSizeFinal = nozzleFinal.sizeMm;
    
    // Find manifold pipe size - should handle total flow using database data
    final manifoldPipe = await findNozzlePipeSize(totalFlowRate);
    final manifoldPipeSize = manifoldPipe.sizeMm;
    
    // For manifold assembly size, use same size as the manifold pipe
    final manifoldAssemblySize = manifoldPipeSize;
    
    return {
      'totalFlowRate': totalFlowRate,
      'nozzleQty1stTrial': nozzleQty1stTrial,
      'flowPerNozzle1stTrial': flowPerNozzle1stTrial,
      'nozzleSize1stTrial': nozzleSize1stTrial,
      'nozzleQtyFinal': nozzleQtyFinal,
      'flowPerNozzleFinal': flowPerNozzleFinal,
      'nozzleSizeFinal': nozzleSizeFinal,
      'manifoldPipeSize': manifoldPipeSize,
      'manifoldAssemblySize': manifoldAssemblySize,
    };
  }

  /// Calculate Smoke Detectors based on room area
  static int calculateDetectors(double roomArea) {
    return (roomArea / AppConfig.detectorCoverageAreaM2).ceil();
  }

  /// Performs all the design calculations using database data
  static Future<DesignResults> calculateDesign(EstimatorFormValues input) async {
    await initialize();
    
    // Get design factor from database
    final designFactor = await getDesignFactor(input.agentType, input.designConcentration);

    // Calculate room dimensions based on input mode
    double roomLength = 0, roomWidth = 0, roomHeight = 0, roomVolume = 0, totalAgentRequired = 0;

    if (input.inputMode == InputMode.dimensions &&
        input.roomLength != null && input.roomWidth != null && input.roomHeight != null) {
      // Using dimensions
      roomLength = input.roomLength!;
      roomWidth = input.roomWidth!;
      roomHeight = input.roomHeight!;
      roomVolume = roomLength * roomWidth * roomHeight;
      totalAgentRequired = roomVolume * designFactor;
    } else if (input.inputMode == InputMode.agentQuantity && input.agentQuantity != null) {
      // Using agent quantity
      totalAgentRequired = input.agentQuantity!;
      roomHeight = input.roomHeight ?? AppConfig.defaultRoomHeightM;
      roomVolume = totalAgentRequired / designFactor;

      // Derive room dimensions (assuming square room)
      final floorArea = roomVolume / roomHeight;
      roomLength = sqrt(floorArea);
      roomWidth = sqrt(floorArea);
    }

    // Calculate room area
    final roomArea = roomLength * roomWidth;

    // Calculate cylinder sizing - first iteration using database data
    final cylinders1stIter = await calculateCylinders1stIter(totalAgentRequired, input.agentType);

    // Calculate cylinder sizing - second iteration (final) using database data
    final cylinders2ndIter = await calculateCylinders2ndIter(
      totalAgentRequired,
      cylinders1stIter['qtyPerCylinder1stIter'],
      cylinders1stIter['numCylinders1stIter'],
      cylinders1stIter['targetFillSingleCyl'],
      input.agentType,
    );

    // Calculate discharge system details using database data
    final discharge = await calculateDischarge(
      cylinders2ndIter['actualTotalKg'],
      roomLength,
      roomWidth,
      roomHeight,
    );

    // Construct the full design results object
    return DesignResults(
      roomData: RoomData(
        roomLength: roomLength,
        roomWidth: roomWidth,
        roomHeight: roomHeight,
        roomArea: roomArea,
        roomVolume: roomVolume,
      ),
      designFactor: designFactor,
      totalAgentRequired: totalAgentRequired,
      cylinder: CylinderData(
        targetFillSingleCyl: cylinders1stIter['targetFillSingleCyl'],
        cylinderSizeLiters1stIter: cylinders1stIter['cylinderSizeLiters1stIter'],
        numCylinders1stIter: cylinders1stIter['numCylinders1stIter'],
        qtyPerCylinder1stIter: cylinders1stIter['qtyPerCylinder1stIter'],
        cylinderSizeLiters2ndIter: cylinders2ndIter['cylinderSizeLiters2ndIter'],
        numCylinders2ndIter: cylinders2ndIter['numCylinders2ndIter'],
        qtyPerCylinder: cylinders2ndIter['qtyPerCylinder'],
        actualTotalKg: cylinders2ndIter['actualTotalKg'],
        fillingRatio: cylinders2ndIter['fillingRatio'],
      ),
      discharge: DischargeData(
        totalFlowRate: discharge['totalFlowRate'],
        nozzleQty1stTrial: discharge['nozzleQty1stTrial'],
        flowPerNozzle1stTrial: discharge['flowPerNozzle1stTrial'],
        nozzleSize1stTrial: discharge['nozzleSize1stTrial'],
        nozzleQtyFinal: discharge['nozzleQtyFinal'],
        flowPerNozzleFinal: discharge['flowPerNozzleFinal'],
        nozzleSizeFinal: discharge['nozzleSizeFinal'],
        manifoldPipeSize: discharge['manifoldPipeSize'],
        manifoldAssemblySize: discharge['manifoldAssemblySize'],
      ),
      systemType: input.systemType.name,
    );
  }
}
