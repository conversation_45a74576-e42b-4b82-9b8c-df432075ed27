import 'dart:async';
import 'dart:convert';
import 'package:path/path.dart';
import 'package:sqflite_common_ffi/sqflite_ffi.dart';
import 'package:path_provider/path_provider.dart';
import 'dart:io';
import 'package:uuid/uuid.dart';
import '../models/project.dart';
import '../models/user.dart';
import '../models/system_catalog.dart';
import '../constants/app_constants.dart';

class DatabaseService {
  static final DatabaseService _instance = DatabaseService._internal();
  static Database? _database;

  factory DatabaseService() => _instance;

  DatabaseService._internal();

  Future<Database> get database async {
    if (_database != null) return _database!;
    _database = await _initDatabase();
    return _database!;
  }

  Future<Database> _initDatabase() async {
    // Initialize FFI for Windows
    if (Platform.isWindows || Platform.isLinux) {
      // Initialize FFI
      sqfliteFfiInit();
      // Change the default factory for Windows and Linux
      databaseFactory = databaseFactoryFfi;
    }

    Directory documentsDirectory = await getApplicationDocumentsDirectory();
    String path = join(documentsDirectory.path, 'firetool.db');

    // Only uncomment this for a clean start
    // if (await databaseExists(path)) {
    //   print('Deleting existing database for clean start');
    //   await deleteDatabase(path);
    // }

    final db = await openDatabase(
      path,
      version: 1,
      onCreate: _onCreate,
      onOpen: (db) async {
        print('Database opened successfully');
        // Print all tables in the database
        var tables = await db.rawQuery("SELECT name FROM sqlite_master WHERE type='table'");
        print('Tables in database: $tables');

        // Check if we need to populate sample data
        final materials = await db.query('materials_catalog');
        if (materials.isEmpty) {
          await _populateSampleData(db);
        }
      },
    );

    return db;
  }

  Future<void> _onCreate(Database db, int version) async {
    // Create projects table
    await db.execute('''
      CREATE TABLE projects(
        id TEXT PRIMARY KEY,
        name TEXT,
        clientName TEXT,
        location TEXT,
        createdAt TEXT,
        updatedAt TEXT,
        currency TEXT,
        exchangeRate REAL,
        metadata TEXT,
        systems TEXT
      )
    ''');

    // Create materials catalog table
    await db.execute('''
      CREATE TABLE materials_catalog(
        id TEXT PRIMARY KEY,
        name TEXT,
        category TEXT,
        description TEXT,
        unit TEXT,
        exWorksUnitCost REAL,
        localUnitCost REAL,
        installationUnitCost REAL,
        isImported INTEGER,
        vendor TEXT,
        approval TEXT
      )
    ''');

    // Create equipment catalog table
    await db.execute('''
      CREATE TABLE equipment_catalog(
        id TEXT PRIMARY KEY,
        name TEXT,
        category TEXT,
        exWorksUnitCost REAL,
        localUnitCost REAL,
        installationUnitCost REAL,
        isImported INTEGER,
        vendor TEXT,
        approval TEXT
      )
    ''');

    // Create labor rates table
    await db.execute('''
      CREATE TABLE labor_rates(
        id TEXT PRIMARY KEY,
        description TEXT,
        hourlyRate REAL
      )
    ''');

    // Create users table
    await db.execute('''
      CREATE TABLE users(
        id TEXT PRIMARY KEY,
        username TEXT UNIQUE,
        passwordHash TEXT,
        role TEXT,
        createdAt TEXT,
        lastLoginAt TEXT
      )
    ''');

    // Create systems catalog table
    await db.execute('''
      CREATE TABLE systems_catalog(
        id TEXT PRIMARY KEY,
        name TEXT,
        category TEXT,
        description TEXT,
        isActive INTEGER
      )
    ''');
  }

  Future<void> _populateSampleData(Database db) async {
    const uuid = Uuid();

    // Add sample materials for all system types
    for (var systemType in AppConstants.systemTypes) {
      final materials = AppConstants.sampleMaterialsBySystem[systemType] ?? [];
      for (var material in materials) {
        await db.insert(
          'materials_catalog',
          {
            'id': uuid.v4(),
            'name': material['name'],
            'category': material['category'],
            'description': material['description'] ?? '',
            'unit': material['unit'],
            'exWorksUnitCost': material['exWorksUnitCost'],
            'localUnitCost': material['localUnitCost'],
            'installationUnitCost': material['installationUnitCost'],
            'isImported': material['isImported'] ? 1 : 0,
            'vendor': material['vendor'] ?? '',
            'approval': material['approval'] ?? '',
          },
        );
      }
    }
  }

  // Project CRUD operations
  Future<String> insertProject(Project project) async {
    final db = await database;
    await db.insert(
      'projects',
      {
        'id': project.id,
        'name': project.name,
        'clientName': project.clientName,
        'createdAt': project.createdAt.toIso8601String(),
        'updatedAt': project.updatedAt.toIso8601String(),
        'currency': project.currency,
        'exchangeRate': project.exchangeRate,
        'metadata': json.encode(project.metadata),
        'systems': json.encode(project.systems.map((s) => s.toMap()).toList()),
      },
      conflictAlgorithm: ConflictAlgorithm.replace,
    );
    return project.id;
  }

  Future<Project> getProject(String id) async {
    try {
      final db = await database;
      final List<Map<String, dynamic>> maps = await db.query(
        'projects',
        where: 'id = ?',
        whereArgs: [id],
      );

      if (maps.isEmpty) {
        throw Exception('Project not found');
      }

      final map = maps.first;

      // Handle metadata
      Map<String, dynamic> metadata = {};
      try {
        if (map['metadata'] != null) {
          metadata = json.decode(map['metadata'] as String) as Map<String, dynamic>;
        }
      } catch (e) {
        print('Error parsing metadata: $e');
      }

      // Handle systems
      List<SystemEstimate> systems = [];
      try {
        if (map['systems'] != null) {
          final systemsJson = json.decode(map['systems'] as String) as List;
          systems = systemsJson
              .map((s) => SystemEstimate.fromMap(s as Map<String, dynamic>))
              .toList();
        }
      } catch (e) {
        print('Error parsing systems: $e');
      }

      // Create project with safe defaults for nullable fields
      return Project(
        id: map['id'] as String? ?? '',
        name: map['name'] as String? ?? 'Unnamed Project',
        clientName: map['clientName'] as String? ?? 'Unknown Client',
        createdAt: map['createdAt'] != null
            ? DateTime.parse(map['createdAt'] as String)
            : DateTime.now(),
        updatedAt: map['updatedAt'] != null
            ? DateTime.parse(map['updatedAt'] as String)
            : DateTime.now(),
        currency: map['currency'] as String? ?? 'SAR',
        exchangeRate: (map['exchangeRate'] as num?)?.toDouble() ?? 3.75,
        metadata: metadata,
        systems: systems,
      );
    } catch (e) {
      print('Error in getProject: $e');
      // Return a default project if anything fails
      return Project(
        id: id,
        name: 'Error Loading Project',
        clientName: 'Error',
      );
    }
  }

  Future<List<Project>> getAllProjects() async {
    try {
      final db = await database;
      final List<Map<String, dynamic>> maps = await db.query('projects');

      List<Project> projects = [];

      for (var map in maps) {
        try {
          // Handle metadata
          Map<String, dynamic> metadata = {};
          try {
            if (map['metadata'] != null) {
              metadata = json.decode(map['metadata'] as String) as Map<String, dynamic>;
            }
          } catch (e) {
            print('Error parsing metadata: $e');
          }

          // Handle systems
          List<SystemEstimate> systems = [];
          try {
            if (map['systems'] != null) {
              final systemsJson = json.decode(map['systems'] as String) as List;
              systems = systemsJson
                  .map((s) => SystemEstimate.fromMap(s as Map<String, dynamic>))
                  .toList();
            }
          } catch (e) {
            print('Error parsing systems: $e');
          }

          // Create project with safe defaults for nullable fields
          projects.add(Project(
            id: map['id'] as String? ?? '',
            name: map['name'] as String? ?? 'Unnamed Project',
            clientName: map['clientName'] as String? ?? 'Unknown Client',
            createdAt: map['createdAt'] != null
                ? DateTime.parse(map['createdAt'] as String)
                : DateTime.now(),
            updatedAt: map['updatedAt'] != null
                ? DateTime.parse(map['updatedAt'] as String)
                : DateTime.now(),
            currency: map['currency'] as String? ?? 'SAR',
            exchangeRate: (map['exchangeRate'] as num?)?.toDouble() ?? 3.75,
            metadata: metadata,
            systems: systems,
          ));
        } catch (e) {
          print('Error processing project: $e');
          // Skip this project if there's an error
        }
      }

      return projects;
    } catch (e) {
      print('Error in getAllProjects: $e');
      // Return an empty list if anything fails
      return [];
    }
  }

  Future<void> updateProject(Project project) async {
    final db = await database;
    project.updatedAt = DateTime.now();

    await db.update(
      'projects',
      {
        'name': project.name,
        'clientName': project.clientName,
        'updatedAt': project.updatedAt.toIso8601String(),
        'currency': project.currency,
        'exchangeRate': project.exchangeRate,
        'metadata': json.encode(project.metadata),
        'systems': json.encode(project.systems.map((s) => s.toMap()).toList()),
      },
      where: 'id = ?',
      whereArgs: [project.id],
    );
  }

  Future<void> deleteProject(String id) async {
    final db = await database;
    await db.delete(
      'projects',
      where: 'id = ?',
      whereArgs: [id],
    );
  }

  // Material catalog operations
  Future<void> insertMaterial(MaterialItem material) async {
    final db = await database;
    await db.insert(
      'materials_catalog',
      {
        'id': material.id,
        'name': material.name,
        'category': material.category,
        'description': material.description,
        'unit': material.unit,
        'exWorksUnitCost': material.exWorksUnitCost,
        'localUnitCost': material.localUnitCost,
        'installationUnitCost': material.installationUnitCost,
        'isImported': material.isImported ? 1 : 0,
        'vendor': material.vendor,
        'approval': material.approval,
      },
      conflictAlgorithm: ConflictAlgorithm.replace,
    );
  }

  Future<List<MaterialItem>> getAllMaterials() async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query('materials_catalog');

    return List.generate(maps.length, (i) {
      return MaterialItem(
        id: maps[i]['id'] as String,
        name: maps[i]['name'] as String,
        category: maps[i]['category'] as String,
        description: maps[i]['description'] as String? ?? '',
        quantity: 0, // Default quantity
        unit: maps[i]['unit'] as String,
        exWorksUnitCost: maps[i]['exWorksUnitCost'] as double,
        localUnitCost: maps[i]['localUnitCost'] as double,
        installationUnitCost: maps[i]['installationUnitCost'] as double,
        isImported: maps[i]['isImported'] == 1,
        vendor: maps[i]['vendor'] as String? ?? '',
        approval: maps[i]['approval'] as String? ?? '',
      );
    });
  }

  // Equipment catalog operations
  Future<void> insertEquipment(EquipmentItem equipment) async {
    final db = await database;
    await db.insert(
      'equipment_catalog',
      {
        'id': equipment.id,
        'name': equipment.name,
        'category': equipment.category,
        'exWorksUnitCost': equipment.exWorksUnitCost,
        'localUnitCost': equipment.localUnitCost,
        'installationUnitCost': equipment.installationUnitCost,
        'isImported': equipment.isImported ? 1 : 0,
      },
      conflictAlgorithm: ConflictAlgorithm.replace,
    );
  }

  Future<List<EquipmentItem>> getAllEquipment() async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query('equipment_catalog');

    return List.generate(maps.length, (i) {
      return EquipmentItem(
        id: maps[i]['id'] as String,
        name: maps[i]['name'] as String,
        category: maps[i]['category'] as String,
        quantity: 0, // Default quantity
        exWorksUnitCost: maps[i]['exWorksUnitCost'] as double,
        localUnitCost: maps[i]['localUnitCost'] as double,
        installationUnitCost: maps[i]['installationUnitCost'] as double,
        isImported: maps[i]['isImported'] == 1,
      );
    });
  }

  // Labor rates operations
  Future<void> insertLaborRate(LaborItem labor) async {
    final db = await database;
    await db.insert(
      'labor_rates',
      {
        'id': labor.id,
        'description': labor.description,
        'hourlyRate': labor.hourlyRate,
      },
      conflictAlgorithm: ConflictAlgorithm.replace,
    );
  }

  Future<List<LaborItem>> getAllLaborRates() async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query('labor_rates');

    return List.generate(maps.length, (i) {
      return LaborItem(
        id: maps[i]['id'] as String,
        description: maps[i]['description'] as String,
        hours: 0, // Default hours
        hourlyRate: maps[i]['hourlyRate'] as double,
      );
    });
  }

  // User operations
  Future<void> insertUser(User user) async {
    final db = await database;
    await db.insert(
      'users',
      {
        'id': user.id,
        'username': user.username,
        'passwordHash': user.passwordHash,
        'role': user.role.toString(),
        'createdAt': user.createdAt.toIso8601String(),
        'lastLoginAt': user.lastLoginAt.toIso8601String(),
      },
      conflictAlgorithm: ConflictAlgorithm.replace,
    );
  }

  Future<User?> getUserByUsername(String username) async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(
      'users',
      where: 'username = ?',
      whereArgs: [username],
    );

    if (maps.isEmpty) {
      return null;
    }

    return User.fromMap(maps.first);
  }

  Future<User?> getUser(String id) async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(
      'users',
      where: 'id = ?',
      whereArgs: [id],
    );

    if (maps.isEmpty) {
      return null;
    }

    return User.fromMap(maps.first);
  }

  Future<List<User>> getAllUsers() async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query('users');

    return List.generate(maps.length, (i) {
      return User.fromMap(maps[i]);
    });
  }

  Future<void> updateUser(User user) async {
    final db = await database;
    await db.update(
      'users',
      {
        'username': user.username,
        'passwordHash': user.passwordHash,
        'role': user.role.toString(),
        'lastLoginAt': user.lastLoginAt.toIso8601String(),
      },
      where: 'id = ?',
      whereArgs: [user.id],
    );
  }

  Future<void> deleteUser(String id) async {
    final db = await database;
    await db.delete(
      'users',
      where: 'id = ?',
      whereArgs: [id],
    );
  }

  // System catalog operations
  Future<void> insertSystemCatalog(SystemCatalogItem system) async {
    final db = await database;
    await db.insert(
      'systems_catalog',
      {
        'id': system.id,
        'name': system.name,
        'category': system.category,
        'description': system.description,
        'isActive': system.isActive ? 1 : 0,
      },
      conflictAlgorithm: ConflictAlgorithm.replace,
    );
  }

  Future<List<SystemCatalogItem>> getAllSystemCatalog() async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query('systems_catalog');

    return List.generate(maps.length, (i) {
      return SystemCatalogItem(
        id: maps[i]['id'] as String,
        name: maps[i]['name'] as String,
        category: maps[i]['category'] as String,
        description: maps[i]['description'] as String,
        isActive: maps[i]['isActive'] == 1,
      );
    });
  }

  Future<void> updateSystemCatalog(SystemCatalogItem system) async {
    final db = await database;
    await db.update(
      'systems_catalog',
      {
        'name': system.name,
        'category': system.category,
        'description': system.description,
        'isActive': system.isActive ? 1 : 0,
      },
      where: 'id = ?',
      whereArgs: [system.id],
    );
  }

  Future<void> deleteSystemCatalog(String id) async {
    final db = await database;
    await db.delete(
      'systems_catalog',
      where: 'id = ?',
      whereArgs: [id],
    );
  }
}
