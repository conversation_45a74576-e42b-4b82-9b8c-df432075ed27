import 'package:flutter/material.dart';
import 'package:speech_to_text/speech_to_text.dart';
import 'package:permission_handler/permission_handler.dart';
import 'dart:io' show Platform;

/// Voice input service that supports English and Arabic speech recognition
class VoiceInputService {
  static final VoiceInputService _instance = VoiceInputService._internal();
  factory VoiceInputService() => _instance;
  VoiceInputService._internal();

  final SpeechToText _speechToText = SpeechToText();
  bool _isInitialized = false;
  bool _isListening = false;
  List<LocaleName> _availableLocales = [];
  String _currentLocale = 'en-US'; // Default to English
  String _preferredArabicLocale = 'ar-SA'; // Saudi Arabic for better accent recognition
  String _preferredEnglishLocale = 'en-US'; // US English
  bool _autoDetectLanguage = true; // Auto-detect language like Gemini

  /// Initialize the voice input service
  Future<bool> initialize() async {
    if (_isInitialized) return true;

    try {
      debugPrint('🎤 Initializing voice input service...');

      // Check if we're on Windows and handle accordingly
      if (Platform.isWindows) {
        debugPrint('🎤 Windows detected - using fallback voice input');
        // For now, mark as initialized but show a helpful message
        _isInitialized = true;
        debugPrint('🎤 Voice input ready (Windows fallback mode)');
        return true;
      }

      // Request microphone permission first on mobile
      if (Platform.isAndroid || Platform.isIOS) {
        final permissionStatus = await Permission.microphone.request();
        debugPrint('🎤 Microphone permission: $permissionStatus');

        if (permissionStatus != PermissionStatus.granted) {
          debugPrint('🎤 Microphone permission denied');
          return false;
        }
      }

      // Try to initialize speech to text with enhanced error handling
      final available = await _speechToText.initialize(
        onError: (error) {
          debugPrint('🎤 Speech recognition error: $error');
          // Don't fail completely on errors, some are recoverable
        },
        onStatus: (status) {
          debugPrint('🎤 Speech recognition status: $status');
          if (status == 'listening') {
            debugPrint('🎤 Successfully started listening');
          } else if (status == 'notListening') {
            debugPrint('🎤 Stopped listening');
          }
        },
        debugLogging: true, // Enable debug logging for troubleshooting
      );

      if (!available) {
        debugPrint('🎤 Speech recognition not available, trying permission request...');

        // Try requesting permission explicitly
        try {
          final permissionStatus = await Permission.microphone.request();
          debugPrint('🎤 Permission request result: $permissionStatus');

          if (permissionStatus == PermissionStatus.granted) {
            // Try initializing again after permission granted
            final retryAvailable = await _speechToText.initialize(
              onError: (error) => debugPrint('🎤 Speech recognition error: $error'),
              onStatus: (status) => debugPrint('🎤 Speech recognition status: $status'),
            );

            if (!retryAvailable) {
              debugPrint('🎤 Speech recognition still not available after permission grant');
              // On Windows, still allow fallback mode
              if (Platform.isWindows) {
                _isInitialized = true;
                debugPrint('🎤 Using Windows fallback mode');
                return true;
              }
              return false;
            }
          } else {
            debugPrint('🎤 Microphone permission not granted: $permissionStatus');
            return false;
          }
        } catch (permissionError) {
          debugPrint('🎤 Permission request failed: $permissionError');
          // On Windows, still allow fallback mode
          if (Platform.isWindows) {
            _isInitialized = true;
            debugPrint('🎤 Using Windows fallback mode after permission error');
            return true;
          }
          return false;
        }
      }

      // Get available locales with detailed logging
      _availableLocales = await _speechToText.locales();
      debugPrint('🎤 Total available locales: ${_availableLocales.length}');

      // Log all available locales for debugging
      for (final locale in _availableLocales) {
        debugPrint('🎤 Available: ${locale.localeId} - ${locale.name}');
      }

      // Find Arabic locales with comprehensive search
      final arabicLocales = _availableLocales.where((locale) =>
        locale.localeId.startsWith('ar') ||
        locale.localeId.contains('arabic') ||
        locale.name.toLowerCase().contains('arabic') ||
        locale.name.contains('العربية')
      ).toList();

      debugPrint('🎤 Found ${arabicLocales.length} Arabic locales:');
      for (final locale in arabicLocales) {
        debugPrint('🎤   Arabic: ${locale.localeId} - ${locale.name}');
      }

      final bestArabicLocale = _findBestArabicLocale(arabicLocales);
      if (bestArabicLocale != null) {
        _preferredArabicLocale = bestArabicLocale.localeId;
        debugPrint('🎤 Selected Arabic locale: $_preferredArabicLocale');
      } else {
        debugPrint('🎤 ⚠️ No Arabic locale found, using fallback: $_preferredArabicLocale');
      }

      // Find English locales
      final englishLocales = _availableLocales.where((locale) =>
        locale.localeId.startsWith('en') ||
        locale.name.toLowerCase().contains('english')
      ).toList();

      debugPrint('🎤 Found ${englishLocales.length} English locales:');
      for (final locale in englishLocales) {
        debugPrint('🎤   English: ${locale.localeId} - ${locale.name}');
      }

      final bestEnglishLocale = _findBestEnglishLocale(englishLocales);
      if (bestEnglishLocale != null) {
        _preferredEnglishLocale = bestEnglishLocale.localeId;
        debugPrint('🎤 Selected English locale: $_preferredEnglishLocale');
      }

      // Set default to preferred English
      _currentLocale = _preferredEnglishLocale;

      _isInitialized = true;
      return true;
    } catch (e) {
      debugPrint('🎤 Voice input initialization failed: $e');
      return false;
    }
  }

  /// Check if voice input is available
  bool get isAvailable => _isInitialized && _speechToText.isAvailable;

  /// Check if currently listening
  bool get isListening => _isListening;

  /// Check if auto-detect language is enabled
  bool get isAutoDetectEnabled => _autoDetectLanguage;

  /// Get available languages
  List<String> get availableLanguages {
    if (!_isInitialized) return [];
    
    final languages = <String>[];
    
    // Add English variants
    for (final locale in _availableLocales) {
      if (locale.localeId.startsWith('en')) {
        languages.add('English (${locale.name})');
      }
    }
    
    // Add Arabic variants
    for (final locale in _availableLocales) {
      if (locale.localeId.startsWith('ar')) {
        languages.add('Arabic (${locale.name})');
      }
    }
    
    return languages;
  }

  /// Set the recognition language
  void setLanguage(String language) {
    if (language.toLowerCase().contains('arabic')) {
      _currentLocale = _preferredArabicLocale;
      debugPrint('🎤 Switched to Arabic: $_currentLocale');
    } else {
      _currentLocale = _preferredEnglishLocale;
      debugPrint('🎤 Switched to English: $_currentLocale');
    }
  }

  /// Force Arabic mode for testing
  void forceArabicMode() {
    _currentLocale = _preferredArabicLocale;
    _autoDetectLanguage = false; // Disable auto-detect for testing
    debugPrint('🎤 Forced Arabic mode: $_currentLocale (auto-detect disabled)');
  }

  /// Force English mode for testing
  void forceEnglishMode() {
    _currentLocale = _preferredEnglishLocale;
    _autoDetectLanguage = false; // Disable auto-detect for testing
    debugPrint('🎤 Forced English mode: $_currentLocale (auto-detect disabled)');
  }

  /// Auto-detect and switch language based on input
  void autoSwitchLanguage(String text) {
    if (!_autoDetectLanguage) return;

    final detectedLanguage = detectLanguage(text);
    if (detectedLanguage == 'Arabic' && !_currentLocale.startsWith('ar')) {
      _currentLocale = _preferredArabicLocale;
      debugPrint('🎤 Auto-switched to Arabic: $_currentLocale');
    } else if (detectedLanguage == 'English' && !_currentLocale.startsWith('en')) {
      _currentLocale = _preferredEnglishLocale;
      debugPrint('🎤 Auto-switched to English: $_currentLocale');
    }
  }

  /// Toggle auto-detect language feature
  void setAutoDetectLanguage(bool enabled) {
    _autoDetectLanguage = enabled;
    debugPrint('🎤 Auto-detect language: ${enabled ? 'enabled' : 'disabled'}');
  }

  /// Test Arabic voice recognition capability
  Future<bool> testArabicSupport() async {
    if (!_isInitialized) {
      await initialize();
    }

    final arabicLocales = _availableLocales.where((locale) =>
      locale.localeId.startsWith('ar')).toList();

    debugPrint('🎤 Arabic support test:');
    debugPrint('🎤   Available Arabic locales: ${arabicLocales.length}');
    debugPrint('🎤   Preferred Arabic locale: $_preferredArabicLocale');
    debugPrint('🎤   Speech recognition available: ${_speechToText.isAvailable}');

    return arabicLocales.isNotEmpty && _speechToText.isAvailable;
  }

  /// Get detailed voice recognition status
  Map<String, dynamic> getVoiceStatus() {
    return {
      'initialized': _isInitialized,
      'available': _speechToText.isAvailable,
      'listening': _isListening,
      'currentLocale': _currentLocale,
      'preferredArabic': _preferredArabicLocale,
      'preferredEnglish': _preferredEnglishLocale,
      'autoDetect': _autoDetectLanguage,
      'totalLocales': _availableLocales.length,
      'arabicLocales': _availableLocales.where((l) => l.localeId.startsWith('ar')).length,
      'englishLocales': _availableLocales.where((l) => l.localeId.startsWith('en')).length,
    };
  }

  /// Start listening for voice input
  Future<void> startListening({
    required Function(String) onResult,
    Function(String)? onPartialResult,
    Function(String)? onError,
  }) async {
    if (!_isInitialized || _isListening) return;

    try {
      _isListening = true;

      // Windows fallback mode - show quick input dialog
      if (Platform.isWindows) {
        debugPrint('🎤 Windows fallback: Showing quick input dialog');
        _isListening = false;
        _showWindowsVoiceInputDialog(onResult, onError);
        return;
      }

      // Enhanced speech recognition parameters for better Arabic/English recognition
      final isArabic = _currentLocale.startsWith('ar');

      debugPrint('🎤 Starting to listen in locale: $_currentLocale (Arabic: $isArabic)');

      await _speechToText.listen(
        onResult: (result) {
          final recognizedText = result.recognizedWords;
          final confidence = result.confidence;
          final isFinal = result.finalResult;

          debugPrint('🎤 Result: "$recognizedText" (confidence: $confidence, final: $isFinal, locale: $_currentLocale)');

          // For Arabic, accept lower confidence scores as Arabic recognition can be less confident
          final minConfidence = isArabic ? 0.5 : 0.7;

          // Auto-detect language if enabled and confidence is acceptable
          if (_autoDetectLanguage && confidence > minConfidence) {
            autoSwitchLanguage(recognizedText);
          }

          if (isFinal) {
            // For Arabic, also accept results with lower confidence
            if (recognizedText.isNotEmpty && (confidence > minConfidence || isArabic)) {
              onResult(recognizedText);
            } else {
              debugPrint('🎤 Rejected result due to low confidence: $confidence (min: $minConfidence)');
            }
            _isListening = false;
          } else if (onPartialResult != null && recognizedText.isNotEmpty) {
            onPartialResult(recognizedText);
          }
        },
        localeId: _currentLocale,
        // Adjust timing based on language - Arabic speakers may need more time
        listenFor: Duration(seconds: isArabic ? 60 : 30), // More time for Arabic
        pauseFor: Duration(seconds: isArabic ? 5 : 3),    // Longer pause for Arabic
        listenOptions: SpeechListenOptions(
          partialResults: true,
          cancelOnError: false, // Don't cancel on error for Arabic
          listenMode: ListenMode.confirmation,
          // Enhanced options for better Arabic recognition
          onDevice: false, // Use cloud recognition for better Arabic accuracy
          sampleRate: 16000, // Standard sample rate
        ),
      );

      debugPrint('🎤 Started listening in $_currentLocale');
    } catch (e) {
      _isListening = false;
      final errorMsg = 'Failed to start voice recognition: $e';
      debugPrint('🎤 $errorMsg');
      if (onError != null) onError(errorMsg);
    }
  }

  /// Stop listening
  Future<void> stopListening() async {
    if (!_isListening) return;
    
    await _speechToText.stop();
    _isListening = false;
    debugPrint('🎤 Stopped listening');
  }

  /// Cancel listening
  Future<void> cancelListening() async {
    if (!_isListening) return;
    
    await _speechToText.cancel();
    _isListening = false;
    debugPrint('🎤 Cancelled listening');
  }

  /// Get current language display name
  String get currentLanguage {
    if (_currentLocale.startsWith('ar')) {
      return 'العربية (Arabic)';
    } else {
      return 'English';
    }
  }

  /// Enhanced language detection for Arabic and English
  String detectLanguage(String text) {
    if (text.trim().isEmpty) return 'English';

    // Enhanced Arabic detection - check for Arabic characters and common patterns
    final arabicRegex = RegExp(r'[\u0600-\u06FF\u0750-\u077F\u08A0-\u08FF\uFB50-\uFDFF\uFE70-\uFEFF]');
    final arabicCharCount = arabicRegex.allMatches(text).length;
    final totalChars = text.replaceAll(RegExp(r'\s+'), '').length;

    // If more than 30% of characters are Arabic, consider it Arabic
    if (totalChars > 0 && (arabicCharCount / totalChars) > 0.3) {
      return 'Arabic';
    }

    // Check for common Arabic words (transliterated)
    final arabicWords = [
      'salam', 'ahlan', 'marhaba', 'shukran', 'afwan', 'inshallah', 'mashallah',
      'habibi', 'yalla', 'khalas', 'maafi', 'mushkila', 'tamam', 'zain',
      'نعم', 'لا', 'شكرا', 'أهلا', 'مرحبا', 'السلام', 'عليكم', 'وعليكم',
    ];

    final lowerText = text.toLowerCase();
    for (final word in arabicWords) {
      if (lowerText.contains(word.toLowerCase())) {
        return 'Arabic';
      }
    }

    // Check for English indicators
    final englishWords = [
      'hello', 'hi', 'thank', 'please', 'yes', 'no', 'good', 'morning',
      'evening', 'night', 'how', 'what', 'where', 'when', 'why', 'who',
    ];

    for (final word in englishWords) {
      if (lowerText.contains(word)) {
        return 'English';
      }
    }

    // Default to English if uncertain
    return 'English';
  }

  /// Find the best Arabic locale for accent recognition
  LocaleName? _findBestArabicLocale(List<LocaleName> arabicLocales) {
    if (arabicLocales.isEmpty) return null;

    // Priority order for Arabic locales (best for accent recognition)
    const priorityOrder = [
      'ar-SA', // Saudi Arabia - most common
      'ar-AE', // UAE - Gulf accent
      'ar-EG', // Egypt - widely understood
      'ar-JO', // Jordan - clear pronunciation
      'ar-LB', // Lebanon - Levantine
      'ar-MA', // Morocco - Maghrebi
      'ar-DZ', // Algeria
      'ar-TN', // Tunisia
      'ar-IQ', // Iraq
      'ar-SY', // Syria
      'ar-YE', // Yemen
      'ar-KW', // Kuwait
      'ar-QA', // Qatar
      'ar-BH', // Bahrain
      'ar-OM', // Oman
      'ar',    // Generic Arabic
    ];

    // Find the highest priority locale available
    for (final priority in priorityOrder) {
      final found = arabicLocales.firstWhere(
        (locale) => locale.localeId == priority,
        orElse: () => LocaleName('', ''),
      );
      if (found.localeId.isNotEmpty) {
        debugPrint('🎤 Selected Arabic locale: ${found.localeId} (${found.name})');
        return found;
      }
    }

    // Fallback to first available Arabic locale
    debugPrint('🎤 Using fallback Arabic locale: ${arabicLocales.first.localeId}');
    return arabicLocales.first;
  }

  /// Find the best English locale for recognition
  LocaleName? _findBestEnglishLocale(List<LocaleName> englishLocales) {
    if (englishLocales.isEmpty) return null;

    // Priority order for English locales
    const priorityOrder = [
      'en-US', // US English - most common
      'en-GB', // British English
      'en-AU', // Australian English
      'en-CA', // Canadian English
      'en-IN', // Indian English
      'en-ZA', // South African English
      'en-NZ', // New Zealand English
      'en-IE', // Irish English
      'en',    // Generic English
    ];

    // Find the highest priority locale available
    for (final priority in priorityOrder) {
      final found = englishLocales.firstWhere(
        (locale) => locale.localeId == priority,
        orElse: () => LocaleName('', ''),
      );
      if (found.localeId.isNotEmpty) {
        debugPrint('🎤 Selected English locale: ${found.localeId} (${found.name})');
        return found;
      }
    }

    // Fallback to first available English locale
    debugPrint('🎤 Using fallback English locale: ${englishLocales.first.localeId}');
    return englishLocales.first;
  }

  /// Show Windows voice input dialog
  void _showWindowsVoiceInputDialog(Function(String) onResult, Function(String)? onError) {
    // This will be called from the widget context, so we need to pass the context
    // For now, just call the error callback to inform the user
    if (onError != null) {
      onError('Windows Voice Input: Tap the microphone to open quick input dialog');
    }
  }

  /// Dispose resources
  void dispose() {
    if (_isListening) {
      _speechToText.cancel();
    }
    _isInitialized = false;
    _isListening = false;
  }
}
