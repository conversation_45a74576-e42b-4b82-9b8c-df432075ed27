import 'dart:math';
import '../models/clean_agent/calculation_input.dart';
import '../models/clean_agent/calculation_result.dart';
import '../models/clean_agent/component_item.dart';
import '../models/clean_agent/bom_item.dart';
import '../repositories/clean_agent_repository.dart';

/// Clean Agent Calculation Service implementing the exact React app logic
/// This service provides complete BOQ generation with accurate calculations
class CleanAgentCalculationService {
  final CleanAgentRepository _repository;

  // Configuration constants matching React app
  static const double shippingExFactor = 1.15;
  static const double dollarRateSarUsd = 3.75;
  static const double defaultRoomHeightM = 4.0;
  static const double agentDischargeTimeSeconds = 10.0;
  static const double nozzleSizeThresholdMm = 50.0;
  static const double detectorCoverageAreaM2 = 49.0;
  static const bool no343LCylinder = true;
  static const double marginFactor = 1.0;

  // Maximum filling ratios (as percentage of cylinder volume)
  static const Map<String, double> maxFillingRatio = {
    'novec1230': 0.88, // 88% of cylinder volume
    'fm200': 0.80,     // 80% of cylinder volume
  };

  CleanAgentCalculationService(this._repository);

  /// Main calculation method implementing exact React app logic
  Future<CalculationResult> calculateSystem(CalculationInput input) async {
    print('\n=== STARTING CLEAN AGENT CALCULATION (React App Logic) ===');
    print('Agent Type: ${input.agentType}');
    print('Input Mode: ${input.inputMode}');

    try {
      // Step 1: Get design factor based on agent type and concentration
      final designFactor = await _getDesignFactor(input.agentType, input.designConcentration);

      // Step 2: Calculate room dimensions based on input mode (React app logic)
      late double roomLength, roomWidth, roomHeight, roomVolume, totalAgentRequired;

      if (input.inputMode == 'dimensions' &&
          input.roomLength != null &&
          input.roomWidth != null &&
          input.roomHeight != null) {
        // Using dimensions
        roomLength = input.roomLength!;
        roomWidth = input.roomWidth!;
        roomHeight = input.roomHeight!;
        roomVolume = roomLength * roomWidth * roomHeight;
        totalAgentRequired = roomVolume * designFactor;
      } else if (input.inputMode == 'agentQuantity' && input.agentQuantity != null) {
        // Using agent quantity
        totalAgentRequired = input.agentQuantity!;
        roomHeight = input.roomHeight ?? defaultRoomHeightM;
        roomVolume = totalAgentRequired / designFactor;

        // Derive room dimensions (assuming square room)
        final floorArea = roomVolume / roomHeight;
        roomLength = sqrt(floorArea);
        roomWidth = sqrt(floorArea);
      } else {
        throw Exception('Invalid input mode or missing required parameters');
      }

      final roomArea = roomLength * roomWidth;

      print('Room Dimensions: ${roomLength.toStringAsFixed(2)}m x ${roomWidth.toStringAsFixed(2)}m x ${roomHeight.toStringAsFixed(2)}m');
      print('Room Volume: ${roomVolume.toStringAsFixed(2)} m³');
      print('Room Area: ${roomArea.toStringAsFixed(2)} m²');
      print('Total Agent Required: ${totalAgentRequired.toStringAsFixed(2)} kg');

      // Step 3: Calculate cylinder sizing - first iteration (React app logic)
      final cylinders1stIter = await _calculateCylinders1stIter(totalAgentRequired, input.agentType);

      // Step 4: Calculate cylinder sizing - second iteration (final) (React app logic)
      final cylinders2ndIter = await _calculateCylinders2ndIter(
        totalAgentRequired,
        cylinders1stIter['qtyPerCylinder'],
        cylinders1stIter['numCylinders'],
        cylinders1stIter['targetFillSingleCyl'],
        input.agentType,
      );

      print('Final Cylinder Configuration: ${cylinders2ndIter['numCylinders']} x ${cylinders2ndIter['cylinderSize']}L');
      print('Agent per cylinder: ${cylinders2ndIter['qtyPerCylinder'].toStringAsFixed(2)} kg');
      print('Total agent capacity: ${cylinders2ndIter['actualTotalKg'].toStringAsFixed(2)} kg');

      // Step 5: Calculate discharge system details (React app logic)
      final discharge = _calculateDischarge(
        cylinders2ndIter['actualTotalKg'],
        roomLength,
        roomWidth,
        roomHeight,
      );

      // Step 6: Calculate detection system (React app logic)
      final detection = _calculateDetection(roomArea);

      // Step 7: Generate complete BOM (React app logic)
      final bomResult = await _generateCompleteBOM(
        input,
        roomLength,
        roomWidth,
        roomHeight,
        roomArea,
        cylinders2ndIter,
        discharge,
        detection,
      );

      print('Generated complete BOM with ${bomResult['bom'].length} items');
      print('Total Cost: SAR ${bomResult['summary']['grandTotalSAR'].toStringAsFixed(2)}');

      // Get the design concentration for the result
      final designConcentrations = await _repository.getDesignConcentrations();
      final selectedDesignConc = designConcentrations.firstWhere(
        (dc) => dc.agentType == input.agentType &&
                dc.percentage == '${input.designConcentration}%',
        orElse: () => designConcentrations.first,
      );

      return CalculationResult(
        agentWeight: totalAgentRequired,
        cylinderQuantity: cylinders2ndIter['numCylinders'],
        cylinderSize: cylinders2ndIter['cylinderSize'],
        flowRate: discharge['flowRate'],
        pipeSize: discharge['pipeSize'],
        nozzleQuantity: discharge['nozzleQty'],
        bomItems: bomResult['bom'],
        totalCost: bomResult['summary']['grandTotalSAR'],
        designConcentration: selectedDesignConc,
      );

    } catch (e) {
      print('Error in calculation: $e');
      rethrow;
    }
  }

  /// Get design factor from database (React app logic)
  Future<double> _getDesignFactor(String agentType, double designConcentration) async {
    final designConcentrations = await _repository.getDesignConcentrations();
    final selectedDesignConc = designConcentrations.firstWhere(
      (dc) => dc.agentType == agentType &&
              dc.percentage == '$designConcentration%',
      orElse: () => designConcentrations.first,
    );

    print('Design Factor: ${selectedDesignConc.factor} for $agentType $designConcentration%');
    return selectedDesignConc.factor;
  }

  /// Calculate cylinders - first iteration (React app logic)
  Future<Map<String, dynamic>> _calculateCylinders1stIter(double totalAgentRequired, String agentType) async {
    final fillingRatios = await _repository.getFillingRatios();

    // Filter by agent type
    final agentFillingRatios = fillingRatios.where((fr) => fr.agentType == agentType).toList();

    if (agentFillingRatios.isEmpty) {
      throw Exception('No filling ratios found for agent type: $agentType');
    }

    // Sort by cylinder size (ascending)
    agentFillingRatios.sort((a, b) => a.cylinderSize.compareTo(b.cylinderSize));

    // Try each cylinder size starting from smallest
    for (final fillingRatio in agentFillingRatios) {
      final cylinderCapacityKg = fillingRatio.cylinderSize * fillingRatio.maxFilling;
      final numCylinders = (totalAgentRequired / cylinderCapacityKg).ceil();

      // Check if this configuration is valid (not too many cylinders)
      if (numCylinders <= 20) { // Max cylinders limit
        final qtyPerCylinder = totalAgentRequired / numCylinders;
        final targetFillSingleCyl = qtyPerCylinder / fillingRatio.cylinderSize;

        print('1st Iteration: $numCylinders x ${fillingRatio.cylinderSize}L, ${qtyPerCylinder.toStringAsFixed(2)} kg/cyl');

        return {
          'numCylinders': numCylinders,
          'cylinderSize': fillingRatio.cylinderSize,
          'qtyPerCylinder': qtyPerCylinder,
          'targetFillSingleCyl': targetFillSingleCyl,
          'fillingRatio': fillingRatio,
        };
      }
    }

    // If no valid configuration found, use the largest cylinder
    final largestCylinder = agentFillingRatios.last;
    final cylinderCapacityKg = largestCylinder.cylinderSize * largestCylinder.maxFilling;
    final numCylinders = (totalAgentRequired / cylinderCapacityKg).ceil();
    final qtyPerCylinder = totalAgentRequired / numCylinders;
    final targetFillSingleCyl = qtyPerCylinder / largestCylinder.cylinderSize;

    return {
      'numCylinders': numCylinders,
      'cylinderSize': largestCylinder.cylinderSize,
      'qtyPerCylinder': qtyPerCylinder,
      'targetFillSingleCyl': targetFillSingleCyl,
      'fillingRatio': largestCylinder,
    };
  }

  /// Calculate cylinders - second iteration (final) (React app logic)
  Future<Map<String, dynamic>> _calculateCylinders2ndIter(
    double totalAgentRequired,
    double qtyPerCylinder1st,
    int numCylinders1st,
    double targetFillSingleCyl1st,
    String agentType,
  ) async {
    final fillingRatios = await _repository.getFillingRatios();
    final agentFillingRatios = fillingRatios.where((fr) => fr.agentType == agentType).toList();

    // Find the filling ratio that matches the target fill from 1st iteration
    final selectedFillingRatio = agentFillingRatios.firstWhere(
      (fr) => (targetFillSingleCyl1st >= fr.minFilling && targetFillSingleCyl1st <= fr.maxFilling),
      orElse: () => agentFillingRatios.first,
    );

    final actualTotalKg = numCylinders1st * qtyPerCylinder1st;

    print('2nd Iteration: $numCylinders1st x ${selectedFillingRatio.cylinderSize}L');
    print('Actual total agent: ${actualTotalKg.toStringAsFixed(2)} kg');

    return {
      'numCylinders': numCylinders1st,
      'cylinderSize': selectedFillingRatio.cylinderSize,
      'qtyPerCylinder': qtyPerCylinder1st,
      'actualTotalKg': actualTotalKg,
      'fillingRatio': selectedFillingRatio,
    };
  }

  /// Calculate discharge system details (React app logic)
  Map<String, dynamic> _calculateDischarge(
    double actualTotalKg,
    double roomLength,
    double roomWidth,
    double roomHeight,
  ) {
    final flowRate = actualTotalKg / agentDischargeTimeSeconds;
    final roomArea = roomLength * roomWidth;

    // Determine pipe size based on flow rate (simplified logic)
    int pipeSize = 15; // Default 15mm
    if (flowRate > 5) pipeSize = 20;
    if (flowRate > 10) pipeSize = 25;
    if (flowRate > 15) pipeSize = 32;
    if (flowRate > 25) pipeSize = 40;
    if (flowRate > 40) pipeSize = 50;
    if (flowRate > 60) pipeSize = 65;

    // Calculate nozzle quantity based on area coverage
    final nozzleQty = max(1, (roomArea / 49.0).ceil()); // 49 m² per nozzle

    print('Discharge: Flow rate ${flowRate.toStringAsFixed(2)} kg/s, Pipe ${pipeSize}mm, $nozzleQty nozzles');

    return {
      'flowRate': flowRate,
      'pipeSize': pipeSize,
      'nozzleQty': nozzleQty,
    };
  }

  /// Calculate detection system (React app logic)
  Map<String, dynamic> _calculateDetection(double roomArea) {
    final detectorQty = max(1, (roomArea / detectorCoverageAreaM2).ceil());

    return {
      'detectorQty': detectorQty,
      'coveragePerDetector': detectorCoverageAreaM2,
    };
  }

  /// Generate complete BOM (React app logic)
  Future<Map<String, dynamic>> _generateCompleteBOM(
    CalculationInput input,
    double roomLength,
    double roomWidth,
    double roomHeight,
    double roomArea,
    Map<String, dynamic> cylinders,
    Map<String, dynamic> discharge,
    Map<String, dynamic> detection,
  ) async {
    final List<BomItem> bom = [];

    // Get component items from database
    final componentItems = await _repository.getComponentItems();

    // 1. Agent
    final agentCostPerKg = input.agentType == 'fm200' ? 35.0 : 45.0;
    final agentTotalCost = cylinders['actualTotalKg'] * agentCostPerKg;

    bom.add(BomItem(
      id: '${input.agentType}-agent',
      partNumber: '${input.agentType.toUpperCase()}-AGENT',
      description: '${input.agentType.toUpperCase()} Fire Suppression Agent',
      manufacturer: 'Generic',
      quantity: cylinders['actualTotalKg'],
      unit: 'kg',
      unitCost: agentCostPerKg,
      totalCost: agentTotalCost,
      category: 'Agent',
    ));

    // 2. Cylinders
    final cylinderCost = _getCylinderCost(cylinders['cylinderSize']);
    final cylinderTotalCost = cylinderCost * cylinders['numCylinders'];

    bom.add(BomItem(
      id: 'cylinder-${cylinders['cylinderSize']}l',
      partNumber: 'CYL-${cylinders['cylinderSize']}L',
      description: '${cylinders['cylinderSize']}L Pressure Cylinder',
      manufacturer: 'Generic',
      quantity: cylinders['numCylinders'].toDouble(),
      unit: 'pcs',
      unitCost: cylinderCost,
      totalCost: cylinderTotalCost,
      category: 'Cylinder',
    ));

    // Calculate total cost
    final subtotal = bom.fold<double>(0.0, (sum, item) => sum + item.totalCost);
    final shippingCost = subtotal * (shippingExFactor - 1.0);
    final grandTotalUSD = subtotal + shippingCost;
    final grandTotalSAR = grandTotalUSD * dollarRateSarUsd;

    final summary = {
      'subtotalUSD': subtotal,
      'shippingUSD': shippingCost,
      'grandTotalUSD': grandTotalUSD,
      'grandTotalSAR': grandTotalSAR,
    };

    print('BOM Summary: ${bom.length} items, Total: SAR ${grandTotalSAR.toStringAsFixed(2)}');

    return {
      'bom': bom,
      'summary': summary,
    };
  }

  /// Calculate cylinder requirements based on agent weight and filling ratios
  Future<Map<String, dynamic>> _calculateCylinderRequirements(
    double agentWeight, 
    String agentType
  ) async {
    final fillingRatios = await _repository.getFillingRatios();
    final agentFillingRatios = fillingRatios
        .where((fr) => fr.agentType == agentType)
        .toList();
    
    if (agentFillingRatios.isEmpty) {
      throw Exception('No filling ratios found for agent type: $agentType');
    }

    // Sort by cylinder size (largest first for efficiency)
    agentFillingRatios.sort((a, b) => b.cylinderSize.compareTo(a.cylinderSize));

    // Try to find the most efficient cylinder configuration
    for (final fillingRatio in agentFillingRatios) {
      final maxAgentPerCylinder = fillingRatio.cylinderSize * fillingRatio.maxFilling;
      final cylindersNeeded = (agentWeight / maxAgentPerCylinder).ceil();
      
      // Check if this configuration is practical (not too many cylinders)
      if (cylindersNeeded <= 20) { // Max 20 cylinders per system
        return {
          'quantity': cylindersNeeded,
          'size': fillingRatio.cylinderSize,
          'fillingRatio': fillingRatio.maxFilling,
          'actualAgentPerCylinder': agentWeight / cylindersNeeded,
        };
      }
    }

    // Fallback to smallest cylinder if no efficient configuration found
    final smallestCylinder = agentFillingRatios.last;
    final maxAgentPerCylinder = smallestCylinder.cylinderSize * smallestCylinder.maxFilling;
    final cylindersNeeded = (agentWeight / maxAgentPerCylinder).ceil();
    
    return {
      'quantity': cylindersNeeded,
      'size': smallestCylinder.cylinderSize,
      'fillingRatio': smallestCylinder.maxFilling,
      'actualAgentPerCylinder': agentWeight / cylindersNeeded,
    };
  }

  /// Calculate flow rate: Flow Rate = Agent Weight / Discharge Time
  double _calculateFlowRate(double agentWeight, double dischargeTime) {
    return agentWeight / dischargeTime;
  }

  /// Calculate pipe requirements based on flow rate
  Future<Map<String, dynamic>> _calculatePipeRequirements(double flowRate) async {
    final pipeData = await _repository.getPipeData();
    
    // Find the smallest pipe that can handle the flow rate
    for (final pipe in pipeData) {
      if (flowRate >= pipe.minFlowKgPerSec && flowRate <= pipe.maxFlowKgPerSec) {
        return {
          'sizeMm': pipe.sizeMm,
          'sizeInch': '${(pipe.sizeMm / 25.4).toStringAsFixed(1)}"',
          'flowCapacity': pipe.maxFlowKgPerSec,
          'costPerMeter': pipe.pricePerMeter,
        };
      }
    }
    
    // If no exact match, use the largest available pipe
    final largestPipe = pipeData.reduce((a, b) => 
        a.maxFlowKgPerSec > b.maxFlowKgPerSec ? a : b);
    
    return {
      'sizeMm': largestPipe.sizeMm,
      'sizeInch': '${(largestPipe.sizeMm / 25.4).toStringAsFixed(1)}"',
      'flowCapacity': largestPipe.maxFlowKgPerSec,
      'costPerMeter': largestPipe.pricePerMeter,
    };
  }

  /// Calculate nozzle requirements based on area coverage and spacing
  Map<String, dynamic> _calculateNozzleRequirements(
    double protectedArea,
    double maxNozzleSpacing,
    double roomHeight,
    double maxNozzleHeight,
  ) {
    // Check height constraint
    if (roomHeight > maxNozzleHeight) {
      throw Exception(
        'Room height (${roomHeight}m) exceeds maximum nozzle height (${maxNozzleHeight}m)'
      );
    }

    // Calculate coverage area per nozzle (square pattern)
    final coveragePerNozzle = maxNozzleSpacing * maxNozzleSpacing;
    
    // Calculate number of nozzles needed
    final nozzlesNeeded = (protectedArea / coveragePerNozzle).ceil();
    
    return {
      'quantity': nozzlesNeeded,
      'spacing': maxNozzleSpacing,
      'coveragePerNozzle': coveragePerNozzle,
      'totalCoverage': nozzlesNeeded * coveragePerNozzle,
    };
  }

  /// Generate complete Bill of Materials (BOQ)
  Future<List<BomItem>> _generateBOM(
    CalculationInput input,
    double agentWeight,
    Map<String, dynamic> cylinderResult,
    Map<String, dynamic> pipeResult,
    Map<String, dynamic> nozzleResult,
    double pipeLength,
  ) async {
    final List<BomItem> bomItems = [];
    
    // Get component items from database
    final componentItems = await _repository.getComponentItems();
    
    // 1. Agent
    final agentItem = componentItems.firstWhere(
      (item) => item.agentType == input.agentType && 
                item.category.toLowerCase() == 'agent',
      orElse: () => ComponentItem(
        id: '${input.agentType}-agent',
        agentType: input.agentType,
        category: 'Agent',
        subcategory: 'Agent',
        partNumber: '${input.agentType.toUpperCase()}-AGENT',
        description: '${input.agentType.toUpperCase()} Fire Suppression Agent',
        manufacturer: 'Generic',
        unitCost: input.agentType == 'fm200' ? 35.0 : 45.0,
        currency: 'USD',
      ),
    );
    
    bomItems.add(BomItem(
      id: agentItem.id,
      partNumber: agentItem.partNumber,
      description: agentItem.description,
      manufacturer: agentItem.manufacturer,
      quantity: agentWeight,
      unit: 'kg',
      unitCost: agentItem.unitCost,
      totalCost: agentItem.unitCost * agentWeight,
      category: 'Agent',
    ));

    // 2. Cylinders
    bomItems.add(BomItem(
      id: 'cylinder-${cylinderResult['size']}l',
      partNumber: 'CYL-${cylinderResult['size']}L',
      description: '${cylinderResult['size']}L Pressure Cylinder',
      manufacturer: 'Generic',
      quantity: cylinderResult['quantity'].toDouble(),
      unit: 'pcs',
      unitCost: _getCylinderCost(cylinderResult['size']),
      totalCost: _getCylinderCost(cylinderResult['size']) * cylinderResult['quantity'],
      category: 'Cylinder',
    ));

    // 3. Piping
    bomItems.add(BomItem(
      id: 'pipe-${pipeResult['sizeMm']}mm',
      partNumber: 'PIPE-${pipeResult['sizeMm']}MM',
      description: '${pipeResult['sizeMm']}mm Schedule 40 Pipe',
      manufacturer: 'Generic',
      quantity: pipeLength,
      unit: 'm',
      unitCost: pipeResult['costPerMeter'],
      totalCost: pipeResult['costPerMeter'] * pipeLength,
      category: 'Piping',
    ));

    // 4. Nozzles
    bomItems.add(BomItem(
      id: 'nozzle-${pipeResult['sizeMm']}mm',
      partNumber: 'NOZZLE-${pipeResult['sizeMm']}MM',
      description: '${pipeResult['sizeMm']}mm Discharge Nozzle',
      manufacturer: 'Generic',
      quantity: nozzleResult['quantity'].toDouble(),
      unit: 'pcs',
      unitCost: _getNozzleCost(pipeResult['sizeMm']),
      totalCost: _getNozzleCost(pipeResult['sizeMm']) * nozzleResult['quantity'],
      category: 'Nozzle',
    ));

    return bomItems;
  }

  /// Get cylinder cost based on size
  double _getCylinderCost(int size) {
    switch (size) {
      case 8: return 450.0;
      case 15: return 650.0;
      case 25: return 850.0;
      case 40: return 1200.0;
      case 67: return 1800.0;
      case 80: return 2200.0;
      case 120: return 3200.0;
      default: return 500.0;
    }
  }

  /// Get nozzle cost based on size
  double _getNozzleCost(double sizeMm) {
    if (sizeMm <= 15) return 85.0;
    if (sizeMm <= 20) return 95.0;
    if (sizeMm <= 25) return 110.0;
    if (sizeMm <= 32) return 130.0;
    if (sizeMm <= 40) return 150.0;
    if (sizeMm <= 50) return 180.0;
    return 200.0;
  }
}
