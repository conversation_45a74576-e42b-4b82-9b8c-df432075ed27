// Core enums matching Flutter app
export enum SystemType {
  ALARM = 'alarm',
  WATER = 'water',
  FOAM = 'foam',
  CLEAN_AGENT = 'cleanAgent',
  CO2 = 'co2',
  MATERIALS = 'materials',
  CUSTOM = 'custom',
}

export enum ColumnDataType {
  TEXT = 'text',
  NUMBER = 'number',
  CURRENCY = 'currency',
  DATE = 'date',
  BOOLEAN = 'boolean',
  DROPDOWN = 'dropdown',
}

export enum CurrencyType {
  USD = 'USD',
  GBP = 'GBP',
  SAR = 'SAR',
  EGP = 'EGP',
  AED = 'AED',
}

// Core interfaces matching Flutter ISAR models
export interface SystemItem {
  id?: number;
  itemId?: string;
  model?: string;
  description?: string;
  manufacturer?: string;
  approval?: string;
  exWorksPrice?: number;
  localPrice?: number;
  installationPrice?: number;
  systemType: SystemType;
  sectionId?: string;
  createdAt?: Date;
}

export interface Project {
  id?: number;
  projectId?: string;
  name?: string;
  clientName?: string;
  projectReference?: string;
  createdAt?: Date;
  updatedAt?: Date;
  currency?: string;
  exchangeRate?: number;
  shippingRate?: number;
  marginRate?: number;
  includeInstallation?: boolean;
  systemType: SystemType;
  systemsJson?: string;
  cleanAgentSystemsJson?: string;
}

export interface Section {
  id?: number;
  sectionId?: string;
  name?: string;
  icon?: string;
  color?: string;
  orderIndex?: number;
  parentSectionId?: string;
  systemType: SystemType;
  createdAt?: Date;
  updatedAt?: Date;
}

export interface DynamicTable {
  id?: number;
  tableId?: string;
  sectionId?: string;
  name?: string;
  displayName?: string;
  description?: string;
  createdAt?: Date;
  updatedAt?: Date;
  orderIndex: number;
}

export interface TableColumn {
  id?: number;
  columnId?: string;
  name?: string;
  tableId?: string;
  dataType: ColumnDataType;
  currencyType: CurrencyType;
  isRequired?: boolean;
  defaultValue?: string;
  orderIndex?: number;
  validationRules?: string;
  dropdownOptions?: string;
  createdAt?: Date;
  updatedAt?: Date;
}

export interface TableRow {
  id?: number;
  rowId?: string;
  tableId?: string;
  data?: string; // JSON string containing all column values
  createdAt?: Date;
  updatedAt?: Date;
}

export interface FlexibleTable {
  id?: number;
  tableId?: string;
  name?: string;
  description?: string;
  sectionId?: string;
  createdAt?: Date;
  updatedAt?: Date;
}

export interface FlexibleColumn {
  id?: number;
  columnId?: string;
  name?: string;
  tableId?: string;
  dataType: ColumnDataType;
  currencyType: CurrencyType;
  isRequired?: boolean;
  defaultValue?: string;
  orderIndex?: number;
  validationRules?: string;
  dropdownOptions?: string;
  createdAt?: Date;
  updatedAt?: Date;
}

export interface FlexibleRow {
  id?: number;
  rowId?: string;
  tableId?: string;
  data?: string; // JSON string containing all column values
  createdAt?: Date;
  updatedAt?: Date;
}

export interface SidebarSection {
  id?: number;
  sectionId?: string;
  name?: string;
  icon?: string;
  color?: string;
  orderIndex?: number;
  parentSectionId?: string;
  systemType: SystemType;
  createdAt?: Date;
  updatedAt?: Date;
}

// Chat and AI related interfaces
export interface ChatMessage {
  role: 'user' | 'assistant' | 'system';
  content: string;
  timestamp: number;
  toolCalls?: ToolCall[];
}

export interface ToolCall {
  id: string;
  type: string;
  function: {
    name: string;
    arguments: string;
  };
}

// Currency information
export const CURRENCY_INFO = {
  [CurrencyType.USD]: { code: 'USD', symbol: '$', name: 'US Dollar' },
  [CurrencyType.GBP]: { code: 'GBP', symbol: '£', name: 'British Pound' },
  [CurrencyType.SAR]: { code: 'SAR', symbol: 'ر.س', name: 'Saudi Riyal' },
  [CurrencyType.EGP]: { code: 'EGP', symbol: 'ج.م', name: 'Egyptian Pound' },
  [CurrencyType.AED]: { code: 'AED', symbol: 'د.إ', name: 'UAE Dirham' },
};
