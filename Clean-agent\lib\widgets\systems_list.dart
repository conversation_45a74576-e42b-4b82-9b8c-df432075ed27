import 'package:flutter/material.dart';
import '../models/estimator_types.dart';
import '../utils/formatters.dart';

class SystemsList extends StatelessWidget {
  final List<QuotedSystem> systems;
  final Function(QuotedSystem) onSystemSelected;
  final Function(String) onSystemDeleted;

  const SystemsList({
    super.key,
    required this.systems,
    required this.onSystemSelected,
    required this.onSystemDeleted,
  });

  @override
  Widget build(BuildContext context) {
    return GridView.builder(
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 3,
        crossAxisSpacing: 16,
        mainAxisSpacing: 16,
        childAspectRatio: 1.2,
      ),
      itemCount: systems.length,
      itemBuilder: (context, index) {
        final system = systems[index];
        return SystemCard(
          system: system,
          onTap: () => onSystemSelected(system),
          onDelete: () => _showDeleteDialog(context, system),
        );
      },
    );
  }

  void _showDeleteDialog(BuildContext context, QuotedSystem system) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('Delete System'),
          content: Text('Are you sure you want to delete "${system.name}"?'),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('Cancel'),
            ),
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
                onSystemDeleted(system.id);
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(
                    content: Text('System deleted'),
                    backgroundColor: Colors.red,
                  ),
                );
              },
              child: const Text('Delete'),
            ),
          ],
        );
      },
    );
  }
}

class SystemCard extends StatelessWidget {
  final QuotedSystem system;
  final VoidCallback onTap;
  final VoidCallback onDelete;

  const SystemCard({
    super.key,
    required this.system,
    required this.onTap,
    required this.onDelete,
  });

  @override
  Widget build(BuildContext context) {
    final agentTypeDisplay = system.inputData.agentType == AgentType.novec1230 
        ? 'NOVEC 1230' 
        : 'FM-200';
    
    return Card(
      elevation: 2,
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Header with delete button
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Expanded(
                    child: Text(
                      system.name,
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                  IconButton(
                    icon: const Icon(Icons.delete_outline, size: 20),
                    onPressed: onDelete,
                    padding: EdgeInsets.zero,
                    constraints: const BoxConstraints(),
                  ),
                ],
              ),
              const SizedBox(height: 12),
              
              // System details
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    _buildDetailRow(
                      Icons.science,
                      'Agent',
                      agentTypeDisplay,
                    ),
                    const SizedBox(height: 8),
                    _buildDetailRow(
                      Icons.fitness_center,
                      'Total',
                      '${formatNumber(system.designResults.cylinder.actualTotalKg)} kg',
                    ),
                    const SizedBox(height: 8),
                    _buildDetailRow(
                      Icons.settings,
                      'System',
                      _getSystemTypeDisplay(system.inputData.systemType),
                    ),
                    const SizedBox(height: 8),
                    _buildDetailRow(
                      Icons.attach_money,
                      'Cost',
                      'SAR ${formatNumber(system.summary.grandTotalSAR)}',
                    ),
                    const Spacer(),
                    _buildDetailRow(
                      Icons.calendar_today,
                      'Created',
                      _formatDate(system.date),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildDetailRow(IconData icon, String label, String value) {
    return Row(
      children: [
        Icon(icon, size: 16, color: Colors.grey[600]),
        const SizedBox(width: 8),
        Text(
          '$label: ',
          style: const TextStyle(
            fontSize: 12,
            color: Colors.grey,
          ),
        ),
        Expanded(
          child: Text(
            value,
            style: const TextStyle(
              fontSize: 12,
              fontWeight: FontWeight.w500,
            ),
            overflow: TextOverflow.ellipsis,
          ),
        ),
      ],
    );
  }

  String _getSystemTypeDisplay(SystemType systemType) {
    switch (systemType) {
      case SystemType.main:
        return 'Main Only';
      case SystemType.reserve:
        return 'Reserve Only';
      case SystemType.mainAndReserve:
        return 'Main + Reserve';
    }
  }

  String _formatDate(String dateString) {
    try {
      final date = DateTime.parse(dateString);
      return '${date.day}/${date.month}/${date.year}';
    } catch (e) {
      return 'Unknown';
    }
  }
}
