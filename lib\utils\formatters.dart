import 'dart:math';
import '../models/estimator_types.dart';

/// Format a number to a specified number of decimal places
String formatNumber(double number, [int decimalPlaces = 2]) {
  if (number.isNaN || number.isInfinite) {
    return '0';
  }
  
  // Round to the specified decimal places
  final factor = pow(10, decimalPlaces);
  final rounded = (number * factor).round() / factor;
  
  // Format with the specified decimal places
  return rounded.toStringAsFixed(decimalPlaces);
}

/// Format a number as currency (SAR) - rounded to nearest whole number with separators
String formatCurrencySAR(double amount) {
  return 'SAR ${formatNumberWithSeparators(amount, 0)}';
}

/// Format a number as currency (USD) - rounded to nearest whole number with separators
String formatCurrencyUSD(double amount) {
  return '\$${formatNumberWithSeparators(amount, 0)}';
}

/// Format a percentage
String formatPercentage(double value, [int decimalPlaces = 1]) {
  return '${formatNumber(value, decimalPlaces)}%';
}

/// Format a large number with thousand separators
String formatNumberWithSeparators(double number, [int decimalPlaces = 2]) {
  final formatted = formatNumber(number, decimalPlaces);
  final parts = formatted.split('.');
  
  // Add thousand separators to the integer part
  final integerPart = parts[0];
  final decimalPart = parts.length > 1 ? parts[1] : '';
  
  String result = '';
  for (int i = 0; i < integerPart.length; i++) {
    if (i > 0 && (integerPart.length - i) % 3 == 0) {
      result += ',';
    }
    result += integerPart[i];
  }
  
  if (decimalPart.isNotEmpty) {
    result += '.$decimalPart';
  }
  
  return result;
}

/// Format a date string to a readable format
String formatDate(String dateString) {
  try {
    final date = DateTime.parse(dateString);
    return '${date.day.toString().padLeft(2, '0')}/${date.month.toString().padLeft(2, '0')}/${date.year}';
  } catch (e) {
    return 'Invalid Date';
  }
}

/// Format a date and time string to a readable format
String formatDateTime(String dateString) {
  try {
    final date = DateTime.parse(dateString);
    return '${formatDate(dateString)} ${date.hour.toString().padLeft(2, '0')}:${date.minute.toString().padLeft(2, '0')}';
  } catch (e) {
    return 'Invalid Date';
  }
}

/// Format agent type for display
String formatAgentType(AgentType agentType) {
  switch (agentType) {
    case AgentType.novec1230:
      return 'NOVEC 1230';
    case AgentType.fm200:
      return 'FM-200';
  }
}

/// Format system type for display
String formatSystemType(SystemType systemType) {
  switch (systemType) {
    case SystemType.main:
      return 'Main System Only';
    case SystemType.reserve:
      return 'Reserve System Only';
    case SystemType.mainAndReserve:
      return 'Main + Reserve System';
  }
}

/// Format installation type for display
String formatInstallationType(InstallationType installationType) {
  switch (installationType) {
    case InstallationType.supplyOnly:
      return 'Supply Only';
    case InstallationType.supplyAndInstall:
      return 'Supply & Installation';
  }
}

/// Format input mode for display
String formatInputMode(InputMode inputMode) {
  switch (inputMode) {
    case InputMode.dimensions:
      return 'Room Dimensions';
    case InputMode.agentQuantity:
      return 'Agent Quantity';
  }
}
