import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:intl/intl.dart';
import '../models/project.dart';
import '../services/project_provider.dart';
import '../widgets/edit_material_dialog.dart';
import '../widgets/searchable_item_dialog.dart';
import '../widgets/pipe_type_selection_dialog.dart';
import 'clean_agent_systems_screen.dart';

class SystemDetailScreen extends StatefulWidget {
  final String systemId;

  const SystemDetailScreen({super.key, required this.systemId});

  @override
  State<SystemDetailScreen> createState() => _SystemDetailScreenState();
}

class _SystemDetailScreenState extends State<SystemDetailScreen> with SingleTickerProviderStateMixin {
  TabController? _tabController;
  final currencyFormatter = NumberFormat.currency(symbol: '\$');

  @override
  void initState() {
    super.initState();
    // Only initialize tab controller for non-clean agent systems
    // We'll check this in build method
  }

  @override
  void dispose() {
    _tabController?.dispose();
    super.dispose();
  }

  SystemEstimate? _getSystem(ProjectProvider projectProvider) {
    if (projectProvider.currentProject == null) {
      return null;
    }

    return projectProvider.currentProject!.systems
        .firstWhere((s) => s.id == widget.systemId, orElse: () => throw Exception('System not found'));
  }

  bool _isCleanAgentSystem(String systemType) {
    return systemType == 'Clean Agent';
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<ProjectProvider>(
      builder: (context, projectProvider, child) {
        final system = _getSystem(projectProvider);

        if (system == null) {
          return Scaffold(
            appBar: AppBar(title: const Text('System Details')),
            body: const Center(child: Text('System not found')),
          );
        }

        // For Clean Agent systems, show the systems management screen
        if (_isCleanAgentSystem(system.type)) {
          return const CleanAgentSystemsScreen();
        }

        // For other systems, initialize tab controller if not already done
        _tabController ??= TabController(length: 2, vsync: this);

        // For other systems, show the normal Materials/Services tabs
        return Scaffold(
          appBar: AppBar(
            title: Text(system.name),
            actions: [
              IconButton(
                icon: const Icon(Icons.save),
                tooltip: 'Save Project',
                onPressed: () async {
                  await projectProvider.saveCurrentProject();
                  if (!mounted) return;

                  final scaffoldMessenger = ScaffoldMessenger.of(context);
                  scaffoldMessenger.showSnackBar(
                    const SnackBar(content: Text('Project saved')),
                  );
                },
              ),
            ],
            bottom: TabBar(
              controller: _tabController!,
              indicatorWeight: 3,
              indicatorSize: TabBarIndicatorSize.tab,
              labelStyle: const TextStyle(
                fontWeight: FontWeight.bold,
                fontSize: 16,
              ),
              unselectedLabelStyle: const TextStyle(
                fontWeight: FontWeight.normal,
                fontSize: 16,
              ),
              tabs: const [
                Tab(
                  icon: Icon(Icons.inventory),
                  text: 'Materials',
                ),
                Tab(
                  icon: Icon(Icons.engineering),
                  text: 'Services',
                ),
              ],
            ),
          ),
          body: TabBarView(
            controller: _tabController!,
            children: [
              _buildMaterialsTab(system, projectProvider),
              _buildServicesTab(system, projectProvider),
            ],
          ),
          bottomNavigationBar: BottomAppBar(
            child: Padding(
              padding: const EdgeInsets.symmetric(horizontal: 8.0, vertical: 8.0),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  // Only show the total sum
                  const Text(
                    'Total:',
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(width: 12),
                  Text(
                    'SAR ${NumberFormat("#,##0").format(system.totalCost.roundToDouble())}',
                    style: const TextStyle(
                      fontSize: 22,
                      fontWeight: FontWeight.bold,
                      color: Colors.blue,
                    ),
                  ),
                ],
              ),
            ),
          ),
          floatingActionButton: FloatingActionButton(
            onPressed: () {
              switch (_tabController!.index) {
                case 0:
                  // For piping systems, show pipe type selection
                  if (system.type.toLowerCase() == 'piping') {
                    showDialog(
                      context: context,
                      builder: (context) => PipeTypeSelectionDialog(
                        onItemSelected: (material) {
                          projectProvider.addMaterial(system.id, material);
                          projectProvider.saveCurrentProject();
                        },
                      ),
                    );
                  } else {
                    // For other systems, show general searchable item dialog
                    showDialog(
                      context: context,
                      builder: (context) => SearchableItemDialog(
                        systemType: system.type,
                        onItemSelected: (material) {
                          projectProvider.addMaterial(system.id, material);
                          projectProvider.saveCurrentProject();
                        },
                      ),
                    );
                  }
                  break;
                case 1:
                  // Create a new service directly with a dialog
                  _showAddServiceDialog(context, system, projectProvider);
                  break;
              }
            },
            child: const Icon(Icons.add),
          ),
        );
      },
    );
  }

  Widget _buildMaterialsTab(SystemEstimate system, ProjectProvider projectProvider) {
    // Combine materials and equipment
    final bool hasMaterials = system.materials.isNotEmpty;
    final bool hasEquipment = system.equipment.isNotEmpty;

    if (!hasMaterials && !hasEquipment) {
      return _buildEmptyState('No materials added yet', 'Add Material');
    }

    // Create a combined list of items to display
    final List<Widget> itemCards = [];

    // Add materials
    if (hasMaterials) {
      itemCards.add(
        const Padding(
          padding: EdgeInsets.fromLTRB(16, 12, 16, 6),
          child: Text(
            'Materials',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
            ),
          ),
        )
      );

      for (final material in system.materials) {
        itemCards.add(_buildCompactMaterialRow(material, system, projectProvider));
      }
    }

    // Add equipment
    if (hasEquipment) {
      itemCards.add(
        const Padding(
          padding: EdgeInsets.fromLTRB(16, 12, 16, 6),
          child: Text(
            'Equipment',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
            ),
          ),
        )
      );

      for (final equipment in system.equipment) {
        itemCards.add(_buildCompactEquipmentRow(equipment, system, projectProvider));
      }
    }

    return ListView(
      children: itemCards,
    );
  }

  Widget _buildServicesTab(SystemEstimate system, ProjectProvider projectProvider) {
    if (system.services.isEmpty) {
      return _buildEmptyState('No service items added yet', 'Add Service');
    }

    return ListView.builder(
      itemCount: system.services.length,
      itemBuilder: (context, index) {
        final service = system.services[index];
        return _buildServiceCard(service, system, projectProvider);
      },
    );
  }

  Widget _buildEmptyState(String message, String buttonText) {
    final projectProvider = Provider.of<ProjectProvider>(context, listen: false);
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Icon(
            Icons.inventory,
            size: 80,
            color: Colors.grey,
          ),
          const SizedBox(height: 16),
          Text(
            message,
            style: const TextStyle(fontSize: 18, color: Colors.grey),
          ),
          const SizedBox(height: 24),
          ElevatedButton.icon(
            icon: const Icon(Icons.add),
            label: Text(buttonText),
            onPressed: () {
              switch (_tabController!.index) {
                case 0:
                  // Get the current system to check its type
                  final system = projectProvider.currentProject!.systems.firstWhere((s) => s.id == widget.systemId);

                  // For piping systems, show pipe type selection
                  if (system.type.toLowerCase() == 'piping') {
                    showDialog(
                      context: context,
                      builder: (context) => PipeTypeSelectionDialog(
                        onItemSelected: (material) {
                          projectProvider.addMaterial(system.id, material);
                          projectProvider.saveCurrentProject();
                        },
                      ),
                    );
                  } else {
                    // For other systems, show general searchable item dialog
                    showDialog(
                      context: context,
                      builder: (context) => SearchableItemDialog(
                        systemType: system.type,
                        onItemSelected: (material) {
                          projectProvider.addMaterial(system.id, material);
                          projectProvider.saveCurrentProject();
                        },
                      ),
                    );
                  }
                  break;
                case 1:
                  _showAddServiceDialog(context,
                    projectProvider.currentProject!.systems.firstWhere((s) => s.id == widget.systemId),
                    projectProvider
                  );
                  break;
              }
            },
          ),
        ],
      ),
    );
  }



  Widget _buildServiceCard(ServiceItem service, SystemEstimate system, ProjectProvider projectProvider) {
    return Card(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Expanded(
                  flex: 3,
                  child: Text(
                    service.description,
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                    ),
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
                const SizedBox(width: 8),
                Expanded(
                  flex: 2,
                  child: Text(
                    'SAR ${NumberFormat("#,##0").format(service.totalCost.roundToDouble())}',
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                      color: Colors.blue,
                    ),
                    textAlign: TextAlign.right,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            Text('Category: ${service.category}'),
            Text('${service.quantity} ${service.unit}'),
            Text('Rate: ${NumberFormat("#,##0").format(service.unitRate)} per ${service.unit}'),
            const SizedBox(height: 8),
            Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                TextButton.icon(
                  icon: const Icon(Icons.edit, size: 16),
                  label: const Text('Edit'),
                  onPressed: () {
                    _showEditServiceDialog(context, service, system, projectProvider);
                  },
                ),
                const SizedBox(width: 8),
                TextButton.icon(
                  icon: const Icon(Icons.delete, size: 16, color: Colors.red),
                  label: const Text('Delete', style: TextStyle(color: Colors.red)),
                  onPressed: () {
                    _showDeleteItemConfirmation(
                      context,
                      'service item',
                      service.description,
                      () => projectProvider.removeService(system.id, service.id),
                    );
                  },
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  // Helper function to format numbers with commas
  String _formatNumberWithCommas(double number) {
    return number.toStringAsFixed(0).replaceAllMapped(
      RegExp(r'(\d{1,3})(?=(\d{3})+(?!\d))'),
      (Match m) => '${m[1]},',
    );
  }

  Widget _buildCompactMaterialRow(MaterialItem material, SystemEstimate system, ProjectProvider projectProvider) {
    // Calculate total unit price (ex-works + local + installation if included)
    final project = projectProvider.currentProject;
    final exchangeRate = project?.exchangeRate ?? 3.75;
    final includeInstallation = project?.includeInstallation ?? true;

    // Calculate unit price based on project settings
    final unitPrice = includeInstallation
        ? material.getTotalUnitRateInSAR(exchangeRate)
        : (material.exWorksUnitCost * exchangeRate) + material.localUnitCost;

    final ceiledUnitPrice = unitPrice.ceil().toDouble();
    final totalPrice = material.quantity * ceiledUnitPrice;

    return GestureDetector(
      onDoubleTap: () => _showEditMaterialDialog(context, material, system, projectProvider),
      child: Container(
        margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 2),
        padding: const EdgeInsets.all(12),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(8),
          border: Border.all(color: Colors.grey.shade200),
        ),
        child: Row(
        children: [
          // Left section - Description and Category
          Expanded(
            flex: 4,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Category badge
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
                  decoration: BoxDecoration(
                    color: Colors.blue.shade50,
                    borderRadius: BorderRadius.circular(4),
                  ),
                  child: Text(
                    material.category,
                    style: TextStyle(
                      fontSize: 10,
                      color: Colors.blue.shade700,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
                const SizedBox(height: 4),
                // Clickable Description - shows container on hover/press
                Material(
                  color: Colors.transparent,
                  child: InkWell(
                    onTap: () => _showItemSelectionDialog(context, system, projectProvider, material),
                    borderRadius: BorderRadius.circular(8),
                    child: Container(
                      width: double.infinity,
                      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(8),
                        color: Colors.transparent, // Default transparent
                      ),
                      child: Row(
                        children: [
                          Expanded(
                            child: Text(
                              material.description.isEmpty ? material.name : material.description,
                              style: const TextStyle(
                                fontSize: 14,
                                fontWeight: FontWeight.w600,
                                color: Colors.black,
                              ),
                              maxLines: 2, // Allow wrapping to 2 lines
                              overflow: TextOverflow.ellipsis,
                            ),
                          ),
                          const SizedBox(width: 4),
                          Icon(
                            Icons.arrow_drop_down,
                            size: 18,
                            color: Colors.grey.shade600,
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
                // Show model/name as non-clickable text if different from description
                if (material.description.isNotEmpty && material.description != material.name) ...[
                  const SizedBox(height: 4),
                  Text(
                    material.name,
                    style: TextStyle(
                      fontSize: 12,
                      color: Colors.grey.shade600,
                    ),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                ],
                // Cost breakdown with background containers
                const SizedBox(height: 4),
                Wrap(
                  spacing: 4,
                  runSpacing: 2,
                  children: [
                    Container(
                      padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                      decoration: BoxDecoration(
                        color: Colors.grey.shade100,
                        borderRadius: BorderRadius.circular(4),
                      ),
                      child: Text(
                        'Ex-works: \$${material.exWorksUnitCost.toStringAsFixed(2)}',
                        style: const TextStyle(fontSize: 9, color: Colors.grey),
                      ),
                    ),
                    Container(
                      padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                      decoration: BoxDecoration(
                        color: Colors.grey.shade100,
                        borderRadius: BorderRadius.circular(4),
                      ),
                      child: Text(
                        'Local: SAR ${material.localUnitCost.toStringAsFixed(2)}',
                        style: const TextStyle(fontSize: 9, color: Colors.grey),
                      ),
                    ),
                    // Only show installation cost if project includes installation
                    if (includeInstallation)
                      Container(
                        padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                        decoration: BoxDecoration(
                          color: Colors.grey.shade100,
                          borderRadius: BorderRadius.circular(4),
                        ),
                        child: Text(
                          'Install: SAR ${material.installationUnitCost.toStringAsFixed(2)}',
                          style: const TextStyle(fontSize: 9, color: Colors.grey),
                        ),
                      ),
                  ],
                ),
              ],
            ),
          ),
          const SizedBox(width: 12), // Add spacing between columns

          // Middle section - Manufacturer and Approval
          Expanded(
            flex: 1,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Manufacturer',
                  style: TextStyle(
                    fontSize: 10,
                    color: Colors.grey.shade600,
                    fontWeight: FontWeight.w500,
                  ),
                ),
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                  decoration: BoxDecoration(
                    color: Colors.grey.shade100,
                    borderRadius: BorderRadius.circular(4),
                  ),
                  child: Text(
                    material.vendor.isEmpty ? 'Naproflat' : material.vendor,
                    style: const TextStyle(fontSize: 11, fontWeight: FontWeight.w500),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
                const SizedBox(height: 8),
                Text(
                  'Approval',
                  style: TextStyle(
                    fontSize: 10,
                    color: Colors.grey.shade600,
                    fontWeight: FontWeight.w500,
                  ),
                ),
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                  decoration: BoxDecoration(
                    color: Colors.grey.shade100,
                    borderRadius: BorderRadius.circular(4),
                  ),
                  child: Text(
                    material.approval.isEmpty ? 'UL/FM' : material.approval,
                    style: const TextStyle(fontSize: 11, fontWeight: FontWeight.w500),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
              ],
            ),
          ),

          const SizedBox(width: 8), // Add spacing between sections

          // Quantity section with manual input (prevent double-click propagation)
          Expanded(
            flex: 2,
            child: GestureDetector(
              onDoubleTap: () {}, // Absorb double-click to prevent propagation
              child: Column(
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                Text(
                  'Quantity',
                  style: TextStyle(
                    fontSize: 10,
                    color: Colors.grey.shade600,
                    fontWeight: FontWeight.w500,
                  ),
                ),
                const SizedBox(height: 4),
                FittedBox(
                  fit: BoxFit.scaleDown,
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      // Minus button - clean design like image
                      Material(
                        color: Colors.transparent,
                        child: InkWell(
                          onTap: () {
                            if (material.quantity > 0) {
                              _updateMaterialQuantity(material, material.quantity - 1, system, projectProvider);
                            }
                          },
                          borderRadius: BorderRadius.circular(8),
                          child: Container(
                            width: 18,
                            height: 18,
                            decoration: BoxDecoration(
                              color: Colors.grey.shade300,
                              borderRadius: BorderRadius.circular(4),
                            ),
                            child: Icon(
                              Icons.remove,
                              size: 12,
                              color: material.quantity > 0 ? Colors.black : Colors.grey.shade500,
                            ),
                          ),
                        ),
                      ),
                      const SizedBox(width: 4), // Minimal spacing
                      // Quantity display - smaller font
                      GestureDetector(
                        onTap: () => _showQuantityEditDialog(context, material, system, projectProvider),
                        child: Container(
                          padding: const EdgeInsets.symmetric(horizontal: 4, vertical: 2),
                          child: Text(
                            '${material.quantity.toInt()}',
                            style: const TextStyle(
                              fontWeight: FontWeight.bold,
                              fontSize: 11,
                              color: Colors.black,
                            ),
                          ),
                        ),
                      ),
                      const SizedBox(width: 4), // Minimal spacing
                      // Plus button - clean design like image
                      Material(
                        color: Colors.transparent,
                        child: InkWell(
                          onTap: () {
                            _updateMaterialQuantity(material, material.quantity + 1, system, projectProvider);
                          },
                          borderRadius: BorderRadius.circular(6),
                          child: Container(
                            width: 18,
                            height: 18,
                            decoration: BoxDecoration(
                              color: Colors.blue.shade500,
                              borderRadius: BorderRadius.circular(4),
                            ),
                            child: const Icon(
                              Icons.add,
                              size: 12,
                              color: Colors.white,
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
            ),
          ),

          const SizedBox(width: 8), // Add spacing between sections

          // Right section - Unit Price and Total
          Expanded(
            flex: 2,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Unit Price',
                  style: TextStyle(
                    fontSize: 10,
                    color: Colors.grey.shade600,
                    fontWeight: FontWeight.w500,
                  ),
                ),
                Text(
                  'SAR ${_formatNumberWithCommas(ceiledUnitPrice)}',
                  style: const TextStyle(fontSize: 12, fontWeight: FontWeight.w500),
                ),
                const SizedBox(height: 4),
                Text(
                  'Total',
                  style: TextStyle(
                    fontSize: 10,
                    color: Colors.grey.shade600,
                    fontWeight: FontWeight.w500,
                  ),
                ),
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                  decoration: BoxDecoration(
                    color: Colors.blue.shade50,
                    borderRadius: BorderRadius.circular(4),
                  ),
                  child: Text(
                    'SAR ${_formatNumberWithCommas(totalPrice)}',
                    style: TextStyle(
                      fontSize: 13,
                      fontWeight: FontWeight.bold,
                      color: Colors.blue.shade700,
                    ),
                  ),
                ),
              ],
            ),
          ),

          // Actions menu
          PopupMenuButton<String>(
            padding: EdgeInsets.zero,
            iconSize: 20,
            onSelected: (value) {
              switch (value) {
                case 'edit':
                  _showEditMaterialDialog(context, material, system, projectProvider);
                  break;
                case 'duplicate':
                  _duplicateMaterial(material, system, projectProvider);
                  break;
                case 'delete':
                  _deleteMaterial(material, system, projectProvider);
                  break;
              }
            },
            itemBuilder: (context) => [
              const PopupMenuItem(
                value: 'edit',
                child: Row(
                  children: [
                    Icon(Icons.edit, size: 16),
                    SizedBox(width: 8),
                    Text('Edit'),
                  ],
                ),
              ),
              const PopupMenuItem(
                value: 'duplicate',
                child: Row(
                  children: [
                    Icon(Icons.copy, size: 16),
                    SizedBox(width: 8),
                    Text('Duplicate'),
                  ],
                ),
              ),
              const PopupMenuItem(
                value: 'delete',
                child: Row(
                  children: [
                    Icon(Icons.delete, size: 16, color: Colors.red),
                    SizedBox(width: 8),
                    Text('Delete', style: TextStyle(color: Colors.red)),
                  ],
                ),
              ),
            ],
          ),
        ],
        ),
      ),
    );
  }

  Widget _buildCompactEquipmentRow(EquipmentItem equipment, SystemEstimate system, ProjectProvider projectProvider) {
    // Calculate total unit price (ex-works + local + installation if included)
    final project = projectProvider.currentProject;
    final exchangeRate = project?.exchangeRate ?? 3.75;
    final includeInstallation = project?.includeInstallation ?? true;

    // Calculate unit price based on project settings
    final unitPrice = includeInstallation
        ? equipment.getTotalUnitRateInSAR(exchangeRate)
        : (equipment.exWorksUnitCost * exchangeRate) + equipment.localUnitCost;

    final ceiledUnitPrice = unitPrice.ceil().toDouble();
    final totalPrice = equipment.quantity * ceiledUnitPrice;

    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 2),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.grey.shade200),
      ),
      child: Row(
        children: [
          // Left section - Name and Category
          Expanded(
            flex: 3,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Category badge
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
                  decoration: BoxDecoration(
                    color: Colors.orange.shade50,
                    borderRadius: BorderRadius.circular(4),
                  ),
                  child: Text(
                    equipment.category,
                    style: TextStyle(
                      fontSize: 10,
                      color: Colors.orange.shade700,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
                const SizedBox(height: 4),
                // Name
                Text(
                  equipment.name,
                  style: const TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.w600,
                  ),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
                // Cost breakdown
                const SizedBox(height: 4),
                Row(
                  children: [
                    Text(
                      'Ex-works: \$${equipment.exWorksUnitCost.toStringAsFixed(2)}',
                      style: const TextStyle(fontSize: 10, color: Colors.grey),
                    ),
                    const SizedBox(width: 12),
                    Text(
                      'Local: SAR ${equipment.localUnitCost.toStringAsFixed(2)}',
                      style: const TextStyle(fontSize: 10, color: Colors.grey),
                    ),
                    // Only show installation cost if project includes installation
                    if (includeInstallation) ...[
                      const SizedBox(width: 12),
                      Text(
                        'Installation: SAR ${equipment.installationUnitCost.toStringAsFixed(2)}',
                        style: const TextStyle(fontSize: 10, color: Colors.grey),
                      ),
                    ],
                  ],
                ),
              ],
            ),
          ),

          const SizedBox(width: 8), // Add spacing between sections

          // Middle section - Manufacturer and Approval
          Expanded(
            flex: 2,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Manufacturer',
                  style: TextStyle(
                    fontSize: 10,
                    color: Colors.grey.shade600,
                    fontWeight: FontWeight.w500,
                  ),
                ),
                Text(
                  equipment.vendor.isEmpty ? 'Naproflat' : equipment.vendor,
                  style: const TextStyle(fontSize: 12),
                ),
                const SizedBox(height: 8),
                Text(
                  'Approval',
                  style: TextStyle(
                    fontSize: 10,
                    color: Colors.grey.shade600,
                    fontWeight: FontWeight.w500,
                  ),
                ),
                Text(
                  equipment.approval.isEmpty ? 'UL/FM' : equipment.approval,
                  style: const TextStyle(fontSize: 12),
                ),
              ],
            ),
          ),

          // Right section - Quantity
          Expanded(
            flex: 2,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.end,
              children: [
                Text(
                  'Quantity',
                  style: TextStyle(
                    fontSize: 10,
                    color: Colors.grey.shade600,
                    fontWeight: FontWeight.w500,
                  ),
                ),
                Row(
                  mainAxisAlignment: MainAxisAlignment.end,
                  children: [
                    // Minus button - clean design like image
                    Material(
                      color: Colors.transparent,
                      child: InkWell(
                        onTap: () {
                          if (equipment.quantity > 0) {
                            final updatedEquipment = EquipmentItem(
                              id: equipment.id,
                              name: equipment.name,
                              category: equipment.category,
                              quantity: equipment.quantity - 1,
                              exWorksUnitCost: equipment.exWorksUnitCost,
                              localUnitCost: equipment.localUnitCost,
                              installationUnitCost: equipment.installationUnitCost,
                              isImported: equipment.isImported,
                              vendor: equipment.vendor,
                              approval: equipment.approval,
                            );
                            projectProvider.updateEquipment(system.id, updatedEquipment);
                            projectProvider.saveCurrentProject();
                          }
                        },
                        borderRadius: BorderRadius.circular(8),
                        child: Container(
                          width: 18,
                          height: 18,
                          decoration: BoxDecoration(
                            color: Colors.grey.shade300,
                            borderRadius: BorderRadius.circular(4),
                          ),
                          child: Icon(
                            Icons.remove,
                            size: 12,
                            color: equipment.quantity > 0 ? Colors.black : Colors.grey.shade500,
                          ),
                        ),
                      ),
                    ),
                    const SizedBox(width: 4), // Minimal spacing
                    // Quantity display - smaller font
                    Container(
                      padding: const EdgeInsets.symmetric(horizontal: 4, vertical: 2),
                      child: Text(
                        '${equipment.quantity.toInt()}',
                        style: const TextStyle(
                          fontWeight: FontWeight.bold,
                          fontSize: 11,
                          color: Colors.black,
                        ),
                      ),
                    ),
                    const SizedBox(width: 4), // Minimal spacing
                    // Plus button - clean design like image
                    Material(
                      color: Colors.transparent,
                      child: InkWell(
                        onTap: () {
                          final updatedEquipment = EquipmentItem(
                            id: equipment.id,
                            name: equipment.name,
                            category: equipment.category,
                            quantity: equipment.quantity + 1,
                            exWorksUnitCost: equipment.exWorksUnitCost,
                            localUnitCost: equipment.localUnitCost,
                            installationUnitCost: equipment.installationUnitCost,
                            isImported: equipment.isImported,
                            vendor: equipment.vendor,
                            approval: equipment.approval,
                          );
                          projectProvider.updateEquipment(system.id, updatedEquipment);
                          projectProvider.saveCurrentProject();
                        },
                        borderRadius: BorderRadius.circular(6),
                        child: Container(
                          width: 18,
                          height: 18,
                          decoration: BoxDecoration(
                            color: Colors.blue.shade500,
                            borderRadius: BorderRadius.circular(4),
                          ),
                          child: const Icon(
                            Icons.add,
                            size: 12,
                            color: Colors.white,
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),

          // Actions menu
          PopupMenuButton<String>(
            padding: EdgeInsets.zero,
            iconSize: 20,
            onSelected: (value) {
              switch (value) {
                case 'edit':
                  _showEditEquipmentDialog(context, equipment, system, projectProvider);
                  break;
                case 'delete':
                  _deleteEquipment(equipment, system, projectProvider);
                  break;
              }
            },
            itemBuilder: (context) => [
              const PopupMenuItem(
                value: 'edit',
                child: Row(
                  children: [
                    Icon(Icons.edit, size: 16),
                    SizedBox(width: 8),
                    Text('Edit'),
                  ],
                ),
              ),
              const PopupMenuItem(
                value: 'delete',
                child: Row(
                  children: [
                    Icon(Icons.delete, size: 16, color: Colors.red),
                    SizedBox(width: 8),
                    Text('Delete', style: TextStyle(color: Colors.red)),
                  ],
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  void _showEditMaterialDialog(BuildContext context, MaterialItem material, SystemEstimate system, ProjectProvider projectProvider) {
    // Get the exchange rate from the current project
    final exchangeRate = projectProvider.currentProject?.exchangeRate ?? 3.75; // Default to 3.75 if not available

    showDialog(
      context: context,
      builder: (context) => EditMaterialDialog(
        material: material,
        exchangeRate: exchangeRate,
        onSave: (updatedMaterial) {
          projectProvider.updateMaterial(system.id, updatedMaterial);
          projectProvider.saveCurrentProject();
        },
      ),
    );
  }



  void _deleteMaterial(MaterialItem material, SystemEstimate system, ProjectProvider projectProvider) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete Material'),
        content: Text('Are you sure you want to delete "${material.name}"?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              projectProvider.removeMaterial(system.id, material.id);
              projectProvider.saveCurrentProject();
              Navigator.of(context).pop();
            },
            child: const Text('Delete', style: TextStyle(color: Colors.red)),
          ),
        ],
      ),
    );
  }

  void _deleteEquipment(EquipmentItem equipment, SystemEstimate system, ProjectProvider projectProvider) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete Equipment'),
        content: Text('Are you sure you want to delete "${equipment.name}"?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              projectProvider.removeEquipment(system.id, equipment.id);
              projectProvider.saveCurrentProject();
              Navigator.of(context).pop();
            },
            child: const Text('Delete', style: TextStyle(color: Colors.red)),
          ),
        ],
      ),
    );
  }

  void _showEditEquipmentDialog(BuildContext context, EquipmentItem equipment, SystemEstimate system, ProjectProvider projectProvider) {
    final quantityController = TextEditingController(text: equipment.quantity.toString());
    final exWorksUnitCostController = TextEditingController(text: equipment.exWorksUnitCost.toString());
    final localUnitCostController = TextEditingController(text: equipment.localUnitCost.toString());
    final installationUnitCostController = TextEditingController(text: equipment.installationUnitCost.toString());
    final vendorController = TextEditingController(text: equipment.vendor);
    final approvalController = TextEditingController(text: equipment.approval);

    // Get the exchange rate from the current project
    final exchangeRate = projectProvider.currentProject?.exchangeRate ?? 3.75; // Default to 3.75 if not available

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('Edit ${equipment.name}'),
        content: SingleChildScrollView(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              TextField(
                controller: quantityController,
                decoration: const InputDecoration(labelText: 'Quantity'),
                keyboardType: const TextInputType.numberWithOptions(decimal: true),
              ),
              TextField(
                controller: vendorController,
                decoration: const InputDecoration(labelText: 'Vendor/Manufacturer'),
              ),
              TextField(
                controller: approvalController,
                decoration: const InputDecoration(labelText: 'Approval/Certification'),
              ),
              const SizedBox(height: 16),
              const Divider(),
              const SizedBox(height: 8),
              TextField(
                controller: exWorksUnitCostController,
                decoration: const InputDecoration(labelText: 'Ex-Works Unit Cost (USD)'),
                keyboardType: const TextInputType.numberWithOptions(decimal: true),
                // No automatic update of local cost
              ),
              TextField(
                controller: localUnitCostController,
                decoration: const InputDecoration(labelText: 'Local Unit Cost (SAR)'),
                keyboardType: const TextInputType.numberWithOptions(decimal: true),
              ),
              TextField(
                controller: installationUnitCostController,
                decoration: const InputDecoration(labelText: 'Installation Unit Cost (SAR)'),
                keyboardType: const TextInputType.numberWithOptions(decimal: true),
              ),
              const SizedBox(height: 8),
              Text('Exchange Rate: \$1 = SAR ${exchangeRate.toStringAsFixed(2)}',
                style: const TextStyle(fontSize: 12, fontStyle: FontStyle.italic),
              ),
              const SizedBox(height: 8),
              // Show calculated total unit rate
              FutureBuilder<void>(
                future: Future.delayed(Duration.zero), // Just to trigger the builder
                builder: (context, snapshot) {
                  final exWorksCost = double.tryParse(exWorksUnitCostController.text) ?? 0.0;
                  final localCost = double.tryParse(localUnitCostController.text) ?? 0.0;
                  final installationCost = double.tryParse(installationUnitCostController.text) ?? 0.0;
                  // Convert ex-works cost to SAR using the exchange rate
                  final totalUnitRate = (exWorksCost * exchangeRate) + localCost + installationCost;

                  return Text(
                    'Total Unit Rate: SAR ${NumberFormat("#,##0.00").format(totalUnitRate)}',
                    style: const TextStyle(fontWeight: FontWeight.bold),
                  );
                },
              ),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              final updatedEquipment = EquipmentItem(
                id: equipment.id,
                name: equipment.name,
                category: equipment.category,
                quantity: double.tryParse(quantityController.text) ?? equipment.quantity,
                exWorksUnitCost: double.tryParse(exWorksUnitCostController.text) ?? equipment.exWorksUnitCost,
                localUnitCost: double.tryParse(localUnitCostController.text) ?? equipment.localUnitCost,
                installationUnitCost: double.tryParse(installationUnitCostController.text) ?? equipment.installationUnitCost,
                isImported: equipment.isImported,
                vendor: vendorController.text,
                approval: approvalController.text,
              );

              projectProvider.updateEquipment(system.id, updatedEquipment);
              Navigator.pop(context);
            },
            child: const Text('Save'),
          ),
        ],
      ),
    );
  }

  void _showEditServiceDialog(BuildContext context, ServiceItem service, SystemEstimate system, ProjectProvider projectProvider) {
    final quantityController = TextEditingController(text: service.quantity.toString());
    final unitRateController = TextEditingController(text: service.unitRate.toString());

    // Service unit options
    final List<String> unitOptions = ['Days', 'Lumpsum'];
    // If the current unit is not in the new options, default to 'Days'
    String selectedUnit = unitOptions.contains(service.unit) ? service.unit : 'Days';

    // Service category options
    final List<String> categoryOptions = [
      'Engineering',
      'Management',
      'Testing',
      'Programming',
      'Commissioning',
      'Documentation',
      'Training',
      'Consultation'
    ];
    String selectedCategory = service.category;

    showDialog(
      context: context,
      builder: (context) => StatefulBuilder(
        builder: (context, setState) => AlertDialog(
          title: const Text('Edit Service'),
          content: SingleChildScrollView(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Text('Service Type', style: TextStyle(fontSize: 12, color: Colors.grey)),
                DropdownButton<String>(
                  isExpanded: true,
                  value: selectedCategory,
                  items: categoryOptions.map((category) {
                    return DropdownMenuItem<String>(
                      value: category,
                      child: Text(category),
                    );
                  }).toList(),
                  onChanged: (value) {
                    if (value != null) {
                      setState(() {
                        selectedCategory = value;
                      });
                    }
                  },
                ),

                const SizedBox(height: 16),
                Row(
                  children: [
                    Expanded(
                      child: TextField(
                        controller: quantityController,
                        decoration: const InputDecoration(labelText: 'Quantity'),
                        keyboardType: const TextInputType.numberWithOptions(decimal: true),
                      ),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: DropdownButton<String>(
                        isExpanded: true,
                        value: selectedUnit,
                        items: unitOptions.map((unit) {
                          return DropdownMenuItem<String>(
                            value: unit,
                            child: Text(unit),
                          );
                        }).toList(),
                        onChanged: (value) {
                          if (value != null) {
                            setState(() {
                              selectedUnit = value;
                            });
                          }
                        },
                      ),
                    ),
                  ],
                ),

                const SizedBox(height: 16),
                TextField(
                  controller: unitRateController,
                  decoration: InputDecoration(
                    labelText: 'Rate per $selectedUnit',
                    prefixText: 'SAR ',
                  ),
                  keyboardType: const TextInputType.numberWithOptions(decimal: true),
                ),

                const SizedBox(height: 8),
                FutureBuilder<void>(
                  future: Future.delayed(Duration.zero),
                  builder: (context, snapshot) {
                    final quantity = double.tryParse(quantityController.text) ?? 0.0;
                    final rate = double.tryParse(unitRateController.text) ?? 0.0;
                    final total = quantity * rate;

                    return Container(
                      width: double.infinity,
                      padding: const EdgeInsets.all(12),
                      decoration: BoxDecoration(
                        color: Colors.blue.withAlpha(25),
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.center,
                        children: [
                          const Text(
                            'Total Cost:',
                            style: TextStyle(
                              fontSize: 14,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          const SizedBox(height: 4),
                          Text(
                            'SAR ${total.round()}',
                            style: const TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                              color: Colors.blue,
                            ),
                          ),
                        ],
                      ),
                    );
                  },
                ),
              ],
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: const Text('Cancel'),
            ),
            TextButton(
              onPressed: () {
                final updatedService = ServiceItem(
                  id: service.id,
                  description: selectedCategory,
                  category: selectedCategory,
                  quantity: double.tryParse(quantityController.text) ?? service.quantity,
                  unit: selectedUnit,
                  unitRate: double.tryParse(unitRateController.text) ?? service.unitRate,
                );

                projectProvider.updateService(system.id, updatedService);
                Navigator.pop(context);
              },
              child: const Text('Save'),
            ),
          ],
        ),
      ),
    );
  }

  void _showAddServiceDialog(BuildContext context, SystemEstimate system, ProjectProvider projectProvider) {
    final quantityController = TextEditingController(text: '1');
    final unitRateController = TextEditingController(text: '0');

    // Service unit options
    final List<String> unitOptions = ['Days', 'Lumpsum'];
    String selectedUnit = 'Days';

    // Service category options
    final List<String> categoryOptions = [
      'Engineering',
      'Management',
      'Testing',
      'Programming',
      'Commissioning',
      'Documentation',
      'Training',
      'Consultation'
    ];
    String selectedCategory = 'Engineering';

    showDialog(
      context: context,
      builder: (context) => StatefulBuilder(
        builder: (context, setState) => AlertDialog(
          title: const Text('Add Service'),
          content: SingleChildScrollView(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Text('Service Type', style: TextStyle(fontSize: 12, color: Colors.grey)),
                DropdownButton<String>(
                  isExpanded: true,
                  value: selectedCategory,
                  items: categoryOptions.map((category) {
                    return DropdownMenuItem<String>(
                      value: category,
                      child: Text(category),
                    );
                  }).toList(),
                  onChanged: (value) {
                    if (value != null) {
                      setState(() {
                        selectedCategory = value;
                      });
                    }
                  },
                ),

                const SizedBox(height: 16),
                Row(
                  children: [
                    Expanded(
                      child: TextField(
                        controller: quantityController,
                        decoration: const InputDecoration(labelText: 'Quantity'),
                        keyboardType: const TextInputType.numberWithOptions(decimal: true),
                      ),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: DropdownButton<String>(
                        isExpanded: true,
                        value: selectedUnit,
                        items: unitOptions.map((unit) {
                          return DropdownMenuItem<String>(
                            value: unit,
                            child: Text(unit),
                          );
                        }).toList(),
                        onChanged: (value) {
                          if (value != null) {
                            setState(() {
                              selectedUnit = value;
                            });
                          }
                        },
                      ),
                    ),
                  ],
                ),

                const SizedBox(height: 16),
                TextField(
                  controller: unitRateController,
                  decoration: InputDecoration(
                    labelText: 'Rate per $selectedUnit',
                    prefixText: 'SAR ',
                  ),
                  keyboardType: const TextInputType.numberWithOptions(decimal: true),
                ),

                const SizedBox(height: 8),
                FutureBuilder<void>(
                  future: Future.delayed(Duration.zero),
                  builder: (context, snapshot) {
                    final quantity = double.tryParse(quantityController.text) ?? 0.0;
                    final rate = double.tryParse(unitRateController.text) ?? 0.0;
                    final total = quantity * rate;

                    return Container(
                      width: double.infinity,
                      padding: const EdgeInsets.all(12),
                      decoration: BoxDecoration(
                        color: Colors.blue.withAlpha(25),
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.center,
                        children: [
                          const Text(
                            'Total Cost:',
                            style: TextStyle(
                              fontSize: 14,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          const SizedBox(height: 4),
                          Text(
                            'SAR ${total.round()}',
                            style: const TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                              color: Colors.blue,
                            ),
                          ),
                        ],
                      ),
                    );
                  },
                ),
              ],
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: const Text('Cancel'),
            ),
            TextButton(
              onPressed: () {
                final newService = ServiceItem(
                  description: selectedCategory,
                  category: selectedCategory,
                  quantity: double.tryParse(quantityController.text) ?? 1.0,
                  unit: selectedUnit,
                  unitRate: double.tryParse(unitRateController.text) ?? 0.0,
                );

                projectProvider.addService(system.id, newService);
                Navigator.pop(context);
              },
              child: const Text('Add'),
            ),
          ],
        ),
      ),
    );
  }

  void _showDeleteItemConfirmation(BuildContext context, String itemType, String itemName, Function() onDelete) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('Delete $itemType'),
        content: Text('Are you sure you want to delete "$itemName"? This action cannot be undone.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              onDelete();
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(content: Text('$itemType deleted')),
              );
            },
            child: const Text('Delete', style: TextStyle(color: Colors.red)),
          ),
        ],
      ),
    );
  }

  // Helper method to update material quantity
  void _updateMaterialQuantity(MaterialItem material, double newQuantity, SystemEstimate system, ProjectProvider projectProvider) {
    final updatedMaterial = MaterialItem(
      id: material.id,
      name: material.name,
      category: material.category,
      description: material.description,
      quantity: newQuantity,
      unit: material.unit,
      exWorksUnitCost: material.exWorksUnitCost,
      localUnitCost: material.localUnitCost,
      installationUnitCost: material.installationUnitCost,
      isImported: material.isImported,
      vendor: material.vendor,
      approval: material.approval,
    );
    projectProvider.updateMaterial(system.id, updatedMaterial);
    projectProvider.saveCurrentProject();
  }

  // Show quantity edit dialog
  void _showQuantityEditDialog(BuildContext context, MaterialItem material, SystemEstimate system, ProjectProvider projectProvider) {
    final controller = TextEditingController(text: material.quantity.toInt().toString());

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Edit Quantity'),
        content: TextField(
          controller: controller,
          keyboardType: TextInputType.number,
          decoration: const InputDecoration(
            labelText: 'Quantity',
            border: OutlineInputBorder(),
          ),
          autofocus: true,
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              final newQuantity = double.tryParse(controller.text) ?? material.quantity;
              if (newQuantity >= 0) {
                _updateMaterialQuantity(material, newQuantity, system, projectProvider);
              }
              Navigator.pop(context);
            },
            child: const Text('Save'),
          ),
        ],
      ),
    );
  }

  // Duplicate material
  void _duplicateMaterial(MaterialItem material, SystemEstimate system, ProjectProvider projectProvider) {
    final duplicatedMaterial = MaterialItem(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      name: material.name,
      category: material.category,
      description: material.description,
      quantity: material.quantity,
      unit: material.unit,
      exWorksUnitCost: material.exWorksUnitCost,
      localUnitCost: material.localUnitCost,
      installationUnitCost: material.installationUnitCost,
      isImported: material.isImported,
      vendor: material.vendor,
      approval: material.approval,
    );

    projectProvider.addMaterial(system.id, duplicatedMaterial);
    projectProvider.saveCurrentProject();

    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Material duplicated')),
    );
  }

  // Show item selection dialog for replacing current material
  void _showItemSelectionDialog(BuildContext context, SystemEstimate system, ProjectProvider projectProvider, MaterialItem currentMaterial) {
    // For piping systems, show pipe type selection
    if (system.type.toLowerCase() == 'piping') {
      showDialog(
        context: context,
        builder: (context) => PipeTypeSelectionDialog(
          onItemSelected: (newMaterial) {
            // Replace the current material with the selected one, keeping the same ID and quantity
            final updatedMaterial = MaterialItem(
              id: currentMaterial.id, // Keep same ID
              name: newMaterial.name,
              category: newMaterial.category,
              description: newMaterial.description,
              quantity: currentMaterial.quantity, // Keep current quantity
              unit: newMaterial.unit,
              exWorksUnitCost: newMaterial.exWorksUnitCost,
              localUnitCost: newMaterial.localUnitCost,
              installationUnitCost: newMaterial.installationUnitCost,
              isImported: newMaterial.isImported,
              vendor: newMaterial.vendor,
              approval: newMaterial.approval,
            );
            projectProvider.updateMaterial(system.id, updatedMaterial);
            projectProvider.saveCurrentProject();
          },
        ),
      );
    } else {
      // For other systems, show general searchable item dialog
      showDialog(
        context: context,
        builder: (context) => SearchableItemDialog(
          systemType: system.type,
          onItemSelected: (newMaterial) {
            // Replace the current material with the selected one, keeping the same ID and quantity
            final updatedMaterial = MaterialItem(
              id: currentMaterial.id, // Keep same ID
              name: newMaterial.name,
              category: newMaterial.category,
              description: newMaterial.description,
              quantity: currentMaterial.quantity, // Keep current quantity
              unit: newMaterial.unit,
              exWorksUnitCost: newMaterial.exWorksUnitCost,
              localUnitCost: newMaterial.localUnitCost,
              installationUnitCost: newMaterial.installationUnitCost,
              isImported: newMaterial.isImported,
              vendor: newMaterial.vendor,
              approval: newMaterial.approval,
            );
            projectProvider.updateMaterial(system.id, updatedMaterial);
            projectProvider.saveCurrentProject();
          },
        ),
      );
    }
  }


}
