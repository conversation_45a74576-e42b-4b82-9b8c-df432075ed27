import 'dart:io';
import 'package:sqflite_common_ffi/sqflite_ffi.dart';
import 'package:path/path.dart' as path;
import 'package:path_provider/path_provider.dart';
import 'package:uuid/uuid.dart';
import '../models/flexible_table_models.dart';

class FlexibleDatabaseService {
  static Database? _database;
  static const String _databaseName = 'flexible_firetool.db';
  final _uuid = const Uuid();

  Future<Database> get database async {
    if (_database != null) return _database!;
    _database = await _initDatabase();
    return _database!;
  }

  Future<Database> _initDatabase() async {
    // Initialize sqflite for desktop
    if (Platform.isWindows || Platform.isLinux || Platform.isMacOS) {
      sqfliteFfiInit();
      databaseFactory = databaseFactoryFfi;
    }

    final documentsDirectory = await getApplicationDocumentsDirectory();
    final dbPath = path.join(documentsDirectory.path, _databaseName);
    
    return await openDatabase(
      dbPath,
      version: 1,
      onCreate: _onCreate,
    );
  }

  Future<void> _onCreate(Database db, int version) async {
    // Create metadata tables for flexible database structure
    await db.execute('''
      CREATE TABLE flexible_databases (
        database_id TEXT PRIMARY KEY,
        name TEXT NOT NULL,
        path TEXT NOT NULL,
        created_at TEXT NOT NULL,
        updated_at TEXT NOT NULL
      )
    ''');

    await db.execute('''
      CREATE TABLE flexible_tables (
        table_id TEXT PRIMARY KEY,
        name TEXT NOT NULL,
        database_id TEXT,
        created_at TEXT NOT NULL,
        updated_at TEXT NOT NULL,
        FOREIGN KEY (database_id) REFERENCES flexible_databases (database_id)
      )
    ''');

    await db.execute('''
      CREATE TABLE flexible_columns (
        column_id TEXT PRIMARY KEY,
        name TEXT NOT NULL,
        table_id TEXT,
        data_type TEXT DEFAULT 'TEXT',
        is_required INTEGER DEFAULT 0,
        default_value TEXT,
        order_index INTEGER DEFAULT 0,
        created_at TEXT NOT NULL,
        updated_at TEXT NOT NULL,
        FOREIGN KEY (table_id) REFERENCES flexible_tables (table_id)
      )
    ''');

    await db.execute('''
      CREATE TABLE flexible_rows (
        row_id TEXT PRIMARY KEY,
        table_id TEXT,
        created_at TEXT NOT NULL,
        updated_at TEXT NOT NULL,
        FOREIGN KEY (table_id) REFERENCES flexible_tables (table_id)
      )
    ''');

    await db.execute('''
      CREATE TABLE flexible_cells (
        cell_id TEXT PRIMARY KEY,
        row_id TEXT,
        column_id TEXT,
        value TEXT,
        created_at TEXT NOT NULL,
        updated_at TEXT NOT NULL,
        FOREIGN KEY (row_id) REFERENCES flexible_rows (row_id),
        FOREIGN KEY (column_id) REFERENCES flexible_columns (column_id)
      )
    ''');

    // Create a default database entry
    final defaultDbId = _uuid.v4();
    final now = DateTime.now().toIso8601String();
    final documentsDirectory = await getApplicationDocumentsDirectory();
    final dbPath = path.join(documentsDirectory.path, _databaseName);
    await db.insert('flexible_databases', {
      'database_id': defaultDbId,
      'name': 'Default Database',
      'path': dbPath,
      'created_at': now,
      'updated_at': now,
    });
  }

  Future<String> getDatabasePath() async {
    final documentsDirectory = await getApplicationDocumentsDirectory();
    return path.join(documentsDirectory.path, _databaseName);
  }

  // Database operations
  Future<List<FlexibleDatabase>> getAllDatabases() async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query('flexible_databases');
    return List.generate(maps.length, (i) => FlexibleDatabase.fromJson(maps[i]));
  }

  Future<List<FlexibleTable>> getTablesForDatabase(String databaseId) async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(
      'flexible_tables',
      where: 'database_id = ?',
      whereArgs: [databaseId],
    );
    return List.generate(maps.length, (i) => FlexibleTable.fromJson(maps[i]));
  }

  Future<FlexibleTable?> getTable(String tableId) async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(
      'flexible_tables',
      where: 'table_id = ?',
      whereArgs: [tableId],
    );
    if (maps.isEmpty) return null;
    return FlexibleTable.fromJson(maps.first);
  }

  Future<List<FlexibleColumn>> getColumnsForTable(String tableId) async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(
      'flexible_columns',
      where: 'table_id = ?',
      whereArgs: [tableId],
      orderBy: 'order_index ASC',
    );
    return List.generate(maps.length, (i) => FlexibleColumn.fromJson(maps[i]));
  }

  Future<List<FlexibleRow>> getRowsForTable(String tableId) async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(
      'flexible_rows',
      where: 'table_id = ?',
      whereArgs: [tableId],
    );
    return List.generate(maps.length, (i) => FlexibleRow.fromJson(maps[i]));
  }

  Future<Map<String, Map<String, FlexibleCell>>> getCellsForTable(String tableId) async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.rawQuery('''
      SELECT c.* FROM flexible_cells c
      INNER JOIN flexible_rows r ON c.row_id = r.row_id
      WHERE r.table_id = ?
    ''', [tableId]);
    
    final Map<String, Map<String, FlexibleCell>> result = {};
    for (final map in maps) {
      final cell = FlexibleCell.fromJson(map);
      if (cell.rowId != null && cell.columnId != null) {
        result[cell.rowId!] ??= {};
        result[cell.rowId!]![cell.columnId!] = cell;
      }
    }
    return result;
  }

  // Create operations
  Future<String> createTable(String name, String? databaseId) async {
    final db = await database;
    final tableId = _uuid.v4();
    final now = DateTime.now().toIso8601String();
    
    await db.insert('flexible_tables', {
      'table_id': tableId,
      'name': name,
      'database_id': databaseId,
      'created_at': now,
      'updated_at': now,
    });
    
    return tableId;
  }

  Future<String> createColumn(String name, String tableId, {String dataType = 'TEXT'}) async {
    final db = await database;
    final columnId = _uuid.v4();
    final now = DateTime.now().toIso8601String();
    
    // Get the next order index
    final List<Map<String, dynamic>> existing = await db.query(
      'flexible_columns',
      where: 'table_id = ?',
      whereArgs: [tableId],
      orderBy: 'order_index DESC',
      limit: 1,
    );
    
    final orderIndex = existing.isEmpty ? 0 : (existing.first['order_index'] as int) + 1;
    
    await db.insert('flexible_columns', {
      'column_id': columnId,
      'name': name,
      'table_id': tableId,
      'data_type': dataType,
      'is_required': 0,
      'order_index': orderIndex,
      'created_at': now,
      'updated_at': now,
    });
    
    return columnId;
  }

  Future<String> createRow(String tableId) async {
    final db = await database;
    final rowId = _uuid.v4();
    final now = DateTime.now().toIso8601String();
    
    await db.insert('flexible_rows', {
      'row_id': rowId,
      'table_id': tableId,
      'created_at': now,
      'updated_at': now,
    });
    
    return rowId;
  }

  Future<void> updateCell(String rowId, String columnId, dynamic value) async {
    final db = await database;
    final now = DateTime.now().toIso8601String();
    
    // Check if cell exists
    final existing = await db.query(
      'flexible_cells',
      where: 'row_id = ? AND column_id = ?',
      whereArgs: [rowId, columnId],
    );
    
    if (existing.isEmpty) {
      // Create new cell
      await db.insert('flexible_cells', {
        'cell_id': _uuid.v4(),
        'row_id': rowId,
        'column_id': columnId,
        'value': value?.toString(),
        'created_at': now,
        'updated_at': now,
      });
    } else {
      // Update existing cell
      await db.update(
        'flexible_cells',
        {
          'value': value?.toString(),
          'updated_at': now,
        },
        where: 'row_id = ? AND column_id = ?',
        whereArgs: [rowId, columnId],
      );
    }
  }

  // Delete operations
  Future<void> deleteTable(String tableId) async {
    final db = await database;
    
    // Delete in order: cells -> rows -> columns -> table
    await db.rawDelete('''
      DELETE FROM flexible_cells 
      WHERE row_id IN (SELECT row_id FROM flexible_rows WHERE table_id = ?)
    ''', [tableId]);
    
    await db.delete('flexible_rows', where: 'table_id = ?', whereArgs: [tableId]);
    await db.delete('flexible_columns', where: 'table_id = ?', whereArgs: [tableId]);
    await db.delete('flexible_tables', where: 'table_id = ?', whereArgs: [tableId]);
  }

  Future<void> deleteRow(String rowId) async {
    final db = await database;
    await db.delete('flexible_cells', where: 'row_id = ?', whereArgs: [rowId]);
    await db.delete('flexible_rows', where: 'row_id = ?', whereArgs: [rowId]);
  }

  Future<void> deleteColumn(String columnId) async {
    final db = await database;
    await db.delete('flexible_cells', where: 'column_id = ?', whereArgs: [columnId]);
    await db.delete('flexible_columns', where: 'column_id = ?', whereArgs: [columnId]);
  }

  Future<void> close() async {
    final db = _database;
    if (db != null) {
      await db.close();
      _database = null;
    }
  }
}