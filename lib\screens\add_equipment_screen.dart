import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../constants/app_constants.dart';
import '../models/project.dart';
import '../services/project_provider.dart';

class AddEquipmentScreen extends StatefulWidget {
  final String systemId;

  const AddEquipmentScreen({super.key, required this.systemId});

  @override
  State<AddEquipmentScreen> createState() => _AddEquipmentScreenState();
}

class _AddEquipmentScreenState extends State<AddEquipmentScreen> {
  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  final _quantityController = TextEditingController();
  final _exWorksUnitCostController = TextEditingController();
  final _localUnitCostController = TextEditingController();

  String _selectedCategory = '';
  bool _isImported = true;

  @override
  void initState() {
    super.initState();
    _initializeCategories();
  }

  void _initializeCategories() {
    final projectProvider = Provider.of<ProjectProvider>(context, listen: false);
    final system = projectProvider.currentProject?.systems
        .firstWhere((s) => s.id == widget.systemId);

    if (system != null) {
      final categories = AppConstants.materialCategories[system.type] ?? [];
      if (categories.isNotEmpty) {
        setState(() {
          _selectedCategory = categories.first;
        });
      }
    }
  }

  @override
  void dispose() {
    _nameController.dispose();
    _quantityController.dispose();
    _exWorksUnitCostController.dispose();
    _localUnitCostController.dispose();
    super.dispose();
  }

  void _addEquipment() {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    final equipment = EquipmentItem(
      name: _nameController.text.trim(),
      category: _selectedCategory,
      quantity: double.tryParse(_quantityController.text) ?? 0,
      exWorksUnitCost: double.tryParse(_exWorksUnitCostController.text) ?? 0,
      localUnitCost: double.tryParse(_localUnitCostController.text) ?? 0,
      isImported: _isImported,
    );

    Provider.of<ProjectProvider>(context, listen: false)
        .addEquipment(widget.systemId, equipment);

    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Equipment added')),
    );

    Navigator.pop(context);
  }

  @override
  Widget build(BuildContext context) {
    final projectProvider = Provider.of<ProjectProvider>(context);
    final system = projectProvider.currentProject?.systems
        .firstWhere((s) => s.id == widget.systemId);

    if (system == null) {
      return Scaffold(
        appBar: AppBar(title: const Text('Add Equipment')),
        body: const Center(child: Text('System not found')),
      );
    }

    final categories = AppConstants.materialCategories[system.type] ?? [];

    return Scaffold(
      appBar: AppBar(
        title: const Text('Add Equipment'),
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Form(
          key: _formKey,
          child: ListView(
            children: [
              TextFormField(
                controller: _nameController,
                decoration: const InputDecoration(
                  labelText: 'Equipment Name',
                  hintText: 'Enter equipment name',
                  prefixIcon: Icon(Icons.build),
                ),
                validator: (value) {
                  if (value == null || value.trim().isEmpty) {
                    return 'Please enter an equipment name';
                  }
                  return null;
                },
              ),
              const SizedBox(height: 16),
              DropdownButtonFormField<String>(
                decoration: const InputDecoration(
                  labelText: 'Category',
                  prefixIcon: Icon(Icons.category),
                ),
                value: categories.contains(_selectedCategory) ? _selectedCategory : categories.first,
                items: categories.map((category) {
                  return DropdownMenuItem<String>(
                    value: category,
                    child: Text(category),
                  );
                }).toList(),
                onChanged: (value) {
                  if (value != null) {
                    setState(() {
                      _selectedCategory = value;
                    });
                  }
                },
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'Please select a category';
                  }
                  return null;
                },
              ),
              const SizedBox(height: 16),
              TextFormField(
                controller: _quantityController,
                decoration: const InputDecoration(
                  labelText: 'Quantity',
                  hintText: 'Enter quantity',
                  prefixIcon: Icon(Icons.numbers),
                ),
                keyboardType: const TextInputType.numberWithOptions(decimal: true),
                validator: (value) {
                  if (value == null || value.trim().isEmpty) {
                    return 'Please enter a quantity';
                  }
                  if (double.tryParse(value) == null) {
                    return 'Please enter a valid number';
                  }
                  return null;
                },
              ),
              const SizedBox(height: 16),
              TextFormField(
                controller: _exWorksUnitCostController,
                decoration: const InputDecoration(
                  labelText: 'Ex-Works Unit Cost (USD)',
                  hintText: 'Enter ex-works cost in USD',
                  prefixIcon: Icon(Icons.attach_money),
                ),
                keyboardType: const TextInputType.numberWithOptions(decimal: true),
                validator: (value) {
                  if (value == null || value.trim().isEmpty) {
                    return 'Please enter an ex-works unit cost';
                  }
                  if (double.tryParse(value) == null) {
                    return 'Please enter a valid number';
                  }
                  return null;
                },
              ),
              const SizedBox(height: 16),
              TextFormField(
                controller: _localUnitCostController,
                decoration: const InputDecoration(
                  labelText: 'Local Unit Cost (SAR)',
                  hintText: 'Enter additional local cost',
                  prefixIcon: Icon(Icons.attach_money),
                ),
                keyboardType: const TextInputType.numberWithOptions(decimal: true),
                validator: (value) {
                  if (value == null || value.trim().isEmpty) {
                    return 'Please enter a local unit cost (or 0 if none)';
                  }
                  if (double.tryParse(value) == null) {
                    return 'Please enter a valid number';
                  }
                  return null;
                },
              ),
              const SizedBox(height: 16),
              SwitchListTile(
                title: const Text('Imported Item'),
                subtitle: const Text('Toggle if this item is imported'),
                value: _isImported,
                onChanged: (value) {
                  setState(() {
                    _isImported = value;
                  });
                },
              ),
              const SizedBox(height: 32),
              ElevatedButton(
                onPressed: _addEquipment,
                style: ElevatedButton.styleFrom(
                  padding: const EdgeInsets.symmetric(vertical: 16),
                ),
                child: const Text('Add Equipment', style: TextStyle(fontSize: 16)),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
