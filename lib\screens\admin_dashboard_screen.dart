import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../services/auth_service.dart';
import '../services/excel_service.dart';
import '../constants/app_constants.dart';
import '../widgets/multi_sheet_excel_import_dialog.dart';

import 'admin_materials_screen.dart';
import 'admin_users_screen.dart';

class AdminDashboardScreen extends StatefulWidget {
  const AdminDashboardScreen({super.key});

  @override
  State<AdminDashboardScreen> createState() => _AdminDashboardScreenState();
}

class _AdminDashboardScreenState extends State<AdminDashboardScreen> {
  final ExcelService _excelService = ExcelService();
  bool _isLoading = false;
  String? _message;

  void _showMessage(String message, {bool isError = false}) {
    setState(() {
      _message = message;
    });

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: isError ? Colors.red : Colors.green,
        duration: const Duration(seconds: 3),
      ),
    );

    Future.delayed(const Duration(seconds: 3), () {
      if (mounted) {
        setState(() {
          _message = null;
        });
      }
    });
  }

  Future<void> _importMaterials() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final count = await _excelService.importMaterialsCatalog();
      if (!mounted) return;

      _showMessage('Successfully imported $count materials');
    } catch (e) {
      if (!mounted) return;
      _showMessage('Error importing materials: $e', isError: true);
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _importEquipment() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final count = await _excelService.importEquipmentCatalog();
      if (!mounted) return;

      _showMessage('Successfully imported $count equipment items');
    } catch (e) {
      if (!mounted) return;
      _showMessage('Error importing equipment: $e', isError: true);
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _importSystems() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final count = await _excelService.importSystemsCatalog();
      if (!mounted) return;

      _showMessage('Successfully imported $count systems');
    } catch (e) {
      if (!mounted) return;
      _showMessage('Error importing systems: $e', isError: true);
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _exportMaterials() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final file = await _excelService.exportMaterialsCatalog();
      if (!mounted) return;

      _showMessage('Materials exported to ${file.path}');
    } catch (e) {
      if (!mounted) return;
      _showMessage('Error exporting materials: $e', isError: true);
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _exportEquipment() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final file = await _excelService.exportEquipmentCatalog();
      if (!mounted) return;

      _showMessage('Equipment exported to ${file.path}');
    } catch (e) {
      if (!mounted) return;
      _showMessage('Error exporting equipment: $e', isError: true);
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _exportSystems() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final file = await _excelService.exportSystemsCatalog();
      if (!mounted) return;

      _showMessage('Systems exported to ${file.path}');
    } catch (e) {
      if (!mounted) return;
      _showMessage('Error exporting systems: $e', isError: true);
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _showMultiSheetImportDialog() async {
    await showDialog(
      context: context,
      builder: (context) => const MultiSheetExcelImportDialog(),
    );

    _showMessage('Multi-sheet import completed successfully');
  }

  @override
  Widget build(BuildContext context) {
    final authService = Provider.of<AuthService>(context);

    if (!authService.isAdmin) {
      return Scaffold(
        appBar: AppBar(
          title: const Text('Admin Dashboard'),
        ),
        body: const Center(
          child: Text('You do not have permission to access this page.'),
        ),
      );
    }

    return Scaffold(
      appBar: AppBar(
        title: const Text('Admin Dashboard'),
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : SingleChildScrollView(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Admin info card
                  Card(
                    elevation: 2,
                    child: Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          const Text(
                            'Admin Dashboard',
                            style: TextStyle(
                              fontSize: 24,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          const SizedBox(height: 8),
                          Text(
                            'Logged in as: ${authService.currentUser?.username}',
                            style: const TextStyle(
                              fontSize: 16,
                            ),
                          ),
                          const SizedBox(height: 16),
                          const Text(
                            'Manage your application data from this dashboard. You can import and export data, and manage users, materials, equipment, and systems.',
                            style: TextStyle(
                              fontSize: 14,
                              color: Colors.grey,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                  const SizedBox(height: 24),

                  // Management sections
                  const Text(
                    'Data Management',
                    style: TextStyle(
                      fontSize: 20,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 16),

                  // Grid of management options
                  GridView.count(
                    crossAxisCount: 2,
                    shrinkWrap: true,
                    physics: const NeverScrollableScrollPhysics(),
                    mainAxisSpacing: 16,
                    crossAxisSpacing: 16,
                    children: [
                      _buildManagementCard(
                        title: 'Materials',
                        icon: Icons.category,
                        onTap: () {
                          Navigator.push(
                            context,
                            MaterialPageRoute(
                              builder: (context) => const AdminMaterialsScreen(),
                            ),
                          );
                        },
                      ),
                      _buildManagementCard(
                        title: 'Fire Alarm Systems',
                        icon: Icons.notifications_active,
                        onTap: () {
                          Navigator.push(
                            context,
                            MaterialPageRoute(
                              builder: (context) => _buildFireAlarmModuleScreen(),
                            ),
                          );
                        },
                      ),
                      _buildManagementCard(
                        title: 'Water Systems',
                        icon: Icons.water_drop,
                        onTap: () {
                          Navigator.push(
                            context,
                            MaterialPageRoute(
                              builder: (context) => _buildWaterSystemsModuleScreen(),
                            ),
                          );
                        },
                      ),
                      _buildManagementCard(
                        title: 'Foam Systems',
                        icon: Icons.bubble_chart,
                        onTap: () {
                          Navigator.push(
                            context,
                            MaterialPageRoute(
                              builder: (context) => _buildFoamSystemsModuleScreen(),
                            ),
                          );
                        },
                      ),
                      _buildManagementCard(
                        title: 'FM200/Novec Systems',
                        icon: Icons.air,
                        onTap: () {
                          Navigator.push(
                            context,
                            MaterialPageRoute(
                              builder: (context) => _buildCleanAgentModuleScreen(),
                            ),
                          );
                        },
                      ),
                      _buildManagementCard(
                        title: 'CO2 Systems',
                        icon: Icons.science,
                        onTap: () {
                          Navigator.push(
                            context,
                            MaterialPageRoute(
                              builder: (context) => _buildCO2SystemsModuleScreen(),
                            ),
                          );
                        },
                      ),
                      _buildManagementCard(
                        title: 'Users',
                        icon: Icons.people,
                        onTap: () {
                          Navigator.push(
                            context,
                            MaterialPageRoute(
                              builder: (context) => const AdminUsersScreen(),
                            ),
                          );
                        },
                      ),
                    ],
                  ),
                  const SizedBox(height: 24),

                  // Import/Export section
                  const Text(
                    'Import/Export',
                    style: TextStyle(
                      fontSize: 20,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 16),

                  // Import/Export cards
                  Row(
                    children: [
                      Expanded(
                        child: Card(
                          elevation: 2,
                          child: Padding(
                            padding: const EdgeInsets.all(16.0),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                const Text(
                                  'Import Data',
                                  style: TextStyle(
                                    fontSize: 18,
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                                const SizedBox(height: 16),
                                _buildActionButton(
                                  label: 'Import Materials',
                                  icon: Icons.upload,
                                  onPressed: _importMaterials,
                                ),
                                const SizedBox(height: 8),
                                _buildActionButton(
                                  label: 'Import Equipment',
                                  icon: Icons.upload,
                                  onPressed: _importEquipment,
                                ),
                                const SizedBox(height: 8),
                                _buildActionButton(
                                  label: 'Import Systems',
                                  icon: Icons.upload,
                                  onPressed: _importSystems,
                                ),
                                const SizedBox(height: 8),
                                _buildActionButton(
                                  label: 'Import Multiple Tables',
                                  icon: Icons.table_chart,
                                  onPressed: _showMultiSheetImportDialog,
                                ),
                              ],
                            ),
                          ),
                        ),
                      ),
                      const SizedBox(width: 16),
                      Expanded(
                        child: Card(
                          elevation: 2,
                          child: Padding(
                            padding: const EdgeInsets.all(16.0),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                const Text(
                                  'Export Data',
                                  style: TextStyle(
                                    fontSize: 18,
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                                const SizedBox(height: 16),
                                _buildActionButton(
                                  label: 'Export Materials',
                                  icon: Icons.download,
                                  onPressed: _exportMaterials,
                                ),
                                const SizedBox(height: 8),
                                _buildActionButton(
                                  label: 'Export Equipment',
                                  icon: Icons.download,
                                  onPressed: _exportEquipment,
                                ),
                                const SizedBox(height: 8),
                                _buildActionButton(
                                  label: 'Export Systems',
                                  icon: Icons.download,
                                  onPressed: _exportSystems,
                                ),
                              ],
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
    );
  }

  Widget _buildManagementCard({
    required String title,
    required IconData icon,
    required VoidCallback onTap,
  }) {
    return Card(
      elevation: 2,
      child: InkWell(
        onTap: onTap,
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                icon,
                size: 48,
                color: AppConstants.primaryColor,
              ),
              const SizedBox(height: 16),
              Text(
                title,
                style: const TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildActionButton({
    required String label,
    required IconData icon,
    required VoidCallback onPressed,
  }) {
    return ElevatedButton.icon(
      onPressed: onPressed,
      icon: Icon(icon),
      label: Text(label),
      style: ElevatedButton.styleFrom(
        minimumSize: const Size(double.infinity, 44),
      ),
    );
  }

  // Module screen builders
  Widget _buildFireAlarmModuleScreen() {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Fire Alarm Systems'),
        backgroundColor: AppConstants.primaryColor,
      ),
      body: DefaultTabController(
        length: 5,
        child: Column(
          children: [
            Container(
              color: Colors.white,
              child: const TabBar(
                isScrollable: true,
                labelColor: AppConstants.primaryColor,
                unselectedLabelColor: Colors.grey,
                indicatorColor: AppConstants.primaryColor,
                indicatorWeight: 3,
                labelStyle: TextStyle(
                  fontWeight: FontWeight.bold,
                  fontSize: 14,
                ),
                unselectedLabelStyle: TextStyle(
                  fontWeight: FontWeight.normal,
                  fontSize: 14,
                ),
                tabs: [
                  Tab(text: 'Notifier'),
                  Tab(text: 'Simplex'),
                  Tab(text: 'Farenhyt'),
                  Tab(text: 'Ex-Proof Devices'),
                  Tab(text: 'Cabling'),
                ],
              ),
            ),
            const Expanded(
              child: TabBarView(
                children: [
                  Center(child: Text('Fire Alarm Database\n(Under maintenance)', textAlign: TextAlign.center)),
                  Center(child: Text('Simplex Database\n(Under maintenance)', textAlign: TextAlign.center)),
                  Center(child: Text('Farenhyt Database\n(Under maintenance)', textAlign: TextAlign.center)),
                  Center(child: Text('Ex-Proof Database\n(Under maintenance)', textAlign: TextAlign.center)),
                  Center(child: Text('Cabling Database\n(Under maintenance)', textAlign: TextAlign.center)),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildWaterSystemsModuleScreen() {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Water Systems'),
        backgroundColor: AppConstants.primaryColor,
      ),
      body: DefaultTabController(
        length: 3,
        child: Column(
          children: [
            Container(
              color: Colors.white,
              child: const TabBar(
                isScrollable: true,
                labelColor: AppConstants.primaryColor,
                unselectedLabelColor: Colors.grey,
                indicatorColor: AppConstants.primaryColor,
                indicatorWeight: 3,
                labelStyle: TextStyle(
                  fontWeight: FontWeight.bold,
                  fontSize: 14,
                ),
                unselectedLabelStyle: TextStyle(
                  fontWeight: FontWeight.normal,
                  fontSize: 14,
                ),
                tabs: [
                  Tab(text: 'Pumps'),
                  Tab(text: 'Sprinklers'),
                  Tab(text: 'Accessories'),
                ],
              ),
            ),
            const Expanded(
              child: TabBarView(
                children: [
                  Center(child: Text('Pumps Database\n(Under maintenance)', textAlign: TextAlign.center)),
                  Center(child: Text('Sprinklers Database\n(Under maintenance)', textAlign: TextAlign.center)),
                  Center(child: Text('Accessories Database\n(Under maintenance)', textAlign: TextAlign.center)),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildFoamSystemsModuleScreen() {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Foam Systems'),
        backgroundColor: AppConstants.primaryColor,
      ),
      body: DefaultTabController(
        length: 3,
        child: Column(
          children: [
            Container(
              color: Colors.white,
              child: const TabBar(
                isScrollable: true,
                labelColor: AppConstants.primaryColor,
                unselectedLabelColor: Colors.grey,
                indicatorColor: AppConstants.primaryColor,
                indicatorWeight: 3,
                labelStyle: TextStyle(
                  fontWeight: FontWeight.bold,
                  fontSize: 14,
                ),
                unselectedLabelStyle: TextStyle(
                  fontWeight: FontWeight.normal,
                  fontSize: 14,
                ),
                tabs: [
                  Tab(text: 'Bladder Tanks'),
                  Tab(text: 'Proportioners'),
                  Tab(text: 'Foam Concentrate'),
                ],
              ),
            ),
            const Expanded(
              child: TabBarView(
                children: [
                  Center(child: Text('Bladder Tanks Database\n(Under maintenance)', textAlign: TextAlign.center)),
                  Center(child: Text('Proportioners Database\n(Under maintenance)', textAlign: TextAlign.center)),
                  Center(child: Text('Foam Concentrate Database\n(Under maintenance)', textAlign: TextAlign.center)),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildCleanAgentModuleScreen() {
    return Scaffold(
      appBar: AppBar(
        title: const Text('FM200/Novec Systems'),
        backgroundColor: AppConstants.primaryColor,
      ),
      body: DefaultTabController(
        length: 2,
        child: Column(
          children: [
            Container(
              color: Colors.white,
              child: const TabBar(
                isScrollable: true,
                labelColor: AppConstants.primaryColor,
                unselectedLabelColor: Colors.grey,
                indicatorColor: AppConstants.primaryColor,
                indicatorWeight: 3,
                labelStyle: TextStyle(
                  fontWeight: FontWeight.bold,
                  fontSize: 14,
                ),
                unselectedLabelStyle: TextStyle(
                  fontWeight: FontWeight.normal,
                  fontSize: 14,
                ),
                tabs: [
                  Tab(text: 'FM200'),
                  Tab(text: 'Novec 1230'),
                ],
              ),
            ),
            const Expanded(
              child: TabBarView(
                children: [
                  Center(child: Text('FM200 Database\n(Under maintenance)', textAlign: TextAlign.center)),
                  Center(child: Text('Novec 1230 Database\n(Under maintenance)', textAlign: TextAlign.center)),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildCO2SystemsModuleScreen() {
    return Scaffold(
      appBar: AppBar(
        title: const Text('CO2 Systems'),
        backgroundColor: AppConstants.primaryColor,
      ),
      body: const Center(child: Text('CO2 Systems Database\n(Under maintenance)', textAlign: TextAlign.center)),
    );
  }
}
