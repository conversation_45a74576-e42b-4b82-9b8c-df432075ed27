class BomItem {
  final String id;
  final String partNumber;
  final String description;
  final String manufacturer;
  final double quantity;
  final String unit;
  final double unitCost;
  final double totalCost;
  final String category;

  BomItem({
    required this.id,
    required this.partNumber,
    required this.description,
    required this.manufacturer,
    required this.quantity,
    required this.unit,
    required this.unitCost,
    required this.totalCost,
    required this.category,
  });

  @override
  String toString() {
    return 'BomItem(partNumber: $partNumber, description: $description, quantity: $quantity $unit, unitCost: \$${unitCost.toStringAsFixed(2)}, totalCost: \$${totalCost.toStringAsFixed(2)})';
  }
}
