import '../models/estimator_types.dart';
import '../data/estimator_config.dart';
import 'estimator_calculations.dart';

class BomGenerator {
  // Helper function to look up component details
  static Component? findComponent(String partNo) {
    try {
      return MasterComponents.components.firstWhere((c) => c.partNo == partNo);
    } catch (e) {
      print('Component with part number $partNo not found');
      return null;
    }
  }

  // Helper function to add item to BOM
  static void addBomItem(
    List<BomItem> bom,
    String partNo,
    int quantity,
    String category, {
    String? subcategory,
    String currency = 'USD',
  }) {
    if (partNo.isEmpty || quantity <= 0) return;
    
    final component = findComponent(partNo);
    if (component == null) return;
    
    bom.add(BomItem(
      partNo: partNo,
      description: component.description,
      quantity: quantity,
      unitCost: component.unitCost,
      totalCost: component.unitCost * quantity,
      manufacturer: component.manufacturer,
      category: category,
      subcategory: subcategory,
      currency: currency,
    ));
  }

  // Calculate pipe lengths based on room dimensions and nozzle quantity
  static Map<String, double> calculatePipeLengths(
    double roomLength,
    double roomWidth,
    double roomHeight,
    int nozzleQty,
  ) {
    // Use the user-provided formulas
    final nozzlePipeLength = nozzleQty * 3.4;
    final manifoldPipeLength = (roomHeight - 1.3) + (0.5 * nozzleQty) + roomLength;
    
    return {
      'nozzlePipeLength': nozzlePipeLength,
      'manifoldPipeLength': manifoldPipeLength,
    };
  }

  // Calculate cable lengths based on room dimensions and detector quantity
  static Map<String, double> calculateCableLengths(
    double roomLength,
    double roomWidth,
    double roomHeight,
    int detectorQty,
  ) {
    // Use the user-provided formulas
    final loopCableLength = ((roomHeight - 1.5) + roomWidth + roomLength) * 2 + 6 + 4 + 4 + 4;
    final powerCableLength = 5 + (detectorQty * 1.5) + 3 + 3 + 5;
    
    return {
      'loopCableLength': loopCableLength,
      'powerCableLength': powerCableLength,
    };
  }

  // Generate Bill of Materials
  static Map<String, dynamic> generateBOM(DesignResults design, EstimatorFormValues input) {
    final bom = <BomItem>[];
    final agentType = input.agentType == AgentType.novec1230 ? 'NOVEC1230' : 'FM200';
    final isMainSystem = input.systemType == SystemType.main;
    final isReserveSystem = input.systemType == SystemType.reserve;
    final isMainAndReserve = input.systemType == SystemType.mainAndReserve;
    final cylinderSize = design.cylinder.cylinderSizeLiters2ndIter;
    final cylinderQty = design.cylinder.numCylinders2ndIter;
    final roomArea = design.roomData.roomArea;
    final roomLength = design.roomData.roomLength;
    final roomWidth = design.roomData.roomWidth;
    final roomHeight = design.roomData.roomHeight;
    
    // Multiplier for main+reserve systems
    final systemMultiplier = isMainAndReserve ? 2 : 1;
    
    // SECTION 1: SUPPRESSION SYSTEM COMPONENTS
    
    // 1. Add cylinders
    final cylinderSpecs = CylinderSpecs.specs[agentType] ?? [];
    CylinderSpec? cylinderSpec;
    try {
      cylinderSpec = cylinderSpecs.where((c) => c.size == cylinderSize).first;
    } catch (e) {
      cylinderSpec = null;
    }
    
    if (cylinderSpec != null) {
      // Add cylinder
      addBomItem(bom, cylinderSpec.partNo, cylinderQty * systemMultiplier, 'Suppression System', subcategory: 'Cylinders');
      
      // Add labels
      final cylinderToLabel = {
        'NOVEC1230': {
          '4.5': '314207337', '8': '314207337', '16': '314207337', '32': '314207306', 
          '52': '314207337', '106': '314207306', '147': '314207306', '180': '314207306', '343': '314207306'
        },
        'FM200': {
          '4.5': '314.205.022', '8': '314.205.022', '16': '314.205.022', '32': '314.205.021',
          '52': '314.205.022', '106': '314.205.021', '147': '314.205.021', '180': '314.205.021', '343': '314.205.021'
        }
      };
      
      final labelPart = cylinderToLabel[agentType]?[cylinderSize.toString()];
      if (labelPart != null) {
        addBomItem(bom, labelPart, cylinderQty * systemMultiplier, 'Suppression System', subcategory: 'Cylinder Accessories');
      }
      
      // Add brackets - 2 per cylinder as requested
      final cylinderToBracket = {
        'NOVEC1230': {
          '4.5': '311.205.020', '8': '311.205.013', '16': '311.205.013', '32': '311.205.013',
          '52': '311.205.014', '106': '311.205.014', '147': '311.205.014', '180': '311.205.014', '343': '311.205.019'
        },
        'FM200': {
          '4.5': '311.205.020', '8': '311.205.013', '16': '311.205.013', '32': '311.205.013',
          '52': '311.205.014', '106': '311.205.014', '147': '311.205.014', '180': '311.205.014', '343': '311.205.019'
        }
      };
      
      final bracketPart = cylinderToBracket[agentType]?[cylinderSize.toString()];
      if (bracketPart != null) {
        addBomItem(bom, bracketPart, 2 * cylinderQty * systemMultiplier, 'Suppression System', subcategory: 'Cylinder Accessories');
      }
    }
    
    // 2. Add actuators
    // Electrical Actuator - 1 per bank (main or reserve)
    final eActuatorQty = cylinderQty < 9 ? 1 : (cylinderQty >= 9 && cylinderQty <= 15 ? 2 : 3);
    addBomItem(bom, '*********', eActuatorQty * systemMultiplier, 'Suppression System', subcategory: 'Actuators');
    
    // Local Manual Actuator - same as electrical
    addBomItem(bom, '304.209.002', eActuatorQty * systemMultiplier, 'Suppression System', subcategory: 'Actuators');
    
    // Pneumatic Actuator - (n-1) per bank where n is cylinder count
    if (cylinderQty > 1) {
      addBomItem(bom, '304.209.004', (cylinderQty - 1) * systemMultiplier, 'Suppression System', subcategory: 'Actuators');
    }
    
    // 3. Add discharge components
    if (cylinderQty > 1) {
      addBomItem(bom, '306.205.003', (cylinderQty - 1) * systemMultiplier, 'Suppression System', subcategory: 'Discharge Components');
    } else {
      addBomItem(bom, '306.205.003', 1, 'Suppression System', subcategory: 'Discharge Components');
    }
    
    // Add pressure switches
    addBomItem(bom, '304.205.006', cylinderQty * systemMultiplier, 'Suppression System', subcategory: 'Switches');
    addBomItem(bom, '437900', 1, 'Suppression System', subcategory: 'Switches');
    
    // Get the discharge components mapping
    final cylinderToDischargeComponents = {
      'NOVEC1230': {
        '4.5': ['306.207.002', '302.209.004'], '8': ['306.207.002', '302.209.004'],
        '16': ['306.207.002', '302.209.004'], '32': ['306.207.002', '302.209.004'],
        '52': ['306.207.003', '302.209.005'], '106': ['306.207.003', '302.209.005'],
        '147': ['306.207.003', '302.209.005'], '180': ['306.207.003', '302.209.005'],
        '343': ['306.207.003', '302.209.005']
      },
      'FM200': {
        '4.5': ['306.205.007', '302.205.001'], '8': ['306.205.007', '302.205.001'],
        '16': ['306.205.007', '302.205.001'], '32': ['306.205.007', '302.205.001'],
        '52': ['306.205.008', '302.205.002'], '106': ['306.205.008', '302.205.002'],
        '147': ['306.205.008', '302.205.002'], '180': ['306.205.008', '302.205.002'],
        '343': ['306.205.008', '302.205.002']
      }
    };
    
    final dischargeComponents = cylinderToDischargeComponents[agentType]?[cylinderSize.toString()];
    if (dischargeComponents != null) {
      // Add discharge hose - 1 per cylinder
      addBomItem(bom, dischargeComponents[0], cylinderQty * systemMultiplier, 'Suppression System', subcategory: 'Discharge Components');
      
      // Add check valve - 1 per cylinder
      addBomItem(bom, dischargeComponents[1], cylinderQty * systemMultiplier, 'Suppression System', subcategory: 'Discharge Components');
    }
    
    // 4. Add Liquid Level Indicator if applicable
    final cylinderToLLI = {
      'NOVEC1230': {
        '4.5': null, '8': null, '16': null, '32': null, '52': null,
        '106': '300.015.127', '147': '300.015.128', '180': '300.015.129', '343': '300.015.128'
      },
      'FM200': {
        '4.5': null, '8': null, '16': null, '32': null, '52': null,
        '106': '300.015.127', '147': '300.015.128', '180': '300.015.128', '343': '300.015.128'
      }
    };
    
    final lliPart = cylinderToLLI[agentType]?[cylinderSize.toString()];
    if (lliPart != null) {
      addBomItem(bom, lliPart, cylinderQty * systemMultiplier, 'Suppression System', subcategory: 'Cylinder Accessories');
    }
    
    // 5. Add agent
    final agentPartNo = AgentData.data[agentType]?['partNumber'] ?? '';
    if (agentPartNo.isNotEmpty) {
      addBomItem(bom, agentPartNo, design.cylinder.actualTotalKg.ceil() * systemMultiplier, 'Suppression System', subcategory: 'Agent');
    }

    // 6. Add nozzles
    final nozzleSize = design.discharge.nozzleSizeFinal;
    final nozzleQty = design.discharge.nozzleQtyFinal;

    final nozzleMapping = {
      'NOVEC1230': {
        15: '310.207.214', 20: '310.207.216', 25: '310.207.218',
        32: '310.207.220', 40: '310.207.222', 50: '310.207.224'
      },
      'FM200': {
        10: '310.205.216', 15: '310.205.218', 20: '310.205.220',
        25: '310.205.222', 32: '310.205.224', 40: '310.205.226', 50: '310.205.228'
      }
    };

    final nozzlePart = nozzleMapping[agentType]?[nozzleSize];
    if (nozzlePart != null) {
      addBomItem(bom, nozzlePart, nozzleQty * systemMultiplier, 'Suppression System', subcategory: 'Nozzles');
    }

    // 7. Add manifold assembly
    final manifoldSize = design.discharge.manifoldAssemblySize;
    final manifoldPart = 'MANIFOLD_${agentType[0]}$manifoldSize';
    addBomItem(bom, manifoldPart, 1 * systemMultiplier, 'Suppression System', subcategory: 'Manifold');

    // 8. Add caution plates
    final doorCautionPart = agentType == 'NOVEC1230' ? '314.207.001' : '314.205.002';
    final manualCautionPart = agentType == 'NOVEC1230' ? '314.207.003' : '314.205.003';
    addBomItem(bom, doorCautionPart, 1, 'Suppression System', subcategory: 'Signage');
    addBomItem(bom, manualCautionPart, 1, 'Suppression System', subcategory: 'Signage');

    // SECTION 2: ALARM & DETECTION SYSTEM
    final detectorQty = EstimatorCalculations.calculateDetectors(roomArea);

    // Control panel
    addBomItem(bom, 'RP-2002E', 1, 'Alarm & Detection', subcategory: 'Control Panel');

    // Detectors
    addBomItem(bom, '2W-B', detectorQty, 'Alarm & Detection', subcategory: 'Detectors');
    addBomItem(bom, '5151-CH', detectorQty, 'Alarm & Detection', subcategory: 'Detectors');

    // Notification devices
    addBomItem(bom, 'P2GRKLED', 2, 'Alarm & Detection', subcategory: 'Notification');
    addBomItem(bom, 'SSM24-6', 1, 'Alarm & Detection', subcategory: 'Notification');

    // Manual stations
    addBomItem(bom, 'NBG-12LR', 1, 'Alarm & Detection', subcategory: 'Manual Stations');
    addBomItem(bom, '2080-9057', 1, 'Alarm & Detection', subcategory: 'Manual Stations');

    // Selector switch for main+reserve
    if (isMainAndReserve) {
      addBomItem(bom, '76496', 1, 'Alarm & Detection', subcategory: 'Control');
    }

    // Disconnect switch
    addBomItem(bom, '2080-9070', 1, 'Alarm & Detection', subcategory: 'Control');

    // SECTION 3: INSTALLATION ITEMS (if supply & install)
    if (input.installationType == InstallationType.supplyAndInstall) {
      // Calculate pipe lengths
      final pipeLengths = calculatePipeLengths(roomLength, roomWidth, roomHeight, nozzleQty);
      final cableLengths = calculateCableLengths(roomLength, roomWidth, roomHeight, detectorQty);

      // Add installation items with calculated quantities
      // This would include pipes, cables, fittings, etc.
      // For now, adding basic items
      addBomItem(bom, 'PIPE_INSTALL', pipeLengths['manifoldPipeLength']!.ceil(), 'Installation Items', subcategory: 'Piping');
      addBomItem(bom, 'CABLE_INSTALL', cableLengths['loopCableLength']!.ceil(), 'Installation Items', subcategory: 'Cabling');
    }

    return {
      'bom': bom,
      'summary': _calculateSummary(bom, design, input),
    };
  }

  // Calculate summary costs with exact same logic as React app
  static BomSummary _calculateSummary(List<BomItem> bom, DesignResults design, EstimatorFormValues input) {
    double suppressionCost = 0;
    double alarmCost = 0;
    double installationItemsCost = 0;
    double suppressionInstallCost = 0;
    double alarmInstallCost = 0;
    double installationServicesInstallCost = 0;

    // Calculate costs by category
    for (final item in bom) {
      switch (item.category) {
        case 'Suppression System':
          suppressionCost += item.totalCost;
          break;
        case 'Alarm & Detection':
          alarmCost += item.totalCost;
          break;
        case 'Installation Items':
          installationItemsCost += item.totalCost;
          break;
        case 'Suppression Installation':
          suppressionInstallCost += item.totalCost;
          break;
        case 'Alarm Installation':
          alarmInstallCost += item.totalCost;
          break;
        case 'Installation Services':
          installationServicesInstallCost += item.totalCost;
          break;
      }
    }

    // Apply shipping factor
    const shippingFactor = AppConfig.shippingExFactor;
    suppressionCost *= shippingFactor;
    alarmCost *= shippingFactor;
    installationItemsCost *= shippingFactor;

    // Calculate totals
    final totalSupplyCostUSD = suppressionCost + alarmCost + installationItemsCost;
    final totalSupplyCostSAR = totalSupplyCostUSD * AppConfig.dollarRateSarUsd;
    final totalInstallCostSAR = suppressionInstallCost + alarmInstallCost + installationServicesInstallCost;
    final grandTotalSAR = totalSupplyCostSAR + totalInstallCostSAR;

    // Apply margin
    const marginFactor = AppConfig.defaultMarginFactor;
    final marginAmountSAR = grandTotalSAR * (marginFactor - 1);

    return BomSummary(
      suppressionCost: suppressionCost,
      alarmCost: alarmCost,
      installationItemsCost: installationItemsCost,
      suppressionInstallCost: suppressionInstallCost,
      alarmInstallCost: alarmInstallCost,
      installationServicesInstallCost: installationServicesInstallCost,
      totalSupplyCostUSD: totalSupplyCostUSD,
      totalSupplyCostSAR: totalSupplyCostSAR,
      totalInstallCostSAR: totalInstallCostSAR,
      grandTotalSAR: grandTotalSAR + marginAmountSAR,
      marginFactor: marginFactor,
      marginAmountSAR: marginAmountSAR,
    );
  }
}
