import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:file_picker/file_picker.dart';
import '../services/enhanced_excel_database_service.dart';

class EnhancedExcelGridWidget extends StatefulWidget {
  final String tableName;
  final String title;
  final Color themeColor;
  final VoidCallback? onDataChanged;

  const EnhancedExcelGridWidget({
    super.key,
    required this.tableName,
    required this.title,
    this.themeColor = Colors.blue,
    this.onDataChanged,
  });

  @override
  State<EnhancedExcelGridWidget> createState() => _EnhancedExcelGridWidgetState();
}

class _EnhancedExcelGridWidgetState extends State<EnhancedExcelGridWidget> {
  final EnhancedExcelDatabaseService _databaseService = EnhancedExcelDatabaseService();
  final ScrollController _horizontalController = ScrollController();
  final ScrollController _verticalController = ScrollController();
  final FocusNode _gridFocusNode = FocusNode();
  final TextEditingController _searchController = TextEditingController();
  final Map<String, TextEditingController> _cellControllers = {};

  // Data
  List<Map<String, dynamic>> _data = [];
  List<Map<String, dynamic>> _filteredData = [];
  Map<String, dynamic>? _schema;
  List<Map<String, dynamic>> _columns = [];

  // UI State
  bool _isLoading = true;
  String? _error;
  bool _isEditing = false;
  String? _editingCellKey;
  bool _showFilters = false;
  String _searchTerm = '';
  final Map<String, String> _columnFilters = {};
  final Map<String, bool> _sortOrder = {}; // true for ascending, false for descending

  // Selection State
  final Set<String> _selectedCells = {}; // Format: "rowIndex:columnName"
  int? _selectedRowIndex;
  String? _selectedColumnName;
  bool _isSelecting = false;
  int? _selectionStartRow;
  String? _selectionStartColumn;

  // Grid Settings
  double _defaultRowHeight = 35.0;
  double _defaultColumnWidth = 120.0;
  final Map<String, double> _columnWidths = {};
  int _frozenRows = 0;
  int _frozenColumns = 1;

  // Cell Formatting
  final Map<String, CellFormat> _cellFormats = {};

  // Clipboard
  List<List<String>>? _clipboardData;

  @override
  void initState() {
    super.initState();
    _loadData();
    _setupKeyboardListeners();
  }

  @override
  void dispose() {
    _horizontalController.dispose();
    _verticalController.dispose();
    _gridFocusNode.dispose();
    _searchController.dispose();
    for (var controller in _cellControllers.values) {
      controller.dispose();
    }
    super.dispose();
  }

  void _setupKeyboardListeners() {
    _gridFocusNode.addListener(() {
      if (_gridFocusNode.hasFocus) {
        // Grid has focus, ready for keyboard navigation
      }
    });
  }

  Future<void> _loadData() async {
    setState(() {
      _isLoading = true;
      _error = null;
    });

    try {
      // Load schema
      _schema = await _databaseService.getTableSchema(widget.tableName);
      if (_schema != null) {
        _columns = List<Map<String, dynamic>>.from(_schema!['columns'] ?? []);
        final settings = _schema!['settings'] as Map<String, dynamic>? ?? {};
        _frozenRows = settings['frozen_rows'] ?? 0;
        _frozenColumns = settings['frozen_columns'] ?? 1;
        _defaultRowHeight = (settings['default_row_height'] ?? 35).toDouble();
        _defaultColumnWidth = (settings['default_column_width'] ?? 120).toDouble();
      }

      // Load data
      _data = await _databaseService.getTableData(widget.tableName);
      _applyFilters();

      setState(() {
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _error = e.toString();
        _isLoading = false;
      });
    }
  }

  void _applyFilters() {
    _filteredData = _data.where((row) {
      // Apply search filter
      if (_searchTerm.isNotEmpty) {
        bool matchesSearch = false;
        for (final column in _columns) {
          final value = row[column['name']]?.toString().toLowerCase() ?? '';
          if (value.contains(_searchTerm.toLowerCase())) {
            matchesSearch = true;
            break;
          }
        }
        if (!matchesSearch) return false;
      }

      // Apply column filters
      for (final entry in _columnFilters.entries) {
        if (entry.value.isNotEmpty) {
          final value = row[entry.key]?.toString().toLowerCase() ?? '';
          if (!value.contains(entry.value.toLowerCase())) {
            return false;
          }
        }
      }

      return true;
    }).toList();
  }

  void _sortByColumn(String columnName) {
    final ascending = _sortOrder[columnName] ?? true;
    
    _filteredData.sort((a, b) {
      final aValue = a[columnName];
      final bValue = b[columnName];
      
      if (aValue == null && bValue == null) return 0;
      if (aValue == null) return ascending ? -1 : 1;
      if (bValue == null) return ascending ? 1 : -1;
      
      final comparison = aValue.toString().compareTo(bValue.toString());
      return ascending ? comparison : -comparison;
    });
    
    setState(() {
      _sortOrder[columnName] = !ascending;
    });
  }

  Future<void> _addRow() async {
    try {
      final newData = <String, dynamic>{};
      for (final column in _columns) {
        newData[column['name']] = '';
      }
      
      await _databaseService.insertRow(widget.tableName, newData);
      await _loadData();
      widget.onDataChanged?.call();
    } catch (e) {
      _showErrorDialog('Error adding row: $e');
    }
  }

  Future<void> _deleteSelectedRows() async {
    if (_selectedRowIndex == null) return;
    
    try {
      final row = _filteredData[_selectedRowIndex!];
      await _databaseService.deleteRow(widget.tableName, row['id']);
      await _loadData();
      widget.onDataChanged?.call();
    } catch (e) {
      _showErrorDialog('Error deleting row: $e');
    }
  }

  Future<void> _updateCell(int rowIndex, String columnName, dynamic value) async {
    try {
      final row = _filteredData[rowIndex];
      final updatedData = Map<String, dynamic>.from(row);
      updatedData.remove('id');
      updatedData.remove('created_at');
      updatedData.remove('updated_at');
      updatedData.remove('sync_id');
      updatedData[columnName] = value;
      
      await _databaseService.updateRow(widget.tableName, row['id'], updatedData);
      await _loadData();
      widget.onDataChanged?.call();
    } catch (e) {
      _showErrorDialog('Error updating cell: $e');
    }
  }

  void _selectCell(int rowIndex, String columnName) {
    setState(() {
      _selectedRowIndex = rowIndex;
      _selectedColumnName = columnName;
      _selectedCells.clear();
      _selectedCells.add('$rowIndex:$columnName');
    });
  }

  void _startCellSelection(int rowIndex, String columnName) {
    setState(() {
      _isSelecting = true;
      _selectionStartRow = rowIndex;
      _selectionStartColumn = columnName;
      _selectedCells.clear();
      _selectedCells.add('$rowIndex:$columnName');
    });
  }

  void _updateCellSelection(int rowIndex, String columnName) {
    if (!_isSelecting || _selectionStartRow == null || _selectionStartColumn == null) return;
    
    setState(() {
      _selectedCells.clear();
      
      final startRowIndex = _selectionStartRow!;
      final endRowIndex = rowIndex;
      final startColIndex = _columns.indexWhere((col) => col['name'] == _selectionStartColumn);
      final endColIndex = _columns.indexWhere((col) => col['name'] == columnName);
      
      final minRow = startRowIndex < endRowIndex ? startRowIndex : endRowIndex;
      final maxRow = startRowIndex > endRowIndex ? startRowIndex : endRowIndex;
      final minCol = startColIndex < endColIndex ? startColIndex : endColIndex;
      final maxCol = startColIndex > endColIndex ? startColIndex : endColIndex;
      
      for (int r = minRow; r <= maxRow; r++) {
        for (int c = minCol; c <= maxCol; c++) {
          if (c < _columns.length) {
            _selectedCells.add('$r:${_columns[c]['name']}');
          }
        }
      }
    });
  }

  void _endCellSelection() {
    setState(() {
      _isSelecting = false;
    });
  }

  void _copySelectedCells() {
    if (_selectedCells.isEmpty) return;
    
    final rows = <int>{};
    final columns = <String>{};
    
    for (final cellKey in _selectedCells) {
      final parts = cellKey.split(':');
      rows.add(int.parse(parts[0]));
      columns.add(parts[1]);
    }
    
    final sortedRows = rows.toList()..sort();
    final sortedColumns = columns.toList();
    
    _clipboardData = [];
    
    for (final rowIndex in sortedRows) {
      final rowData = <String>[];
      for (final columnName in sortedColumns) {
        if (_selectedCells.contains('$rowIndex:$columnName')) {
          final value = _filteredData[rowIndex][columnName]?.toString() ?? '';
          rowData.add(value);
        } else {
          rowData.add('');
        }
      }
      _clipboardData!.add(rowData);
    }
    
    // Copy to system clipboard
    final clipboardText = _clipboardData!.map((row) => row.join('\t')).join('\n');
    Clipboard.setData(ClipboardData(text: clipboardText));
    
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Copied to clipboard')),
    );
  }

  Future<void> _pasteFromClipboard() async {
    if (_selectedRowIndex == null || _selectedColumnName == null) return;
    
    try {
      final clipboardData = await Clipboard.getData('text/plain');
      if (clipboardData?.text == null) return;
      
      final lines = clipboardData!.text!.split('\n');
      final startRowIndex = _selectedRowIndex!;
      final startColIndex = _columns.indexWhere((col) => col['name'] == _selectedColumnName);
      
      for (int i = 0; i < lines.length; i++) {
        final rowIndex = startRowIndex + i;
        if (rowIndex >= _filteredData.length) break;
        
        final cells = lines[i].split('\t');
        for (int j = 0; j < cells.length; j++) {
          final colIndex = startColIndex + j;
          if (colIndex >= _columns.length) break;
          
          final columnName = _columns[colIndex]['name'];
          await _updateCell(rowIndex, columnName, cells[j]);
        }
      }
      
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Pasted from clipboard')),
      );
    } catch (e) {
      _showErrorDialog('Error pasting: $e');
    }
  }

  Future<void> _importFromExcel() async {
    try {
      final result = await FilePicker.platform.pickFiles(
        type: FileType.custom,
        allowedExtensions: ['xlsx', 'xls', 'csv'],
      );
      
      if (result != null && result.files.single.path != null) {
        final importResult = await _databaseService.importFromExcel(
          result.files.single.path!,
          widget.tableName,
        );
        
        await _loadData();
        widget.onDataChanged?.call();
        
        _showImportResultDialog(importResult);
      }
    } catch (e) {
      _showErrorDialog('Error importing file: $e');
    }
  }

  Future<void> _exportToExcel() async {
    try {
      final fileName = '${widget.title}_${DateTime.now().millisecondsSinceEpoch}';
      final filePath = await _databaseService.exportToExcel(widget.tableName, fileName);
      
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Exported to: $filePath')),
      );
    } catch (e) {
      _showErrorDialog('Error exporting file: $e');
    }
  }

  void _showImportResultDialog(Map<String, dynamic> result) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Import Results'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('Total rows: ${result['total_rows']}'),
            Text('Successful: ${result['successful_rows']}'),
            Text('Errors: ${result['errors'].length}'),
            if (result['errors'].isNotEmpty) ...[
              const SizedBox(height: 16),
              const Text('Errors:', style: TextStyle(fontWeight: FontWeight.bold)),
              ...result['errors'].take(5).map<Widget>((error) => Text('• $error')),
              if (result['errors'].length > 5)
                Text('... and ${result['errors'].length - 5} more'),
            ],
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('OK'),
          ),
        ],
      ),
    );
  }

  void _showErrorDialog(String message) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Error'),
        content: Text(message),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('OK'),
          ),
        ],
      ),
    );
  }

  void _showColumnManagementDialog() {
    showDialog(
      context: context,
      builder: (context) => ColumnManagementDialog(
        columns: _columns,
        onColumnsChanged: (newColumns) async {
          final newSchema = Map<String, dynamic>.from(_schema!);
          newSchema['columns'] = newColumns;
          await _databaseService.updateTableSchema(widget.tableName, newSchema);
          await _loadData();
          widget.onDataChanged?.call();
        },
      ),
    );
  }

  Widget _buildToolbar() {
    return Container(
      padding: const EdgeInsets.all(8.0),
      decoration: BoxDecoration(
        color: widget.themeColor.withOpacity(0.1),
        border: Border(bottom: BorderSide(color: Colors.grey.shade300)),
      ),
      child: Wrap(
        spacing: 8.0,
        runSpacing: 8.0,
        children: [
          // File operations
          ElevatedButton.icon(
            onPressed: _importFromExcel,
            icon: const Icon(Icons.file_upload),
            label: const Text('Import'),
            style: ElevatedButton.styleFrom(backgroundColor: widget.themeColor),
          ),
          ElevatedButton.icon(
            onPressed: _exportToExcel,
            icon: const Icon(Icons.file_download),
            label: const Text('Export'),
            style: ElevatedButton.styleFrom(backgroundColor: widget.themeColor),
          ),
          
          const VerticalDivider(),
          
          // Row operations
          ElevatedButton.icon(
            onPressed: _addRow,
            icon: const Icon(Icons.add),
            label: const Text('Add Row'),
          ),
          ElevatedButton.icon(
            onPressed: _selectedRowIndex != null ? _deleteSelectedRows : null,
            icon: const Icon(Icons.delete),
            label: const Text('Delete'),
          ),
          
          const VerticalDivider(),
          
          // Edit operations
          ElevatedButton.icon(
            onPressed: _selectedCells.isNotEmpty ? _copySelectedCells : null,
            icon: const Icon(Icons.copy),
            label: const Text('Copy'),
          ),
          ElevatedButton.icon(
            onPressed: _selectedRowIndex != null ? _pasteFromClipboard : null,
            icon: const Icon(Icons.paste),
            label: const Text('Paste'),
          ),
          
          const VerticalDivider(),
          
          // Column management
          ElevatedButton.icon(
            onPressed: _showColumnManagementDialog,
            icon: const Icon(Icons.view_column),
            label: const Text('Columns'),
          ),
          
          // Search
          SizedBox(
            width: 200,
            child: TextField(
              controller: _searchController,
              decoration: InputDecoration(
                hintText: 'Search...',
                prefixIcon: const Icon(Icons.search),
                suffixIcon: _searchTerm.isNotEmpty
                    ? IconButton(
                        onPressed: () {
                          _searchController.clear();
                          setState(() {
                            _searchTerm = '';
                            _applyFilters();
                          });
                        },
                        icon: const Icon(Icons.clear),
                      )
                    : null,
                border: const OutlineInputBorder(),
                contentPadding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
              ),
              onChanged: (value) {
                setState(() {
                  _searchTerm = value;
                  _applyFilters();
                });
              },
            ),
          ),
          
          // Filter toggle
          IconButton(
            onPressed: () {
              setState(() {
                _showFilters = !_showFilters;
              });
            },
            icon: Icon(
              _showFilters ? Icons.filter_list_off : Icons.filter_list,
              color: _showFilters ? widget.themeColor : null,
            ),
            tooltip: 'Toggle Filters',
          ),
        ],
      ),
    );
  }

  Widget _buildFilterRow() {
    if (!_showFilters) return const SizedBox.shrink();
    
    return Container(
      padding: const EdgeInsets.all(8.0),
      decoration: BoxDecoration(
        color: Colors.grey.shade50,
        border: Border(bottom: BorderSide(color: Colors.grey.shade300)),
      ),
      child: Row(
        children: [
          const SizedBox(width: 50), // Row number column
          ..._columns.map((column) {
            final columnName = column['name'];
            return Container(
              width: _columnWidths[columnName] ?? _defaultColumnWidth,
              padding: const EdgeInsets.symmetric(horizontal: 4.0),
              child: TextField(
                decoration: InputDecoration(
                  hintText: 'Filter ${column['label'] ?? columnName}',
                  border: const OutlineInputBorder(),
                  contentPadding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                ),
                onChanged: (value) {
                  setState(() {
                    _columnFilters[columnName] = value;
                    _applyFilters();
                  });
                },
              ),
            );
          }),
        ],
      ),
    );
  }

  Widget _buildHeaderRow() {
    return Container(
      height: _defaultRowHeight,
      decoration: BoxDecoration(
        color: widget.themeColor.withOpacity(0.1),
        border: Border(bottom: BorderSide(color: Colors.grey.shade300)),
      ),
      child: Row(
        children: [
          // Row number header
          Container(
            width: 50,
            alignment: Alignment.center,
            decoration: BoxDecoration(
              border: Border(right: BorderSide(color: Colors.grey.shade300)),
            ),
            child: const Text('#', style: TextStyle(fontWeight: FontWeight.bold)),
          ),
          
          // Column headers
          ..._columns.asMap().entries.map((entry) {
            final index = entry.key;
            final column = entry.value;
            final columnName = column['name'];
            final label = column['label'] ?? columnName;
            
            return GestureDetector(
              onTap: () => _sortByColumn(columnName),
              child: Container(
                width: _columnWidths[columnName] ?? _defaultColumnWidth,
                alignment: Alignment.centerLeft,
                padding: const EdgeInsets.symmetric(horizontal: 8.0),
                decoration: BoxDecoration(
                  border: Border(right: BorderSide(color: Colors.grey.shade300)),
                ),
                child: Row(
                  children: [
                    Expanded(
                      child: Text(
                        label,
                        style: const TextStyle(fontWeight: FontWeight.bold),
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                    if (_sortOrder.containsKey(columnName))
                      Icon(
                        _sortOrder[columnName]! ? Icons.arrow_upward : Icons.arrow_downward,
                        size: 16,
                      ),
                  ],
                ),
              ),
            );
          }),
        ],
      ),
    );
  }

  Widget _buildDataRow(int index) {
    final row = _filteredData[index];
    final isSelected = _selectedRowIndex == index;
    
    return Container(
      height: _defaultRowHeight,
      decoration: BoxDecoration(
        color: isSelected ? widget.themeColor.withOpacity(0.1) : null,
        border: Border(bottom: BorderSide(color: Colors.grey.shade200)),
      ),
      child: Row(
        children: [
          // Row number
          GestureDetector(
            onTap: () => _selectCell(index, _columns.first['name']),
            child: Container(
              width: 50,
              alignment: Alignment.center,
              decoration: BoxDecoration(
                border: Border(right: BorderSide(color: Colors.grey.shade300)),
                color: isSelected ? widget.themeColor.withOpacity(0.2) : null,
              ),
              child: Text('${index + 1}'),
            ),
          ),
          
          // Data cells
          ..._columns.map((column) {
            final columnName = column['name'];
            final value = row[columnName];
            final cellKey = '$index:$columnName';
            final isCellSelected = _selectedCells.contains(cellKey);
            
            return GestureDetector(
              onTap: () => _selectCell(index, columnName),
              onPanStart: (_) => _startCellSelection(index, columnName),
              onPanUpdate: (details) {
                // Calculate which cell we're over based on position
                // This is a simplified version - you might want more precise calculation
              },
              onPanEnd: (_) => _endCellSelection(),
              onDoubleTap: () => _startEditingCell(index, columnName),
              child: Container(
                width: _columnWidths[columnName] ?? _defaultColumnWidth,
                alignment: Alignment.centerLeft,
                padding: const EdgeInsets.symmetric(horizontal: 8.0),
                decoration: BoxDecoration(
                  border: Border(right: BorderSide(color: Colors.grey.shade300)),
                  color: isCellSelected ? widget.themeColor.withOpacity(0.3) : null,
                ),
                child: _editingCellKey == cellKey
                    ? _buildCellEditor(index, columnName, value)
                    : Text(
                        value?.toString() ?? '',
                        overflow: TextOverflow.ellipsis,
                      ),
              ),
            );
          }),
        ],
      ),
    );
  }

  Widget _buildCellEditor(int rowIndex, String columnName, dynamic value) {
    final controller = _cellControllers['$rowIndex:$columnName'] ??= 
        TextEditingController(text: value?.toString() ?? '');
    
    return TextField(
      controller: controller,
      autofocus: true,
      decoration: const InputDecoration(
        border: InputBorder.none,
        contentPadding: EdgeInsets.zero,
      ),
      onSubmitted: (newValue) {
        _updateCell(rowIndex, columnName, newValue);
        _stopEditingCell();
      },
      onEditingComplete: () {
        _updateCell(rowIndex, columnName, controller.text);
        _stopEditingCell();
      },
    );
  }

  void _startEditingCell(int rowIndex, String columnName) {
    setState(() {
      _editingCellKey = '$rowIndex:$columnName';
      _isEditing = true;
    });
  }

  void _stopEditingCell() {
    setState(() {
      _editingCellKey = null;
      _isEditing = false;
    });
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return const Center(child: CircularProgressIndicator());
    }
    
    if (_error != null) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.error, size: 64, color: Colors.red.shade300),
            const SizedBox(height: 16),
            Text('Error: $_error'),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: _loadData,
              child: const Text('Retry'),
            ),
          ],
        ),
      );
    }
    
    return Focus(
      focusNode: _gridFocusNode,
      onKey: (node, event) {
        if (event is RawKeyDownEvent) {
          // Handle keyboard shortcuts
          if (event.isControlPressed) {
            if (event.logicalKey == LogicalKeyboardKey.keyC) {
              _copySelectedCells();
              return KeyEventResult.handled;
            } else if (event.logicalKey == LogicalKeyboardKey.keyV) {
              _pasteFromClipboard();
              return KeyEventResult.handled;
            }
          }
          
          // Handle arrow navigation
          if (_selectedRowIndex != null && _selectedColumnName != null) {
            int newRowIndex = _selectedRowIndex!;
            int newColIndex = _columns.indexWhere((col) => col['name'] == _selectedColumnName);
            
            if (event.logicalKey == LogicalKeyboardKey.arrowUp && newRowIndex > 0) {
              newRowIndex--;
            } else if (event.logicalKey == LogicalKeyboardKey.arrowDown && newRowIndex < _filteredData.length - 1) {
              newRowIndex++;
            } else if (event.logicalKey == LogicalKeyboardKey.arrowLeft && newColIndex > 0) {
              newColIndex--;
            } else if (event.logicalKey == LogicalKeyboardKey.arrowRight && newColIndex < _columns.length - 1) {
              newColIndex++;
            } else if (event.logicalKey == LogicalKeyboardKey.enter || event.logicalKey == LogicalKeyboardKey.f2) {
              _startEditingCell(_selectedRowIndex!, _selectedColumnName!);
              return KeyEventResult.handled;
            }
            
            if (newRowIndex != _selectedRowIndex || newColIndex != _columns.indexWhere((col) => col['name'] == _selectedColumnName)) {
              _selectCell(newRowIndex, _columns[newColIndex]['name']);
              return KeyEventResult.handled;
            }
          }
        }
        return KeyEventResult.ignored;
      },
      child: Column(
        children: [
          _buildToolbar(),
          _buildFilterRow(),
          Expanded(
            child: Scrollbar(
              controller: _horizontalController,
              scrollbarOrientation: ScrollbarOrientation.bottom,
              child: Scrollbar(
                controller: _verticalController,
                child: SingleChildScrollView(
                  controller: _horizontalController,
                  scrollDirection: Axis.horizontal,
                  child: SizedBox(
                    width: 50 + _columns.fold<double>(0, (sum, col) => 
                        sum + (_columnWidths[col['name']] ?? _defaultColumnWidth)),
                    child: Column(
                      children: [
                        _buildHeaderRow(),
                        Expanded(
                          child: ListView.builder(
                            controller: _verticalController,
                            itemCount: _filteredData.length,
                            itemBuilder: (context, index) => _buildDataRow(index),
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}

class CellFormat {
  final bool bold;
  final bool italic;
  final Color? backgroundColor;
  final Color? textColor;
  final TextAlign alignment;
  
  const CellFormat({
    this.bold = false,
    this.italic = false,
    this.backgroundColor,
    this.textColor,
    this.alignment = TextAlign.left,
  });
}

class ColumnManagementDialog extends StatefulWidget {
  final List<Map<String, dynamic>> columns;
  final Function(List<Map<String, dynamic>>) onColumnsChanged;
  
  const ColumnManagementDialog({
    super.key,
    required this.columns,
    required this.onColumnsChanged,
  });
  
  @override
  State<ColumnManagementDialog> createState() => _ColumnManagementDialogState();
}

class _ColumnManagementDialogState extends State<ColumnManagementDialog> {
  late List<Map<String, dynamic>> _columns;
  
  @override
  void initState() {
    super.initState();
    _columns = List<Map<String, dynamic>>.from(widget.columns);
  }
  
  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: const Text('Manage Columns'),
      content: SizedBox(
        width: 500,
        height: 400,
        child: Column(
          children: [
            Row(
              children: [
                ElevatedButton.icon(
                  onPressed: _addColumn,
                  icon: const Icon(Icons.add),
                  label: const Text('Add Column'),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Expanded(
              child: ReorderableListView.builder(
                itemCount: _columns.length,
                onReorder: (oldIndex, newIndex) {
                  setState(() {
                    if (newIndex > oldIndex) newIndex--;
                    final item = _columns.removeAt(oldIndex);
                    _columns.insert(newIndex, item);
                  });
                },
                itemBuilder: (context, index) {
                  final column = _columns[index];
                  return ListTile(
                    key: ValueKey(column['name']),
                    title: Text(column['label'] ?? column['name']),
                    subtitle: Text('Type: ${column['type']}'),
                    trailing: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        IconButton(
                          onPressed: () => _editColumn(index),
                          icon: const Icon(Icons.edit),
                        ),
                        IconButton(
                          onPressed: () => _deleteColumn(index),
                          icon: const Icon(Icons.delete),
                        ),
                      ],
                    ),
                  );
                },
              ),
            ),
          ],
        ),
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: const Text('Cancel'),
        ),
        ElevatedButton(
          onPressed: () {
            widget.onColumnsChanged(_columns);
            Navigator.of(context).pop();
          },
          child: const Text('Save'),
        ),
      ],
    );
  }
  
  void _addColumn() {
    setState(() {
      _columns.add({
        'name': 'new_column_${_columns.length}',
        'type': 'text',
        'label': 'New Column',
        'required': false,
      });
    });
  }
  
  void _editColumn(int index) {
    // Show column edit dialog
    // Implementation would include form for editing column properties
  }
  
  void _deleteColumn(int index) {
    setState(() {
      _columns.removeAt(index);
    });
  }
}