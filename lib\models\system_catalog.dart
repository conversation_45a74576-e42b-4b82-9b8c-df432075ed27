import 'package:uuid/uuid.dart';

class SystemCatalogItem {
  final String id;
  String name;
  String category;
  String description;
  bool isActive;

  SystemCatalogItem({
    String? id,
    required this.name,
    required this.category,
    this.description = '',
    this.isActive = true,
  }) : id = id ?? const Uuid().v4();

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'name': name,
      'category': category,
      'description': description,
      'isActive': isActive ? 1 : 0,
    };
  }

  factory SystemCatalogItem.fromMap(Map<String, dynamic> map) {
    return SystemCatalogItem(
      id: map['id'],
      name: map['name'],
      category: map['category'],
      description: map['description'] ?? '',
      isActive: map['isActive'] == 1,
    );
  }
}
