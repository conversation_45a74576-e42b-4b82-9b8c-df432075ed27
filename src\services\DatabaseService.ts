// Mock database service for initial testing
import { v4 as uuidv4 } from 'uuid';
import {
  SystemItem,
  Project,
  Section,
  DynamicTable,
  TableColumn,
  TableRow,
  FlexibleTable,
  FlexibleColumn,
  FlexibleRow,
  SidebarSection,
  SystemType,
  ColumnDataType,
  CurrencyType,
} from '../models/types';

export class DatabaseService {
  private static instance: DatabaseService;
  private mockData: SystemItem[] = [];

  private constructor() {}

  public static getInstance(): DatabaseService {
    if (!DatabaseService.instance) {
      DatabaseService.instance = new DatabaseService();
    }
    return DatabaseService.instance;
  }

  public async initializeDatabase(): Promise<void> {
    try {
      console.log('Initializing mock database...');
      await this.initializeSampleData();
    } catch (error) {
      console.error('Database initialization failed:', error);
      throw error;
    }
  }

  // Mock table creation - no actual database operations

  private async initializeSampleData(): Promise<void> {
    // Initialize mock data
    this.mockData = [
      {
        id: 1,
        itemId: uuidv4(),
        model: 'FM200-25KG',
        description: 'FM200 Fire Suppression Cylinder 25kg',
        manufacturer: 'Kidde',
        approval: 'UL Listed',
        exWorksPrice: 1200,
        localPrice: 1500,
        installationPrice: 200,
        systemType: SystemType.CLEAN_AGENT,
        createdAt: new Date(),
      },
      {
        id: 2,
        itemId: uuidv4(),
        model: 'NOVEC-50KG',
        description: 'NOVEC 1230 Fire Suppression Cylinder 50kg',
        manufacturer: '3M',
        approval: 'FM Approved',
        exWorksPrice: 2000,
        localPrice: 2500,
        installationPrice: 300,
        systemType: SystemType.CLEAN_AGENT,
        createdAt: new Date(),
      },
    ];
    console.log('Sample data initialized:', this.mockData.length, 'items');
  }

  // Mock CRUD operations
  public async createSystemItem(item: SystemItem): Promise<string> {
    const itemId = item.itemId || uuidv4();
    const newItem = {
      ...item,
      id: this.mockData.length + 1,
      itemId,
      createdAt: new Date(),
    };
    this.mockData.push(newItem);
    return itemId;
  }

  public async getSystemItems(systemType?: SystemType): Promise<SystemItem[]> {
    if (systemType) {
      return this.mockData.filter(item => item.systemType === systemType);
    }
    return [...this.mockData];
  }

  public async searchSystemItems(
    systemType: SystemType,
    query: string
  ): Promise<SystemItem[]> {
    const searchTerm = query.toLowerCase();
    return this.mockData.filter(item =>
      item.systemType === systemType && (
        item.model?.toLowerCase().includes(searchTerm) ||
        item.description?.toLowerCase().includes(searchTerm) ||
        item.manufacturer?.toLowerCase().includes(searchTerm)
      )
    );
  }

  public async close(): Promise<void> {
    console.log('Mock database closed');
  }
}
