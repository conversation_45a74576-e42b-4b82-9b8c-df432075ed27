import SQLite from 'react-native-sqlite-storage';
import { v4 as uuidv4 } from 'uuid';
import {
  SystemItem,
  Project,
  Section,
  DynamicTable,
  TableColumn,
  TableRow,
  FlexibleTable,
  FlexibleColumn,
  FlexibleRow,
  SidebarSection,
  SystemType,
  ColumnDataType,
  CurrencyType,
} from '../models/types';

// Enable debugging
SQLite.DEBUG(true);
SQLite.enablePromise(true);

export class DatabaseService {
  private static instance: DatabaseService;
  private database: SQLite.SQLiteDatabase | null = null;

  private constructor() {}

  public static getInstance(): DatabaseService {
    if (!DatabaseService.instance) {
      DatabaseService.instance = new DatabaseService();
    }
    return DatabaseService.instance;
  }

  public async initializeDatabase(): Promise<void> {
    try {
      this.database = await SQLite.openDatabase({
        name: 'FireToolRN.db',
        location: 'default',
      });

      await this.createTables();
      await this.initializeSampleData();
    } catch (error) {
      console.error('Database initialization failed:', error);
      throw error;
    }
  }

  private async createTables(): Promise<void> {
    if (!this.database) throw new Error('Database not initialized');

    const createTableQueries = [
      // SystemItems table
      `CREATE TABLE IF NOT EXISTS system_items (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        item_id TEXT UNIQUE,
        model TEXT,
        description TEXT,
        manufacturer TEXT,
        approval TEXT,
        ex_works_price REAL,
        local_price REAL,
        installation_price REAL,
        system_type TEXT NOT NULL,
        section_id TEXT,
        created_at TEXT
      )`,

      // Projects table
      `CREATE TABLE IF NOT EXISTS projects (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        project_id TEXT UNIQUE,
        name TEXT,
        client_name TEXT,
        project_reference TEXT,
        created_at TEXT,
        updated_at TEXT,
        currency TEXT,
        exchange_rate REAL,
        shipping_rate REAL,
        margin_rate REAL,
        include_installation INTEGER,
        system_type TEXT NOT NULL,
        systems_json TEXT,
        clean_agent_systems_json TEXT
      )`,

      // Sections table
      `CREATE TABLE IF NOT EXISTS sections (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        section_id TEXT UNIQUE,
        name TEXT,
        icon TEXT,
        color TEXT,
        order_index INTEGER,
        parent_section_id TEXT,
        system_type TEXT NOT NULL,
        created_at TEXT,
        updated_at TEXT
      )`,

      // Dynamic Tables
      `CREATE TABLE IF NOT EXISTS dynamic_tables (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        table_id TEXT UNIQUE,
        section_id TEXT,
        name TEXT,
        display_name TEXT,
        description TEXT,
        created_at TEXT,
        updated_at TEXT,
        order_index INTEGER DEFAULT 0
      )`,

      // Table Columns
      `CREATE TABLE IF NOT EXISTS table_columns (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        column_id TEXT UNIQUE,
        name TEXT,
        table_id TEXT,
        data_type TEXT NOT NULL,
        currency_type TEXT NOT NULL,
        is_required INTEGER,
        default_value TEXT,
        order_index INTEGER,
        validation_rules TEXT,
        dropdown_options TEXT,
        created_at TEXT,
        updated_at TEXT
      )`,

      // Table Rows
      `CREATE TABLE IF NOT EXISTS table_rows (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        row_id TEXT UNIQUE,
        table_id TEXT,
        data TEXT,
        created_at TEXT,
        updated_at TEXT
      )`,

      // Flexible Tables
      `CREATE TABLE IF NOT EXISTS flexible_tables (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        table_id TEXT UNIQUE,
        name TEXT,
        description TEXT,
        section_id TEXT,
        created_at TEXT,
        updated_at TEXT
      )`,

      // Flexible Columns
      `CREATE TABLE IF NOT EXISTS flexible_columns (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        column_id TEXT UNIQUE,
        name TEXT,
        table_id TEXT,
        data_type TEXT NOT NULL,
        currency_type TEXT NOT NULL,
        is_required INTEGER,
        default_value TEXT,
        order_index INTEGER,
        validation_rules TEXT,
        dropdown_options TEXT,
        created_at TEXT,
        updated_at TEXT
      )`,

      // Flexible Rows
      `CREATE TABLE IF NOT EXISTS flexible_rows (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        row_id TEXT UNIQUE,
        table_id TEXT,
        data TEXT,
        created_at TEXT,
        updated_at TEXT
      )`,

      // Sidebar Sections
      `CREATE TABLE IF NOT EXISTS sidebar_sections (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        section_id TEXT UNIQUE,
        name TEXT,
        icon TEXT,
        color TEXT,
        order_index INTEGER,
        parent_section_id TEXT,
        system_type TEXT NOT NULL,
        created_at TEXT,
        updated_at TEXT
      )`,
    ];

    for (const query of createTableQueries) {
      await this.database.executeSql(query);
    }
  }

  private async initializeSampleData(): Promise<void> {
    // Check if data already exists
    const [result] = await this.database!.executeSql(
      'SELECT COUNT(*) as count FROM system_items'
    );
    
    if (result.rows.item(0).count > 0) {
      return; // Data already exists
    }

    // Insert sample system items
    const sampleItems: Partial<SystemItem>[] = [
      {
        itemId: uuidv4(),
        model: 'FM200-25KG',
        description: 'FM200 Fire Suppression Cylinder 25kg',
        manufacturer: 'Kidde',
        approval: 'UL Listed',
        exWorksPrice: 1200,
        localPrice: 1500,
        installationPrice: 200,
        systemType: SystemType.CLEAN_AGENT,
        createdAt: new Date(),
      },
      {
        itemId: uuidv4(),
        model: 'NOVEC-50KG',
        description: 'NOVEC 1230 Fire Suppression Cylinder 50kg',
        manufacturer: '3M',
        approval: 'FM Approved',
        exWorksPrice: 2000,
        localPrice: 2500,
        installationPrice: 300,
        systemType: SystemType.CLEAN_AGENT,
        createdAt: new Date(),
      },
    ];

    for (const item of sampleItems) {
      await this.createSystemItem(item as SystemItem);
    }
  }

  // SystemItem CRUD operations
  public async createSystemItem(item: SystemItem): Promise<string> {
    if (!this.database) throw new Error('Database not initialized');
    
    const itemId = item.itemId || uuidv4();
    const now = new Date().toISOString();

    await this.database.executeSql(
      `INSERT INTO system_items (
        item_id, model, description, manufacturer, approval,
        ex_works_price, local_price, installation_price,
        system_type, section_id, created_at
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`,
      [
        itemId,
        item.model || null,
        item.description || null,
        item.manufacturer || null,
        item.approval || null,
        item.exWorksPrice || null,
        item.localPrice || null,
        item.installationPrice || null,
        item.systemType,
        item.sectionId || null,
        now,
      ]
    );

    return itemId;
  }

  public async getSystemItems(systemType?: SystemType): Promise<SystemItem[]> {
    if (!this.database) throw new Error('Database not initialized');

    let query = 'SELECT * FROM system_items';
    let params: any[] = [];

    if (systemType) {
      query += ' WHERE system_type = ?';
      params.push(systemType);
    }

    query += ' ORDER BY created_at DESC';

    const [result] = await this.database.executeSql(query, params);
    const items: SystemItem[] = [];

    for (let i = 0; i < result.rows.length; i++) {
      const row = result.rows.item(i);
      items.push({
        id: row.id,
        itemId: row.item_id,
        model: row.model,
        description: row.description,
        manufacturer: row.manufacturer,
        approval: row.approval,
        exWorksPrice: row.ex_works_price,
        localPrice: row.local_price,
        installationPrice: row.installation_price,
        systemType: row.system_type as SystemType,
        sectionId: row.section_id,
        createdAt: row.created_at ? new Date(row.created_at) : undefined,
      });
    }

    return items;
  }

  public async searchSystemItems(
    systemType: SystemType,
    query: string
  ): Promise<SystemItem[]> {
    if (!this.database) throw new Error('Database not initialized');

    const searchQuery = `
      SELECT * FROM system_items 
      WHERE system_type = ? AND (
        model LIKE ? OR 
        description LIKE ? OR 
        manufacturer LIKE ?
      )
      ORDER BY created_at DESC
    `;

    const searchTerm = `%${query}%`;
    const [result] = await this.database.executeSql(searchQuery, [
      systemType,
      searchTerm,
      searchTerm,
      searchTerm,
    ]);

    const items: SystemItem[] = [];
    for (let i = 0; i < result.rows.length; i++) {
      const row = result.rows.item(i);
      items.push({
        id: row.id,
        itemId: row.item_id,
        model: row.model,
        description: row.description,
        manufacturer: row.manufacturer,
        approval: row.approval,
        exWorksPrice: row.ex_works_price,
        localPrice: row.local_price,
        installationPrice: row.installation_price,
        systemType: row.system_type as SystemType,
        sectionId: row.section_id,
        createdAt: row.created_at ? new Date(row.created_at) : undefined,
      });
    }

    return items;
  }

  public async close(): Promise<void> {
    if (this.database) {
      await this.database.close();
      this.database = null;
    }
  }
}
