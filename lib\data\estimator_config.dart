// Global Configuration Parameters
class AppConfig {
  static const double shippingExFactor = 1.15;
  static const double dollarRateSarUsd = 3.75;
  static const double defaultRoomHeightM = 4.0;
  static const int agentDischargeTimeSeconds = 10;
  static const int nozzleSizeThresholdMm = 50;
  static const double detectorCoverageAreaM2 = 49;
  static const bool no343LCylinder = true;
  static const double defaultMarginFactor = 1.0;
  
  static const Map<String, double> maxFillingRatio = {
    'NOVEC1230': 0.88,
    'FM200': 0.80,
  };
}

// Design Concentration Factors
class DesignFactors {
  static const Map<String, Map<String, Map<String, dynamic>>> factors = {
    'NOVEC1230': {
      '4.5%': {'factor': 0.656, 'default': true},
      '4.7%': {'factor': 0.687, 'default': false},
      '5.6%': {'factor': 0.826, 'default': false},
      '5.9%': {'factor': 0.873, 'default': false},
    },
    'FM200': {
      '7.4%': {'factor': 0.583, 'default': true},
      '8.5%': {'factor': 0.678, 'default': false},
      '9.0%': {'factor': 0.722, 'default': false},
    },
  };
}

// Agent-specific data
class AgentData {
  static const Map<String, Map<String, dynamic>> data = {
    'NOVEC1230': {
      'costPerKg': 32.2582,
      'partNumber': '300.207.001',
    },
    'FM200': {
      'costPerKg': 20.3595,
      'partNumber': '300.205.001',
    },
  };
}

// Cylinder Specifications
class CylinderSpec {
  final int excelRow;
  final double size;
  final double maxKg;
  final double minKg;
  final String partNo;
  final String bracketPart;
  final String labelPart;
  final int bracketQtyRule;

  const CylinderSpec({
    required this.excelRow,
    required this.size,
    required this.maxKg,
    required this.minKg,
    required this.partNo,
    required this.bracketPart,
    required this.labelPart,
    required this.bracketQtyRule,
  });
}

class CylinderSpecs {
  static const Map<String, List<CylinderSpec>> specs = {
    'NOVEC1230': [
      CylinderSpec(excelRow: 4, size: 343, maxKg: 302, minKg: 175, partNo: '303.207.008', bracketPart: '311.205.019', labelPart: '314207306', bracketQtyRule: 1),
      CylinderSpec(excelRow: 5, size: 180, maxKg: 159, minKg: 92, partNo: '303.207.007', bracketPart: '311.205.014', labelPart: '314207306', bracketQtyRule: 1),
      CylinderSpec(excelRow: 6, size: 147, maxKg: 130, minKg: 75, partNo: '303.207.006', bracketPart: '311.205.014', labelPart: '314207306', bracketQtyRule: 1),
      CylinderSpec(excelRow: 7, size: 106, maxKg: 94, minKg: 55, partNo: '303.207.005', bracketPart: '311.205.014', labelPart: '314207306', bracketQtyRule: 1),
      CylinderSpec(excelRow: 8, size: 52, maxKg: 46, minKg: 27, partNo: '303.207.004', bracketPart: '311.205.014', labelPart: '314207337', bracketQtyRule: 1),
      CylinderSpec(excelRow: 9, size: 32, maxKg: 29, minKg: 17, partNo: '303.207.003', bracketPart: '311.205.013', labelPart: '314207306', bracketQtyRule: 2),
      CylinderSpec(excelRow: 10, size: 16, maxKg: 15, minKg: 9, partNo: '303.207.002', bracketPart: '311.205.013', labelPart: '314207337', bracketQtyRule: 2),
      CylinderSpec(excelRow: 11, size: 8, maxKg: 8, minKg: 5, partNo: '303.207.001', bracketPart: '311.205.013', labelPart: '314207337', bracketQtyRule: 2),
      CylinderSpec(excelRow: 12, size: 4.5, maxKg: 4, minKg: 3, partNo: '303.207.010', bracketPart: '311.205.020', labelPart: '314207337', bracketQtyRule: 2),
    ],
    'FM200': [
      CylinderSpec(excelRow: 4, size: 343, maxKg: 214, minKg: 175, partNo: '303.205.022', bracketPart: '311.205.019', labelPart: '314.205.021', bracketQtyRule: 1),
      CylinderSpec(excelRow: 5, size: 180, maxKg: 159, minKg: 92, partNo: '303.205.021', bracketPart: '311.205.014', labelPart: '314.205.021', bracketQtyRule: 1),
      CylinderSpec(excelRow: 6, size: 147, maxKg: 130, minKg: 75, partNo: '303.205.020', bracketPart: '311.205.014', labelPart: '314.205.021', bracketQtyRule: 1),
      CylinderSpec(excelRow: 7, size: 106, maxKg: 94, minKg: 55, partNo: '303.205.019', bracketPart: '311.205.014', labelPart: '314.205.021', bracketQtyRule: 1),
      CylinderSpec(excelRow: 8, size: 52, maxKg: 46, minKg: 27, partNo: '303.205.018', bracketPart: '311.205.014', labelPart: '314.205.022', bracketQtyRule: 1),
      CylinderSpec(excelRow: 9, size: 32, maxKg: 29, minKg: 17, partNo: '303.205.017', bracketPart: '311.205.013', labelPart: '314.205.021', bracketQtyRule: 1),
      CylinderSpec(excelRow: 10, size: 16, maxKg: 15, minKg: 9, partNo: '303.205.016', bracketPart: '311.205.013', labelPart: '314.205.022', bracketQtyRule: 1),
      CylinderSpec(excelRow: 11, size: 8, maxKg: 8, minKg: 5, partNo: '303.205.015', bracketPart: '311.205.013', labelPart: '314.205.022', bracketQtyRule: 1),
      CylinderSpec(excelRow: 12, size: 4.5, maxKg: 4, minKg: 3, partNo: '303.205.026', bracketPart: '311.205.020', labelPart: '314.205.022', bracketQtyRule: 1),
    ],
  };
}

// Pipe and Nozzle Sizing Data
class PipeData {
  final int excelRow;
  final String sizeDesc;
  final int sizeMm;
  final double minFlow;
  final double maxFlow;

  const PipeData({
    required this.excelRow,
    required this.sizeDesc,
    required this.sizeMm,
    required this.minFlow,
    required this.maxFlow,
  });
}

class NozzlePipeData {
  static const List<PipeData> data = [
    PipeData(excelRow: 4, sizeDesc: "1/2\"", sizeMm: 10, minFlow: 0.272, maxFlow: 0.907),
    PipeData(excelRow: 5, sizeDesc: "1/2\"", sizeMm: 15, minFlow: 0.454, maxFlow: 1.361),
    PipeData(excelRow: 6, sizeDesc: "3/4\"", sizeMm: 20, minFlow: 0.907, maxFlow: 2.495),
    PipeData(excelRow: 7, sizeDesc: "1\"", sizeMm: 25, minFlow: 1.588, maxFlow: 3.855),
    PipeData(excelRow: 8, sizeDesc: "1 1/4\"", sizeMm: 32, minFlow: 2.722, maxFlow: 5.67),
    PipeData(excelRow: 9, sizeDesc: "1 1/2\"", sizeMm: 40, minFlow: 4.082, maxFlow: 9.072),
    PipeData(excelRow: 10, sizeDesc: "2\"", sizeMm: 50, minFlow: 6.35, maxFlow: 13.61),
    PipeData(excelRow: 11, sizeDesc: "2 1/2\"", sizeMm: 65, minFlow: 9.072, maxFlow: 24.95),
    PipeData(excelRow: 12, sizeDesc: "3\"", sizeMm: 90, minFlow: 13.61, maxFlow: 40.82),
    PipeData(excelRow: 13, sizeDesc: "4\"", sizeMm: 100, minFlow: 24.95, maxFlow: 56.7),
    PipeData(excelRow: 14, sizeDesc: "5\"", sizeMm: 125, minFlow: 40.82, maxFlow: 90.72),
    PipeData(excelRow: 15, sizeDesc: "6\"", sizeMm: 150, minFlow: 54.43, maxFlow: 136.1),
  ];
}

// Installation rates
class InstallationRates {
  static const Map<int, double> pipeRates = {
    10: 75,
    15: 85,
    20: 95,
    25: 105,
    32: 120,
    40: 135,
    50: 150,
    65: 180,
    90: 220,
    100: 250,
  };

  static const Map<String, double> cableRates = {
    '2x1.5': 25,
    '2x2.5': 30,
  };
}

// Component data
class Component {
  final String partNo;
  final String description;
  final double unitCost;
  final String manufacturer;

  const Component({
    required this.partNo,
    required this.description,
    required this.unitCost,
    required this.manufacturer,
  });
}

class MasterComponents {
  static const List<Component> components = [
    // Agent
    Component(partNo: '300.207.001', description: 'Novec 1230 Agent', unitCost: 32.2582, manufacturer: 'SFFECO'),
    Component(partNo: '300.205.001', description: 'FM200 Gas', unitCost: 20.3595, manufacturer: 'SFFECO'),

    // NOVEC Cylinders
    Component(partNo: '303.207.010', description: '4.5L SAPPHIRE Container Assy, Novec 1230', unitCost: 755.93, manufacturer: 'SFFECO'),
    Component(partNo: '303.207.001', description: '8L SAPPHIRE Container Assy, Novec 1230', unitCost: 780.53, manufacturer: 'SFFECO'),
    Component(partNo: '303.207.002', description: '16L SAPPHIRE Container Assy, Novec 1230', unitCost: 808.58, manufacturer: 'SFFECO'),
    Component(partNo: '303.207.003', description: '32L SAPPHIRE Container Assy, Novec 1230', unitCost: 853.73, manufacturer: 'SFFECO'),
    Component(partNo: '303.207.004', description: '52L SAPPHIRE Container Assy, Novec 1230', unitCost: 1206.95, manufacturer: 'SFFECO'),
    Component(partNo: '303.207.005', description: '106L SAPPHIRE Container Assy, Novec 1230', unitCost: 1281.27, manufacturer: 'SFFECO'),
    Component(partNo: '303.207.006', description: '147L SAPPHIRE Container Assy, Novec 1230', unitCost: 1404.31, manufacturer: 'SFFECO'),
    Component(partNo: '303.207.007', description: '180L SAPPHIRE Container Assy, Novec 1230', unitCost: 1452.94, manufacturer: 'SFFECO'),
    Component(partNo: '303.207.008', description: '343L SAPPHIRE Container Assy, Novec 1230', unitCost: 3461.83, manufacturer: 'SFFECO'),

    // FM200 Cylinders
    Component(partNo: '303.205.026', description: '4.5L FM200 Container (DOT)', unitCost: 727.49, manufacturer: 'SFFECO'),
    Component(partNo: '303.205.015', description: '8L FM200 Container (DOT)', unitCost: 751.16, manufacturer: 'SFFECO'),
    Component(partNo: '303.205.016', description: '16L FM200 Container (DOT)', unitCost: 778.16, manufacturer: 'SFFECO'),
    Component(partNo: '303.205.017', description: '32L FM200 Container (DOT)', unitCost: 821.59, manufacturer: 'SFFECO'),
    Component(partNo: '303.205.018', description: '52L FM200 Container (DOT)', unitCost: 1161.54, manufacturer: 'SFFECO'),
    Component(partNo: '303.205.019', description: '106L FM200 Container (DOT)', unitCost: 1233.06, manufacturer: 'SFFECO'),
    Component(partNo: '303.205.020', description: '147L FM200 Container (DOT)', unitCost: 1351.46, manufacturer: 'SFFECO'),
    Component(partNo: '303.205.021', description: '180L FM200 Container (DOT)', unitCost: 1398.25, manufacturer: 'SFFECO'),
    Component(partNo: '303.205.022', description: '343L FM200 Container (DOT)', unitCost: 3331.55, manufacturer: 'SFFECO'),

    // Labels
    Component(partNo: '314207306', description: 'SAPPHIRE Label (32L+ Novec)', unitCost: 17.72, manufacturer: 'SFFECO'),
    Component(partNo: '314207337', description: 'SAPPHIRE Label (4.5/8/16/52L Novec)', unitCost: 16.04, manufacturer: 'SFFECO'),
    Component(partNo: '314.205.021', description: 'FM200 Label (32L+)', unitCost: 17.05, manufacturer: 'SFFECO'),
    Component(partNo: '314.205.022', description: 'FM200 Label (4.5/8/16L)', unitCost: 15.43, manufacturer: 'SFFECO'),

    // Brackets
    Component(partNo: '311.205.013', description: '8/16/32L Cylinder Bracket (Strap Style)', unitCost: 15.75, manufacturer: 'SFFECO'),
    Component(partNo: '311.205.014', description: '52/106/147/180L Cylinder Bracket (Strap Style)', unitCost: 18.22, manufacturer: 'SFFECO'),
    Component(partNo: '311.205.019', description: '343L Cylinder Bracket (Strap Style)', unitCost: 120.82, manufacturer: 'SFFECO'),
    Component(partNo: '311.205.020', description: '4.5L Cylinder Bracket (Strap Style)', unitCost: 8.48, manufacturer: 'SFFECO'),

    // Actuators
    Component(partNo: '304205030', description: 'Electrical Actuator - UL (TLXUS)', unitCost: 234.83, manufacturer: 'SFFECO'),
    Component(partNo: '304.209.002', description: 'Local Manual Actuator', unitCost: 63.85, manufacturer: 'SFFECO'),
    Component(partNo: '304.209.004', description: 'Pneumatic Actuator', unitCost: 50.44, manufacturer: 'SFFECO'),

    // Discharge Components
    Component(partNo: '306.207.002', description: '25mm Novec discharge hose', unitCost: 49.75, manufacturer: 'SFFECO'),
    Component(partNo: '306.207.003', description: '50mm Novec discharge hose', unitCost: 180.59, manufacturer: 'SFFECO'),
    Component(partNo: '306.205.003', description: 'Flexible pilot hose', unitCost: 21.29, manufacturer: 'SFFECO'),
    Component(partNo: '302.209.004', description: '25mm Novec manifold check valve', unitCost: 147.13, manufacturer: 'SFFECO'),
    Component(partNo: '302.209.005', description: '50mm Novec manifold check valve', unitCost: 191.26, manufacturer: 'SFFECO'),
    Component(partNo: '306.205.005', description: '80mm valve discharge hose', unitCost: 281.50, manufacturer: 'SFFECO'),
    Component(partNo: '306.205.006', description: '80mm discharge hose/check valve assembly', unitCost: 1740.52, manufacturer: 'SFFECO'),

    // FM200 Discharge Components
    Component(partNo: '306.205.007', description: '25mm FM200 discharge hose', unitCost: 47.88, manufacturer: 'SFFECO'),
    Component(partNo: '306.205.008', description: '50mm FM200 discharge hose', unitCost: 173.79, manufacturer: 'SFFECO'),
    Component(partNo: '302.205.001', description: '25mm FM200 manifold check valve', unitCost: 141.59, manufacturer: 'SFFECO'),
    Component(partNo: '302.205.002', description: '50mm FM200 manifold check valve', unitCost: 184.06, manufacturer: 'SFFECO'),

    // Liquid Level Indicators
    Component(partNo: '300.015.127', description: '106L container liquid level indicator', unitCost: 420.32, manufacturer: 'SFFECO'),
    Component(partNo: '300.015.128', description: 'LLI for Large Novec (147L+, seamless+343L)', unitCost: 447.66, manufacturer: 'SFFECO'),
    Component(partNo: '300.015.129', description: '180L container liquid level indicator', unitCost: 272.98, manufacturer: 'SFFECO'),

    // Caution Plates
    Component(partNo: '314.207.001', description: 'SAPPHIRE Door Caution Plate - English', unitCost: 4.03, manufacturer: 'SFFECO'),
    Component(partNo: '314.207.003', description: 'SAPPHIRE Manual Release Caution Plate - English', unitCost: 2.77, manufacturer: 'SFFECO'),
    Component(partNo: '314.205.002', description: 'FM-200 Door Caution Plate - English', unitCost: 6.85, manufacturer: 'SFFECO'),
    Component(partNo: '314.205.003', description: 'FM-200 Manual Release Caution Plate', unitCost: 2.77, manufacturer: 'SFFECO'),

    // Nozzles - NOVEC
    Component(partNo: '310.207.213', description: 'Novec Brass Nozzle - 15mm 180-degree NPT', unitCost: 35.14, manufacturer: 'SFFECO'),
    Component(partNo: '310.207.214', description: 'Novec Brass Nozzle - 15mm 360-degree NPT', unitCost: 35.14, manufacturer: 'SFFECO'),
    Component(partNo: '310.207.215', description: 'Novec Brass Nozzle - 20mm 180-degree NPT', unitCost: 41.82, manufacturer: 'SFFECO'),
    Component(partNo: '310.207.216', description: 'Novec Brass Nozzle - 20mm 360-degree NPT', unitCost: 41.82, manufacturer: 'SFFECO'),
    Component(partNo: '310.207.217', description: 'Novec Brass Nozzle - 25mm 180-degree NPT', unitCost: 47.77, manufacturer: 'SFFECO'),
    Component(partNo: '310.207.218', description: 'Novec Brass Nozzle - 25mm 360-degree NPT', unitCost: 47.77, manufacturer: 'SFFECO'),
    Component(partNo: '310.207.219', description: 'Novec Brass Nozzle - 32mm 180-degree NPT', unitCost: 63.00, manufacturer: 'SFFECO'),
    Component(partNo: '310.207.220', description: 'Novec Brass Nozzle - 32mm 360-degree NPT', unitCost: 63.00, manufacturer: 'SFFECO'),
    Component(partNo: '310.207.221', description: 'Novec Brass Nozzle - 40mm 180-degree NPT', unitCost: 74.19, manufacturer: 'SFFECO'),
    Component(partNo: '310.207.222', description: 'Novec Brass Nozzle - 40mm 360-degree NPT', unitCost: 74.19, manufacturer: 'SFFECO'),
    Component(partNo: '310.207.223', description: 'Novec Brass Nozzle - 50mm 180-degree NPT', unitCost: 120.29, manufacturer: 'SFFECO'),
    Component(partNo: '310.207.224', description: 'Novec Brass Nozzle - 50mm 360-degree NPT', unitCost: 120.29, manufacturer: 'SFFECO'),

    // Nozzles - FM200
    Component(partNo: '310.205.216', description: 'FM200 Brass Nozzle - 10mm 360-degree NPT', unitCost: 20.63, manufacturer: 'SFFECO'),
    Component(partNo: '310.205.218', description: 'FM200 Brass Nozzle - 15mm 360-degree NPT', unitCost: 23.42, manufacturer: 'SFFECO'),
    Component(partNo: '310.205.220', description: 'FM200 Brass Nozzle - 20mm 360-degree NPT', unitCost: 26.64, manufacturer: 'SFFECO'),
    Component(partNo: '310.205.222', description: 'FM200 Brass Nozzle - 25mm 360-degree NPT', unitCost: 33.91, manufacturer: 'SFFECO'),
    Component(partNo: '310.205.224', description: 'FM200 Brass Nozzle - 32mm 360-degree NPT', unitCost: 39.70, manufacturer: 'SFFECO'),
    Component(partNo: '310.205.226', description: 'FM200 Brass Nozzle - 40mm 360-degree NPT', unitCost: 65.10, manufacturer: 'SFFECO'),
    Component(partNo: '310.205.228', description: 'FM200 Brass Nozzle - 50mm 360-degree NPT', unitCost: 88.14, manufacturer: 'SFFECO'),

    // Manifold Assemblies
    Component(partNo: 'MANIFOLD_N25', description: '25mm Novec Manifold Assy Kit', unitCost: 180.00, manufacturer: 'SFFECO'),
    Component(partNo: 'MANIFOLD_N32', description: '32mm Novec Manifold Assy Kit', unitCost: 200.00, manufacturer: 'SFFECO'),
    Component(partNo: 'MANIFOLD_N50', description: '50mm Novec Manifold Assy Kit', unitCost: 220.00, manufacturer: 'SFFECO'),
    Component(partNo: 'MANIFOLD_N65', description: '65mm Novec Manifold Assy Kit', unitCost: 250.00, manufacturer: 'SFFECO'),
    Component(partNo: 'MANIFOLD_F25', description: '25mm FM200 Manifold Assy Kit', unitCost: 175.00, manufacturer: 'SFFECO'),
    Component(partNo: 'MANIFOLD_F32', description: '32mm FM200 Manifold Assy Kit', unitCost: 195.00, manufacturer: 'SFFECO'),
    Component(partNo: 'MANIFOLD_F50', description: '50mm FM200 Manifold Assy Kit', unitCost: 215.00, manufacturer: 'SFFECO'),
    Component(partNo: 'MANIFOLD_F65', description: '65mm FM200 Manifold Assy Kit', unitCost: 245.00, manufacturer: 'SFFECO'),

    // Manifold Brackets
    Component(partNo: '311.205.015', description: '65mm Manifold bracket assembly', unitCost: 51.19, manufacturer: 'SFFECO'),
    Component(partNo: '311.205.010', description: '80mm/90mm Manifold bracket assembly', unitCost: 60.26, manufacturer: 'SFFECO'),
    Component(partNo: '311.205.011', description: '100mm Manifold bracket assembly', unitCost: 61.55, manufacturer: 'SFFECO'),
    Component(partNo: '311.205.012', description: '150mm Manifold bracket assembly', unitCost: 62.49, manufacturer: 'SFFECO'),

    // Alarm & Detection Components
    Component(partNo: 'RP-2002E', description: 'Agent release control panel, 240 Volts', unitCost: 611.39, manufacturer: 'ANSUL'),
    Component(partNo: '2W-B', description: 'i³ Photo Detector, 2-wire, 12/24 Vdc, Photo. c/w base', unitCost: 51.68, manufacturer: 'ANSUL'),
    Component(partNo: '5151-CH', description: '135°F (57°C) electronic fixed & ROR heat; c/w base', unitCost: 18.47, manufacturer: 'ANSUL'),
    Component(partNo: 'P2GRKLED', description: 'HORN/STRB LED RED WALL OUTDOOR 2X4', unitCost: 70.04, manufacturer: 'ANSUL'),
    Component(partNo: 'SSM24-6', description: 'System Sensor 24V, 6" Bell. c/w backbox WBB', unitCost: 63.32, manufacturer: 'ANSUL'),
    Component(partNo: 'NBG-12LR', description: 'Dual action Releasing station, red, terminal block, Notifier Key lock. c/w backbox SB-I/O', unitCost: 44.58, manufacturer: 'ANSUL'),
    Component(partNo: '2080-9057', description: 'Abort Switch, Surface mount', unitCost: 117.95, manufacturer: 'ANSUL'),
    Component(partNo: '76496', description: 'Selector Main/Reserve tyco', unitCost: 180.00, manufacturer: 'ANSUL'),
    Component(partNo: '2080-9070', description: 'Disconnect switch', unitCost: 178.12, manufacturer: 'ANSUL'),

    // Other Components
    Component(partNo: '304.205.006', description: 'Supervisory pressure switch - standard', unitCost: 19.33, manufacturer: 'SFFECO'),
    Component(partNo: '437900', description: 'Discharge pressure switch - standard', unitCost: 102.20, manufacturer: 'SFFECO'),

    // Installation Items
    Component(partNo: 'PIPE_INSTALL', description: 'Pipe Installation per meter', unitCost: 25.0, manufacturer: 'Installation'),
    Component(partNo: 'CABLE_INSTALL', description: 'Cable Installation per meter', unitCost: 15.0, manufacturer: 'Installation'),
  ];
}
