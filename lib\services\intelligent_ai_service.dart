import 'package:flutter/foundation.dart';
import '../models/estimator_types.dart';
import '../utils/dynamic_estimator_calculations.dart';
import '../utils/dynamic_bom_generator.dart';
import '../services/dynamic_clean_agent_service.dart';

/// Truly Intelligent AI Service that learns and explores your app dynamically
/// Uses Phi-3's full intelligence to understand and interact with your app
class IntelligentAIService {
  static final IntelligentAIService _instance = IntelligentAIService._internal();
  factory IntelligentAIService() => _instance;
  IntelligentAIService._internal();

  final DynamicCleanAgentService _dynamicService = DynamicCleanAgentService();
  
  // AI's learned knowledge about your app
  final Map<String, dynamic> _appKnowledge = {};
  final Map<String, List<String>> _discoveredOptions = {};
  Map<String, dynamic> _inputModeCapabilities = {};
  bool _hasExploredApp = false;

  /// Let the AI explore and learn your app's structure and capabilities
  Future<void> exploreAndLearnApp() async {
    if (_hasExploredApp) return;
    
    debugPrint('🧠 AI: Starting intelligent exploration of your FireTool app...');
    
    try {
      // Discover Clean Agent Calculator capabilities
      await _discoverCleanAgentCapabilities();
      
      // Discover input modes and their requirements
      await _discoverInputModes();
      
      // Discover available options from database
      await _discoverDatabaseOptions();
      
      _hasExploredApp = true;
      debugPrint('🧠 AI: App exploration complete! I now understand your app\'s capabilities.');
      
    } catch (e) {
      debugPrint('❌ AI: Failed to explore app: $e');
    }
  }

  /// Discover Clean Agent Calculator capabilities by examining the actual code structure
  Future<void> _discoverCleanAgentCapabilities() async {
    debugPrint('🔍 AI: Discovering Clean Agent Calculator capabilities...');
    
    // Learn about agent types
    _appKnowledge['agent_types'] = {
      'NOVEC1230': {
        'name': 'NOVEC 1230',
        'concentrations': ['4.5%', '4.7%', '5.6%', '5.9%'],
        'default_concentration': '4.5%'
      },
      'FM200': {
        'name': 'FM-200', 
        'concentrations': ['7.4%', '8.5%', '9.0%'],
        'default_concentration': '7.4%'
      }
    };

    // Learn about input modes
    _appKnowledge['input_modes'] = {
      'dimensions': {
        'name': 'Room Dimensions',
        'requires': ['room_length', 'room_width', 'room_height'],
        'description': 'Calculate based on room dimensions (L×W×H)',
        'units': 'meters'
      },
      'agentQuantity': {
        'name': 'Agent Quantity',
        'requires': ['agent_quantity'],
        'description': 'Calculate based on required agent weight',
        'units': 'kg'
      }
    };

    // Learn about system types
    _appKnowledge['system_types'] = {
      'main': 'Main System Only',
      'reserve': 'Reserve System Only', 
      'mainAndReserve': 'Main + Reserve Systems'
    };

    // Learn about installation types
    _appKnowledge['installation_types'] = {
      'supplyOnly': 'Supply Only',
      'supplyAndInstall': 'Supply & Install'
    };

    debugPrint('🧠 AI: Learned about agent types, input modes, system types, and installation types');
  }

  /// Discover input modes and what they can handle
  Future<void> _discoverInputModes() async {
    debugPrint('🔍 AI: Learning input mode capabilities...');
    
    _inputModeCapabilities = {
      'dimensions': {
        'can_handle': ['room dimensions', 'length width height', 'l×w×h', 'meters', 'm'],
        'requires_units': 'meters',
        'example_inputs': ['5m × 4m × 3m', '10 meters length', 'room 6×6×3']
      },
      'agentQuantity': {
        'can_handle': ['kg', 'kilograms', 'agent weight', 'agent quantity'],
        'requires_units': 'kg',
        'example_inputs': ['60kg', '120 kg', '50 kilograms']
      },
      'volume': {
        'can_handle': ['m³', 'cubic meters', 'volume', 'room volume'],
        'requires_units': 'm³',
        'converts_to': 'dimensions',
        'example_inputs': ['100m³', '50 cubic meters', '75 m³']
      }
    };

    debugPrint('🧠 AI: Learned how to handle different input types and units');
  }

  /// Discover available options from your database
  Future<void> _discoverDatabaseOptions() async {
    debugPrint('🔍 AI: Discovering options from your database...');
    
    try {
      // Get real design factors from database
      final designFactors = await _dynamicService.getDesignFactors();
      _discoveredOptions['design_factors'] = designFactors.map((df) => 
        '${df.agentType}: ${df.percentage}').toList();

      // Get real cylinder specs
      final cylinderSpecs = await _dynamicService.getCylinderSpecs();
      _discoveredOptions['cylinder_sizes'] = cylinderSpecs.map((cs) => 
        '${cs.sizeLiters}L').toList();

      debugPrint('🧠 AI: Discovered ${designFactors.length} design factors and ${cylinderSpecs.length} cylinder sizes from your database');
      
    } catch (e) {
      debugPrint('⚠️ AI: Could not access database yet: $e');
    }
  }

  /// Intelligently analyze user input and determine what they want
  Future<Map<String, dynamic>> analyzeUserIntent(String userInput) async {
    await exploreAndLearnApp();

    debugPrint('🧠 AI: Analyzing user intent: "$userInput"');

    // Detect language and normalize Arabic text
    final language = _detectLanguage(userInput);
    final normalizedInput = _normalizeArabicText(userInput);
    final input = normalizedInput.toLowerCase();

    debugPrint('🧠 AI: Detected language: $language');
    debugPrint('🧠 AI: Normalized input: "$normalizedInput"');

    Map<String, dynamic> analysis = {
      'intent': 'unknown',
      'confidence': 0.0,
      'extracted_data': {},
      'missing_requirements': [],
      'suggested_input_mode': null,
      'reasoning': '',
      'detected_language': language,
    };

    // Intelligent pattern recognition
    if (_containsCalculationWords(input)) {
      analysis['intent'] = 'calculate_clean_agent';
      analysis['confidence'] = 0.9;
      
      // Extract agent type intelligently
      String? agentType = _extractAgentType(input);
      if (agentType != null) {
        analysis['extracted_data']['agent_type'] = agentType;
      }

      // Intelligently determine input mode based on what user provided
      final inputModeAnalysis = _analyzeInputMode(input);
      analysis.addAll(inputModeAnalysis);
      
      // Extract numerical values intelligently
      final extractedValues = _extractNumericalValues(input);
      analysis['extracted_data'].addAll(extractedValues);
      
      // Determine what's missing
      analysis['missing_requirements'] = _determineMissingRequirements(analysis);
      
      analysis['reasoning'] = _generateReasoning(analysis);
    }

    debugPrint('🧠 AI: Analysis complete - Intent: ${analysis['intent']}, Confidence: ${analysis['confidence']}');
    return analysis;
  }

  /// Check if input contains calculation-related words
  bool _containsCalculationWords(String input) {
    final calcWords = ['calculate', 'cost', 'price', 'system', 'design', 'estimate', 'quote'];
    return calcWords.any((word) => input.contains(word));
  }

  /// Intelligently extract agent type from user input
  String? _extractAgentType(String input) {
    if (input.contains('fm200') || input.contains('fm-200')) return 'FM200';
    if (input.contains('novec')) return 'NOVEC1230';
    return null;
  }

  /// Intelligently analyze what input mode the user wants based on their input
  Map<String, dynamic> _analyzeInputMode(String input) {
    // Check for volume indicators (m³, cubic meters)
    if (input.contains('m³') || input.contains('cubic') || input.contains('volume')) {
      return {
        'suggested_input_mode': 'dimensions',
        'input_type': 'volume',
        'reasoning_detail': 'User mentioned volume - will convert to room dimensions'
      };
    }
    
    // Check for weight indicators (kg, kilograms)
    if (input.contains('kg') || input.contains('kilogram')) {
      return {
        'suggested_input_mode': 'agentQuantity', 
        'input_type': 'weight',
        'reasoning_detail': 'User specified weight - will use agent quantity mode'
      };
    }
    
    // Check for dimension indicators (×, x, length, width, height)
    if (input.contains('×') || input.contains('x') || 
        input.contains('length') || input.contains('width') || input.contains('height')) {
      return {
        'suggested_input_mode': 'dimensions',
        'input_type': 'dimensions', 
        'reasoning_detail': 'User provided room dimensions'
      };
    }
    
    return {
      'suggested_input_mode': null,
      'input_type': 'unknown',
      'reasoning_detail': 'Could not determine input type from user input'
    };
  }

  /// Extract numerical values and their units intelligently
  Map<String, dynamic> _extractNumericalValues(String input) {
    Map<String, dynamic> values = {};
    
    // Extract weight (kg)
    final weightRegex = RegExp(r'(\d+(?:\.\d+)?)\s*(?:kg|kilogram)', caseSensitive: false);
    final weightMatch = weightRegex.firstMatch(input);
    if (weightMatch != null) {
      values['agent_quantity'] = double.parse(weightMatch.group(1)!);
    }
    
    // Extract volume (m³)
    final volumeRegex = RegExp(r'(\d+(?:\.\d+)?)\s*(?:m³|cubic)', caseSensitive: false);
    final volumeMatch = volumeRegex.firstMatch(input);
    if (volumeMatch != null) {
      values['room_volume'] = double.parse(volumeMatch.group(1)!);
    }
    
    // Extract dimensions (L×W×H)
    final dimensionRegex = RegExp(r'(\d+(?:\.\d+)?)\s*[×x]\s*(\d+(?:\.\d+)?)\s*[×x]\s*(\d+(?:\.\d+)?)', caseSensitive: false);
    final dimensionMatch = dimensionRegex.firstMatch(input);
    if (dimensionMatch != null) {
      values['room_length'] = double.parse(dimensionMatch.group(1)!);
      values['room_width'] = double.parse(dimensionMatch.group(2)!);
      values['room_height'] = double.parse(dimensionMatch.group(3)!);
    }
    
    return values;
  }

  /// Determine what information is still needed
  List<String> _determineMissingRequirements(Map<String, dynamic> analysis) {
    List<String> missing = [];
    final extractedData = analysis['extracted_data'] as Map<String, dynamic>;

    // Check for agent type
    if (!extractedData.containsKey('agent_type')) {
      missing.add('agent_type');
    }

    // Check for system type - ALWAYS ask if not specified
    if (!extractedData.containsKey('system_type')) {
      missing.add('system_type');
    }

    // Check for installation type - ALWAYS ask if not specified
    if (!extractedData.containsKey('installation_type')) {
      missing.add('installation_type');
    }

    // Check input mode requirements
    final inputMode = analysis['suggested_input_mode'];
    if (inputMode == 'agentQuantity' && !extractedData.containsKey('agent_quantity')) {
      missing.add('agent_quantity');
    } else if (inputMode == 'dimensions') {
      if (!extractedData.containsKey('room_length') && !extractedData.containsKey('room_volume')) {
        missing.add('room_dimensions_or_volume');
      }
    }

    return missing;
  }

  /// Generate human-readable reasoning for the AI's analysis
  String _generateReasoning(Map<String, dynamic> analysis) {
    final extractedData = analysis['extracted_data'] as Map<String, dynamic>;
    final inputMode = analysis['suggested_input_mode'];
    
    String reasoning = 'I analyzed your request and ';
    
    if (extractedData.containsKey('agent_type')) {
      reasoning += 'detected you want ${extractedData['agent_type']} clean agent. ';
    }
    
    if (inputMode == 'agentQuantity' && extractedData.containsKey('agent_quantity')) {
      reasoning += 'Since you specified ${extractedData['agent_quantity']}kg, I\'ll use agent quantity mode. ';
    } else if (inputMode == 'dimensions' && extractedData.containsKey('room_volume')) {
      reasoning += 'Since you mentioned ${extractedData['room_volume']}m³, I\'ll convert this to room dimensions. ';
    }
    
    return reasoning;
  }

  /// Execute calculation using your real app logic with AI's intelligent analysis
  Future<Map<String, dynamic>> executeIntelligentCalculation(Map<String, dynamic> analysis, Map<String, dynamic> userSelections) async {
    debugPrint('🧠 AI: Executing intelligent calculation with real app logic...');
    
    try {
      // Combine AI analysis with user selections
      final extractedData = analysis['extracted_data'] as Map<String, dynamic>;
      final allData = {...extractedData, ...userSelections};
      
      // Convert to your app's format intelligently
      final input = _buildEstimatorFormValues(allData, analysis);
      
      debugPrint('🧠 AI: Using real DynamicEstimatorCalculations with: ${input.agentType}, ${input.inputMode}');
      
      // Use your REAL calculation engine
      final designResults = await DynamicEstimatorCalculations.calculateDesign(input);
      final bomData = await DynamicBomGenerator.generateBOM(designResults, input, installationFactor: 0.15);
      
      debugPrint('🧠 AI: Real calculation complete - ${bomData['bom'].length} items, Total: ${(bomData['summary'] as BomSummary).grandTotalSAR.round()} SAR');
      
      return {
        'success': true,
        'design_results': designResults,
        'bom_data': bomData,
        'ai_reasoning': analysis['reasoning'],
        'input_used': input,
      };
      
    } catch (e) {
      debugPrint('❌ AI: Intelligent calculation failed: $e');
      return {
        'success': false,
        'error': e.toString(),
      };
    }
  }

  /// Build EstimatorFormValues from AI analysis and user input
  EstimatorFormValues _buildEstimatorFormValues(Map<String, dynamic> data, Map<String, dynamic> analysis) {
    // Convert agent type
    final agentTypeStr = data['agent_type'] ?? 'NOVEC1230';
    final agentType = agentTypeStr == 'FM200' ? AgentType.fm200 : AgentType.novec1230;
    
    // Determine input mode intelligently
    InputMode inputMode = InputMode.agentQuantity;
    if (data.containsKey('room_length') || data.containsKey('room_volume')) {
      inputMode = InputMode.dimensions;
    }
    
    // Get concentration
    final concentrationStr = data['concentration'] ?? 
        (agentType == AgentType.fm200 ? '7.4%' : '4.5%');
    
    // Convert system type - should be provided by user selection
    final systemTypeStr = data['system_type'];
    if (systemTypeStr == null) {
      throw Exception('System type must be specified by user - dialog should have asked for this');
    }

    SystemType systemType = SystemType.main;
    if (systemTypeStr == 'main_reserve') {
      systemType = SystemType.mainAndReserve;
    } else if (systemTypeStr == 'main_only') {
      systemType = SystemType.main;
    }
    
    // Convert installation type
    final installationTypeStr = data['installation_type'] ?? 'supply_only';
    final installationType = installationTypeStr == 'supply_install' 
        ? InstallationType.supplyAndInstall 
        : InstallationType.supplyOnly;
    
    return EstimatorFormValues(
      agentType: agentType,
      designConcentration: concentrationStr,
      inputMode: inputMode,
      roomLength: data['room_length']?.toDouble(),
      roomWidth: data['room_width']?.toDouble(), 
      roomHeight: data['room_height']?.toDouble() ?? 3.0,
      agentQuantity: data['agent_quantity']?.toDouble(),
      systemType: systemType,
      installationType: installationType,
    );
  }

  /// Get the AI's learned knowledge about your app
  Map<String, dynamic> getAppKnowledge() => _appKnowledge;
  
  /// Get discovered input mode capabilities
  Map<String, dynamic> getInputModeCapabilities() => _inputModeCapabilities;

  /// Detect language of input text (Arabic or English)
  String _detectLanguage(String text) {
    if (text.trim().isEmpty) return 'English';

    // Enhanced Arabic detection - check for Arabic characters
    final arabicRegex = RegExp(r'[\u0600-\u06FF\u0750-\u077F\u08A0-\u08FF\uFB50-\uFDFF\uFE70-\uFEFF]');
    final arabicCharCount = arabicRegex.allMatches(text).length;
    final totalChars = text.replaceAll(RegExp(r'\s+'), '').length;

    // If more than 20% of characters are Arabic, consider it Arabic
    if (totalChars > 0 && (arabicCharCount / totalChars) > 0.2) {
      return 'Arabic';
    }

    // Check for common Arabic words
    final arabicWords = [
      'احسب', 'حساب', 'تكلفة', 'سعر', 'تقدير', 'تصميم', 'نظام', 'كلفة',
      'عامل', 'نظيف', 'نوفيك', 'إطفاء', 'مطفئ', 'حريق', 'نار',
      'كيلو', 'كيلوجرام', 'وزن', 'كجم', 'متر', 'غرفة', 'مساحة',
      'مرحبا', 'أهلا', 'شكرا', 'نعم', 'لا', 'السلام', 'عليكم'
    ];

    final lowerText = text.toLowerCase();
    for (final word in arabicWords) {
      if (lowerText.contains(word)) {
        return 'Arabic';
      }
    }

    return 'English';
  }

  /// Normalize Arabic text for better processing
  String _normalizeArabicText(String text) {
    if (_detectLanguage(text) != 'Arabic') return text;

    String normalized = text;

    // Normalize Arabic numbers to English numbers
    final arabicToEnglish = {
      '٠': '0', '١': '1', '٢': '2', '٣': '3', '٤': '4',
      '٥': '5', '٦': '6', '٧': '7', '٨': '8', '٩': '9'
    };

    arabicToEnglish.forEach((arabic, english) {
      normalized = normalized.replaceAll(arabic, english);
    });

    // Normalize common Arabic terms to English equivalents for processing
    final arabicTerms = {
      'احسب': 'calculate',
      'حساب': 'calculate',
      'تكلفة': 'cost',
      'سعر': 'price',
      'تقدير': 'estimate',
      'تصميم': 'design',
      'نظام': 'system',
      'عامل نظيف': 'clean agent',
      'نوفيك': 'novec',
      'إطفاء': 'suppression',
      'مطفئ': 'suppression',
      'حريق': 'fire',
      'كيلو': 'kg',
      'كيلوجرام': 'kg',
      'وزن': 'weight',
      'متر': 'meter',
      'غرفة': 'room',
      'مساحة': 'area'
    };

    arabicTerms.forEach((arabic, english) {
      normalized = normalized.replaceAll(arabic, english);
    });

    return normalized;
  }
}
