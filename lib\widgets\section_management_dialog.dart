import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../models/isar_models.dart';
import '../providers/sidebar_provider.dart';

class SectionManagementDialog extends StatefulWidget {
  final SidebarSection? section; // For editing existing section
  final SidebarSection? parentSection; // For creating subsection
  
  const SectionManagementDialog({
    super.key,
    this.section,
    this.parentSection,
  });

  @override
  State<SectionManagementDialog> createState() => _SectionManagementDialogState();
}

class _SectionManagementDialogState extends State<SectionManagementDialog> {
  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  
  String _selectedIcon = 'folder';
  Color _selectedColor = Colors.blue;
  SystemType _selectedSystemType = SystemType.custom;
  bool _isLoading = false;

  final List<String> _availableIcons = [
    'folder',
    'alarm',
    'water_drop',
    'bubble_chart',
    'cloud',
    'air',
    'co2',
    'inventory',
    'table_chart',
    'category',
    'settings',
    'build',
  ];

  final List<Color> _availableColors = [
    Colors.blue,
    Colors.red,
    Colors.green,
    Colors.orange,
    Colors.purple,
    Colors.teal,
    Colors.indigo,
    Colors.pink,
    Colors.amber,
    Colors.cyan,
  ];

  @override
  void initState() {
    super.initState();
    if (widget.section != null) {
      // Editing existing section
      _nameController.text = widget.section!.name ?? '';
      _selectedIcon = widget.section!.icon ?? 'folder';
      _selectedColor = _parseColor(widget.section!.color) ?? Colors.blue;
      _selectedSystemType = widget.section!.systemType;
    }
  }

  @override
  void dispose() {
    _nameController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final isEditing = widget.section != null;
    final isSubsection = widget.parentSection != null;
    
    return AlertDialog(
      title: Text(
        isEditing 
            ? 'Edit Section'
            : isSubsection 
                ? 'Create Subsection'
                : 'Create Section',
      ),
      content: SizedBox(
        width: 400,
        child: Form(
          key: _formKey,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              if (isSubsection)
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: Theme.of(context).colorScheme.surfaceContainerHighest,
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Row(
                    children: [
                      Icon(
                        Icons.folder,
                        size: 16,
                        color: Theme.of(context).colorScheme.primary,
                      ),
                      const SizedBox(width: 8),
                      Text(
                        'Parent: ${widget.parentSection!.name}',
                        style: TextStyle(
                          color: Theme.of(context).colorScheme.primary,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ],
                  ),
                ),
              
              if (isSubsection) const SizedBox(height: 16),
              
              // Name field
              TextFormField(
                controller: _nameController,
                decoration: const InputDecoration(
                  labelText: 'Section Name',
                  border: OutlineInputBorder(),
                ),
                validator: (value) {
                  if (value == null || value.trim().isEmpty) {
                    return 'Please enter a section name';
                  }
                  return null;
                },
              ),
              
              const SizedBox(height: 16),
              
              // System Type (only for root sections)
              if (!isSubsection) ...[
                const Text(
                  'System Type',
                  style: TextStyle(fontWeight: FontWeight.bold),
                ),
                const SizedBox(height: 8),
                DropdownButtonFormField<SystemType>(
                  value: _selectedSystemType,
                  decoration: const InputDecoration(
                    border: OutlineInputBorder(),
                  ),
                  items: SystemType.values.map((type) {
                    return DropdownMenuItem(
                      value: type,
                      child: Text(_getSystemTypeDisplayName(type)),
                    );
                  }).toList(),
                  onChanged: (value) {
                    if (value != null) {
                      setState(() {
                        _selectedSystemType = value;
                      });
                    }
                  },
                ),
                const SizedBox(height: 16),
              ],
              
              // Icon selection
              const Text(
                'Icon',
                style: TextStyle(fontWeight: FontWeight.bold),
              ),
              const SizedBox(height: 8),
              Container(
                height: 60,
                decoration: BoxDecoration(
                  border: Border.all(color: Colors.grey.shade300),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: ListView.builder(
                  scrollDirection: Axis.horizontal,
                  padding: const EdgeInsets.symmetric(horizontal: 8),
                  itemCount: _availableIcons.length,
                  itemBuilder: (context, index) {
                    final icon = _availableIcons[index];
                    final isSelected = icon == _selectedIcon;
                    
                    return GestureDetector(
                      onTap: () => setState(() => _selectedIcon = icon),
                      child: Container(
                        width: 44,
                        height: 44,
                        margin: const EdgeInsets.symmetric(horizontal: 4, vertical: 8),
                        decoration: BoxDecoration(
                          color: isSelected 
                              ? _selectedColor.withOpacity(0.2)
                              : Colors.transparent,
                          border: Border.all(
                            color: isSelected 
                                ? _selectedColor
                                : Colors.transparent,
                            width: 2,
                          ),
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: Icon(
                          _getIconData(icon),
                          color: isSelected ? _selectedColor : Colors.grey.shade600,
                        ),
                      ),
                    );
                  },
                ),
              ),
              
              const SizedBox(height: 16),
              
              // Color selection
              const Text(
                'Color',
                style: TextStyle(fontWeight: FontWeight.bold),
              ),
              const SizedBox(height: 8),
              Container(
                height: 60,
                decoration: BoxDecoration(
                  border: Border.all(color: Colors.grey.shade300),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: ListView.builder(
                  scrollDirection: Axis.horizontal,
                  padding: const EdgeInsets.symmetric(horizontal: 8),
                  itemCount: _availableColors.length,
                  itemBuilder: (context, index) {
                    final color = _availableColors[index];
                    final isSelected = color == _selectedColor;
                    
                    return GestureDetector(
                      onTap: () => setState(() => _selectedColor = color),
                      child: Container(
                        width: 44,
                        height: 44,
                        margin: const EdgeInsets.symmetric(horizontal: 4, vertical: 8),
                        decoration: BoxDecoration(
                          color: color,
                          border: Border.all(
                            color: isSelected ? Colors.black : Colors.grey.shade300,
                            width: isSelected ? 3 : 1,
                          ),
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: isSelected
                            ? const Icon(
                                Icons.check,
                                color: Colors.white,
                                size: 20,
                              )
                            : null,
                      ),
                    );
                  },
                ),
              ),
              
              const SizedBox(height: 16),
              
              // Preview
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Theme.of(context).colorScheme.surfaceContainerHighest,
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Row(
                  children: [
                    Container(
                      width: 32,
                      height: 32,
                      decoration: BoxDecoration(
                        color: _selectedColor.withOpacity(0.1),
                        borderRadius: BorderRadius.circular(6),
                      ),
                      child: Icon(
                        _getIconData(_selectedIcon),
                        size: 20,
                        color: _selectedColor,
                      ),
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: Text(
                        _nameController.text.isEmpty 
                            ? 'Section Name Preview'
                            : _nameController.text,
                        style: TextStyle(
                          fontWeight: FontWeight.bold,
                          color: _nameController.text.isEmpty 
                              ? Colors.grey.shade500
                              : null,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
      actions: [
        TextButton(
          onPressed: _isLoading ? null : () => Navigator.of(context).pop(),
          child: const Text('Cancel'),
        ),
        ElevatedButton(
          onPressed: _isLoading ? null : _handleSave,
          child: _isLoading
              ? const SizedBox(
                  width: 16,
                  height: 16,
                  child: CircularProgressIndicator(strokeWidth: 2),
                )
              : Text(isEditing ? 'Update' : 'Create'),
        ),
      ],
    );
  }

  IconData _getIconData(String iconName) {
    switch (iconName) {
      case 'alarm': return Icons.notifications_active;
      case 'water_drop': return Icons.water_drop;
      case 'bubble_chart': return Icons.bubble_chart;
      case 'cloud': return Icons.cloud;
      case 'air': return Icons.air;
      case 'co2': return Icons.co2;
      case 'inventory': return Icons.inventory;
      case 'table_chart': return Icons.table_chart;
      case 'category': return Icons.category;
      case 'settings': return Icons.settings;
      case 'build': return Icons.build;
      case 'folder': return Icons.folder;
      default: return Icons.folder;
    }
  }

  String _getSystemTypeDisplayName(SystemType type) {
    switch (type) {
      case SystemType.alarm: return 'Fire Alarm Systems';
      case SystemType.water: return 'Water Systems';
      case SystemType.foam: return 'Foam Systems';
      case SystemType.cleanAgent: return 'Clean Agent Systems';
      case SystemType.co2: return 'CO2 Systems';
      case SystemType.materials: return 'Materials';
      case SystemType.custom: return 'Custom Section';
    }
  }

  Color? _parseColor(String? colorString) {
    if (colorString == null) return null;
    try {
      return Color(int.parse(colorString.replaceFirst('#', '0xFF')));
    } catch (e) {
      return null;
    }
  }

  String _colorToString(Color color) {
    return '#${color.value.toRadixString(16).substring(2).toUpperCase()}';
  }

  Future<void> _handleSave() async {
    if (!_formKey.currentState!.validate()) return;

    setState(() => _isLoading = true);

    try {
      final provider = Provider.of<SidebarProvider>(context, listen: false);
      
      if (widget.section != null) {
        // Update existing section
        await provider.updateSection(
          widget.section!.sectionId!,
          name: _nameController.text.trim(),
          icon: _selectedIcon,
          color: _colorToString(_selectedColor),
        );
      } else {
        // Create new section
        await provider.createSection(
          name: _nameController.text.trim(),
          icon: _selectedIcon,
          color: _colorToString(_selectedColor),
          parentSectionId: widget.parentSection?.sectionId,
          systemType: _selectedSystemType,
        );
      }

      if (mounted) {
        Navigator.of(context).pop();
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error: ${e.toString()}'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() => _isLoading = false);
      }
    }
  }
}
