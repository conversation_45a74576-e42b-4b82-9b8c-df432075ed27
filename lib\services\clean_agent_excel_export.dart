import 'dart:io';
import 'package:excel/excel.dart';
import 'package:file_selector/file_selector.dart';
import 'package:flutter/material.dart';
import '../models/clean_agent_system.dart';
import '../models/estimator_types.dart';
import '../utils/formatters.dart';

class CleanAgentExcelExport {
  static Future<void> exportSystems({
    required BuildContext context,
    required List<CleanAgentSystem> systems,
    required String projectName,
  }) async {
    try {
      // Create Excel workbook
      final excel = Excel.createExcel();
      
      // Remove default sheet
      excel.delete('Sheet1');
      
      // Create overview sheet
      await _createOverviewSheet(excel, systems, projectName);
      
      // Create detailed sheets for each system
      for (int i = 0; i < systems.length; i++) {
        final system = systems[i];
        await _createSystemDetailSheet(excel, system, i + 1);
      }
      
      // Save file
      await _saveExcelFile(context, excel, projectName);
      
    } catch (e) {
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Export failed: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  static Future<void> _createOverviewSheet(Excel excel, List<CleanAgentSystem> systems, String projectName) async {
    final sheet = excel['Overview'];
    
    // Title
    sheet.cell(CellIndex.indexByString('A1')).value = TextCellValue('$projectName - Clean Agent Systems Overview');
    sheet.merge(CellIndex.indexByString('A1'), CellIndex.indexByString('F1'));
    
    // Headers
    final headers = ['System Name', 'Agent Type', 'Room Size (L×W×H)', 'Quantity', 'Unit Cost (SAR)', 'Total Cost (SAR)'];
    for (int i = 0; i < headers.length; i++) {
      final cell = sheet.cell(CellIndex.indexByColumnRow(columnIndex: i, rowIndex: 2));
      cell.value = TextCellValue(headers[i]);
      cell.cellStyle = CellStyle(
        bold: true,
        backgroundColorHex: ExcelColor.orange100,
      );
    }
    
    // Data rows
    int row = 3;
    double totalCost = 0;
    int totalQuantity = 0;
    
    for (final system in systems) {
      final systemTotalCost = system.totalCost * system.quantity;
      totalCost += systemTotalCost;
      totalQuantity += system.quantity;
      
      sheet.cell(CellIndex.indexByColumnRow(columnIndex: 0, rowIndex: row)).value = TextCellValue(system.name);
      sheet.cell(CellIndex.indexByColumnRow(columnIndex: 1, rowIndex: row)).value = TextCellValue(system.agentType.name.toUpperCase());
      sheet.cell(CellIndex.indexByColumnRow(columnIndex: 2, rowIndex: row)).value = TextCellValue('${system.roomLength}×${system.roomWidth}×${system.roomHeight}m');
      sheet.cell(CellIndex.indexByColumnRow(columnIndex: 3, rowIndex: row)).value = IntCellValue(system.quantity);
      sheet.cell(CellIndex.indexByColumnRow(columnIndex: 4, rowIndex: row)).value = DoubleCellValue(system.totalCost.roundToDouble());
      sheet.cell(CellIndex.indexByColumnRow(columnIndex: 5, rowIndex: row)).value = DoubleCellValue(systemTotalCost.roundToDouble());
      
      row++;
    }
    
    // Total row
    row++;
    sheet.cell(CellIndex.indexByColumnRow(columnIndex: 0, rowIndex: row)).value = TextCellValue('TOTAL');
    sheet.cell(CellIndex.indexByColumnRow(columnIndex: 3, rowIndex: row)).value = IntCellValue(totalQuantity);
    sheet.cell(CellIndex.indexByColumnRow(columnIndex: 5, rowIndex: row)).value = DoubleCellValue(totalCost.roundToDouble());
    
    // Style total row
    for (int i = 0; i < 6; i++) {
      final cell = sheet.cell(CellIndex.indexByColumnRow(columnIndex: i, rowIndex: row));
      cell.cellStyle = CellStyle(
        bold: true,
        backgroundColorHex: ExcelColor.orange200,
      );
    }
  }

  static Future<void> _createSystemDetailSheet(Excel excel, CleanAgentSystem system, int systemNumber) async {
    final sheetName = 'System $systemNumber - ${system.name}';
    final sheet = excel[sheetName];
    
    int currentRow = 0;
    
    // System Information Section
    sheet.cell(CellIndex.indexByColumnRow(columnIndex: 0, rowIndex: currentRow)).value = TextCellValue('SYSTEM INFORMATION');
    sheet.cell(CellIndex.indexByColumnRow(columnIndex: 0, rowIndex: currentRow)).cellStyle = CellStyle(bold: true, fontSize: 14);
    currentRow += 2;
    
    final systemInfo = [
      ['System Name:', system.name],
      ['Agent Type:', system.agentType.name.toUpperCase()],
      ['Room Dimensions:', '${system.roomLength} × ${system.roomWidth} × ${system.roomHeight} m'],
      ['Room Volume:', '${system.roomVolume.toStringAsFixed(2)} m³'],
      ['Design Concentration:', '${(system.designConcentration * 100).toStringAsFixed(1)}%'],
      ['Agent Required:', '${system.agentRequired.toStringAsFixed(1)} kg'],
      ['Actual Agent:', '${system.actualAgent.toStringAsFixed(1)} kg'],
      ['Cylinders:', '${system.cylinderQty} × ${system.cylinderSize}L'],
      ['Nozzles:', '${system.nozzleQty} × ${system.nozzleSize}mm'],
      ['Quantity:', system.quantity.toString()],
      ['Unit Cost:', formatCurrencySAR(system.totalCost)],
      ['Total Cost:', formatCurrencySAR(system.totalCost * system.quantity)],
    ];
    
    for (final info in systemInfo) {
      sheet.cell(CellIndex.indexByColumnRow(columnIndex: 0, rowIndex: currentRow)).value = TextCellValue(info[0]);
      sheet.cell(CellIndex.indexByColumnRow(columnIndex: 1, rowIndex: currentRow)).value = TextCellValue(info[1]);
      sheet.cell(CellIndex.indexByColumnRow(columnIndex: 0, rowIndex: currentRow)).cellStyle = CellStyle(bold: true);
      currentRow++;
    }
    
    currentRow += 2;
    
    // Cost Breakdown Section
    sheet.cell(CellIndex.indexByColumnRow(columnIndex: 0, rowIndex: currentRow)).value = TextCellValue('COST BREAKDOWN');
    sheet.cell(CellIndex.indexByColumnRow(columnIndex: 0, rowIndex: currentRow)).cellStyle = CellStyle(bold: true, fontSize: 14);
    currentRow += 2;
    
    final costBreakdown = [
      ['Suppression System (USD):', formatCurrencyUSD(system.suppressionCost)],
      ['Alarm & Detection (USD):', formatCurrencyUSD(system.alarmCost)],
      ['Installation Materials (SAR):', formatCurrencySAR(system.installationMaterialsCost)],
      ['Installation Labor (SAR):', formatCurrencySAR(system.installationLaborCost)],
      ['Total System Cost (SAR):', formatCurrencySAR(system.totalCost)],
    ];
    
    for (final cost in costBreakdown) {
      sheet.cell(CellIndex.indexByColumnRow(columnIndex: 0, rowIndex: currentRow)).value = TextCellValue(cost[0]);
      sheet.cell(CellIndex.indexByColumnRow(columnIndex: 1, rowIndex: currentRow)).value = TextCellValue(cost[1]);
      sheet.cell(CellIndex.indexByColumnRow(columnIndex: 0, rowIndex: currentRow)).cellStyle = CellStyle(bold: true);
      currentRow++;
    }
    
    currentRow += 2;

    // Detailed BOM Section
    if (system.bomItems.isNotEmpty) {
      sheet.cell(CellIndex.indexByColumnRow(columnIndex: 0, rowIndex: currentRow)).value = TextCellValue('DETAILED BILL OF MATERIALS');
      sheet.cell(CellIndex.indexByColumnRow(columnIndex: 0, rowIndex: currentRow)).cellStyle = CellStyle(bold: true, fontSize: 14);
      currentRow += 2;

      // BOM Headers
      final bomHeaders = ['Part No.', 'Description', 'Quantity', 'Unit Cost', 'Currency', 'Total Cost'];
      for (int i = 0; i < bomHeaders.length; i++) {
        final cell = sheet.cell(CellIndex.indexByColumnRow(columnIndex: i, rowIndex: currentRow));
        cell.value = TextCellValue(bomHeaders[i]);
        cell.cellStyle = CellStyle(
          bold: true,
          backgroundColorHex: ExcelColor.lightBlue100,
        );
      }
      currentRow++;

      // BOM Items
      for (final itemData in system.bomItems) {
        final item = BomItem.fromJson(itemData);
        sheet.cell(CellIndex.indexByColumnRow(columnIndex: 0, rowIndex: currentRow)).value = TextCellValue(item.partNo);
        sheet.cell(CellIndex.indexByColumnRow(columnIndex: 1, rowIndex: currentRow)).value = TextCellValue(item.description);
        sheet.cell(CellIndex.indexByColumnRow(columnIndex: 2, rowIndex: currentRow)).value = IntCellValue(item.quantity);
        sheet.cell(CellIndex.indexByColumnRow(columnIndex: 3, rowIndex: currentRow)).value = DoubleCellValue(item.unitCost.roundToDouble());
        sheet.cell(CellIndex.indexByColumnRow(columnIndex: 4, rowIndex: currentRow)).value = TextCellValue(item.currency ?? 'USD');
        sheet.cell(CellIndex.indexByColumnRow(columnIndex: 5, rowIndex: currentRow)).value = DoubleCellValue(item.totalCost.roundToDouble());
        currentRow++;
      }
    } else {
      // Note about missing BOM data
      sheet.cell(CellIndex.indexByColumnRow(columnIndex: 0, rowIndex: currentRow)).value = TextCellValue('Note: Detailed BOM data not available for this system.');
      sheet.cell(CellIndex.indexByColumnRow(columnIndex: 0, rowIndex: currentRow)).cellStyle = CellStyle(italic: true);
      currentRow++;

      sheet.cell(CellIndex.indexByColumnRow(columnIndex: 0, rowIndex: currentRow)).value = TextCellValue('This system was created before BOM data saving was implemented.');
      sheet.cell(CellIndex.indexByColumnRow(columnIndex: 0, rowIndex: currentRow)).cellStyle = CellStyle(italic: true);
    }
  }

  static Future<void> _saveExcelFile(BuildContext context, Excel excel, String projectName) async {
    try {
      // Suggest filename
      final fileName = '${projectName}_CleanAgent_Systems_${DateTime.now().millisecondsSinceEpoch}.xlsx';
      
      // Get save location
      final location = await getSaveLocation(
        suggestedName: fileName,
        acceptedTypeGroups: [
          const XTypeGroup(
            label: 'Excel files',
            extensions: ['xlsx'],
          ),
        ],
      );
      
      if (location != null) {
        // Save file
        final bytes = excel.save();
        if (bytes != null) {
          final file = File(location.path);
          await file.writeAsBytes(bytes);
          
          if (context.mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text('Excel file exported successfully to ${location.path}'),
                backgroundColor: Colors.green,
                duration: const Duration(seconds: 3),
              ),
            );
          }
        }
      }
    } catch (e) {
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to save file: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }
}
