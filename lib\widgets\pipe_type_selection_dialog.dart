import 'package:flutter/material.dart';
import '../models/project.dart';
import '../services/dynamic_schema_service.dart';
import 'pipe_item_selection_dialog.dart';

class PipeTypeSelectionDialog extends StatefulWidget {
  final Function(MaterialItem) onItemSelected;

  const PipeTypeSelectionDialog({
    super.key,
    required this.onItemSelected,
  });

  @override
  State<PipeTypeSelectionDialog> createState() => _PipeTypeSelectionDialogState();
}

class _PipeTypeSelectionDialogState extends State<PipeTypeSelectionDialog> {
  List<Map<String, dynamic>> _pipeTypes = [];
  bool _isLoading = true;
  String? _selectedPipeType;
  
  @override
  void initState() {
    super.initState();
    _loadPipeTypes();
  }

  Future<void> _loadPipeTypes() async {
    try {
      final schemaService = DynamicSchemaService.instance;

      // Get all sections to find piping-related tables
      final sections = await schemaService.getAllSections();

      List<Map<String, dynamic>> pipeTypes = [];

      for (final section in sections) {
        // Look for sections that contain pipe data
        if (section.name?.toLowerCase().contains('pipe') == true ||
            section.name?.toLowerCase().contains('piping') == true) {

          final tables = await schemaService.getTablesForSection(section.sectionId!);

          for (final table in tables) {
            // Look for pipe type tables (like "Interpipe Black Seamless", "Shield Seamless", etc.)
            if (table.name != null &&
                (table.name!.toLowerCase().contains('interpipe') ||
                 table.name!.toLowerCase().contains('shield') ||
                 table.name!.toLowerCase().contains('seamless') ||
                 table.name!.toLowerCase().contains('pipe'))) {

              // This is a pipe type - add it to the list
              pipeTypes.add({
                'typeName': table.name!,
                'description': 'Select items from ${table.name}',
                'tableId': table.tableId!,
                'sectionId': section.sectionId!,
                'category': 'Pipe Type',
              });
            }
          }
        }
      }

      setState(() {
        _pipeTypes = pipeTypes;
        _isLoading = false;
      });
    } catch (e) {
      print('Error loading pipe types: $e');
      setState(() {
        _isLoading = false;
      });
    }
  }

  void _selectPipeType(Map<String, dynamic> pipeType) {
    // Close this dialog and open the searchable item dialog for this specific pipe type
    Navigator.of(context).pop();

    // Show searchable dialog for the selected pipe type table
    showDialog(
      context: context,
      builder: (context) => PipeItemSelectionDialog(
        pipeTypeName: pipeType['typeName']?.toString() ?? 'Unknown',
        tableId: pipeType['tableId']?.toString() ?? '',
        sectionId: pipeType['sectionId']?.toString() ?? '',
        onItemSelected: widget.onItemSelected,
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      child: Container(
        width: 500,
        height: 600,
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
            // Header
            Row(
              children: [
                const Icon(Icons.plumbing, color: Colors.blue),
                const SizedBox(width: 8),
                const Text(
                  'Select Pipe Type',
                  style: TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const Spacer(),
                IconButton(
                  onPressed: () => Navigator.of(context).pop(),
                  icon: const Icon(Icons.close),
                ),
              ],
            ),
            const SizedBox(height: 16),
            
            // Content
            Expanded(
              child: _isLoading
                  ? const Center(child: CircularProgressIndicator())
                  : _pipeTypes.isEmpty
                      ? const Center(
                          child: Column(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Icon(Icons.error_outline, size: 64, color: Colors.grey),
                              SizedBox(height: 16),
                              Text(
                                'No pipe data found in database',
                                style: TextStyle(fontSize: 16, color: Colors.grey),
                              ),
                              SizedBox(height: 8),
                              Text(
                                'Please check your database configuration',
                                style: TextStyle(fontSize: 14, color: Colors.grey),
                              ),
                            ],
                          ),
                        )
                      : ListView.builder(
                          itemCount: _pipeTypes.length,
                          itemBuilder: (context, index) {
                            final pipeType = _pipeTypes[index];
                            return Card(
                              margin: const EdgeInsets.symmetric(vertical: 4),
                              child: ListTile(
                                leading: Container(
                                  padding: const EdgeInsets.all(8),
                                  decoration: BoxDecoration(
                                    color: Colors.blue.shade50,
                                    borderRadius: BorderRadius.circular(8),
                                  ),
                                  child: const Icon(
                                    Icons.plumbing,
                                    color: Colors.blue,
                                    size: 24,
                                  ),
                                ),
                                title: Text(
                                  pipeType['typeName']?.toString() ?? 'Unknown',
                                  style: const TextStyle(
                                    fontWeight: FontWeight.bold,
                                    fontSize: 16,
                                  ),
                                ),
                                subtitle: Text(
                                  pipeType['description']?.toString() ?? '',
                                  style: const TextStyle(
                                    fontSize: 14,
                                    color: Colors.grey,
                                  ),
                                ),
                                trailing: const Icon(Icons.arrow_forward_ios, color: Colors.blue),
                                onTap: () => _selectPipeType(pipeType),
                              ),
                            );
                          },
                        ),
            ),
          ],
        ),
      ),
    );
  }
}
