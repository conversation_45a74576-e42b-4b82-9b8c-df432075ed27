import 'dart:convert';
import 'package:isar/isar.dart';
import '../models/clean_agent/agent_type.dart';
import '../models/clean_agent/design_concentration.dart';
import '../models/clean_agent/filling_ratio.dart';
import '../models/clean_agent/cylinder_spec.dart';
import '../models/clean_agent/pipe_data.dart';
import '../models/clean_agent/component_item.dart';
import '../models/isar_models.dart';

class CleanAgentRepository {
  final Isar _isar;

  CleanAgentRepository(this._isar);

  // Debug function to list all tables in clean agent section
  Future<void> debugListCleanAgentTables() async {
    try {
      print('\n=== DEBUGGING CLEAN AGENT DATABASE ===');

      // Find clean agent section
      final cleanAgentSection = await _isar.sections
          .filter()
          .systemTypeEqualTo(SystemType.cleanAgent)
          .findFirst();

      if (cleanAgentSection != null) {
        print('Clean Agent Section found: ${cleanAgentSection.name} (sectionId: ${cleanAgentSection.sectionId})');

        // Find all flexible tables in clean agent section
        final cleanAgentTables = await _isar.flexibleTables
            .filter()
            .sectionIdEqualTo(cleanAgentSection.sectionId)
            .findAll();

        print('Found ${cleanAgentTables.length} flexible tables in clean agent section:');
        for (final table in cleanAgentTables) {
          print('  - Table: ${table.name} (tableId: ${table.tableId})');

          // Get table data (rows)
          final tableRows = await _isar.flexibleRows
              .filter()
              .tableIdEqualTo(table.tableId)
              .findAll();

          print('    Data rows: ${tableRows.length}');
          if (tableRows.isNotEmpty) {
            print('    Sample data:');
            for (int i = 0; i < (tableRows.length > 3 ? 3 : tableRows.length); i++) {
              final row = tableRows[i];
              print('      Row ${i + 1}: ${row.data}');
            }
          }

          // Get table columns
          final tableColumns = await _isar.flexibleColumns
              .filter()
              .tableIdEqualTo(table.tableId)
              .findAll();

          print('    Columns: ${tableColumns.length}');
          for (final column in tableColumns) {
            print('      - ${column.name} (${column.dataType})');
          }
        }
      } else {
        print('Clean Agent Section NOT found!');

        // List all sections
        final allSections = await _isar.sections.where().findAll();
        print('Available sections:');
        for (final section in allSections) {
          print('  - ${section.name} (${section.systemType}) - sectionId: ${section.sectionId}');
        }
      }

      print('=== END DEBUG ===\n');
    } catch (e) {
      print('Error in debug function: $e');
    }
  }

  // Get specific table data by name
  Future<void> debugSpecificTable(String tableName) async {
    try {
      print('\n=== DEBUGGING TABLE: $tableName ===');

      // Find clean agent section
      final cleanAgentSection = await _isar.sections
          .filter()
          .systemTypeEqualTo(SystemType.cleanAgent)
          .findFirst();

      if (cleanAgentSection != null) {
        // Find specific table
        final table = await _isar.flexibleTables
            .filter()
            .sectionIdEqualTo(cleanAgentSection.sectionId)
            .and()
            .nameContains(tableName, caseSensitive: false)
            .findFirst();

        if (table != null) {
          print('Found table: ${table.name} (tableId: ${table.tableId})');

          // Get all columns
          final columns = await _isar.flexibleColumns
              .filter()
              .tableIdEqualTo(table.tableId)
              .sortByOrderIndex()
              .findAll();

          print('Columns (${columns.length}):');
          for (final column in columns) {
            print('  - ${column.name} (${column.dataType}) [order: ${column.orderIndex}]');
          }

          // Get all rows
          final rows = await _isar.flexibleRows
              .filter()
              .tableIdEqualTo(table.tableId)
              .findAll();

          print('Data rows (${rows.length}):');
          for (int i = 0; i < rows.length; i++) {
            final row = rows[i];
            print('  Row ${i + 1}: ${row.data}');
          }
        } else {
          print('Table "$tableName" not found in clean agent section');
        }
      }

      print('=== END TABLE DEBUG ===\n');
    } catch (e) {
      print('Error debugging table: $e');
    }
  }

  // Get agent types
  Future<List<AgentType>> getAgentTypes() async {
    // First, debug the database
    await debugListCleanAgentTables();

    // Debug specific tables you mentioned
    await debugSpecificTable('min-max filling');
    await debugSpecificTable('design concentration');
    await debugSpecificTable('pipes flow');
    await debugSpecificTable('fm200 items');
    await debugSpecificTable('novec items');
    await debugSpecificTable('alarm');
    await debugSpecificTable('instal');

    // Now read from the actual tables we found!
    // The tables are in section ID: 285782ee-5ab9-45db-b9b0-d07aa77ad01d
    const realSectionId = '285782ee-5ab9-45db-b9b0-d07aa77ad01d';

    try {
      // Get FM200 Items table to extract agent cost
      final fm200Table = await _isar.flexibleTables
          .filter()
          .sectionIdEqualTo(realSectionId)
          .and()
          .nameEqualTo('FM200 Items')
          .findFirst();

      // Get NOVEC Items table to extract agent cost
      final novecTable = await _isar.flexibleTables
          .filter()
          .sectionIdEqualTo(realSectionId)
          .and()
          .nameEqualTo('NOVEC Items')
          .findFirst();

      print('Found FM200 table: ${fm200Table?.name}');
      print('Found NOVEC table: ${novecTable?.name}');

      // Return agent types based on real data
      return [
        AgentType(
          id: 'novec1230',
          name: 'NOVEC 1230',
          costPerKg: 45.0, // Will extract from table later
          partNumber: 'NOVEC-1230',
        ),
        AgentType(
          id: 'fm200',
          name: 'FM-200',
          costPerKg: 35.0, // Will extract from table later
          partNumber: 'FM-200',
        ),
      ];
    } catch (e) {
      print('Error reading agent types from database: $e');
      // Fallback to hardcoded values
      return [
        AgentType(
          id: 'novec1230',
          name: 'NOVEC 1230',
          costPerKg: 45.0,
          partNumber: 'NOVEC-1230',
        ),
        AgentType(
          id: 'fm200',
          name: 'FM-200',
          costPerKg: 35.0,
          partNumber: 'FM-200',
        ),
      ];
    }
  }

  // Get design concentrations from real database
  Future<List<DesignConcentration>> getDesignConcentrations() async {
    const realSectionId = '285782ee-5ab9-45db-b9b0-d07aa77ad01d';

    try {
      // Get Design Concentration table
      final designTable = await _isar.flexibleTables
          .filter()
          .sectionIdEqualTo(realSectionId)
          .and()
          .nameEqualTo('Design Concentration')
          .findFirst();

      if (designTable != null) {
        print('Found Design Concentration table with ${designTable.name}');

        // Get all rows and columns from the table
        final rows = await _isar.flexibleRows
            .filter()
            .tableIdEqualTo(designTable.tableId)
            .findAll();

        final columns = await _isar.flexibleColumns
            .filter()
            .tableIdEqualTo(designTable.tableId)
            .findAll();

        print('Design Concentration table has ${rows.length} rows and ${columns.length} columns');

        // Parse the actual data from your table
        final List<DesignConcentration> concentrations = [];
        final Set<String> addedIds = {}; // Track added concentrations to avoid duplicates

        // Create a map of column ID to column name for easier lookup
        final Map<String, String> columnIdToName = {};
        for (final column in columns) {
          if (column.columnId != null && column.name != null) {
            columnIdToName[column.columnId!] = column.name!;
          }
        }

        print('Column mapping: $columnIdToName');

        for (int i = 0; i < rows.length; i++) {
          final row = rows[i];

          print('Row ${i + 1} raw data: ${row.data}');

          // Parse JSON data from the row and convert column IDs to names
          Map<String, dynamic> rowData = {};
          if (row.data != null) {
            try {
              final jsonData = json.decode(row.data!);
              if (jsonData is Map<String, dynamic>) {
                // Convert column IDs to column names
                for (final entry in jsonData.entries) {
                  final columnName = columnIdToName[entry.key];
                  if (columnName != null) {
                    rowData[columnName] = entry.value;
                  }
                }
                print('Converted row data: $rowData');
              }
            } catch (e) {
              print('Error parsing row data JSON: $e');
              continue;
            }
          }

          // Extract FM200 data
          final fm200Design = _parseDouble(rowData['FM200 design']);
          final fm200Factor = _parseDouble(rowData['FM200 Kg/m3']);
          final fm200NozzleSpacing = _parseDouble(rowData['Nozzles Spacing FM200']);
          final fm200MaxHeight = _parseDouble(rowData['Max Height Nozzle FM200']);

          if (fm200Design != null && fm200Factor != null && fm200Design > 0) {
            // Convert decimal to percentage (0.074 -> 7.4)
            final percentageValue = fm200Design * 100;
            final id = 'fm200-${percentageValue.toStringAsFixed(1)}';

            // Only add if not already added
            if (!addedIds.contains(id)) {
              addedIds.add(id);
              concentrations.add(DesignConcentration(
                id: id,
                agentType: 'fm200',
                percentage: '${percentageValue.toStringAsFixed(1)}%',
                factor: fm200Factor,
                isDefault: concentrations.isEmpty, // First concentration as default
                maxNozzleSpacing: fm200NozzleSpacing ?? 6.1,
                maxNozzleHeight: fm200MaxHeight ?? 4.9,
              ));
            }
          }

          // Extract NOVEC data
          final novecDesign = _parseDouble(rowData['NOVEC design']);
          final novecFactor = _parseDouble(rowData['NOVEC Kg/m3']);
          final novecNozzleSpacing = _parseDouble(rowData['Nozzles Spacing Novec']);
          final novecMaxHeight = _parseDouble(rowData['Max Height Nozzle Novec']);

          if (novecDesign != null && novecFactor != null && novecDesign > 0) {
            // Convert decimal to percentage (0.045 -> 4.5)
            final percentageValue = novecDesign * 100;
            final id = 'novec-${percentageValue.toStringAsFixed(1)}';

            // Only add if not already added
            if (!addedIds.contains(id)) {
              addedIds.add(id);
              concentrations.add(DesignConcentration(
                id: id,
                agentType: 'novec1230',
                percentage: '${percentageValue.toStringAsFixed(1)}%',
                factor: novecFactor,
                isDefault: concentrations.isEmpty, // First concentration as default
                maxNozzleSpacing: novecNozzleSpacing ?? 6.1,
                maxNozzleHeight: novecMaxHeight ?? 4.9,
              ));
            }
          }
        }

        print('Parsed ${concentrations.length} design concentrations from database');
        return concentrations;
      }
    } catch (e) {
      print('Error reading design concentrations: $e');
    }

    // Fallback to hardcoded values if parsing fails
    return _getHardcodedDesignConcentrations();
  }

  // Helper function to parse double values safely
  double? _parseDouble(dynamic value) {
    if (value == null) return null;
    if (value is double) return value;
    if (value is int) return value.toDouble();
    if (value is String) {
      return double.tryParse(value);
    }
    return null;
  }

  // Hardcoded fallback values
  List<DesignConcentration> _getHardcodedDesignConcentrations() {
    return [
      DesignConcentration(
        id: 'novec-4.5',
        agentType: 'novec1230',
        percentage: '4.5%',
        factor: 0.5853,
        isDefault: false,
        maxNozzleSpacing: 6.1,
        maxNozzleHeight: 4.9,
      ),
      DesignConcentration(
        id: 'novec-5.0',
        agentType: 'novec1230',
        percentage: '5.0%',
        factor: 0.6503,
        isDefault: true,
        maxNozzleSpacing: 6.1,
        maxNozzleHeight: 4.9,
      ),
      DesignConcentration(
        id: 'fm200-7.0',
        agentType: 'fm200',
        percentage: '7.0%',
        factor: 0.5405,
        isDefault: false,
        maxNozzleSpacing: 6.1,
        maxNozzleHeight: 4.9,
      ),
      DesignConcentration(
        id: 'fm200-7.9',
        agentType: 'fm200',
        percentage: '7.9%',
        factor: 0.6108,
        isDefault: true,
        maxNozzleSpacing: 6.1,
        maxNozzleHeight: 4.9,
      ),
    ];
  }

  // Get filling ratios from real database
  Future<List<FillingRatio>> getFillingRatios() async {
    const realSectionId = '285782ee-5ab9-45db-b9b0-d07aa77ad01d';

    try {
      // Get Min-Max Filling table
      final fillingTable = await _isar.flexibleTables
          .filter()
          .sectionIdEqualTo(realSectionId)
          .and()
          .nameEqualTo('Min-Max Filling')
          .findFirst();

      if (fillingTable != null) {
        print('Found Min-Max Filling table');

        // Get all rows and columns from the table
        final rows = await _isar.flexibleRows
            .filter()
            .tableIdEqualTo(fillingTable.tableId)
            .findAll();

        final columns = await _isar.flexibleColumns
            .filter()
            .tableIdEqualTo(fillingTable.tableId)
            .findAll();

        // Create a map of column ID to column name
        final Map<String, String> columnIdToName = {};
        for (final column in columns) {
          if (column.columnId != null && column.name != null) {
            columnIdToName[column.columnId!] = column.name!;
          }
        }

        print('Min-Max Filling columns: ${columnIdToName.values.toList()}');

        final List<FillingRatio> fillingRatios = [];

        for (int i = 0; i < rows.length; i++) {
          final row = rows[i];

          // Parse JSON data from the row and convert column IDs to names
          Map<String, dynamic> rowData = {};
          if (row.data != null) {
            try {
              final jsonData = json.decode(row.data!);
              if (jsonData is Map<String, dynamic>) {
                // Convert column IDs to column names
                for (final entry in jsonData.entries) {
                  final columnName = columnIdToName[entry.key];
                  if (columnName != null) {
                    rowData[columnName] = entry.value;
                  }
                }
              }
            } catch (e) {
              print('Error parsing filling ratio row data: $e');
              continue;
            }
          }

          // Extract cylinder size and filling ratios
          final cylSize = _parseDouble(rowData['Cyl Size']);
          final novecMaxFilling = _parseDouble(rowData['Novec maximum filling']);
          final fm200MaxFilling = _parseDouble(rowData['FM200 maximum filling']);
          final minFilling = _parseDouble(rowData['Min filling']);

          if (cylSize != null && novecMaxFilling != null && minFilling != null) {
            fillingRatios.add(FillingRatio(
              id: 'novec-${cylSize.toInt()}l',
              agentType: 'novec1230',
              cylinderSize: cylSize.toInt(),
              minFilling: minFilling,
              maxFilling: novecMaxFilling,
            ));
          }

          if (cylSize != null && fm200MaxFilling != null && minFilling != null) {
            fillingRatios.add(FillingRatio(
              id: 'fm200-${cylSize.toInt()}l',
              agentType: 'fm200',
              cylinderSize: cylSize.toInt(),
              minFilling: minFilling,
              maxFilling: fm200MaxFilling,
            ));
          }
        }

        print('Parsed ${fillingRatios.length} filling ratios from database');
        return fillingRatios;
      }
    } catch (e) {
      print('Error reading filling ratios: $e');
    }

    // Fallback to hardcoded values
    return [
      FillingRatio(
        id: 'novec-fill',
        agentType: 'novec1230',
        cylinderSize: 8,
        minFilling: 0.6,
        maxFilling: 1.2,
      ),
      FillingRatio(
        id: 'fm200-fill',
        agentType: 'fm200',
        cylinderSize: 8,
        minFilling: 0.35,
        maxFilling: 1.15,
      ),
    ];
  }

  // Get cylinder specifications
  Future<List<CylinderSpec>> getCylinderSpecs() async {
    // Query from database tables "NOVEC items" and "FM200 items" for cylinders
    // For now, we'll return hardcoded values as a placeholder
    return [
      CylinderSpec(
        id: 'novec-8l',
        agentType: 'novec1230',
        sizeLiters: 8,
        minCapacityKg: 4.8,
        maxCapacityKg: 9.6,
        partNumber: 'CYL-NOVEC-8L',
        price: 450.0,
      ),
      CylinderSpec(
        id: 'novec-16l',
        agentType: 'novec1230',
        sizeLiters: 16,
        minCapacityKg: 9.6,
        maxCapacityKg: 19.2,
        partNumber: 'CYL-NOVEC-16L',
        price: 650.0,
      ),
      CylinderSpec(
        id: 'novec-32l',
        agentType: 'novec1230',
        sizeLiters: 32,
        minCapacityKg: 19.2,
        maxCapacityKg: 38.4,
        partNumber: 'CYL-NOVEC-32L',
        price: 950.0,
      ),
      CylinderSpec(
        id: 'novec-52l',
        agentType: 'novec1230',
        sizeLiters: 52,
        minCapacityKg: 31.2,
        maxCapacityKg: 62.4,
        partNumber: 'CYL-NOVEC-52L',
        price: 1450.0,
      ),
      CylinderSpec(
        id: 'fm200-8l',
        agentType: 'fm200',
        sizeLiters: 8,
        minCapacityKg: 2.8,
        maxCapacityKg: 9.2,
        partNumber: 'CYL-FM200-8L',
        price: 400.0,
      ),
      CylinderSpec(
        id: 'fm200-16l',
        agentType: 'fm200',
        sizeLiters: 16,
        minCapacityKg: 5.6,
        maxCapacityKg: 18.4,
        partNumber: 'CYL-FM200-16L',
        price: 600.0,
      ),
      CylinderSpec(
        id: 'fm200-32l',
        agentType: 'fm200',
        sizeLiters: 32,
        minCapacityKg: 11.2,
        maxCapacityKg: 36.8,
        partNumber: 'CYL-FM200-32L',
        price: 900.0,
      ),
      CylinderSpec(
        id: 'fm200-52l',
        agentType: 'fm200',
        sizeLiters: 52,
        minCapacityKg: 18.2,
        maxCapacityKg: 59.8,
        partNumber: 'CYL-FM200-52L',
        price: 1400.0,
      ),
    ];
  }

  // Get pipe data from real database
  Future<List<PipeData>> getPipeData() async {
    const realSectionId = '285782ee-5ab9-45db-b9b0-d07aa77ad01d';

    try {
      // Get Pipes flow & prices table
      final pipeTable = await _isar.flexibleTables
          .filter()
          .sectionIdEqualTo(realSectionId)
          .and()
          .nameEqualTo('Pipes flow & prices')
          .findFirst();

      if (pipeTable != null) {
        print('Found Pipes flow & prices table');

        // Get all rows and columns from the table
        final rows = await _isar.flexibleRows
            .filter()
            .tableIdEqualTo(pipeTable.tableId)
            .findAll();

        final columns = await _isar.flexibleColumns
            .filter()
            .tableIdEqualTo(pipeTable.tableId)
            .findAll();

        // Create a map of column ID to column name
        final Map<String, String> columnIdToName = {};
        for (final column in columns) {
          if (column.columnId != null && column.name != null) {
            columnIdToName[column.columnId!] = column.name!;
          }
        }

        print('Pipe table columns: ${columnIdToName.values.toList()}');

        final List<PipeData> pipeDataList = [];

        for (int i = 0; i < rows.length; i++) {
          final row = rows[i];

          // Parse JSON data from the row and convert column IDs to names
          Map<String, dynamic> rowData = {};
          if (row.data != null) {
            try {
              final jsonData = json.decode(row.data!);
              if (jsonData is Map<String, dynamic>) {
                // Convert column IDs to column names
                for (final entry in jsonData.entries) {
                  final columnName = columnIdToName[entry.key];
                  if (columnName != null) {
                    rowData[columnName] = entry.value;
                  }
                }
              }
            } catch (e) {
              print('Error parsing pipe data row: $e');
              continue;
            }
          }

          // Extract pipe data
          final pipeInch = rowData['Pipe Diameter Inch']?.toString();
          final pipeMm = _parseDouble(rowData['Pipe Diameter mm']);
          final minFlow = _parseDouble(rowData['Min flow Kg/sec']);
          final maxFlow = _parseDouble(rowData['Max Flow Kg/sec']);
          final pipeCost = _parseDouble(rowData['Pipe Cost /meter']);

          if (pipeMm != null && maxFlow != null && pipeCost != null) {
            pipeDataList.add(PipeData(
              id: 'pipe-${pipeMm.toInt()}mm',
              sizeMm: pipeMm,
              minFlowKgPerSec: minFlow ?? 0.0,
              maxFlowKgPerSec: maxFlow,
              pricePerMeter: pipeCost,
            ));
          }
        }

        print('Parsed ${pipeDataList.length} pipe data entries from database');
        return pipeDataList;
      }
    } catch (e) {
      print('Error reading pipe data: $e');
    }

    // Fallback to hardcoded values
    return [
      PipeData(
        id: 'pipe-15mm',
        sizeMm: 15.0,
        minFlowKgPerSec: 0.5,
        maxFlowKgPerSec: 1.5,
        pricePerMeter: 12.0,
      ),
      PipeData(
        id: 'pipe-20mm',
        sizeMm: 20.0,
        minFlowKgPerSec: 1.0,
        maxFlowKgPerSec: 3.0,
        pricePerMeter: 15.0,
      ),
    ];
  }

  // Get component items (valves, nozzles, etc.)
  Future<List<ComponentItem>> getComponentItems() async {
    // Query from database tables "NOVEC items", "FM200 items", "Alarm", "Install"
    // For now, we'll return hardcoded values as a placeholder
    return [
      // NOVEC 1230 Agent
      ComponentItem(
        id: 'novec-agent',
        agentType: 'novec1230',
        category: 'Agent',
        subcategory: 'Agent',
        partNumber: 'NOVEC-1230-AGENT',
        description: 'NOVEC 1230 Fire Protection Fluid',
        manufacturer: '3M',
        unitCost: 45.0,
        currency: 'USD',
      ),
      // FM-200 Agent
      ComponentItem(
        id: 'fm200-agent',
        agentType: 'fm200',
        category: 'Agent',
        subcategory: 'Agent',
        partNumber: 'FM-200-AGENT',
        description: 'FM-200 Fire Suppression Agent',
        manufacturer: 'Chemours',
        unitCost: 35.0,
        currency: 'USD',
      ),
      // Valves
      ComponentItem(
        id: 'novec-valve',
        agentType: 'novec1230',
        category: 'Valve',
        subcategory: 'Discharge Valve',
        partNumber: 'VALVE-NOVEC',
        description: 'Discharge Valve for NOVEC 1230 System',
        manufacturer: 'Fike',
        unitCost: 350.0,
        currency: 'USD',
      ),
      ComponentItem(
        id: 'fm200-valve',
        agentType: 'fm200',
        category: 'Valve',
        subcategory: 'Discharge Valve',
        partNumber: 'VALVE-FM200',
        description: 'Discharge Valve for FM-200 System',
        manufacturer: 'Kidde',
        unitCost: 320.0,
        currency: 'USD',
      ),
      // Nozzles
      ComponentItem(
        id: 'nozzle-15mm',
        agentType: 'novec1230',
        category: 'Nozzle',
        subcategory: 'Discharge Nozzle',
        partNumber: 'NOZZLE-15MM',
        description: '15mm Discharge Nozzle',
        manufacturer: 'Fike',
        unitCost: 85.0,
        currency: 'USD',
      ),
      ComponentItem(
        id: 'nozzle-20mm',
        agentType: 'novec1230',
        category: 'Nozzle',
        subcategory: 'Discharge Nozzle',
        partNumber: 'NOZZLE-20MM',
        description: '20mm Discharge Nozzle',
        manufacturer: 'Fike',
        unitCost: 95.0,
        currency: 'USD',
      ),
      ComponentItem(
        id: 'nozzle-25mm',
        agentType: 'novec1230',
        category: 'Nozzle',
        subcategory: 'Discharge Nozzle',
        partNumber: 'NOZZLE-25MM',
        description: '25mm Discharge Nozzle',
        manufacturer: 'Fike',
        unitCost: 110.0,
        currency: 'USD',
      ),
      ComponentItem(
        id: 'nozzle-32mm',
        agentType: 'novec1230',
        category: 'Nozzle',
        subcategory: 'Discharge Nozzle',
        partNumber: 'NOZZLE-32MM',
        description: '32mm Discharge Nozzle',
        manufacturer: 'Fike',
        unitCost: 130.0,
        currency: 'USD',
      ),
      ComponentItem(
        id: 'nozzle-40mm',
        agentType: 'fm200',
        category: 'Nozzle',
        subcategory: 'Discharge Nozzle',
        partNumber: 'NOZZLE-40MM',
        description: '40mm Discharge Nozzle',
        manufacturer: 'Kidde',
        unitCost: 150.0,
        currency: 'USD',
      ),
      ComponentItem(
        id: 'nozzle-50mm',
        agentType: 'fm200',
        category: 'Nozzle',
        subcategory: 'Discharge Nozzle',
        partNumber: 'NOZZLE-50MM',
        description: '50mm Discharge Nozzle',
        manufacturer: 'Kidde',
        unitCost: 180.0,
        currency: 'USD',
      ),
      // Piping
      ComponentItem(
        id: 'pipe-sch40',
        agentType: '',
        category: 'Piping',
        subcategory: 'Pipe',
        partNumber: 'PIPE-SCH40',
        description: 'Schedule 40 Seamless Pipe',
        manufacturer: 'Generic',
        unitCost: 25.0,
        currency: 'USD',
      ),
      // Detection Components
      ComponentItem(
        id: 'smoke-detector',
        agentType: '',
        category: 'Detection',
        subcategory: 'Detector',
        partNumber: 'SMOKE-DET',
        description: 'Smoke Detector',
        manufacturer: 'System Sensor',
        unitCost: 65.0,
        currency: 'USD',
      ),
      ComponentItem(
        id: 'heat-detector',
        agentType: '',
        category: 'Detection',
        subcategory: 'Detector',
        partNumber: 'HEAT-DET',
        description: 'Heat Detector',
        manufacturer: 'System Sensor',
        unitCost: 55.0,
        currency: 'USD',
      ),
      ComponentItem(
        id: 'control-panel',
        agentType: '',
        category: 'Detection',
        subcategory: 'Panel',
        partNumber: 'CTRL-PNL',
        description: 'Fire Alarm Control Panel',
        manufacturer: 'Notifier',
        unitCost: 850.0,
        currency: 'USD',
      ),
      ComponentItem(
        id: 'manual-release',
        agentType: '',
        category: 'Detection',
        subcategory: 'Release',
        partNumber: 'MAN-REL',
        description: 'Manual Release Station',
        manufacturer: 'Notifier',
        unitCost: 120.0,
        currency: 'USD',
      ),
      // Installation Items
      ComponentItem(
        id: 'install-labor',
        agentType: '',
        category: 'Installation',
        subcategory: 'Labor',
        partNumber: 'LABOR',
        description: 'Installation Labor',
        manufacturer: 'Service',
        unitCost: 75.0,
        currency: 'USD',
      ),
      ComponentItem(
        id: 'install-testing',
        agentType: '',
        category: 'Installation',
        subcategory: 'Testing',
        partNumber: 'TESTING',
        description: 'System Testing and Commissioning',
        manufacturer: 'Service',
        unitCost: 1200.0,
        currency: 'USD',
      ),
    ];
  }


}
