import 'package:flutter/material.dart';
import '../models/isar_models.dart';

class ImportPreviewData {
  final List<String> headers;
  final List<List<dynamic>> sampleRows;
  final List<ColumnDataType> detectedTypes;
  final String fileName;

  ImportPreviewData({
    required this.headers,
    required this.sampleRows,
    required this.detectedTypes,
    required this.fileName,
  });
}

class ColumnMapping {
  final String header;
  ColumnDataType dataType;
  bool shouldImport;

  ColumnMapping({
    required this.header,
    required this.dataType,
    this.shouldImport = true,
  });
}

class ImportPreviewDialog extends StatefulWidget {
  final ImportPreviewData previewData;
  final Function(List<ColumnMapping>) onConfirm;

  const ImportPreviewDialog({
    super.key,
    required this.previewData,
    required this.onConfirm,
  });

  @override
  State<ImportPreviewDialog> createState() => _ImportPreviewDialogState();
}

class _ImportPreviewDialogState extends State<ImportPreviewDialog> {
  late List<ColumnMapping> _columnMappings;

  @override
  void initState() {
    super.initState();
    _columnMappings = List.generate(
      widget.previewData.headers.length,
      (index) => ColumnMapping(
        header: widget.previewData.headers[index],
        dataType: widget.previewData.detectedTypes[index],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      child: Container(
        width: 800,
        height: 600,
        padding: const EdgeInsets.all(24),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header
            Row(
              children: [
                Icon(
                  Icons.file_upload,
                  color: Theme.of(context).colorScheme.primary,
                  size: 24,
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Text(
                        'Import Preview',
                        style: TextStyle(
                          fontFamily: 'Inter',
                          fontSize: 20,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                      Text(
                        'File: ${widget.previewData.fileName}',
                        style: TextStyle(
                          fontFamily: 'Inter',
                          fontSize: 14,
                          color: Colors.grey.shade600,
                        ),
                      ),
                    ],
                  ),
                ),
                IconButton(
                  onPressed: () => Navigator.of(context).pop(),
                  icon: const Icon(Icons.close),
                ),
              ],
            ),
            
            const SizedBox(height: 24),
            
            // Instructions
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.blue.shade50,
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.blue.shade200),
              ),
              child: Row(
                children: [
                  Icon(Icons.info, color: Colors.blue.shade600, size: 20),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Text(
                      'Review the detected column types and adjust if needed. Uncheck columns you don\'t want to import.',
                      style: TextStyle(
                        fontFamily: 'Inter',
                        fontSize: 14,
                        color: Colors.blue.shade700,
                      ),
                    ),
                  ),
                ],
              ),
            ),
            
            const SizedBox(height: 16),
            
            // Column mappings
            Expanded(
              child: SingleChildScrollView(
                child: Column(
                  children: [
                    // Headers
                    Container(
                      padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 16),
                      decoration: BoxDecoration(
                        color: Colors.grey.shade100,
                        borderRadius: const BorderRadius.vertical(top: Radius.circular(8)),
                      ),
                      child: const Row(
                        children: [
                          SizedBox(width: 40), // Checkbox space
                          Expanded(flex: 2, child: Text('Column Name', style: TextStyle(fontFamily: 'Inter', fontWeight: FontWeight.w600))),
                          Expanded(flex: 2, child: Text('Data Type', style: TextStyle(fontFamily: 'Inter', fontWeight: FontWeight.w600))),
                          Expanded(flex: 3, child: Text('Sample Data', style: TextStyle(fontFamily: 'Inter', fontWeight: FontWeight.w600))),
                        ],
                      ),
                    ),
                    
                    // Column rows
                    ...List.generate(_columnMappings.length, (index) {
                      return _buildColumnRow(index);
                    }),
                  ],
                ),
              ),
            ),
            
            const SizedBox(height: 24),
            
            // Actions
            Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                TextButton(
                  onPressed: () => Navigator.of(context).pop(),
                  child: const Text(
                    'Cancel',
                    style: TextStyle(fontFamily: 'Inter'),
                  ),
                ),
                const SizedBox(width: 12),
                ElevatedButton(
                  onPressed: _columnMappings.any((m) => m.shouldImport) ? _confirmImport : null,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Theme.of(context).colorScheme.primary,
                    foregroundColor: Colors.white,
                    padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
                  ),
                  child: const Text(
                    'Import Data',
                    style: TextStyle(
                      fontFamily: 'Inter',
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildColumnRow(int index) {
    final mapping = _columnMappings[index];
    final sampleData = widget.previewData.sampleRows.isNotEmpty && 
                     index < widget.previewData.sampleRows.first.length
        ? widget.previewData.sampleRows.first[index]?.toString() ?? ''
        : '';

    return Container(
      padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 16),
      decoration: BoxDecoration(
        border: Border(
          bottom: BorderSide(color: Colors.grey.shade200),
        ),
      ),
      child: Row(
        children: [
          // Checkbox
          SizedBox(
            width: 40,
            child: Checkbox(
              value: mapping.shouldImport,
              onChanged: (value) {
                setState(() {
                  mapping.shouldImport = value ?? false;
                });
              },
            ),
          ),
          
          // Column name
          Expanded(
            flex: 2,
            child: Text(
              mapping.header,
              style: const TextStyle(
                fontFamily: 'Inter',
                fontSize: 14,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
          
          // Data type dropdown
          Expanded(
            flex: 2,
            child: DropdownButton<ColumnDataType>(
              value: mapping.dataType,
              isExpanded: true,
              onChanged: mapping.shouldImport ? (value) {
                setState(() {
                  mapping.dataType = value ?? ColumnDataType.text;
                });
              } : null,
              items: ColumnDataType.values.map((type) {
                return DropdownMenuItem(
                  value: type,
                  child: Text(
                    _getDataTypeLabel(type),
                    style: const TextStyle(fontFamily: 'Inter', fontSize: 14),
                  ),
                );
              }).toList(),
            ),
          ),
          
          // Sample data
          Expanded(
            flex: 3,
            child: Text(
              sampleData.length > 30 ? '${sampleData.substring(0, 30)}...' : sampleData,
              style: TextStyle(
                fontFamily: 'Inter',
                fontSize: 13,
                color: Colors.grey.shade600,
                fontStyle: FontStyle.italic,
              ),
            ),
          ),
        ],
      ),
    );
  }

  String _getDataTypeLabel(ColumnDataType type) {
    switch (type) {
      case ColumnDataType.text:
        return 'Text';
      case ColumnDataType.number:
        return 'Number';
      case ColumnDataType.currency:
        return 'Currency';
      case ColumnDataType.date:
        return 'Date';
      case ColumnDataType.boolean:
        return 'Boolean';
      case ColumnDataType.dropdown:
        return 'Dropdown';
    }
  }

  void _confirmImport() {
    final selectedMappings = _columnMappings.where((m) => m.shouldImport).toList();
    Navigator.of(context).pop();
    widget.onConfirm(selectedMappings);
  }
}
