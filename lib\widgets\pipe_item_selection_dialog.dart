import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'dart:convert';
import '../models/project.dart';
import '../services/dynamic_schema_service.dart';

class PipeItemSelectionDialog extends StatefulWidget {
  final String pipeTypeName;
  final String tableId;
  final String sectionId;
  final Function(MaterialItem) onItemSelected;

  const PipeItemSelectionDialog({
    super.key,
    required this.pipeTypeName,
    required this.tableId,
    required this.sectionId,
    required this.onItemSelected,
  });

  @override
  State<PipeItemSelectionDialog> createState() => _PipeItemSelectionDialogState();
}

class _PipeItemSelectionDialogState extends State<PipeItemSelectionDialog> {
  final TextEditingController _searchController = TextEditingController();
  final TextEditingController _manualNameController = TextEditingController();
  final TextEditingController _manualDescriptionController = TextEditingController();
  final TextEditingController _manualExWorksController = TextEditingController();
  final TextEditingController _manualLocalPriceController = TextEditingController();
  final TextEditingController _manualInstallPriceController = TextEditingController();
  final TextEditingController _manualVendorController = TextEditingController();
  final TextEditingController _manualApprovalController = TextEditingController();
  final TextEditingController _manualUnitController = TextEditingController();
  
  List<Map<String, dynamic>> _allItems = [];
  List<Map<String, dynamic>> _filteredItems = [];
  bool _isLoading = true;
  bool _showManualInput = false;
  
  @override
  void initState() {
    super.initState();
    _loadItems();
    _searchController.addListener(_filterItems);
  }

  @override
  void dispose() {
    _searchController.dispose();
    _manualNameController.dispose();
    _manualDescriptionController.dispose();
    _manualExWorksController.dispose();
    _manualLocalPriceController.dispose();
    _manualInstallPriceController.dispose();
    _manualVendorController.dispose();
    _manualApprovalController.dispose();
    _manualUnitController.dispose();
    super.dispose();
  }

  Future<void> _loadItems() async {
    try {
      final schemaService = DynamicSchemaService.instance;
      
      // Get rows and columns for the specific table
      final rows = await schemaService.getRowsForTable(widget.tableId);
      final columns = await schemaService.getColumnsForTable(widget.tableId);
      
      List<Map<String, dynamic>> items = [];
      
      for (final row in rows) {
        if (row.data == null) continue;
        
        // Parse row data from JSON
        final rowData = jsonDecode(row.data!) as Map<String, dynamic>;
        
        // Extract relevant data
        String description = '';
        String model = '';
        double localPrice = 0.0;
        double installPrice = 0.0;
        
        for (final column in columns) {
          final value = rowData[column.columnId];
          if (value != null) {
            final columnName = column.name?.toLowerCase() ?? '';
            
            if (columnName.contains('description') || columnName.contains('desc')) {
              description = value.toString();
            } else if (columnName.contains('model') || columnName.contains('name') || columnName.contains('size')) {
              model = value.toString();
            } else if (columnName.contains('total') && !columnName.contains('install')) {
              localPrice = double.tryParse(value.toString()) ?? 0.0;
            } else if (columnName.contains('install')) {
              installPrice = double.tryParse(value.toString()) ?? 0.0;
            }
          }
        }
        
        if (description.isNotEmpty || model.isNotEmpty) {
          items.add({
            'model': model,
            'description': description,
            'localPrice': localPrice,
            'installPrice': installPrice,
            'category': widget.pipeTypeName,
            'tableName': widget.pipeTypeName,
          });
        }
      }
      
      setState(() {
        _allItems = items;
        _filteredItems = items;
        _isLoading = false;
      });
    } catch (e) {
      print('Error loading items: $e');
      setState(() {
        _isLoading = false;
      });
    }
  }

  void _filterItems() {
    final query = _searchController.text.toLowerCase();
    setState(() {
      _filteredItems = _allItems.where((item) {
        final model = item['model']?.toString().toLowerCase() ?? '';
        final description = item['description']?.toString().toLowerCase() ?? '';
        return model.contains(query) || description.contains(query);
      }).toList();
    });
  }

  void _selectItem(Map<String, dynamic> item) {
    final material = MaterialItem(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      name: item['model']?.toString() ?? 'Unknown',
      category: item['category']?.toString() ?? 'Piping',
      description: item['description']?.toString() ?? '',
      quantity: 1,
      unit: 'meter',
      exWorksUnitCost: 0.0, // Not available from database
      localUnitCost: item['localPrice']?.toDouble() ?? 0.0,
      installationUnitCost: item['installPrice']?.toDouble() ?? 0.0,
      isImported: false,
      vendor: '',
      approval: '',
    );
    
    widget.onItemSelected(material);
    Navigator.of(context).pop();
  }

  void _createManualItem() {
    if (_manualNameController.text.trim().isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Please enter a name')),
      );
      return;
    }

    final material = MaterialItem(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      name: _manualNameController.text.trim(),
      category: widget.pipeTypeName,
      description: _manualDescriptionController.text.trim(),
      quantity: 1,
      unit: _manualUnitController.text.trim().isEmpty ? 'meter' : _manualUnitController.text.trim(),
      exWorksUnitCost: double.tryParse(_manualExWorksController.text) ?? 0.0,
      localUnitCost: double.tryParse(_manualLocalPriceController.text) ?? 0.0,
      installationUnitCost: double.tryParse(_manualInstallPriceController.text) ?? 0.0,
      isImported: false,
      vendor: _manualVendorController.text.trim(),
      approval: _manualApprovalController.text.trim(),
    );
    
    widget.onItemSelected(material);
    Navigator.of(context).pop();
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      child: Container(
        width: 600,
        height: 700,
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
            // Header
            Row(
              children: [
                const Icon(Icons.plumbing, color: Colors.blue),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    widget.pipeTypeName,
                    style: const TextStyle(
                      fontSize: 20,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
                IconButton(
                  onPressed: () => Navigator.of(context).pop(),
                  icon: const Icon(Icons.close),
                ),
              ],
            ),
            const SizedBox(height: 16),
            
            // Toggle buttons
            Row(
              children: [
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: () => setState(() => _showManualInput = false),
                    icon: const Icon(Icons.search),
                    label: const Text('Search Database'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: !_showManualInput ? Colors.blue : Colors.grey.shade300,
                      foregroundColor: !_showManualInput ? Colors.white : Colors.black,
                    ),
                  ),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: () => setState(() => _showManualInput = true),
                    icon: const Icon(Icons.edit),
                    label: const Text('Manual Entry'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: _showManualInput ? Colors.blue : Colors.grey.shade300,
                      foregroundColor: _showManualInput ? Colors.white : Colors.black,
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            
            // Content
            Expanded(
              child: _showManualInput ? _buildManualInput() : _buildSearchContent(),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSearchContent() {
    return Column(
      children: [
        // Search field
        TextField(
          controller: _searchController,
          decoration: const InputDecoration(
            labelText: 'Search by model or description',
            prefixIcon: Icon(Icons.search),
            border: OutlineInputBorder(),
          ),
        ),
        const SizedBox(height: 16),
        
        // Results
        Expanded(
          child: _isLoading
              ? const Center(child: CircularProgressIndicator())
              : _filteredItems.isEmpty
                  ? const Center(
                      child: Text(
                        'No items found.\nTry a different search term or use manual entry.',
                        textAlign: TextAlign.center,
                        style: TextStyle(color: Colors.grey),
                      ),
                    )
                  : ListView.builder(
                      itemCount: _filteredItems.length,
                      itemBuilder: (context, index) {
                        final item = _filteredItems[index];
                        return Card(
                          child: ListTile(
                            title: Text(
                              item['model']?.toString() ?? 'Unknown',
                              style: const TextStyle(fontWeight: FontWeight.bold),
                            ),
                            subtitle: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                if (item['description']?.toString().isNotEmpty == true)
                                  Text(item['description'].toString()),
                                const SizedBox(height: 4),
                                Text(
                                  'Total: SAR ${item['localPrice']?.toStringAsFixed(2) ?? '0.00'} | '
                                  'Install: SAR ${item['installPrice']?.toStringAsFixed(2) ?? '0.00'}',
                                  style: TextStyle(
                                    fontSize: 12,
                                    color: Colors.green.shade700,
                                  ),
                                ),
                              ],
                            ),
                            trailing: const Icon(Icons.add_circle_outline),
                            onTap: () => _selectItem(item),
                          ),
                        );
                      },
                    ),
        ),
      ],
    );
  }

  Widget _buildManualInput() {
    return SingleChildScrollView(
      child: Column(
        children: [
          TextField(
            controller: _manualNameController,
            decoration: const InputDecoration(
              labelText: 'Item Name *',
              border: OutlineInputBorder(),
            ),
          ),
          const SizedBox(height: 12),
          TextField(
            controller: _manualDescriptionController,
            decoration: const InputDecoration(
              labelText: 'Description',
              border: OutlineInputBorder(),
            ),
            maxLines: 2,
          ),
          const SizedBox(height: 12),
          TextField(
            controller: _manualExWorksController,
            decoration: const InputDecoration(
              labelText: 'Ex-works Price (USD)',
              border: OutlineInputBorder(),
              prefixText: '\$ ',
            ),
            keyboardType: TextInputType.number,
            inputFormatters: [FilteringTextInputFormatter.allow(RegExp(r'^\d*\.?\d*'))],
          ),
          const SizedBox(height: 12),
          TextField(
            controller: _manualLocalPriceController,
            decoration: const InputDecoration(
              labelText: 'Local Price (SAR)',
              border: OutlineInputBorder(),
              prefixText: 'SAR ',
            ),
            keyboardType: TextInputType.number,
            inputFormatters: [FilteringTextInputFormatter.allow(RegExp(r'^\d*\.?\d*'))],
          ),
          const SizedBox(height: 12),
          TextField(
            controller: _manualInstallPriceController,
            decoration: const InputDecoration(
              labelText: 'Installation Price (SAR)',
              border: OutlineInputBorder(),
              prefixText: 'SAR ',
            ),
            keyboardType: TextInputType.number,
            inputFormatters: [FilteringTextInputFormatter.allow(RegExp(r'^\d*\.?\d*'))],
          ),
          const SizedBox(height: 12),
          TextField(
            controller: _manualVendorController,
            decoration: const InputDecoration(
              labelText: 'Manufacturer/Vendor',
              border: OutlineInputBorder(),
            ),
          ),
          const SizedBox(height: 12),
          TextField(
            controller: _manualApprovalController,
            decoration: const InputDecoration(
              labelText: 'Approval/Certification',
              border: OutlineInputBorder(),
            ),
          ),
          const SizedBox(height: 12),
          TextField(
            controller: _manualUnitController,
            decoration: const InputDecoration(
              labelText: 'Unit',
              border: OutlineInputBorder(),
            ),
          ),
          const SizedBox(height: 24),
          SizedBox(
            width: double.infinity,
            child: ElevatedButton(
              onPressed: _createManualItem,
              child: const Text('Add Item'),
            ),
          ),
        ],
      ),
    );
  }
}
