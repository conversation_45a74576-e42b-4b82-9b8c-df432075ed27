import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../models/clean_agent_system.dart';
import '../models/estimator_types.dart';
import '../services/project_provider.dart';
import '../services/clean_agent_excel_export.dart';
import '../screens/clean_agent_calculator_screen.dart';
import '../utils/formatters.dart';

class CleanAgentSystemsScreen extends StatefulWidget {
  const CleanAgentSystemsScreen({super.key});

  @override
  State<CleanAgentSystemsScreen> createState() => _CleanAgentSystemsScreenState();
}

class _CleanAgentSystemsScreenState extends State<CleanAgentSystemsScreen> {
  @override
  Widget build(BuildContext context) {
    return Consumer<ProjectProvider>(
      builder: (context, projectProvider, child) {
        final project = projectProvider.currentProject;
        if (project == null) {
          return const Scaffold(
            body: Center(
              child: Text('No active project'),
            ),
          );
        }

        // Get clean agent systems from the project
        final cleanAgentSystems = project.cleanAgentSystems ?? [];

        return Scaffold(
          appBar: AppBar(
            title: const Text('Clean Agent Systems'),
            backgroundColor: Colors.orange.shade700,
            foregroundColor: Colors.white,
            actions: [
              if (cleanAgentSystems.isNotEmpty)
                IconButton(
                  icon: const Icon(Icons.file_download),
                  tooltip: 'Export to Excel',
                  onPressed: () => _exportToExcel(context, cleanAgentSystems, project.name),
                ),
              IconButton(
                icon: const Icon(Icons.add),
                tooltip: 'Add New System',
                onPressed: () => _navigateToCalculator(context, null),
              ),
            ],
          ),
          body: cleanAgentSystems.isEmpty
              ? _buildEmptyState(context)
              : _buildSystemsList(context, cleanAgentSystems, projectProvider),
        );
      },
    );
  }

  Widget _buildEmptyState(BuildContext context) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.fire_extinguisher,
            size: 80,
            color: Colors.grey.shade400,
          ),
          const SizedBox(height: 16),
          Text(
            'No Clean Agent Systems',
            style: Theme.of(context).textTheme.headlineSmall?.copyWith(
              color: Colors.grey.shade600,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'Add your first clean agent system',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: Colors.grey.shade500,
            ),
          ),
          const SizedBox(height: 24),
          ElevatedButton.icon(
            onPressed: () => _navigateToCalculator(context, null),
            icon: const Icon(Icons.add),
            label: const Text('Add System'),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.orange.shade700,
              foregroundColor: Colors.white,
              padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSystemsList(BuildContext context, List<CleanAgentSystem> systems, ProjectProvider projectProvider) {
    // Calculate totals
    final totalQuantity = systems.fold<int>(0, (sum, system) => sum + system.quantity);
    final totalCost = systems.fold<double>(0, (sum, system) => sum + (system.totalCost * system.quantity));

    return LayoutBuilder(
      builder: (context, constraints) {
        final isSmallScreen = constraints.maxWidth < 800;

        if (isSmallScreen) {
          // Mobile/small screen layout - use cards
          return _buildMobileLayout(context, systems, projectProvider, totalQuantity, totalCost);
        } else {
          // Desktop layout - use table
          return _buildDesktopLayout(context, systems, projectProvider, totalQuantity, totalCost);
        }
      },
    );
  }

  Widget _buildDesktopLayout(BuildContext context, List<CleanAgentSystem> systems, ProjectProvider projectProvider, int totalQuantity, double totalCost) {
    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        children: [
          // Systems list
          Expanded(
            child: ListView.builder(
              itemCount: systems.length,
              itemBuilder: (context, index) {
                final system = systems[index];
                return _buildDesktopSystemCard(context, system, projectProvider);
              },
            ),
          ),
          // Total card
          Card(
            color: Colors.orange.shade50,
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Text(
                        'TOTAL',
                        style: TextStyle(fontWeight: FontWeight.bold, fontSize: 16),
                      ),
                      Text(
                        'Clean Agent Systems ($totalQuantity items)',
                        style: const TextStyle(fontSize: 12, color: Colors.grey),
                      ),
                    ],
                  ),
                  Text(
                    formatCurrencySAR(totalCost),
                    style: TextStyle(
                      fontWeight: FontWeight.bold,
                      fontSize: 18,
                      color: Colors.orange.shade700,
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDesktopSystemCard(BuildContext context, CleanAgentSystem system, ProjectProvider projectProvider) {
    final totalCost = system.totalCost * system.quantity;

    return Card(
      margin: const EdgeInsets.only(bottom: 8),
      child: Padding(
        padding: const EdgeInsets.all(12.0),
        child: Row(
          children: [
            // System info (left side)
            Expanded(
              flex: 5,
              child: InkWell(
                onTap: () => _navigateToCalculator(context, system),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      system.name,
                      style: TextStyle(
                        fontWeight: FontWeight.bold,
                        fontSize: 16,
                        color: Colors.blue.shade700,
                        decoration: TextDecoration.underline,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      system.description.isNotEmpty ? system.description : system.summaryDescription,
                      style: TextStyle(
                        fontSize: 12,
                        color: Colors.grey.shade600,
                      ),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ],
                ),
              ),
            ),
            // Quantity controls (center)
            Expanded(
              flex: 2,
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  const Text('Qty: ', style: TextStyle(fontWeight: FontWeight.w500)),
                  IconButton(
                    onPressed: system.quantity > 1 ? () => _updateQuantity(system, system.quantity - 1, projectProvider) : null,
                    icon: const Icon(Icons.remove, size: 16),
                    constraints: const BoxConstraints(minWidth: 32, minHeight: 32),
                    padding: EdgeInsets.zero,
                  ),
                  Container(
                    width: 40,
                    alignment: Alignment.center,
                    child: Text(
                      system.quantity.toString(),
                      style: const TextStyle(fontWeight: FontWeight.w500),
                    ),
                  ),
                  IconButton(
                    onPressed: () => _updateQuantity(system, system.quantity + 1, projectProvider),
                    icon: const Icon(Icons.add, size: 16),
                    constraints: const BoxConstraints(minWidth: 32, minHeight: 32),
                    padding: EdgeInsets.zero,
                  ),
                ],
              ),
            ),
            // Cost info (right side)
            Expanded(
              flex: 3,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.end,
                children: [
                  Text(
                    formatCurrencySAR(totalCost),
                    style: TextStyle(
                      fontWeight: FontWeight.bold,
                      fontSize: 16,
                      color: Colors.orange.shade700,
                    ),
                  ),
                  if (system.quantity > 1)
                    Text(
                      '${formatCurrencySAR(system.totalCost)} each',
                      style: TextStyle(
                        fontSize: 12,
                        color: Colors.grey.shade600,
                      ),
                    ),
                ],
              ),
            ),
            // Actions
            PopupMenuButton<String>(
              onSelected: (value) => _handleSystemAction(context, value, system, projectProvider),
              itemBuilder: (context) => [
                const PopupMenuItem(
                  value: 'duplicate',
                  child: Row(
                    children: [
                      Icon(Icons.copy, size: 16),
                      SizedBox(width: 8),
                      Text('Duplicate'),
                    ],
                  ),
                ),
                const PopupMenuItem(
                  value: 'delete',
                  child: Row(
                    children: [
                      Icon(Icons.delete, size: 16, color: Colors.red),
                      SizedBox(width: 8),
                      Text('Delete', style: TextStyle(color: Colors.red)),
                    ],
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildMobileLayout(BuildContext context, List<CleanAgentSystem> systems, ProjectProvider projectProvider, int totalQuantity, double totalCost) {
    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        children: [
          // Systems list
          Expanded(
            child: ListView.builder(
              itemCount: systems.length,
              itemBuilder: (context, index) {
                final system = systems[index];
                return _buildMobileSystemCard(context, system, projectProvider);
              },
            ),
          ),
          // Total card
          Card(
            color: Colors.orange.shade50,
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Text(
                        'TOTAL',
                        style: TextStyle(fontWeight: FontWeight.bold, fontSize: 16),
                      ),
                      Text(
                        'Clean Agent Systems ($totalQuantity items)',
                        style: const TextStyle(fontSize: 12, color: Colors.grey),
                      ),
                    ],
                  ),
                  Text(
                    formatCurrencySAR(totalCost),
                    style: TextStyle(
                      fontWeight: FontWeight.bold,
                      fontSize: 18,
                      color: Colors.orange.shade700,
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildMobileSystemCard(BuildContext context, CleanAgentSystem system, ProjectProvider projectProvider) {
    final totalCost = system.totalCost * system.quantity;

    return Card(
      margin: const EdgeInsets.only(bottom: 8),
      child: InkWell(
        onTap: () => _navigateToCalculator(context, system),
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Header with name and actions
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Expanded(
                    child: Text(
                      system.name,
                      style: TextStyle(
                        fontWeight: FontWeight.bold,
                        fontSize: 16,
                        color: Colors.blue.shade700,
                      ),
                    ),
                  ),
                  PopupMenuButton<String>(
                    onSelected: (value) => _handleSystemAction(context, value, system, projectProvider),
                    itemBuilder: (context) => [
                      const PopupMenuItem(
                        value: 'duplicate',
                        child: Row(
                          children: [
                            Icon(Icons.copy, size: 16),
                            SizedBox(width: 8),
                            Text('Duplicate'),
                          ],
                        ),
                      ),
                      const PopupMenuItem(
                        value: 'delete',
                        child: Row(
                          children: [
                            Icon(Icons.delete, size: 16, color: Colors.red),
                            SizedBox(width: 8),
                            Text('Delete', style: TextStyle(color: Colors.red)),
                          ],
                        ),
                      ),
                    ],
                  ),
                ],
              ),
              const SizedBox(height: 8),
              // Description
              Text(
                system.description.isNotEmpty ? system.description : system.summaryDescription,
                style: TextStyle(fontSize: 12, color: Colors.grey.shade600),
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),
              const SizedBox(height: 12),
              // Quantity and cost row
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  // Quantity controls
                  Row(
                    children: [
                      const Text('Qty: ', style: TextStyle(fontWeight: FontWeight.w500)),
                      IconButton(
                        onPressed: system.quantity > 1 ? () => _updateQuantity(system, system.quantity - 1, projectProvider) : null,
                        icon: const Icon(Icons.remove, size: 16),
                        constraints: const BoxConstraints(minWidth: 32, minHeight: 32),
                        padding: EdgeInsets.zero,
                      ),
                      Container(
                        width: 40,
                        alignment: Alignment.center,
                        child: Text(
                          system.quantity.toString(),
                          style: const TextStyle(fontWeight: FontWeight.w500),
                        ),
                      ),
                      IconButton(
                        onPressed: () => _updateQuantity(system, system.quantity + 1, projectProvider),
                        icon: const Icon(Icons.add, size: 16),
                        constraints: const BoxConstraints(minWidth: 32, minHeight: 32),
                        padding: EdgeInsets.zero,
                      ),
                    ],
                  ),
                  // Total cost
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.end,
                    children: [
                      Text(
                        formatCurrencySAR(totalCost),
                        style: TextStyle(
                          fontWeight: FontWeight.bold,
                          fontSize: 16,
                          color: Colors.orange.shade700,
                        ),
                      ),
                      if (system.quantity > 1)
                        Text(
                          '${formatCurrencySAR(system.totalCost)} each',
                          style: TextStyle(
                            fontSize: 12,
                            color: Colors.grey.shade600,
                          ),
                        ),
                    ],
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }



  void _updateQuantity(CleanAgentSystem system, int newQuantity, ProjectProvider projectProvider) {
    final updatedSystem = system.copyWith(quantity: newQuantity);
    projectProvider.updateCleanAgentSystem(updatedSystem);
  }



  void _handleSystemAction(BuildContext context, String action, CleanAgentSystem system, ProjectProvider projectProvider) {
    switch (action) {
      case 'view':
        _navigateToCalculator(context, system);
        break;
      case 'duplicate':
        _duplicateSystem(context, system, projectProvider);
        break;
      case 'delete':
        _deleteSystem(context, system, projectProvider);
        break;
    }
  }

  void _navigateToCalculator(BuildContext context, CleanAgentSystem? system) {
    final projectProvider = Provider.of<ProjectProvider>(context, listen: false);
    final project = projectProvider.currentProject;

    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => CleanAgentCalculatorScreen(
          exchangeRate: project?.exchangeRate ?? 3.75,
          shippingFactor: project?.shippingRate ?? 1.15,
          marginFactor: project?.marginRate ?? 1.0,
          existingSystem: system,
          onSystemSaved: (savedSystem) {
            // Refresh the systems list
            setState(() {});
          },
        ),
      ),
    );
  }

  void _duplicateSystem(BuildContext context, CleanAgentSystem system, ProjectProvider projectProvider) {
    // Generate new name and description for the duplicated system
    final agentName = system.agentType == AgentType.fm200 ? 'FM200' : 'NOVEC';
    final agentKg = system.agentRequired.toInt();
    final newName = '$agentName ${agentKg}KG (Copy)';

    final cylinderQty = system.cylinderQty;
    final cylinderSize = system.cylinderSize;
    final nozzleQty = system.nozzleQty;
    final nozzleSize = system.nozzleSize;
    final manifoldSize = system.manifoldPipeSize;

    final newDescription = '${agentKg}kg $agentName system with $cylinderQty × ${cylinderSize}L cylinder${cylinderQty > 1 ? 's' : ''} '
        'c/w actuators, $nozzleQty × ${nozzleSize}mm nozzle${nozzleQty > 1 ? 's' : ''}, '
        '${manifoldSize}mm manifold pipe system for ${system.roomVolume.toStringAsFixed(1)}m³ room.';

    final duplicatedSystem = system.copyWith(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      name: newName,
      description: newDescription,
    );

    projectProvider.addCleanAgentSystem(duplicatedSystem);

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('System "${system.name}" duplicated'),
        backgroundColor: Colors.green,
      ),
    );
  }

  void _deleteSystem(BuildContext context, CleanAgentSystem system, ProjectProvider projectProvider) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete System'),
        content: Text('Are you sure you want to delete "${system.name}"?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              projectProvider.removeCleanAgentSystem(system.id);
              Navigator.pop(context);
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text('System "${system.name}" deleted'),
                  backgroundColor: Colors.red,
                ),
              );
            },
            style: TextButton.styleFrom(foregroundColor: Colors.red),
            child: const Text('Delete'),
          ),
        ],
      ),
    );
  }

  void _exportToExcel(BuildContext context, List<CleanAgentSystem> systems, String projectName) async {
    try {
      // Show loading indicator
      showDialog(
        context: context,
        barrierDismissible: false,
        builder: (context) => const AlertDialog(
          content: Row(
            children: [
              CircularProgressIndicator(),
              SizedBox(width: 16),
              Text('Exporting to Excel...'),
            ],
          ),
        ),
      );

      // Export to Excel
      await CleanAgentExcelExport.exportSystems(
        context: context,
        systems: systems,
        projectName: projectName,
      );

      // Close loading dialog
      if (context.mounted) {
        Navigator.pop(context);
      }
    } catch (e) {
      // Close loading dialog
      if (context.mounted) {
        Navigator.pop(context);
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Export failed: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }
}
