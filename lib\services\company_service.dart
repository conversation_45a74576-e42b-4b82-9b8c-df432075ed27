import 'dart:convert';
import 'package:shared_preferences/shared_preferences.dart';
import '../config/supabase_config.dart';

class Company {
  final String id;
  final String name;
  final String supabaseUrl;
  final String supabaseAnonKey;
  final DateTime createdAt;

  Company({
    required this.id,
    required this.name,
    required this.supabaseUrl,
    required this.supabaseAnonKey,
    required this.createdAt,
  });

  Map<String, dynamic> toJson() => {
    'id': id,
    'name': name,
    'supabaseUrl': supabaseUrl,
    'supabaseAnonKey': supabaseAnonKey,
    'createdAt': createdAt.toIso8601String(),
  };

  factory Company.fromJson(Map<String, dynamic> json) => Company(
    id: json['id'],
    name: json['name'],
    supabaseUrl: json['supabaseUrl'],
    supabaseAnonKey: json['supabaseAnonKey'],
    createdAt: DateTime.parse(json['createdAt']),
  );
}

class CompanyService {
  static const String _companiesKey = 'companies';
  static const String _currentCompanyKey = 'current_company';

  // Get all companies
  Future<List<Company>> getCompanies() async {
    final prefs = await SharedPreferences.getInstance();
    final companiesJson = prefs.getString(_companiesKey);
    
    if (companiesJson == null) return [];
    
    final List<dynamic> companiesList = jsonDecode(companiesJson);
    return companiesList.map((json) => Company.fromJson(json)).toList();
  }

  // Add new company
  Future<void> addCompany(Company company) async {
    final companies = await getCompanies();
    companies.add(company);
    await _saveCompanies(companies);
  }

  // Update company
  Future<void> updateCompany(Company company) async {
    final companies = await getCompanies();
    final index = companies.indexWhere((c) => c.id == company.id);
    if (index != -1) {
      companies[index] = company;
      await _saveCompanies(companies);
    }
  }

  // Delete company
  Future<void> deleteCompany(String companyId) async {
    final companies = await getCompanies();
    companies.removeWhere((c) => c.id == companyId);
    await _saveCompanies(companies);
  }

  // Set current company
  Future<void> setCurrentCompany(String companyId) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString(_currentCompanyKey, companyId);

    // Update Supabase configuration
    final companies = await getCompanies();
    final company = companies.firstWhere((c) => c.id == companyId);

    SupabaseConfig.setCredentials(
      url: company.supabaseUrl,
      anonKey: company.supabaseAnonKey,
      company: company.name, // Use company name, not ID
    );
  }

  // Get current company
  Future<Company?> getCurrentCompany() async {
    final prefs = await SharedPreferences.getInstance();
    final currentCompanyId = prefs.getString(_currentCompanyKey);
    
    if (currentCompanyId == null) return null;
    
    final companies = await getCompanies();
    try {
      return companies.firstWhere((c) => c.id == currentCompanyId);
    } catch (e) {
      return null;
    }
  }

  // Initialize current company configuration
  Future<void> initializeCurrentCompany() async {
    final currentCompany = await getCurrentCompany();
    if (currentCompany != null) {
      SupabaseConfig.setCredentials(
        url: currentCompany.supabaseUrl,
        anonKey: currentCompany.supabaseAnonKey,
        company: currentCompany.name, // Use company name, not ID
      );
    }
  }

  // Private helper to save companies
  Future<void> _saveCompanies(List<Company> companies) async {
    final prefs = await SharedPreferences.getInstance();
    final companiesJson = jsonEncode(companies.map((c) => c.toJson()).toList());
    await prefs.setString(_companiesKey, companiesJson);
  }
}
