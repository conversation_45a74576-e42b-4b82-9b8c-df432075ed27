import 'package:uuid/uuid.dart';
import '../models/undo_redo_models.dart';
import '../models/isar_models.dart';
import 'dynamic_schema_service.dart';

class UndoRedoService {
  static final UndoRedoService _instance = UndoRedoService._internal();
  factory UndoRedoService() => _instance;
  UndoRedoService._internal();

  static UndoRedoService get instance => _instance;

  final List<UndoRedoAction> _undoStack = [];
  final List<UndoRedoAction> _redoStack = [];
  final int _maxStackSize = 50;
  final Uuid _uuid = const Uuid();
  final DynamicSchemaService _schemaService = DynamicSchemaService.instance;

  bool get canUndo => _undoStack.isNotEmpty;
  bool get canRedo => _redoStack.isNotEmpty;

  List<UndoRedoAction> get undoStack => List.unmodifiable(_undoStack);
  List<UndoRedoAction> get redoStack => List.unmodifiable(_redoStack);

  void addAction(UndoRedoAction action) {
    _undoStack.add(action);
    _redoStack.clear(); // Clear redo stack when new action is added
    
    // Limit stack size
    if (_undoStack.length > _maxStackSize) {
      _undoStack.removeAt(0);
    }
  }

  Future<bool> undo() async {
    if (!canUndo) return false;

    final action = _undoStack.removeLast();
    
    try {
      await _executeUndo(action);
      _redoStack.add(action);
      return true;
    } catch (e) {
      // If undo fails, put the action back
      _undoStack.add(action);
      rethrow;
    }
  }

  Future<bool> redo() async {
    if (!canRedo) return false;

    final action = _redoStack.removeLast();
    
    try {
      await _executeRedo(action);
      _undoStack.add(action);
      return true;
    } catch (e) {
      // If redo fails, put the action back
      _redoStack.add(action);
      rethrow;
    }
  }

  Future<void> _executeUndo(UndoRedoAction action) async {
    switch (action.type) {
      case ActionType.cellEdit:
        await _undoCellEdit(action);
        break;
      case ActionType.rowAdd:
        await _undoRowAdd(action);
        break;
      case ActionType.rowDelete:
        await _undoRowDelete(action);
        break;
      case ActionType.columnAdd:
        await _undoColumnAdd(action);
        break;
      case ActionType.columnDelete:
        await _undoColumnDelete(action);
        break;
      case ActionType.columnEdit:
        await _undoColumnEdit(action);
        break;
      case ActionType.tableCreate:
        await _undoTableCreate(action);
        break;
      case ActionType.tableDelete:
        await _undoTableDelete(action);
        break;
      case ActionType.tableEdit:
        await _undoTableEdit(action);
        break;
    }
  }

  Future<void> _executeRedo(UndoRedoAction action) async {
    switch (action.type) {
      case ActionType.cellEdit:
        await _redoCellEdit(action);
        break;
      case ActionType.rowAdd:
        await _redoRowAdd(action);
        break;
      case ActionType.rowDelete:
        await _redoRowDelete(action);
        break;
      case ActionType.columnAdd:
        await _redoColumnAdd(action);
        break;
      case ActionType.columnDelete:
        await _redoColumnDelete(action);
        break;
      case ActionType.columnEdit:
        await _redoColumnEdit(action);
        break;
      case ActionType.tableCreate:
        await _redoTableCreate(action);
        break;
      case ActionType.tableDelete:
        await _redoTableDelete(action);
        break;
      case ActionType.tableEdit:
        await _redoTableEdit(action);
        break;
    }
  }

  // Cell edit operations
  Future<void> _undoCellEdit(UndoRedoAction action) async {
    final cellAction = CellEditAction.fromJson(action.data);
    final rowData = <String, dynamic>{cellAction.columnId: cellAction.oldValue};
    await _schemaService.updateRowData(cellAction.rowId, rowData);
  }

  Future<void> _redoCellEdit(UndoRedoAction action) async {
    final cellAction = CellEditAction.fromJson(action.data);
    final rowData = <String, dynamic>{cellAction.columnId: cellAction.newValue};
    await _schemaService.updateRowData(cellAction.rowId, rowData);
  }

  // Row operations
  Future<void> _undoRowAdd(UndoRedoAction action) async {
    final rowAction = RowAction.fromJson(action.data);
    await _schemaService.deleteRow(rowAction.rowId);
  }

  Future<void> _redoRowAdd(UndoRedoAction action) async {
    final rowAction = RowAction.fromJson(action.data);
    final rowId = await _schemaService.createRow(rowAction.tableId);
    if (rowAction.rowData != null) {
      await _schemaService.updateRowData(rowId, rowAction.rowData!);
    }
  }

  Future<void> _undoRowDelete(UndoRedoAction action) async {
    final rowAction = RowAction.fromJson(action.data);
    final rowId = await _schemaService.createRow(rowAction.tableId);
    if (rowAction.rowData != null) {
      await _schemaService.updateRowData(rowId, rowAction.rowData!);
    }
  }

  Future<void> _redoRowDelete(UndoRedoAction action) async {
    final rowAction = RowAction.fromJson(action.data);
    await _schemaService.deleteRow(rowAction.rowId);
  }

  // Column operations
  Future<void> _undoColumnAdd(UndoRedoAction action) async {
    final columnAction = ColumnAction.fromJson(action.data);
    await _schemaService.deleteColumn(columnAction.columnId);
  }

  Future<void> _redoColumnAdd(UndoRedoAction action) async {
    final columnAction = ColumnAction.fromJson(action.data);
    final columnData = columnAction.columnData!;
    await _schemaService.createColumn(
      tableId: columnAction.tableId,
      name: columnData['name'],
      dataType: ColumnDataType.values.firstWhere((e) => e.name == columnData['dataType']),
      isRequired: columnData['isRequired'],
      defaultValue: columnData['defaultValue'],
      validationRules: columnData['validationRules'],
      dropdownOptions: columnData['dropdownOptions'],
    );
  }

  Future<void> _undoColumnDelete(UndoRedoAction action) async {
    final columnAction = ColumnAction.fromJson(action.data);
    final columnData = columnAction.columnData!;
    await _schemaService.createColumn(
      tableId: columnAction.tableId,
      name: columnData['name'],
      dataType: ColumnDataType.values.firstWhere((e) => e.name == columnData['dataType']),
      isRequired: columnData['isRequired'],
      defaultValue: columnData['defaultValue'],
      validationRules: columnData['validationRules'],
      dropdownOptions: columnData['dropdownOptions'],
    );
  }

  Future<void> _redoColumnDelete(UndoRedoAction action) async {
    final columnAction = ColumnAction.fromJson(action.data);
    await _schemaService.deleteColumn(columnAction.columnId);
  }

  Future<void> _undoColumnEdit(UndoRedoAction action) async {
    final columnAction = ColumnAction.fromJson(action.data);
    final previousData = action.previousData!;
    await _schemaService.updateColumn(
      columnAction.columnId,
      name: previousData['name'],
      dataType: ColumnDataType.values.firstWhere((e) => e.name == previousData['dataType']),
      isRequired: previousData['isRequired'],
      defaultValue: previousData['defaultValue'],
      validationRules: previousData['validationRules'],
      dropdownOptions: previousData['dropdownOptions'],
    );
  }

  Future<void> _redoColumnEdit(UndoRedoAction action) async {
    final columnAction = ColumnAction.fromJson(action.data);
    final columnData = columnAction.columnData!;
    await _schemaService.updateColumn(
      columnAction.columnId,
      name: columnData['name'],
      dataType: ColumnDataType.values.firstWhere((e) => e.name == columnData['dataType']),
      isRequired: columnData['isRequired'],
      defaultValue: columnData['defaultValue'],
      validationRules: columnData['validationRules'],
      dropdownOptions: columnData['dropdownOptions'],
    );
  }

  // Table operations
  Future<void> _undoTableCreate(UndoRedoAction action) async {
    final tableAction = TableAction.fromJson(action.data);
    await _schemaService.deleteTable(tableAction.tableId);
  }

  Future<void> _redoTableCreate(UndoRedoAction action) async {
    final tableAction = TableAction.fromJson(action.data);
    final tableData = tableAction.tableData!;
    await _schemaService.createTable(
      name: tableData['name'],
      sectionId: tableAction.sectionId,
      description: tableData['description'],
    );
  }

  Future<void> _undoTableDelete(UndoRedoAction action) async {
    final tableAction = TableAction.fromJson(action.data);
    final tableData = tableAction.tableData!;
    await _schemaService.createTable(
      name: tableData['name'],
      sectionId: tableAction.sectionId,
      description: tableData['description'],
    );
  }

  Future<void> _redoTableDelete(UndoRedoAction action) async {
    final tableAction = TableAction.fromJson(action.data);
    await _schemaService.deleteTable(tableAction.tableId);
  }

  Future<void> _undoTableEdit(UndoRedoAction action) async {
    final tableAction = TableAction.fromJson(action.data);
    final previousData = action.previousData!;
    await _schemaService.updateTable(
      tableAction.tableId,
      name: previousData['name'],
      description: previousData['description'],
    );
  }

  Future<void> _redoTableEdit(UndoRedoAction action) async {
    final tableAction = TableAction.fromJson(action.data);
    final tableData = tableAction.tableData!;
    await _schemaService.updateTable(
      tableAction.tableId,
      name: tableData['name'],
      description: tableData['description'],
    );
  }

  void clear() {
    _undoStack.clear();
    _redoStack.clear();
  }

  // Helper methods to create actions
  UndoRedoAction createCellEditAction({
    required String tableId,
    required String rowId,
    required String columnId,
    required dynamic newValue,
    required dynamic oldValue,
  }) {
    final cellAction = CellEditAction(
      tableId: tableId,
      rowId: rowId,
      columnId: columnId,
      newValue: newValue,
      oldValue: oldValue,
    );

    return UndoRedoAction(
      id: _uuid.v4(),
      type: ActionType.cellEdit,
      description: 'Edit cell value',
      timestamp: DateTime.now(),
      data: cellAction.toJson(),
    );
  }

  UndoRedoAction createRowAddAction({
    required String tableId,
    required String rowId,
    Map<String, dynamic>? rowData,
  }) {
    final rowAction = RowAction(
      tableId: tableId,
      rowId: rowId,
      rowData: rowData,
    );

    return UndoRedoAction(
      id: _uuid.v4(),
      type: ActionType.rowAdd,
      description: 'Add row',
      timestamp: DateTime.now(),
      data: rowAction.toJson(),
    );
  }

  UndoRedoAction createRowDeleteAction({
    required String tableId,
    required String rowId,
    required Map<String, dynamic> rowData,
  }) {
    final rowAction = RowAction(
      tableId: tableId,
      rowId: rowId,
      rowData: rowData,
    );

    return UndoRedoAction(
      id: _uuid.v4(),
      type: ActionType.rowDelete,
      description: 'Delete row',
      timestamp: DateTime.now(),
      data: rowAction.toJson(),
    );
  }
}
