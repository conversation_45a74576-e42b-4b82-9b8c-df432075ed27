import 'dart:convert';
import 'dart:io';
import 'package:file_picker/file_picker.dart';
import 'package:path/path.dart' as path;
import '../models/isar_models.dart';
import 'dynamic_schema_service.dart';
import 'supabase_sync_service.dart';
import 'company_service.dart';
import 'isar_service.dart';

class BackupService {
  final DynamicSchemaService _schemaService = DynamicSchemaService.instance;
  final CompanyService _companyService = CompanyService();
  final IsarService _isarService = IsarService.instance;
  late final SupabaseSyncService _supabaseService;

  BackupService() {
    _supabaseService = SupabaseSyncService(_schemaService);
  }

  /// Create a full backup of all application data
  Future<Map<String, dynamic>> createFullBackup() async {
    try {
      // Get save location from user
      final String? outputFile = await FilePicker.platform.saveFile(
        dialogTitle: 'Save Backup File',
        fileName: 'firetool_backup_${DateTime.now().millisecondsSinceEpoch}.json',
        type: FileType.custom,
        allowedExtensions: ['json'],
      );

      if (outputFile == null) {
        throw Exception('No save location selected');
      }

      // Collect all data
      final backupData = await _collectAllData();

      // Write to file
      final file = File(outputFile);
      await file.writeAsString(jsonEncode(backupData));

      return {
        'success': true,
        'filename': path.basename(outputFile),
        'path': outputFile,
        'size': await file.length(),
        'timestamp': DateTime.now().toIso8601String(),
      };
    } catch (e) {
      throw Exception('Failed to create backup: $e');
    }
  }

  /// Restore data from a backup file
  Future<Map<String, dynamic>> restoreFromBackup(String filePath) async {
    try {
      print('=== RESTORE DEBUG: Starting restore from $filePath ===');
      final file = File(filePath);
      if (!await file.exists()) {
        throw Exception('Backup file not found');
      }

      final jsonString = await file.readAsString();
      print('=== RESTORE DEBUG: File read successfully, size: ${jsonString.length} characters ===');

      final backupData = jsonDecode(jsonString) as Map<String, dynamic>;
      print('=== RESTORE DEBUG: JSON parsed successfully ===');
      print('=== RESTORE DEBUG: Backup contains keys: ${backupData.keys.toList()} ===');

      // Log what's in the backup
      if (backupData['companies'] != null) {
        print('=== RESTORE DEBUG: Found ${backupData['companies'].length} companies ===');
      }
      if (backupData['sections'] != null) {
        print('=== RESTORE DEBUG: Found ${backupData['sections'].length} sections ===');
      }
      if (backupData['tables'] != null) {
        print('=== RESTORE DEBUG: Found ${backupData['tables'].length} tables ===');
      }
      if (backupData['projects'] != null) {
        print('=== RESTORE DEBUG: Found ${backupData['projects'].length} projects ===');
      }
      if (backupData['data'] != null) {
        print('=== RESTORE DEBUG: Found data for ${backupData['data'].keys.length} tables ===');
      }

      // Validate backup format
      if (!_validateBackupFormat(backupData)) {
        throw Exception('Invalid backup file format');
      }

      // Restore data
      await _restoreAllData(backupData);

      return {
        'success': true,
        'filename': path.basename(filePath),
        'timestamp': DateTime.now().toIso8601String(),
      };
    } catch (e) {
      print('=== RESTORE DEBUG: ERROR: $e ===');
      throw Exception('Failed to restore backup: $e');
    }
  }

  /// Upload all database tables to Supabase
  /// This includes: sections, tables, columns, and all row data
  Future<Map<String, dynamic>> uploadAllToSupabase() async {
    try {
      await _supabaseService.initialize();
      
      final tables = await _schemaService.getAllTables();
      int uploadedCount = 0;

      for (final table in tables) {
        try {
          await _supabaseService.uploadTableToSupabase(table.tableId!);
          uploadedCount++;
        } catch (e) {
          // Continue with other tables even if one fails
        }
      }

      return {
        'success': true,
        'tables_uploaded': uploadedCount,
        'total_tables': tables.length,
        'timestamp': DateTime.now().toIso8601String(),
      };
    } catch (e) {
      throw Exception('Failed to upload to Supabase: $e');
    }
  }

  /// Download all database tables from Supabase
  /// This includes: sections, tables, columns, and all row data
  Future<Map<String, dynamic>> downloadAllFromSupabase() async {
    try {
      await _supabaseService.initialize();
      
      final tables = await _schemaService.getAllTables();
      int downloadedCount = 0;

      for (final table in tables) {
        try {
          await _supabaseService.downloadTableFromSupabase(table.tableId!);
          downloadedCount++;
        } catch (e) {
          // Continue with other tables even if one fails
        }
      }

      return {
        'success': true,
        'tables_downloaded': downloadedCount,
        'total_tables': tables.length,
        'timestamp': DateTime.now().toIso8601String(),
      };
    } catch (e) {
      throw Exception('Failed to download from Supabase: $e');
    }
  }

  /// Collect all application data for backup
  Future<Map<String, dynamic>> _collectAllData() async {
    final sections = await _schemaService.getAllSections();
    final tables = await _schemaService.getAllTables();
    final companies = await _companyService.getCompanies();
    final currentCompany = await _companyService.getCurrentCompany();

    // Get ISAR projects (using same approach as project provider)
    final isar = await _isarService.database;
    final List<Project> projects = [];

    // Try to get projects by checking a reasonable range of IDs
    for (int i = 1; i <= 1000; i++) {
      final project = await isar.projects.get(i);
      if (project != null) {
        projects.add(project);
      }
    }

    final backupData = <String, dynamic>{
      'version': '1.0',
      'created_at': DateTime.now().toIso8601String(),
      'app_name': 'FireTool',
      'companies': companies.map((c) => c.toJson()).toList(),
      'current_company': currentCompany?.toJson(),
      'sections': [],
      'tables': [],
      'data': {},
      'projects': [], // Add projects to backup
    };

    // Export sections
    for (final section in sections) {
      backupData['sections'].add({
        'sectionId': section.sectionId,
        'name': section.name,
        'color': section.color,
        'parentSectionId': section.parentSectionId,
        'createdAt': section.createdAt?.toIso8601String(),
        'updatedAt': section.updatedAt?.toIso8601String(),
      });
    }

    // Export tables and their data
    for (final table in tables) {
      // Export table structure
      final columns = await _schemaService.getColumnsForTable(table.tableId!);
      backupData['tables'].add({
        'tableId': table.tableId,
        'name': table.name,
        'sectionId': table.sectionId,
        'createdAt': table.createdAt?.toIso8601String(),
        'updatedAt': table.updatedAt?.toIso8601String(),
        'columns': columns.map((c) => {
          'columnId': c.columnId,
          'name': c.name,
          'dataType': c.dataType.toString(),
          'isRequired': c.isRequired,
          'defaultValue': c.defaultValue,
          'createdAt': c.createdAt?.toIso8601String(),
          'updatedAt': c.updatedAt?.toIso8601String(),
        }).toList(),
      });

      // Export table data
      final rows = await _schemaService.getRowsForTable(table.tableId!);
      backupData['data'][table.tableId!] = rows.map((r) => {
        'rowId': r.rowId,
        'data': r.data,
        'createdAt': r.createdAt?.toIso8601String(),
        'updatedAt': r.updatedAt?.toIso8601String(),
      }).toList();
    }

    // Export projects
    for (final project in projects) {
      backupData['projects'].add({
        'projectId': project.projectId,
        'name': project.name,
        'clientName': project.clientName,
        'projectReference': project.projectReference,
        'currency': project.currency,
        'exchangeRate': project.exchangeRate,
        'shippingRate': project.shippingRate,
        'marginRate': project.marginRate,
        'systemType': project.systemType.toString(),
        'systemsJson': project.systemsJson,
        'cleanAgentSystemsJson': project.cleanAgentSystemsJson,
        'createdAt': project.createdAt?.toIso8601String(),
        'updatedAt': project.updatedAt?.toIso8601String(),
      });
    }

    return backupData;
  }

  /// Restore sections with proper hierarchy handling
  Future<Map<String, String>> _restoreSectionsWithHierarchy(List<dynamic> sectionsData) async {
    print('=== RESTORE DEBUG: Starting section hierarchy restoration ===');

    // Create a map to track section ID mapping from backup to mobile
    final Map<String, String> sectionIdMapping = {};

    // First pass: Create all root sections (sections without parent)
    final rootSections = sectionsData.where((s) => s['parentSectionId'] == null).toList();
    print('=== RESTORE DEBUG: Found ${rootSections.length} root sections ===');

    for (final sectionData in rootSections) {
      try {
        final existingSections = await _schemaService.getAllSections();
        final existingSection = existingSections.where((s) => s.name == sectionData['name']).firstOrNull;

        if (existingSection == null) {
          // Create new root section
          print('=== RESTORE DEBUG: Creating root section: ${sectionData['name']} with color: ${sectionData['color']} ===');
          try {
            final newSectionId = await _schemaService.createSection(
              name: sectionData['name'],
              color: sectionData['color'],
              parentSectionId: null, // Root section
            );
            sectionIdMapping[sectionData['sectionId']] = newSectionId;
            print('=== RESTORE DEBUG: Successfully created root section: ${sectionData['name']} (${sectionData['sectionId']} -> $newSectionId) ===');
          } catch (e) {
            print('=== RESTORE DEBUG: ERROR creating root section ${sectionData['name']}: $e ===');
          }
        } else {
          // Use existing section
          sectionIdMapping[sectionData['sectionId']] = existingSection.sectionId!;
          print('=== RESTORE DEBUG: Root section already exists: ${sectionData['name']} (${sectionData['sectionId']} -> ${existingSection.sectionId}) ===');
        }
      } catch (e) {
        print('=== RESTORE DEBUG: Error creating root section ${sectionData['name']}: $e ===');
      }
    }

    // Second pass: Create child sections (sections with parent)
    final childSections = sectionsData.where((s) => s['parentSectionId'] != null).toList();
    print('=== RESTORE DEBUG: Found ${childSections.length} child sections ===');

    for (final sectionData in childSections) {
      try {
        final existingSections = await _schemaService.getAllSections();
        final existingSection = existingSections.where((s) => s.name == sectionData['name']).firstOrNull;

        if (existingSection == null) {
          // Map parent section ID from backup to mobile
          final backupParentId = sectionData['parentSectionId'];
          final mobileParentId = sectionIdMapping[backupParentId];

          print('=== RESTORE DEBUG: Creating child section: ${sectionData['name']} ===');
          print('=== RESTORE DEBUG: Backup parent ID: $backupParentId ===');
          print('=== RESTORE DEBUG: Mobile parent ID: $mobileParentId ===');
          print('=== RESTORE DEBUG: Current mapping: $sectionIdMapping ===');

          if (mobileParentId == null) {
            print('=== RESTORE DEBUG: ERROR - Parent section not found for ${sectionData['name']} (parent ID: $backupParentId) ===');
            print('=== RESTORE DEBUG: Available mappings: ${sectionIdMapping.keys.toList()} ===');
            continue;
          }

          // Create new child section
          try {
            final newSectionId = await _schemaService.createSection(
              name: sectionData['name'],
              color: sectionData['color'],
              parentSectionId: mobileParentId,
            );
            sectionIdMapping[sectionData['sectionId']] = newSectionId;
            print('=== RESTORE DEBUG: Successfully created child section: ${sectionData['name']} (${sectionData['sectionId']} -> $newSectionId) with parent $mobileParentId ===');
          } catch (e) {
            print('=== RESTORE DEBUG: ERROR creating child section ${sectionData['name']}: $e ===');
          }
        } else {
          // Use existing section but update its parent relationship
          sectionIdMapping[sectionData['sectionId']] = existingSection.sectionId!;
          print('=== RESTORE DEBUG: Child section already exists: ${sectionData['name']} (${sectionData['sectionId']} -> ${existingSection.sectionId}) ===');

          // Update the existing section's parent relationship
          final backupParentId = sectionData['parentSectionId'];
          final mobileParentId = sectionIdMapping[backupParentId];

          if (mobileParentId != null && existingSection.parentSectionId != mobileParentId) {
            print('=== RESTORE DEBUG: Updating parent relationship for existing section: ${sectionData['name']} ===');
            print('=== RESTORE DEBUG: Old parent: ${existingSection.parentSectionId} -> New parent: $mobileParentId ===');

            try {
              // Update the section's parent relationship directly
              existingSection.parentSectionId = mobileParentId;
              existingSection.updatedAt = DateTime.now();

              // Save the updated section using IsarService
              final isar = await IsarService.instance.database;
              await isar.writeTxn(() async {
                await isar.sidebarSections.put(existingSection);
              });

              print('=== RESTORE DEBUG: Successfully updated parent relationship for: ${sectionData['name']} ===');
            } catch (e) {
              print('=== RESTORE DEBUG: ERROR updating parent relationship for ${sectionData['name']}: $e ===');
            }
          } else {
            print('=== RESTORE DEBUG: Parent relationship already correct for: ${sectionData['name']} ===');
          }
        }
      } catch (e) {
        print('=== RESTORE DEBUG: Error creating child section ${sectionData['name']}: $e ===');
      }
    }

    print('=== RESTORE DEBUG: Section hierarchy restoration completed. Mapping: $sectionIdMapping ===');
    return sectionIdMapping;
  }

  /// Validate backup file format
  bool _validateBackupFormat(Map<String, dynamic> backupData) {
    final requiredKeys = ['version', 'created_at', 'sections', 'tables', 'data'];

    for (final key in requiredKeys) {
      if (!backupData.containsKey(key)) {
        return false;
      }
    }

    // Projects is optional for backward compatibility
    return true;
  }

  /// Restore all data from backup
  Future<void> _restoreAllData(Map<String, dynamic> backupData) async {
    print('Starting restore process...');

    // Restore companies (update existing or create new)
    if (backupData['companies'] != null) {
      print('Restoring ${backupData['companies'].length} companies...');
      for (final companyData in backupData['companies']) {
        final company = Company.fromJson(companyData);
        try {
          // Check if company exists
          final existingCompanies = await _companyService.getCompanies();
          final existingCompany = existingCompanies.where((c) => c.name == company.name).firstOrNull;

          if (existingCompany != null) {
            // Update existing company by creating a new one with existing ID
            final updatedCompany = Company(
              id: existingCompany.id,
              name: company.name,
              supabaseUrl: company.supabaseUrl,
              supabaseAnonKey: company.supabaseAnonKey,
              createdAt: existingCompany.createdAt,
            );
            await _companyService.updateCompany(updatedCompany);
            print('Updated existing company: ${company.name}');
          } else {
            // Create new company
            await _companyService.addCompany(company);
            print('Created new company: ${company.name}');
          }
        } catch (e) {
          print('Error restoring company ${company.name}: $e');
        }
      }
    }

    // Restore sections with proper hierarchy handling
    Map<String, String> sectionIdMapping = {};
    if (backupData['sections'] != null) {
      print('=== RESTORE DEBUG: Restoring ${backupData['sections'].length} sections with hierarchy... ===');
      print('=== RESTORE DEBUG: Section data preview: ${backupData['sections'].take(2).toList()} ===');
      sectionIdMapping = await _restoreSectionsWithHierarchy(backupData['sections']);
      print('=== RESTORE DEBUG: Final section mapping: $sectionIdMapping ===');
    }

    // Restore tables and data with section mapping
    if (backupData['tables'] != null) {
      print('=== RESTORE DEBUG: Restoring ${backupData['tables'].length} tables with section mapping... ===');
      print('=== RESTORE DEBUG: Table data preview: ${backupData['tables'].take(2).toList()} ===');
      print('=== RESTORE DEBUG: Using section mapping: $sectionIdMapping ===');
      await _restoreTablesWithData(backupData['tables'], backupData['data'], backupData['sections'], sectionIdMapping);
    }

    // Restore projects
    if (backupData['projects'] != null) {
      print('=== RESTORE DEBUG: Restoring ${backupData['projects'].length} projects... ===');
      final isar = await _isarService.database;

      await isar.writeTxn(() async {
        // First, clear all existing projects to avoid unique index violations
        print('=== RESTORE DEBUG: Clearing existing projects... ===');
        await isar.projects.clear();
        print('=== RESTORE DEBUG: Existing projects cleared ===');

        for (final projectData in backupData['projects']) {
          print('=== RESTORE DEBUG: Restoring project: ${projectData['name']} ===');

          final project = Project.create(
            projectId: projectData['projectId'],
            name: projectData['name'],
            clientName: projectData['clientName'],
            projectReference: projectData['projectReference'],
            currency: projectData['currency'],
            exchangeRate: projectData['exchangeRate']?.toDouble(),
            shippingRate: projectData['shippingRate']?.toDouble(),
            marginRate: projectData['marginRate']?.toDouble(),
            systemsJson: projectData['systemsJson'],
            cleanAgentSystemsJson: projectData['cleanAgentSystemsJson'],
            createdAt: projectData['createdAt'] != null
                ? DateTime.parse(projectData['createdAt'])
                : DateTime.now(),
            updatedAt: projectData['updatedAt'] != null
                ? DateTime.parse(projectData['updatedAt'])
                : DateTime.now(),
          );

          // Parse systemType if available
          if (projectData['systemType'] != null) {
            final systemTypeString = projectData['systemType'] as String;
            if (systemTypeString.contains('alarm')) {
              project.systemType = SystemType.alarm;
            } else if (systemTypeString.contains('cleanAgent')) {
              project.systemType = SystemType.cleanAgent;
            }
          }

          await isar.projects.put(project);
          print('=== RESTORE DEBUG: Successfully restored project: ${projectData['name']} ===');
        }
      });
      print('=== RESTORE DEBUG: All projects restored successfully ===');
    } else {
      print('=== RESTORE DEBUG: No projects found in backup ===');
    }

    // Set current company if specified
    if (backupData['current_company'] != null) {
      final currentCompanyData = backupData['current_company'];
      final companies = await _companyService.getCompanies();
      final company = companies.where((c) => c.name == currentCompanyData['name']).firstOrNull;
      if (company != null) {
        await _companyService.setCurrentCompany(company.id);
      }
    }

    // Refresh the UI to show updated sections and tables
    print('=== RESTORE DEBUG: Backup restore completed successfully ===');
  }

  /// Restore tables and their data using section ID mapping
  Future<void> _restoreTablesWithData(
    List<dynamic> tablesData,
    Map<String, dynamic>? backupDataSection,
    List<dynamic>? backupSectionsData,
    Map<String, String> sectionIdMapping
  ) async {
    print('=== RESTORE DEBUG: Starting table restoration with ${tablesData.length} tables ===');

    for (final tableData in tablesData) {
      try {
        print('=== RESTORE DEBUG: Processing table: ${tableData['name']} ===');

        // Find section name from backup sections data
        String? targetSectionName;
        final backupSectionId = tableData['sectionId'];

        print('=== RESTORE DEBUG: Looking for section with backup ID: $backupSectionId ===');

        // Find section name from backup sections data
        if (backupSectionsData != null) {
          for (final sectionData in backupSectionsData) {
            if (sectionData['sectionId'] == backupSectionId) {
              targetSectionName = sectionData['name'];
              print('=== RESTORE DEBUG: Found section name from backup: $targetSectionName ===');
              break;
            }
          }
        }

        // If not found in backup, try to use section mapping
        if (targetSectionName == null && sectionIdMapping.containsKey(backupSectionId)) {
          final mobileSectionId = sectionIdMapping[backupSectionId];
          final allSections = await _schemaService.getAllSections();
          final targetSection = allSections.where((s) => s.sectionId == mobileSectionId).firstOrNull;
          if (targetSection != null) {
            targetSectionName = targetSection.name;
            print('=== RESTORE DEBUG: Found section name from mapping: $targetSectionName ===');
          }
        }

        // Final fallback: use first available section
        if (targetSectionName == null) {
          final allSections = await _schemaService.getAllSections();
          if (allSections.isNotEmpty) {
            targetSectionName = allSections.first.name;
            print('=== RESTORE DEBUG: Using fallback section: $targetSectionName ===');
          }
        }

        if (targetSectionName == null) {
          print('=== RESTORE DEBUG: ERROR - No sections available for table ${tableData['name']} ===');
          continue;
        }

        print('=== RESTORE DEBUG: Target section found: $targetSectionName ===');

        // Check if table exists
        final existingTable = await _schemaService.getTableByName(tableData['name'], targetSectionName);
        String? tableId;

        if (existingTable != null) {
          // Use existing table and clear its data
          tableId = existingTable.tableId!;
          print('=== RESTORE DEBUG: Table already exists: ${tableData['name']} with ID: $tableId ===');

          // Clear existing data to replace with backup data
          final existingRows = await _schemaService.getRowsForTableByName(tableData['name'], targetSectionName);
          for (final row in existingRows) {
            await _schemaService.deleteRow(row.rowId!);
          }
          print('=== RESTORE DEBUG: Cleared ${existingRows.length} existing rows for table: ${tableData['name']} ===');
        } else {
          // Create new table
          print('=== RESTORE DEBUG: Creating new table: ${tableData['name']} in section: $targetSectionName ===');
          tableId = await _schemaService.createTableByName(
            tableName: tableData['name'],
            sectionName: targetSectionName,
          );

          if (tableId == null) {
            print('=== RESTORE DEBUG: ERROR - Failed to create table: ${tableData['name']} ===');
            continue;
          }

          print('=== RESTORE DEBUG: Created new table: ${tableData['name']} with ID: $tableId ===');

          // Create columns
          if (tableData['columns'] != null) {
            print('=== RESTORE DEBUG: Creating ${tableData['columns'].length} columns... ===');
            for (final columnData in tableData['columns']) {
              print('=== RESTORE DEBUG: Creating column: ${columnData['name']} (${columnData['dataType']}) ===');
              await _schemaService.createColumnByName(
                columnName: columnData['name'],
                tableName: tableData['name'],
                sectionName: targetSectionName,
                dataType: _parseDataType(columnData['dataType']),
                isRequired: columnData['isRequired'] ?? false,
                defaultValue: columnData['defaultValue'],
              );
            }
            print('=== RESTORE DEBUG: Created ${tableData['columns'].length} columns for table: ${tableData['name']} ===');
          }
        }

        // Restore table data
        await _restoreTableData(tableData, backupDataSection, targetSectionName);

      } catch (e, stackTrace) {
        print('=== RESTORE DEBUG: ERROR restoring table ${tableData['name']}: $e ===');
        print('=== RESTORE DEBUG: Stack trace: $stackTrace ===');
      }
    }
  }

  /// Restore data for a specific table
  Future<void> _restoreTableData(
    Map<String, dynamic> tableData,
    Map<String, dynamic>? backupDataSection,
    String targetSectionName,
  ) async {
    final originalTableId = tableData['tableId'];
    print('=== RESTORE DEBUG: Looking for data with original table ID: $originalTableId ===');

    if (backupDataSection != null && backupDataSection[originalTableId] != null) {
      final rowsData = backupDataSection[originalTableId] as List<dynamic>;
      print('=== RESTORE DEBUG: Found ${rowsData.length} rows to restore ===');

      for (int i = 0; i < rowsData.length; i++) {
        final rowData = rowsData[i];
        print('=== RESTORE DEBUG: Creating row ${i + 1}/${rowsData.length} ===');

        // Create new row
        final rowId = await _schemaService.createRowByName(tableData['name'], targetSectionName);
        if (rowId == null) {
          print('=== RESTORE DEBUG: ERROR - Failed to create row for table: ${tableData['name']} ===');
          continue;
        }
        print('=== RESTORE DEBUG: Created row with ID: $rowId ===');

        // Restore row data
        if (rowData['data'] != null && rowData['data'].isNotEmpty) {
          final data = rowData['data'] is String
              ? jsonDecode(rowData['data'])
              : rowData['data'];
          print('=== RESTORE DEBUG: Updating row data: $data ===');

          // Convert column IDs to column names for more robust restoration
          final dataByColumnNames = <String, dynamic>{};
          if (tableData['columns'] != null) {
            for (final columnData in tableData['columns']) {
              final columnId = columnData['columnId'];
              final columnName = columnData['name'];
              if (data[columnId] != null) {
                dataByColumnNames[columnName] = data[columnId];
              }
            }
          }

          // Update row data using column names
          await _schemaService.updateRowDataByNames(
            rowId,
            tableData['name'],
            targetSectionName,
            dataByColumnNames
          );
          print('=== RESTORE DEBUG: Updated row $rowId with ${dataByColumnNames.length} column values ===');
        } else {
          print('=== RESTORE DEBUG: No data to update for row $rowId ===');
        }
      }
      print('=== RESTORE DEBUG: Successfully restored ${rowsData.length} rows for table: ${tableData['name']} ===');
    } else {
      print('=== RESTORE DEBUG: No data found for table ${tableData['name']} (originalTableId: $originalTableId) ===');
      if (backupDataSection != null) {
        print('=== RESTORE DEBUG: Available data keys: ${backupDataSection.keys.toList()} ===');
      } else {
        print('=== RESTORE DEBUG: No data section in backup ===');
      }
    }
  }

  /// Parse data type from string
  ColumnDataType _parseDataType(String dataTypeString) {
    switch (dataTypeString) {
      case 'ColumnDataType.text':
        return ColumnDataType.text;
      case 'ColumnDataType.number':
        return ColumnDataType.number;
      case 'ColumnDataType.currency':
        return ColumnDataType.currency;
      default:
        return ColumnDataType.text;
    }
  }
}
