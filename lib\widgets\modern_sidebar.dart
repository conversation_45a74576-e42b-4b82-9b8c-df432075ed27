import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../models/isar_models.dart';
import '../providers/sidebar_provider.dart';
import '../services/dynamic_schema_service.dart';
import 'section_management_dialog.dart';

class ModernSidebar extends StatefulWidget {
  final double width;
  final Color? backgroundColor;
  final bool isCollapsed;
  final VoidCallback? onToggleCollapse;

  const ModernSidebar({
    super.key,
    this.width = 280,
    this.backgroundColor,
    this.isCollapsed = false,
    this.onToggleCollapse,
  });

  @override
  State<ModernSidebar> createState() => _ModernSidebarState();
}

class _ModernSidebarState extends State<ModernSidebar> {
  final Set<String> _expandedSections = {};
  final Map<String, List<FlexibleTable>> _sectionTables = {};
  final DynamicSchemaService _schemaService = DynamicSchemaService.instance;

  @override
  void initState() {
    super.initState();
    // Pre-load tables for all sections to avoid async issues during expansion
    _preloadAllTables();
  }

  Future<void> _preloadAllTables() async {
    try {
      final provider = Provider.of<SidebarProvider>(context, listen: false);
      final sections = provider.rootSections;

      for (final section in sections) {
        if (section.sectionId != null) {
          final tables = await _schemaService.getTablesForSection(section.sectionId!);
          if (mounted) {
            _sectionTables[section.sectionId!] = tables;
          }
        }
      }

      if (mounted) {
        setState(() {
          // Tables are now loaded
        });
      }
    } catch (e) {
      debugPrint('Error preloading tables: $e');
    }
  }

  Future<void> _loadTablesForSection(String sectionId) async {
    if (_sectionTables.containsKey(sectionId)) return; // Already loaded

    try {
      final tables = await _schemaService.getTablesForSection(sectionId);
      if (mounted) {
        setState(() {
          _sectionTables[sectionId] = tables;
        });
      }
    } catch (e) {
      // Handle error silently or log it
      debugPrint('Error loading tables for section $sectionId: $e');
      // Set empty list to prevent repeated attempts
      if (mounted) {
        setState(() {
          _sectionTables[sectionId] = [];
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final effectiveWidth = widget.isCollapsed ? 60.0 : widget.width;

    return AnimatedContainer(
      duration: const Duration(milliseconds: 200),
      curve: Curves.easeOut,
      width: effectiveWidth,
      decoration: BoxDecoration(
        color: widget.backgroundColor ?? Theme.of(context).colorScheme.surface,
        border: Border(
          right: BorderSide(
            color: Theme.of(context).dividerColor,
            width: 1,
          ),
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 4,
            offset: const Offset(2, 0),
          ),
        ],
      ),
      child: Column(
        children: [
          _buildHeader(),
          if (!widget.isCollapsed)
            Expanded(
              child: _buildSectionList(),
            )
          else
            // Show collapsed section indicators
            Expanded(
              child: _buildCollapsedSectionIndicators(),
            ),
        ],
      ),
    );
  }

  Widget _buildHeader() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surfaceContainerHighest,
        border: Border(
          bottom: BorderSide(
            color: Theme.of(context).dividerColor,
          ),
        ),
      ),
      child: widget.isCollapsed
          ? Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                IconButton(
                  icon: Icon(
                    Icons.menu,
                    color: Theme.of(context).colorScheme.primary,
                    size: 24,
                  ),
                  tooltip: 'Expand sidebar',
                  onPressed: widget.onToggleCollapse,
                ),
                const SizedBox(height: 12),
                IconButton(
                  icon: Icon(
                    Icons.add,
                    color: Theme.of(context).colorScheme.primary,
                    size: 20,
                  ),
                  tooltip: 'Add Section',
                  onPressed: () => _showSectionDialog(),
                ),
              ],
            )
          : Row(
              children: [
                Icon(
                  Icons.dashboard,
                  color: Theme.of(context).colorScheme.primary,
                  size: 24,
                ),
                const SizedBox(width: 12),
                const Expanded(
                  child: Text(
                    'Database Sections',
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
                IconButton(
                  icon: const Icon(Icons.chevron_left),
                  tooltip: 'Collapse sidebar',
                  onPressed: widget.onToggleCollapse,
                ),
                IconButton(
                  icon: const Icon(Icons.add),
                  tooltip: 'Add Section',
                  onPressed: () => _showSectionDialog(),
                ),
              ],
            ),
    );
  }

  Widget _buildCollapsedSectionIndicators() {
    return Consumer<SidebarProvider>(
      builder: (context, provider, child) {
        final rootSections = provider.rootSections;

        if (rootSections.isEmpty) {
          return const SizedBox.shrink();
        }

        return ListView.builder(
          padding: const EdgeInsets.symmetric(vertical: 8),
          itemCount: rootSections.length,
          itemBuilder: (context, index) {
            final section = rootSections[index];
            final isSelected = provider.selectedSection?.sectionId == section.sectionId;
            final sectionColor = _parseColor(section.color) ?? Theme.of(context).colorScheme.primary;

            return Container(
              margin: const EdgeInsets.symmetric(vertical: 2, horizontal: 8),
              child: Tooltip(
                message: section.name ?? 'Unnamed Section',
                child: InkWell(
                  onTap: () => provider.selectSection(section),
                  borderRadius: BorderRadius.circular(8),
                  child: Container(
                    width: 44,
                    height: 44,
                    decoration: BoxDecoration(
                      color: isSelected ? sectionColor.withValues(alpha: 0.2) : null,
                      borderRadius: BorderRadius.circular(8),
                      border: isSelected ? Border.all(color: sectionColor, width: 2) : null,
                    ),
                    child: Icon(
                      _getIconData(section.icon ?? 'folder'),
                      color: isSelected ? sectionColor : Colors.grey.shade600,
                      size: 20,
                    ),
                  ),
                ),
              ),
            );
          },
        );
      },
    );
  }

  Widget _buildSectionList() {
    return Consumer<SidebarProvider>(
      builder: (context, provider, child) {
        if (provider.isLoading) {
          return const Center(child: CircularProgressIndicator());
        }

        if (provider.error != null) {
          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  Icons.error_outline,
                  size: 48,
                  color: Colors.red.shade300,
                ),
                const SizedBox(height: 16),
                Text(
                  'Error loading sections',
                  style: TextStyle(
                    color: Colors.red.shade700,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 8),
                Text(
                  provider.error!,
                  textAlign: TextAlign.center,
                  style: TextStyle(
                    color: Colors.red.shade600,
                    fontSize: 12,
                  ),
                ),
                const SizedBox(height: 16),
                ElevatedButton(
                  onPressed: () {
                    provider.clearError();
                    provider.loadSections();
                  },
                  child: const Text('Retry'),
                ),
              ],
            ),
          );
        }

        final rootSections = provider.rootSections;

        if (rootSections.isEmpty) {
          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  Icons.folder_open,
                  size: 48,
                  color: Colors.grey.shade400,
                ),
                const SizedBox(height: 16),
                Text(
                  'No sections yet',
                  style: TextStyle(
                    color: Colors.grey.shade600,
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 8),
                Text(
                  'Create your first section to get started',
                  style: TextStyle(
                    color: Colors.grey.shade500,
                    fontSize: 14,
                  ),
                ),
                const SizedBox(height: 16),
                ElevatedButton.icon(
                  onPressed: () => _showSectionDialog(),
                  icon: const Icon(Icons.add),
                  label: const Text('Add Section'),
                ),
              ],
            ),
          );
        }

        return ListView.builder(
          padding: const EdgeInsets.symmetric(vertical: 8),
          itemCount: rootSections.length,
          itemBuilder: (context, index) {
            return _buildSectionTile(rootSections[index], 0);
          },
        );
      },
    );
  }

  Widget _buildSectionTile(SidebarSection section, int depth) {
    final provider = Provider.of<SidebarProvider>(context, listen: false);
    final isSelected = provider.selectedSection?.sectionId == section.sectionId;
    final hasSubsections = provider.hasSubsections(section.sectionId!);
    final isExpanded = _expandedSections.contains(section.sectionId);
    final subsections = provider.getSubsections(section.sectionId!);

    // Check if this section has tables
    final hasTables = _sectionTables[section.sectionId]?.isNotEmpty ?? false;
    final hasChildren = hasSubsections || hasTables;

    return Column(
      children: [
        Container(
          margin: EdgeInsets.only(
            left: 8 + (depth * 16.0),
            right: 8,
            bottom: 2,
          ),
          decoration: BoxDecoration(
            color: isSelected
                ? Theme.of(context).colorScheme.primary.withValues(alpha: 0.1)
                : null,
            borderRadius: BorderRadius.circular(8),
            border: isSelected
                ? Border.all(
                    color: Theme.of(context).colorScheme.primary.withValues(alpha: 0.3),
                  )
                : null,
          ),
          child: InkWell(
            onTap: () => provider.selectSection(section),
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
              child: Row(
                children: [
                  if (hasChildren)
                    GestureDetector(
                      onTap: () => _toggleExpansion(section.sectionId!),
                      child: Icon(
                        isExpanded ? Icons.keyboard_arrow_down : Icons.keyboard_arrow_right,
                        size: 20,
                        color: Colors.grey.shade600,
                      ),
                    )
                  else
                    const SizedBox(width: 20),
                  const SizedBox(width: 8),
                  _buildSectionIcon(section),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Text(
                      section.name ?? 'Unnamed Section',
                      style: TextStyle(
                        fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
                        color: isSelected
                            ? Theme.of(context).colorScheme.primary
                            : null,
                      ),
                    ),
                  ),
                  PopupMenuButton<String>(
                    icon: Icon(
                      Icons.more_vert,
                      size: 16,
                      color: Colors.grey.shade600,
                    ),
                    onSelected: (value) => _handleSectionAction(value, section),
                    itemBuilder: (context) => [
                const PopupMenuItem(
                  value: 'edit',
                  child: Row(
                    children: [
                      Icon(Icons.edit, size: 16),
                      SizedBox(width: 8),
                      Text('Edit'),
                    ],
                  ),
                ),
                const PopupMenuItem(
                  value: 'add_subsection',
                  child: Row(
                    children: [
                      Icon(Icons.create_new_folder, size: 16),
                      SizedBox(width: 8),
                      Text('Add Subsection'),
                    ],
                  ),
                ),
                const PopupMenuItem(
                  value: 'add_table',
                  child: Row(
                    children: [
                      Icon(Icons.table_chart, size: 16),
                      SizedBox(width: 8),
                      Text('Add Table'),
                    ],
                  ),
                ),
                const PopupMenuDivider(),
                const PopupMenuItem(
                  value: 'delete',
                  child: Row(
                    children: [
                      Icon(Icons.delete, size: 16, color: Colors.red),
                      SizedBox(width: 8),
                      Text('Delete', style: TextStyle(color: Colors.red)),
                    ],
                  ),
                ),
              ],
                  ),
                ],
              ),
            ),
          ),
        ),
        if (hasChildren && isExpanded) ...[
          // Show subsections first
          ...subsections.map((subsection) =>
              _buildSectionTile(subsection, depth + 1)),
          // Then show tables for this section
          ...(_sectionTables[section.sectionId] ?? []).map((table) =>
              _buildTableTile(table, section, depth + 1)),
        ],
      ],
    );
  }

  Widget _buildSectionIcon(SidebarSection section) {
    final iconName = section.icon ?? 'folder';
    final color = _parseColor(section.color) ?? Theme.of(context).colorScheme.primary;

    return Container(
      width: 24,
      height: 24,
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(4),
      ),
      child: Icon(
        _getIconData(iconName),
        size: 16,
        color: color,
      ),
    );
  }

  Widget _buildTableTile(FlexibleTable table, SidebarSection section, int depth) {
    final provider = Provider.of<SidebarProvider>(context, listen: false);
    final isSelected = provider.selectedTable?.tableId == table.tableId;
    final sectionColor = _parseColor(section.color) ?? Theme.of(context).colorScheme.primary;

    return Container(
      margin: EdgeInsets.only(
        left: (depth * 16.0) + 8,
        right: 8,
        bottom: 2,
      ),
      decoration: BoxDecoration(
        color: isSelected
            ? sectionColor.withValues(alpha: 0.1)
            : null,
        borderRadius: BorderRadius.circular(6),
        border: isSelected
            ? Border.all(
                color: sectionColor.withValues(alpha: 0.3),
                width: 1,
              )
            : null,
      ),
      child: ListTile(
        dense: true,
        contentPadding: const EdgeInsets.symmetric(horizontal: 12, vertical: 2),
        leading: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            const SizedBox(width: 20), // Indent for table
            Container(
              width: 20,
              height: 20,
              decoration: BoxDecoration(
                color: sectionColor.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(4),
              ),
              child: Icon(
                Icons.table_chart,
                size: 14,
                color: sectionColor,
              ),
            ),
          ],
        ),
        title: Text(
          table.name ?? 'Untitled Table',
          style: TextStyle(
            fontFamily: 'Inter',
            fontSize: 13,
            fontWeight: isSelected ? FontWeight.w600 : FontWeight.w400,
            color: isSelected ? sectionColor : null,
          ),
        ),
        trailing: PopupMenuButton<String>(
          icon: Icon(
            Icons.more_vert,
            size: 14,
            color: Colors.grey.shade600,
          ),
          itemBuilder: (context) => [
            const PopupMenuItem(
              value: 'edit',
              child: Row(
                children: [
                  Icon(Icons.edit, size: 16),
                  SizedBox(width: 8),
                  Text('Edit Table'),
                ],
              ),
            ),
            PopupMenuItem(
              value: 'move',
              child: Row(
                children: [
                  Icon(Icons.folder_open, size: 16, color: Colors.orange[600]),
                  const SizedBox(width: 8),
                  const Text('Move to Section'),
                ],
              ),
            ),
            const PopupMenuItem(
              value: 'delete',
              child: Row(
                children: [
                  Icon(Icons.delete, size: 16, color: Colors.red),
                  SizedBox(width: 8),
                  Text('Delete Table', style: TextStyle(color: Colors.red)),
                ],
              ),
            ),
          ],
          onSelected: (value) => _handleTableAction(value, table, section),
        ),
        onTap: () => provider.selectTable(table),
      ),
    );
  }

  IconData _getIconData(String iconName) {
    switch (iconName) {
      case 'alarm': return Icons.notifications_active;
      case 'water_drop': return Icons.water_drop;
      case 'bubble_chart': return Icons.bubble_chart;
      case 'cloud': return Icons.cloud;
      case 'air': return Icons.air;
      case 'co2': return Icons.co2;
      case 'inventory': return Icons.inventory;
      case 'table_chart': return Icons.table_chart;
      case 'folder': return Icons.folder;
      default: return Icons.folder;
    }
  }

  Color? _parseColor(String? colorString) {
    if (colorString == null) return null;
    try {
      return Color(int.parse(colorString.replaceFirst('#', '0xFF')));
    } catch (e) {
      return null;
    }
  }

  void _toggleExpansion(String sectionId) {
    setState(() {
      if (_expandedSections.contains(sectionId)) {
        _expandedSections.remove(sectionId);
      } else {
        _expandedSections.add(sectionId);
      }
    });
    // No async operations, no rebuilding - just simple show/hide
  }

  void _handleSectionAction(String action, SidebarSection section) {
    switch (action) {
      case 'edit':
        _showSectionDialog(section: section);
        break;
      case 'add_subsection':
        _showSectionDialog(parentSection: section);
        break;
      case 'add_table':
        _showTableDialog(section);
        break;
      case 'delete':
        _showDeleteConfirmation(section);
        break;
    }
  }

  void _handleTableAction(String action, FlexibleTable table, SidebarSection section) {
    switch (action) {
      case 'edit':
        _showEditTableDialog(table, section);
        break;
      case 'move':
        _showMoveTableDialog(table, section);
        break;
      case 'delete':
        _showDeleteTableConfirmation(table, section);
        break;
    }
  }

  void _showSectionDialog({SidebarSection? section, SidebarSection? parentSection}) {
    showDialog(
      context: context,
      builder: (context) => SectionManagementDialog(
        section: section,
        parentSection: parentSection,
      ),
    );
  }

  void _showTableDialog(SidebarSection section) {
    final nameController = TextEditingController();
    final descriptionController = TextEditingController();

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Create New Table'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            TextField(
              controller: nameController,
              decoration: const InputDecoration(
                labelText: 'Table Name',
                border: OutlineInputBorder(),
              ),
            ),
            const SizedBox(height: 16),
            TextField(
              controller: descriptionController,
              decoration: const InputDecoration(
                labelText: 'Description (Optional)',
                border: OutlineInputBorder(),
              ),
              maxLines: 3,
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () async {
              if (nameController.text.trim().isNotEmpty) {
                final provider = Provider.of<SidebarProvider>(context, listen: false);
                await provider.selectSection(section);
                await provider.createTable(
                  name: nameController.text.trim(),
                  description: descriptionController.text.trim().isEmpty 
                      ? null 
                      : descriptionController.text.trim(),
                );
                if (context.mounted) Navigator.of(context).pop();
              }
            },
            child: const Text('Create'),
          ),
        ],
      ),
    );
  }

  void _showDeleteConfirmation(SidebarSection section) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete Section'),
        content: Text(
          'Are you sure you want to delete "${section.name}"?\n\n'
          'This will also delete all subsections and tables within this section. '
          'This action cannot be undone.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () async {
              final provider = Provider.of<SidebarProvider>(context, listen: false);
              await provider.deleteSection(section.sectionId!);
              if (context.mounted) Navigator.of(context).pop();
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red,
              foregroundColor: Colors.white,
            ),
            child: const Text('Delete'),
          ),
        ],
      ),
    );
  }

  void _showEditTableDialog(FlexibleTable table, SidebarSection section) {
    final nameController = TextEditingController(text: table.name);
    final descriptionController = TextEditingController(text: table.description);

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text(
          'Edit Table',
          style: TextStyle(
            fontFamily: 'Inter',
            fontWeight: FontWeight.w600,
          ),
        ),
        content: SizedBox(
          width: 400,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              TextField(
                controller: nameController,
                decoration: const InputDecoration(
                  labelText: 'Table Name',
                  border: OutlineInputBorder(),
                ),
              ),
              const SizedBox(height: 16),
              TextField(
                controller: descriptionController,
                decoration: const InputDecoration(
                  labelText: 'Description (Optional)',
                  border: OutlineInputBorder(),
                ),
                maxLines: 3,
              ),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text(
              'Cancel',
              style: TextStyle(fontFamily: 'Inter'),
            ),
          ),
          ElevatedButton(
            onPressed: () async {
              if (nameController.text.trim().isNotEmpty) {
                final navigator = Navigator.of(context);
                final messenger = ScaffoldMessenger.of(context);
                try {
                  await _schemaService.updateTable(
                    table.tableId!,
                    name: nameController.text.trim(),
                    description: descriptionController.text.trim().isEmpty
                        ? null
                        : descriptionController.text.trim(),
                  );
                  // Refresh tables for the section
                  if (mounted) {
                    final currentProvider = Provider.of<SidebarProvider>(context, listen: false);
                    if (currentProvider.selectedSection?.sectionId != null) {
                      _sectionTables.remove(currentProvider.selectedSection!.sectionId!);
                      await _loadTablesForSection(currentProvider.selectedSection!.sectionId!);
                    }
                  }
                  if (mounted) {
                    navigator.pop();
                    messenger.showSnackBar(
                      const SnackBar(
                        content: Text('Table updated successfully'),
                        backgroundColor: Colors.green,
                      ),
                    );
                  }
                } catch (e) {
                  if (mounted) {
                    messenger.showSnackBar(
                      SnackBar(
                        content: Text('Error updating table: $e'),
                        backgroundColor: Colors.red,
                      ),
                    );
                  }
                }
              }
            },
            child: const Text(
              'Update',
              style: TextStyle(fontFamily: 'Inter'),
            ),
          ),
        ],
      ),
    );
  }

  void _showMoveTableDialog(FlexibleTable table, SidebarSection currentSection) {
    showDialog(
      context: context,
      builder: (context) => FutureBuilder<List<SidebarSection>>(
        future: _schemaService.getAllSections(),
        builder: (context, snapshot) {
          if (!snapshot.hasData) {
            return const AlertDialog(
              title: Text('Loading...'),
              content: SizedBox(
                height: 100,
                child: Center(child: CircularProgressIndicator()),
              ),
            );
          }

          final allSections = snapshot.data!;
          // Filter out the current section
          final availableSections = allSections
              .where((s) => s.sectionId != currentSection.sectionId)
              .toList();

          if (availableSections.isEmpty) {
            return AlertDialog(
              title: const Text(
                'Move Table',
                style: TextStyle(
                  fontFamily: 'Inter',
                  fontWeight: FontWeight.w600,
                ),
              ),
              content: const Text('No other sections available to move this table to.'),
              actions: [
                TextButton(
                  onPressed: () => Navigator.of(context).pop(),
                  child: const Text('OK'),
                ),
              ],
            );
          }

          return AlertDialog(
            title: const Text(
              'Move Table',
              style: TextStyle(
                fontFamily: 'Inter',
                fontWeight: FontWeight.w600,
              ),
            ),
            content: SizedBox(
              width: 400,
              child: Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Move "${table.name}" to:',
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  const SizedBox(height: 16),
                  Container(
                    padding: const EdgeInsets.all(12),
                    decoration: BoxDecoration(
                      color: Colors.blue.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(8),
                      border: Border.all(color: Colors.blue.withValues(alpha: 0.3)),
                    ),
                    child: Row(
                      children: [
                        Icon(Icons.info_outline, color: Colors.blue[700], size: 16),
                        const SizedBox(width: 8),
                        Expanded(
                          child: Text(
                            'Currently in: ${currentSection.name}',
                            style: TextStyle(
                              color: Colors.blue[700],
                              fontSize: 12,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                  const SizedBox(height: 16),
                  const Text(
                    'Select destination section:',
                    style: TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  const SizedBox(height: 8),
                  ...availableSections.map((section) => Card(
                    margin: const EdgeInsets.only(bottom: 8),
                    child: ListTile(
                      leading: Icon(
                        _getIconData(section.icon ?? 'folder'),
                        color: Colors.blue[600],
                      ),
                      title: Text(
                        section.name ?? 'Unnamed Section',
                        style: const TextStyle(
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                      trailing: const Icon(Icons.arrow_forward_ios, size: 16),
                      onTap: () async {
                        final navigator = Navigator.of(context);
                        final messenger = ScaffoldMessenger.of(context);
                        try {
                          await _schemaService.moveTableToSection(
                            table.tableId!,
                            section.sectionId!,
                          );

                          // Refresh tables for both sections
                          if (mounted) {
                            _sectionTables.remove(currentSection.sectionId!);
                            _sectionTables.remove(section.sectionId!);

                            // Reload current section if it's selected
                            final currentProvider = Provider.of<SidebarProvider>(context, listen: false);
                            if (currentProvider.selectedSection?.sectionId != null) {
                              await _loadTablesForSection(currentProvider.selectedSection!.sectionId!);
                            }
                          }

                          if (mounted) {
                            navigator.pop();
                            messenger.showSnackBar(
                              SnackBar(
                                content: Text('Table "${table.name}" moved to "${section.name}"'),
                                backgroundColor: Colors.green,
                              ),
                            );
                          }
                        } catch (e) {
                          if (mounted) {
                            messenger.showSnackBar(
                              SnackBar(
                                content: Text('Error moving table: $e'),
                                backgroundColor: Colors.red,
                              ),
                            );
                          }
                        }
                      },
                    ),
                  )),
                ],
              ),
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: const Text(
                  'Cancel',
                  style: TextStyle(fontFamily: 'Inter'),
                ),
              ),
            ],
          );
        },
      ),
    );
  }

  void _showDeleteTableConfirmation(FlexibleTable table, SidebarSection section) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text(
          'Delete Table',
          style: TextStyle(
            fontFamily: 'Inter',
            fontWeight: FontWeight.w600,
          ),
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Are you sure you want to delete "${table.name}"?',
              style: const TextStyle(fontFamily: 'Inter'),
            ),
            const SizedBox(height: 8),
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.red.shade50,
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.red.shade200),
              ),
              child: Row(
                children: [
                  Icon(Icons.warning, color: Colors.red.shade600, size: 20),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      'This action cannot be undone. All data in this table will be permanently deleted.',
                      style: TextStyle(
                        fontFamily: 'Inter',
                        fontSize: 13,
                        color: Colors.red.shade700,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text(
              'Cancel',
              style: TextStyle(fontFamily: 'Inter'),
            ),
          ),
          ElevatedButton(
            onPressed: () async {
              final navigator = Navigator.of(context);
              final messenger = ScaffoldMessenger.of(context);
              try {
                await _schemaService.deleteTable(table.tableId!);
                // Refresh tables for the section
                _sectionTables.remove(section.sectionId!);
                if (mounted) {
                  navigator.pop();
                  messenger.showSnackBar(
                    SnackBar(
                      content: Text('Table "${table.name}" deleted successfully'),
                      backgroundColor: Colors.green,
                    ),
                  );
                }
              } catch (e) {
                if (mounted) {
                  messenger.showSnackBar(
                    SnackBar(
                      content: Text('Error deleting table: $e'),
                      backgroundColor: Colors.red,
                    ),
                  );
                }
              }
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red.shade600,
              foregroundColor: Colors.white,
            ),
            child: const Text(
              'Delete',
              style: TextStyle(fontFamily: 'Inter'),
            ),
          ),
        ],
      ),
    );
  }
}
