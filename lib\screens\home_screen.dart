import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../constants/app_constants.dart';
import '../services/project_provider.dart';
import '../services/auth_service.dart';
import '../services/app_refresh_service.dart';
import 'project_list_screen.dart';
import 'new_project_screen.dart';
import 'login_screen.dart';
import 'clean_agent_calculator_screen.dart';

class HomeScreen extends StatefulWidget {
  const HomeScreen({super.key});

  @override
  State<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> {
  @override
  Widget build(BuildContext context) {
    final authService = Provider.of<AuthService>(context);
    final projectProvider = Provider.of<ProjectProvider>(context);

    return Scaffold(
      appBar: AppBar(
        title: const Text(AppConstants.appName),
        centerTitle: true,
        actions: [
          // Refresh button
          RefreshButton(
            tooltip: 'Refresh App (Hot Reload)',
            onPressed: () async {
              await AppRefreshService().refreshApp(context);
            },
          ),

          // Logout button
          IconButton(
            icon: const Icon(Icons.logout),
            tooltip: 'Logout',
            onPressed: () async {
              await authService.logout();
              if (!context.mounted) return;

              Navigator.of(context).pushReplacement(
                MaterialPageRoute(builder: (context) => const LoginScreen()),
              );
            },
          ),
        ],
      ),
      body: Center(
        child: SingleChildScrollView(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              const Icon(
                Icons.local_fire_department,
                size: 120,
                color: AppConstants.primaryColor,
              ),
              const SizedBox(height: 24),
              const Text(
                'Fire System Estimating Tool',
                style: TextStyle(
                  fontSize: 24,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 8),
              const Padding(
                padding: EdgeInsets.symmetric(horizontal: 32.0),
                child: Text(
                  'Create accurate estimates for fire alarm, sprinkler, and suppression systems',
                  textAlign: TextAlign.center,
                  style: TextStyle(
                    fontSize: 16,
                    color: AppConstants.secondaryTextColor,
                  ),
                ),
              ),
              const SizedBox(height: 16),

              // User info
              Padding(
                padding: const EdgeInsets.all(8.0),
                child: Text(
                  'Logged in as: ${authService.currentUser?.username ?? "Unknown"}',
                  style: const TextStyle(
                    fontSize: 14,
                    fontStyle: FontStyle.italic,
                  ),
                ),
              ),
              const SizedBox(height: 32),

              // Main action buttons
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 32.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.stretch,
                  children: [
                    ElevatedButton.icon(
                      icon: const Icon(Icons.add),
                      label: const Text('New Project'),
                      style: ElevatedButton.styleFrom(
                        padding: const EdgeInsets.symmetric(vertical: 16),
                        textStyle: const TextStyle(fontSize: 18),
                      ),
                      onPressed: () {
                        Navigator.push(
                          context,
                          MaterialPageRoute(builder: (context) => const NewProjectScreen()),
                        );
                      },
                    ),
                    const SizedBox(height: 16),
                    OutlinedButton.icon(
                      icon: const Icon(Icons.folder_open),
                      label: const Text('Open Project'),
                      style: OutlinedButton.styleFrom(
                        padding: const EdgeInsets.symmetric(vertical: 16),
                        textStyle: const TextStyle(fontSize: 18),
                      ),
                      onPressed: () {
                        Navigator.push(
                          context,
                          MaterialPageRoute(builder: (context) => const ProjectListScreen()),
                        );
                      },
                    ),

                    // Quick Quote button
                    const SizedBox(height: 16),
                    OutlinedButton.icon(
                      icon: const Icon(Icons.calculate),
                      label: const Text('Quick Quote'),
                      style: OutlinedButton.styleFrom(
                        padding: const EdgeInsets.symmetric(vertical: 16),
                        textStyle: const TextStyle(fontSize: 18),
                      ),
                      onPressed: () => _showQuickQuoteDialog(context),
                    ),

                    // AI Assistant button
                    const SizedBox(height: 16),
                    OutlinedButton.icon(
                      icon: const Icon(Icons.smart_toy),
                      label: const Text('AI Assistant'),
                      style: OutlinedButton.styleFrom(
                        padding: const EdgeInsets.symmetric(vertical: 16),
                        textStyle: const TextStyle(fontSize: 18),
                      ),
                      onPressed: () {
                        Navigator.pushNamed(context, '/ai_assistant');
                      },
                    ),

                    // Admin Dashboard button (renamed from Database Management)
                    const SizedBox(height: 16),
                    OutlinedButton.icon(
                      icon: const Icon(Icons.admin_panel_settings),
                      label: const Text('Admin Dashboard'),
                      style: OutlinedButton.styleFrom(
                        padding: const EdgeInsets.symmetric(vertical: 16),
                        textStyle: const TextStyle(fontSize: 18),
                      ),
                      onPressed: () {
                        Navigator.pushNamed(context, '/unified_dashboard');
                      },
                    ),
                  ],
                ),
              ),

              const SizedBox(height: 32),

              // Error messages
              if (projectProvider.error != null)
                Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Text(
                    projectProvider.error!,
                    style: const TextStyle(color: AppConstants.errorColor),
                    textAlign: TextAlign.center,
                  ),
                ),
            ],
          ),
        ),
      ),
    );
  }

  // Map of system types to their icons (same as AddSystemScreen)
  final Map<String, IconData> _systemIcons = {
    'Fire Alarm': Icons.notifications_active,
    'Water Sprinkler': Icons.water_drop,
    'Foam System': Icons.bubble_chart,
    'Clean Agent': Icons.science,
    'CO2 System': Icons.gas_meter,
    'Emergency Lighting': Icons.lightbulb,
    'Fire Extinguishers': Icons.fire_extinguisher,
    'Fire Hydrant': Icons.water,
    'Fire Hose Reel': Icons.water,
    'Deluge System': Icons.water_drop,
    'Pre-Action System': Icons.water_drop,
    'Dry Chemical System': Icons.science,
    'Piping': Icons.plumbing,
  };

  // Get default icon if not found in the map
  IconData _getIconForSystem(String systemType) {
    return _systemIcons[systemType] ?? Icons.build;
  }

  void _showQuickQuoteDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text(
          'Quick Quote - Select System Type',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
          ),
        ),
        content: SizedBox(
          width: double.maxFinite,
          height: 400,
          child: ListView.builder(
            itemCount: AppConstants.systemTypes.length,
            itemBuilder: (context, index) {
              final systemType = AppConstants.systemTypes[index];
              return _buildSystemListItem(
                systemType: systemType,
                icon: _getIconForSystem(systemType),
                onTap: () => _handleSystemSelection(context, systemType),
              );
            },
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
        ],
      ),
    );
  }

  Widget _buildSystemListItem({
    required String systemType,
    required IconData icon,
    required VoidCallback onTap,
  }) {
    return Card(
      margin: const EdgeInsets.symmetric(vertical: 4, horizontal: 0),
      elevation: 1,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(8),
      ),
      child: ListTile(
        contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        leading: Container(
          padding: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: Colors.blue.shade50,
            borderRadius: BorderRadius.circular(8),
          ),
          child: Icon(
            icon,
            size: 24,
            color: AppConstants.primaryColor,
          ),
        ),
        title: Text(
          systemType,
          style: const TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.bold,
          ),
        ),
        subtitle: Text(
          'Get quick cost estimate for $systemType',
          style: const TextStyle(
            fontSize: 12,
            color: Colors.grey,
          ),
        ),
        onTap: onTap,
      ),
    );
  }

  void _handleSystemSelection(BuildContext context, String systemType) {
    Navigator.of(context).pop(); // Close the dialog

    if (systemType == 'Clean Agent') {
      // For Clean Agent systems, open the calculator directly
      Navigator.push(
        context,
        MaterialPageRoute(
          builder: (context) => const CleanAgentCalculatorScreen(
            exchangeRate: 3.75, // Default exchange rate
            shippingFactor: 1.15, // Default shipping factor
            marginFactor: 1.0, // Default margin factor
          ),
        ),
      );
    } else {
      // For other systems, show a message that they're not yet implemented
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('$systemType quick quote coming soon!'),
          backgroundColor: Colors.orange,
        ),
      );
    }
  }
}
