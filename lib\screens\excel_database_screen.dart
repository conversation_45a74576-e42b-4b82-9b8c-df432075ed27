import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../services/auth_service.dart';
import 'complete_excel_grid_factory.dart';
import '../constants/app_constants.dart';

class ExcelDatabaseScreen extends StatefulWidget {
  final String? initialSystem;

  const ExcelDatabaseScreen({super.key, this.initialSystem});

  @override
  State<ExcelDatabaseScreen> createState() => _ExcelDatabaseScreenState();
}

class _ExcelDatabaseScreenState extends State<ExcelDatabaseScreen> {
  @override
  void initState() {
    super.initState();

    // If initialSystem is provided, navigate to that system's data screen
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (widget.initialSystem != null) {
        _navigateToDataScreen(widget.initialSystem!);
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    final authService = Provider.of<AuthService>(context);

    if (!authService.isAdmin) {
      return Scaffold(
        appBar: AppBar(
          title: const Text('Excel Database Management'),
        ),
        body: const Center(
          child: Text('You do not have permission to access this page.'),
        ),
      );
    }

    return Scaffold(
      appBar: AppBar(
        title: const Text('Excel Database Management'),
        backgroundColor: AppConstants.primaryColor,
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Admin info card
            Card(
              elevation: 4,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'Excel-like Database Management',
                      style: TextStyle(
                        fontSize: 24,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Text(
                      'Logged in as: ${authService.currentUser?.username}',
                      style: const TextStyle(
                        fontSize: 16,
                      ),
                    ),
                    const SizedBox(height: 16),
                    const Text(
                      'Manage your application data with our true Excel-like interface. Direct typing in cells, multiple cell selection, keyboard navigation, and more.',
                      style: TextStyle(
                        fontSize: 14,
                        color: Colors.grey,
                      ),
                    ),
                  ],
                ),
              ),
            ),
            const SizedBox(height: 24),

            // Features card
            Card(
              elevation: 4,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'Excel-like Features',
                      style: TextStyle(
                        fontSize: 20,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 16),
                    _buildFeatureItem(
                      icon: Icons.keyboard,
                      title: 'Direct Typing',
                      description: 'Just select a cell and start typing',
                    ),
                    _buildFeatureItem(
                      icon: Icons.select_all,
                      title: 'Multiple Cell Selection',
                      description: 'Click and drag to select multiple cells',
                    ),
                    _buildFeatureItem(
                      icon: Icons.arrow_forward,
                      title: 'Keyboard Navigation',
                      description: 'Use arrow keys, Tab, and Enter to navigate',
                    ),
                    _buildFeatureItem(
                      icon: Icons.content_copy,
                      title: 'Copy/Paste',
                      description: 'Ctrl+C to copy, Ctrl+V to paste',
                    ),
                    _buildFeatureItem(
                      icon: Icons.settings_overscan,
                      title: 'Resizable Columns/Rows',
                      description: 'Drag column/row borders to resize',
                    ),
                    _buildFeatureItem(
                      icon: Icons.file_upload,
                      title: 'Import/Export',
                      description: 'Import from Excel or export to Excel',
                    ),
                  ],
                ),
              ),
            ),
            const SizedBox(height: 24),

            // Database sections
            const Text(
              'Database Sections',
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),

            // Grid of database options
            GridView.count(
              crossAxisCount: 3,
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              mainAxisSpacing: 16,
              crossAxisSpacing: 16,
              children: [
                _buildDatabaseCard(
                  title: 'Fire Alarm Systems',
                  icon: Icons.notifications_active,
                  color: Colors.red.shade700,
                  onTap: () => _navigateToDataScreen('alarm'),
                ),
                _buildDatabaseCard(
                  title: 'Water Systems',
                  icon: Icons.water_drop,
                  color: Colors.blue.shade700,
                  onTap: () => _navigateToDataScreen('water'),
                ),
                _buildDatabaseCard(
                  title: 'Foam Systems',
                  icon: Icons.bubble_chart,
                  color: Colors.amber.shade700,
                  onTap: () => _navigateToDataScreen('foam'),
                ),
                _buildDatabaseCard(
                  title: 'FM200 Systems',
                  icon: Icons.air,
                  color: Colors.green.shade700,
                  onTap: () => _navigateToDataScreen('fm200'),
                ),
                _buildDatabaseCard(
                  title: 'Novec Systems',
                  icon: Icons.air_sharp,
                  color: Colors.teal.shade700,
                  onTap: () => _navigateToDataScreen('novec'),
                ),
                _buildDatabaseCard(
                  title: 'CO2 Systems',
                  icon: Icons.science,
                  color: Colors.purple.shade700,
                  onTap: () => _navigateToDataScreen('co2'),
                ),
                _buildDatabaseCard(
                  title: 'Materials',
                  icon: Icons.category,
                  color: Colors.brown.shade700,
                  onTap: () => _navigateToDataScreen('materials'),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildFeatureItem({
    required IconData icon,
    required String title,
    required String description,
  }) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 12),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: Color.fromRGBO(
                AppConstants.primaryColor.red,
                AppConstants.primaryColor.green,
                AppConstants.primaryColor.blue,
                0.1,
              ),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Icon(
              icon,
              color: AppConstants.primaryColor,
              size: 24,
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: const TextStyle(
                    fontWeight: FontWeight.bold,
                    fontSize: 16,
                  ),
                ),
                Text(
                  description,
                  style: TextStyle(
                    color: Colors.grey.shade600,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDatabaseCard({
    required String title,
    required IconData icon,
    required Color color,
    required VoidCallback onTap,
  }) {
    return Card(
      elevation: 4,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: Color.fromRGBO(
                    color.red,
                    color.green,
                    color.blue,
                    0.1,
                  ),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Icon(
                  icon,
                  size: 48,
                  color: color,
                ),
              ),
              const SizedBox(height: 16),
              Text(
                title,
                style: const TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                ),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _navigateToDataScreen(String systemType) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => CompleteExcelGridFactory.createScreen(systemType),
      ),
    );
  }
}
