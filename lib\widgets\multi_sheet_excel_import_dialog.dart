import 'package:flutter/material.dart';
import 'package:file_picker/file_picker.dart';
import 'package:excel/excel.dart';
import 'dart:io';
import '../widgets/import_preview_dialog.dart';
import '../services/dynamic_schema_service.dart';
import '../models/isar_models.dart';

class MultiSheetExcelImportDialog extends StatefulWidget {
  final String? currentSectionId;
  final String? currentSectionName;
  final VoidCallback? onImportComplete;

  const MultiSheetExcelImportDialog({
    super.key,
    this.currentSectionId,
    this.currentSectionName,
    this.onImportComplete,
  });

  @override
  State<MultiSheetExcelImportDialog> createState() => _MultiSheetExcelImportDialogState();
}

// Simple data classes for import results
class SheetImportInfo {
  final String sheetName;
  final String subsectionName;
  final int rowsImported;

  SheetImportInfo({
    required this.sheetName,
    required this.subsectionName,
    required this.rowsImported,
  });
}

class MultiSheetImportResult {
  final bool success;
  final String sectionName;
  final int totalTablesImported;
  final int totalRowsImported;
  final List<SheetImportInfo> importedSheets;
  final List<String> errors;

  MultiSheetImportResult({
    required this.success,
    required this.sectionName,
    required this.totalTablesImported,
    required this.totalRowsImported,
    required this.importedSheets,
    required this.errors,
  });
}

class _MultiSheetExcelImportDialogState extends State<MultiSheetExcelImportDialog> {
  String? _selectedFilePath;
  List<String> _sheetNames = [];
  bool _overwriteExisting = false;
  bool _isLoading = false;
  bool _isAnalyzing = false;



  @override
  void dispose() {
    super.dispose();
  }

  Future<void> _pickExcelFile() async {
    try {
      setState(() => _isAnalyzing = true);
      
      final result = await FilePicker.platform.pickFiles(
        type: FileType.custom,
        allowedExtensions: ['xlsx', 'xls'],
        allowMultiple: false,
      );

      if (result != null && result.files.isNotEmpty) {
        _selectedFilePath = result.files.first.path;
        await _analyzeExcelFile();
      }
    } catch (e) {
      _showError('Error picking file: $e');
    } finally {
      setState(() => _isAnalyzing = false);
    }
  }

  Future<void> _analyzeExcelFile() async {
    if (_selectedFilePath == null) return;

    try {
      final bytes = await File(_selectedFilePath!).readAsBytes();
      final excel = Excel.decodeBytes(bytes);

      setState(() {
        _sheetNames = excel.tables.keys.toList();
      });

    } catch (e) {
      _showError('Error analyzing Excel file: $e');
    }
  }

  Future<void> _performImport() async {
    if (_selectedFilePath == null) {
      _showError('Please select a file');
      return;
    }

    setState(() => _isLoading = true);

    try {
      // Read Excel file to get sheets
      final bytes = await File(_selectedFilePath!).readAsBytes();
      final excel = Excel.decodeBytes(bytes);

      // Process each sheet with column mapping
      final errors = <String>[];
      final importedSheets = <SheetImportInfo>[];
      int totalRowsImported = 0;

      for (final sheetName in excel.tables.keys) {
        try {
          final sheet = excel.tables[sheetName];
          if (sheet == null || sheet.rows.isEmpty) {
            errors.add('Sheet "$sheetName" is empty');
            continue;
          }

          // Convert sheet to rows format - EXACT same logic as single table import
          final rows = <List<dynamic>>[];
          for (final row in sheet.rows) {
            final rowData = row.map((cell) => cell?.value?.toString() ?? '').toList();
            rows.add(rowData);
          }

          if (rows.isEmpty) continue;

          // Analyze data types - EXACT same logic as single table import
          final headers = rows.first.map((h) => h.toString().trim()).toList();
          final dataRows = rows.skip(1).toList();
          final detectedTypes = _analyzeDataTypes(headers, dataRows);

          // Show column mapping dialog for this sheet - data is already strings
          final sampleRows = dataRows.take(5).map((row) =>
            row.map((cell) => cell?.toString() ?? '').toList()
          ).toList();

          final mappings = await _showColumnMappingDialog(
            sheetName,
            headers,
            sampleRows,
            detectedTypes,
          );

          if (mappings == null) {
            // User cancelled
            continue;
          }

          // Create table and import with mappings
          final rowsImported = await _importSheetWithMappings(
            sheetName,
            rows,
            mappings,
          );

          importedSheets.add(SheetImportInfo(
            sheetName: sheetName,
            subsectionName: sheetName,
            rowsImported: rowsImported,
          ));

          totalRowsImported += rowsImported;

        } catch (e) {
          errors.add('Error importing sheet "$sheetName": $e');
        }
      }

      final result = MultiSheetImportResult(
        success: importedSheets.isNotEmpty,
        sectionName: widget.currentSectionName ?? 'Imported Tables',
        totalTablesImported: importedSheets.length,
        totalRowsImported: totalRowsImported,
        importedSheets: importedSheets,
        errors: errors,
      );

      if (result.success) {
        _showSuccessDialog(result);
        // Call refresh callback to update the database management screen
        widget.onImportComplete?.call();
      } else {
        _showError('Import failed:\n${result.errors.join('\n')}');
      }
    } catch (e) {
      _showError('Import error: $e');
    } finally {
      setState(() => _isLoading = false);
    }
  }

  void _showSuccessDialog(MultiSheetImportResult result) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Row(
          children: [
            Icon(Icons.check_circle, color: Colors.green),
            SizedBox(width: 8),
            Text('Import Successful'),
          ],
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('Section: ${result.sectionName}'),
            const SizedBox(height: 8),
            Text('Tables imported: ${result.totalTablesImported}'),
            Text('Total rows: ${result.totalRowsImported}'),
            const SizedBox(height: 16),
            const Text('Imported sheets:', style: TextStyle(fontWeight: FontWeight.bold)),
            ...result.importedSheets.map((sheet) => Padding(
              padding: const EdgeInsets.only(left: 16, top: 4),
              child: Text('• ${sheet.sheetName} → ${sheet.subsectionName} (${sheet.rowsImported} rows)'),
            )),
            if (result.errors.isNotEmpty) ...[
              const SizedBox(height: 16),
              const Text('Warnings:', style: TextStyle(fontWeight: FontWeight.bold, color: Colors.orange)),
              ...result.errors.map((error) => Padding(
                padding: const EdgeInsets.only(left: 16, top: 4),
                child: Text('• $error', style: const TextStyle(color: Colors.orange)),
              )),
            ],
          ],
        ),
        actions: [
          TextButton(
            onPressed: () {
              Navigator.of(context).pop(); // Close success dialog
              Navigator.of(context).pop(); // Close import dialog
            },
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }

  void _showError(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.red,
        duration: const Duration(seconds: 5),
      ),
    );
  }



  @override
  Widget build(BuildContext context) {
    return Dialog(
      child: Container(
        width: 600,
        height: 500,
        padding: const EdgeInsets.all(24),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header
            Row(
              children: [
                const Icon(Icons.table_chart, size: 32, color: Colors.blue),
                const SizedBox(width: 12),
                const Text(
                  'Import Multiple Tables from Excel',
                  style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
                ),
                const Spacer(),
                IconButton(
                  onPressed: () => Navigator.of(context).pop(),
                  icon: const Icon(Icons.close),
                ),
              ],
            ),
            const SizedBox(height: 24),

            // Import destination info
            if (widget.currentSectionName != null)
              Card(
                child: Padding(
                  padding: const EdgeInsets.all(16),
                  child: Row(
                    children: [
                      Icon(
                        Icons.folder,
                        color: Colors.blue[600],
                      ),
                      const SizedBox(width: 12),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            const Text(
                              'Import Destination',
                              style: TextStyle(
                                fontSize: 14,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                            const SizedBox(height: 4),
                            Text(
                              widget.currentSectionName!,
                              style: TextStyle(
                                fontSize: 16,
                                color: Colors.blue[700],
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                            const SizedBox(height: 4),
                            Text(
                              'Tables will be imported to this section',
                              style: TextStyle(
                                color: Colors.grey[600],
                                fontSize: 12,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            const SizedBox(height: 16),

            // File selection
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text('1. Select Excel File', style: TextStyle(fontWeight: FontWeight.bold)),
                    const SizedBox(height: 12),
                    Row(
                      children: [
                        Expanded(
                          child: Text(
                            _selectedFilePath?.split('/').last.split('\\').last ?? 'No file selected',
                            style: TextStyle(
                              color: _selectedFilePath != null ? Colors.black : Colors.grey,
                            ),
                          ),
                        ),
                        const SizedBox(width: 12),
                        ElevatedButton.icon(
                          onPressed: _isAnalyzing ? null : _pickExcelFile,
                          icon: _isAnalyzing
                            ? const SizedBox(width: 16, height: 16, child: CircularProgressIndicator(strokeWidth: 2))
                            : const Icon(Icons.file_upload),
                          label: Text(_isAnalyzing ? 'Analyzing...' : 'Browse'),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),
            const SizedBox(height: 16),

            // Sheet preview
            if (_sheetNames.isNotEmpty) ...[
              Expanded(
                child: Card(
                  child: Padding(
                    padding: const EdgeInsets.all(16),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        const Text('2. Sheets to Import', style: TextStyle(fontWeight: FontWeight.bold)),
                        const SizedBox(height: 12),

                        CheckboxListTile(
                          title: const Text('Overwrite existing tables'),
                          value: _overwriteExisting,
                          onChanged: (value) => setState(() => _overwriteExisting = value ?? false),
                        ),

                        const SizedBox(height: 16),
                        const Text('Each sheet will be imported as a separate table:', style: TextStyle(fontWeight: FontWeight.w500)),
                        const SizedBox(height: 8),

                        Expanded(
                          child: ListView.builder(
                            itemCount: _sheetNames.length,
                            itemBuilder: (context, index) {
                              return ListTile(
                                leading: const Icon(Icons.table_chart, color: Colors.green),
                                title: Text(_sheetNames[index]),
                                subtitle: Text('Will be imported as "${_sheetNames[index]}" table'),
                              );
                            },
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ),
              const SizedBox(height: 16),
            ],

            // Import button
            SizedBox(
              width: double.infinity,
              child: ElevatedButton.icon(
                onPressed: (_selectedFilePath != null && !_isLoading)
                  ? _performImport
                  : null,
                icon: _isLoading
                  ? const SizedBox(width: 20, height: 20, child: CircularProgressIndicator(strokeWidth: 2))
                  : const Icon(Icons.upload),
                label: Text(_isLoading ? 'Importing...' : 'Import Tables'),
                style: ElevatedButton.styleFrom(
                  padding: const EdgeInsets.symmetric(vertical: 16),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Future<List<ColumnMapping>?> _showColumnMappingDialog(
    String sheetName,
    List<String> headers,
    List<List<dynamic>> sampleRows,
    List<ColumnDataType> detectedTypes,
  ) async {
    final previewData = ImportPreviewData(
      headers: headers,
      sampleRows: sampleRows,
      detectedTypes: detectedTypes,
      fileName: '$sheetName (Sheet)',
    );

    List<ColumnMapping>? result;
    await showDialog(
      context: context,
      builder: (context) => ImportPreviewDialog(
        previewData: previewData,
        onConfirm: (mappings) {
          result = mappings;
        },
      ),
    );

    return result;
  }

  Future<int> _importSheetWithMappings(
    String sheetName,
    List<List<dynamic>> rows,
    List<ColumnMapping> mappings,
  ) async {
    try {
      final schemaService = DynamicSchemaService.instance;

      // Use current section passed from parent
      String sectionId;
      String sectionName;

      if (widget.currentSectionId != null && widget.currentSectionName != null) {
        // Import to current section
        sectionId = widget.currentSectionId!;
        sectionName = widget.currentSectionName!;
        print('Importing to current section: $sectionName ($sectionId)');
      } else {
        // Fallback: Get or create "Imported Tables" section
        final sections = await schemaService.getAllSections();
        SidebarSection? targetSection = sections.where((s) => s.name == 'Imported Tables').firstOrNull;

        if (targetSection == null) {
          // Create a new section for imported tables
          sectionId = await schemaService.createSection(
            name: 'Imported Tables',
            icon: 'table_chart',
            systemType: SystemType.custom,
          );
          sectionName = 'Imported Tables';
        } else {
          sectionId = targetSection.sectionId!;
          sectionName = targetSection.name!;
        }
        print('Importing to fallback section: $sectionName ($sectionId)');
      }

      print('Creating table "$sheetName" in section "$sectionId"');

      // Create table
      final tableId = await schemaService.createTable(
        name: sheetName,
        sectionId: sectionId,
      );

      print('Created table with ID: $tableId');

      // Create columns based on mappings
      for (final mapping in mappings) {
        if (mapping.shouldImport) {
          print('Creating column: ${mapping.header} (${mapping.dataType})');
          await schemaService.createColumn(
            tableId: tableId,
            name: mapping.header,
            dataType: mapping.dataType,
            isRequired: false,
          );
        }
      }

      print('Created ${mappings.where((m) => m.shouldImport).length} columns');

      // Import data directly using schema service - EXACT same logic as single import
      final headers = rows.first.map((h) => h.toString().trim()).toList();
      final dataRows = rows.skip(1).toList();

      // Get columns for this table
      final columns = await schemaService.getColumnsForTable(tableId);

      // Create mapping from header index to column
      final columnMapping = <int, FlexibleColumn>{};
      for (int i = 0; i < headers.length; i++) {
        final header = headers[i];
        final mapping = mappings.where((m) => m.header == header && m.shouldImport).firstOrNull;
        if (mapping != null) {
          final column = columns.where((c) => c.name?.toLowerCase() == header.toLowerCase()).firstOrNull;
          if (column != null) {
            columnMapping[i] = column;
          }
        }
      }

      // Process each data row
      int successfulRows = 0;
      for (int rowIndex = 0; rowIndex < dataRows.length; rowIndex++) {
        try {
          final row = dataRows[rowIndex];
          final rowData = <String, dynamic>{};

          // Map data to columns
          for (final entry in columnMapping.entries) {
            final cellIndex = entry.key;
            final column = entry.value;

            if (cellIndex < row.length) {
              // EXACT same logic as single table import - data is already strings
              final cellValue = row[cellIndex]?.toString().trim() ?? '';

              // Validate and convert data based on column type - SAME AS SINGLE IMPORT
              final convertedValue = _convertCellValue(cellValue, column.dataType);
              if (convertedValue != null) {
                rowData[column.columnId!] = convertedValue;
              }
            }
          }

          // Create row in database
          final rowId = await schemaService.createRow(tableId);
          await schemaService.updateRowData(rowId, rowData);
          successfulRows++;
        } catch (e) {
          print('Error importing row $rowIndex: $e');
          // Skip this row on error
          continue;
        }
      }

      print('Successfully imported $successfulRows rows to table "$sheetName"');
      return successfulRows;

    } catch (e) {
      print('Error in _importSheetWithMappings: $e');
      return 0;
    }
  }

  List<ColumnDataType> _analyzeDataTypes(List<String> headers, List<List<dynamic>> dataRows) {
    final types = <ColumnDataType>[];

    for (int colIndex = 0; colIndex < headers.length; colIndex++) {
      ColumnDataType detectedType = ColumnDataType.text;

      // Sample a few rows to detect type
      for (final row in dataRows.take(10)) {
        if (colIndex < row.length) {
          // EXACT same logic as single table import - data is already strings
          final value = row[colIndex]?.toString().trim() ?? '';
          if (value.isNotEmpty) {
            if (_isNumeric(value)) {
              detectedType = ColumnDataType.number;
            } else if (_isCurrency(value)) {
              detectedType = ColumnDataType.currency;
              break; // Currency is more specific than number
            } else if (_isDate(value)) {
              detectedType = ColumnDataType.date;
            } else if (_isBoolean(value)) {
              detectedType = ColumnDataType.boolean;
            }
          }
        }
      }

      types.add(detectedType);
    }

    return types;
  }

  bool _isNumeric(String value) {
    return double.tryParse(value.replaceAll(',', '')) != null;
  }

  bool _isCurrency(String value) {
    final currencyPattern = RegExp(r'^\$?[\d,]+\.?\d*$');
    return currencyPattern.hasMatch(value.replaceAll(' ', ''));
  }

  bool _isDate(String value) {
    return DateTime.tryParse(value) != null;
  }

  bool _isBoolean(String value) {
    final lower = value.toLowerCase();
    return ['true', 'false', 'yes', 'no', '1', '0'].contains(lower);
  }

  // EXACT same method as single table import
  dynamic _convertCellValue(String value, ColumnDataType dataType) {
    if (value.isEmpty) return null;

    try {
      switch (dataType) {
        case ColumnDataType.text:
          return value;
        case ColumnDataType.number:
          return double.tryParse(value) ?? value;
        case ColumnDataType.currency:
          // Remove currency symbols and parse as number
          final cleanValue = value.replaceAll(RegExp(r'[^\d.-]'), '');
          return double.tryParse(cleanValue) ?? value;
        case ColumnDataType.date:
          // Try to parse as date, return as string if fails
          try {
            DateTime.parse(value);
            return value;
          } catch (e) {
            return value;
          }
        case ColumnDataType.boolean:
          final lowerValue = value.toLowerCase();
          if (['true', 'yes', '1', 'on'].contains(lowerValue)) {
            return true;
          } else if (['false', 'no', '0', 'off'].contains(lowerValue)) {
            return false;
          }
          return value;
        case ColumnDataType.dropdown:
          return value;
      }
    } catch (e) {
      return value;
    }
  }
}
