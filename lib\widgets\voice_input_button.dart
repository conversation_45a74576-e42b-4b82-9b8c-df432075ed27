import 'package:flutter/material.dart';
import '../services/voice_input_service.dart';
import 'dart:io' show Platform;

/// Voice input button with microphone icon and recording animation
class VoiceInputButton extends StatefulWidget {
  final Function(String) onVoiceInput;
  final Function(String)? onPartialResult;
  final bool enabled;

  const VoiceInputButton({
    super.key,
    required this.onVoiceInput,
    this.onPartialResult,
    this.enabled = true,
  });

  @override
  State<VoiceInputButton> createState() => _VoiceInputButtonState();
}

class _VoiceInputButtonState extends State<VoiceInputButton>
    with TickerProviderStateMixin {
  final VoiceInputService _voiceService = VoiceInputService();
  late AnimationController _pulseController;
  late AnimationController _scaleController;
  late Animation<double> _pulseAnimation;
  late Animation<double> _scaleAnimation;
  
  bool _isInitialized = false;
  bool _isListening = false;
  String _partialText = '';

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
    _initializeVoiceService();
  }

  void _initializeAnimations() {
    _pulseController = AnimationController(
      duration: const Duration(milliseconds: 1000),
      vsync: this,
    );
    
    _scaleController = AnimationController(
      duration: const Duration(milliseconds: 200),
      vsync: this,
    );

    _pulseAnimation = Tween<double>(
      begin: 1.0,
      end: 1.3,
    ).animate(CurvedAnimation(
      parent: _pulseController,
      curve: Curves.easeInOut,
    ));

    _scaleAnimation = Tween<double>(
      begin: 1.0,
      end: 0.9,
    ).animate(CurvedAnimation(
      parent: _scaleController,
      curve: Curves.easeInOut,
    ));

    _pulseController.repeat(reverse: true);
  }

  Future<void> _initializeVoiceService() async {
    final initialized = await _voiceService.initialize();
    if (mounted) {
      setState(() {
        _isInitialized = initialized;
      });
    }
    
    if (!initialized) {
      _showErrorSnackBar('Voice input not available. Please check microphone permissions.');
    }
  }

  void _showErrorSnackBar(String message) {
    if (!mounted) return;
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.orange.shade600,
        duration: const Duration(seconds: 4),
        action: SnackBarAction(
          label: 'OK',
          textColor: Colors.white,
          onPressed: () {},
        ),
      ),
    );
  }

  void _showLanguageSelector() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('🎤 Voice Recognition Settings'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Auto-detect toggle
            SwitchListTile(
              title: const Text('Auto-detect Language'),
              subtitle: const Text('Automatically switch between Arabic and English'),
              value: _voiceService.isAutoDetectEnabled,
              onChanged: (value) {
                _voiceService.setAutoDetectLanguage(value);
                Navigator.of(context).pop();
                _showLanguageSnackBar(value
                  ? 'Auto-detect enabled'
                  : 'Auto-detect disabled');
              },
            ),
            const Divider(),
            const Text(
              'Manual Language Selection:',
              style: TextStyle(fontWeight: FontWeight.w600),
            ),
            const SizedBox(height: 8),
            ListTile(
              leading: const Text('🇺🇸', style: TextStyle(fontSize: 24)),
              title: const Text('English'),
              subtitle: const Text('US English with global accent support'),
              onTap: () {
                _voiceService.setLanguage('English');
                Navigator.of(context).pop();
                _showLanguageSnackBar('Switched to English');
              },
            ),
            ListTile(
              leading: const Text('🇸🇦', style: TextStyle(fontSize: 24)),
              title: const Text('العربية (Arabic)'),
              subtitle: const Text('Arabic with Gulf, Levantine & Egyptian accents'),
              onTap: () {
                _voiceService.setLanguage('Arabic');
                Navigator.of(context).pop();
                _showLanguageSnackBar('تم التبديل إلى العربية');
              },
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () async {
              Navigator.of(context).pop();
              await _testVoiceRecognition();
            },
            child: const Text('Test Arabic'),
          ),
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
        ],
      ),
    );
  }

  void _showLanguageSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.green.shade600,
        duration: const Duration(seconds: 2),
      ),
    );
  }

  Future<void> _toggleVoiceInput() async {
    if (!_isInitialized || !widget.enabled) return;

    // On Windows, show quick input dialog
    if (Platform.isWindows) {
      _showWindowsQuickInput();
      return;
    }

    if (_isListening) {
      await _stopListening();
    } else {
      await _startListening();
    }
  }

  /// Show Windows quick input dialog
  void _showWindowsQuickInput() {
    final textController = TextEditingController();

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Row(
          children: [
            Container(
              width: 32,
              height: 32,
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  colors: [Colors.blue.shade600, Colors.purple.shade600],
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                ),
                borderRadius: BorderRadius.circular(16),
              ),
              child: const Icon(Icons.mic, size: 16, color: Colors.white),
            ),
            const SizedBox(width: 12),
            const Text('Quick Voice Input'),
          ],
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              'Voice recognition is not available on Windows. Please type your message:',
              style: TextStyle(color: Colors.grey.shade600),
            ),
            const SizedBox(height: 16),
            TextField(
              controller: textController,
              autofocus: true,
              decoration: const InputDecoration(
                hintText: 'e.g., "80 kg FM200 supply only"',
                border: OutlineInputBorder(),
              ),
              onSubmitted: (text) {
                if (text.isNotEmpty) {
                  widget.onVoiceInput(text);
                  Navigator.of(context).pop();
                }
              },
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              final text = textController.text.trim();
              if (text.isNotEmpty) {
                widget.onVoiceInput(text);
                Navigator.of(context).pop();
              }
            },
            child: const Text('Send'),
          ),
        ],
      ),
    );
  }

  Future<void> _startListening() async {
    setState(() {
      _isListening = true;
      _partialText = '';
    });

    _scaleController.forward();

    await _voiceService.startListening(
      onResult: (text) {
        if (mounted) {
          setState(() {
            _isListening = false;
            _partialText = '';
          });
          _scaleController.reverse();
          
          if (text.isNotEmpty) {
            widget.onVoiceInput(text);
            _showResultSnackBar('🎤 Recognized: "$text"');
          }
        }
      },
      onPartialResult: (text) {
        if (mounted) {
          setState(() {
            _partialText = text;
          });
          if (widget.onPartialResult != null) {
            widget.onPartialResult!(text);
          }
        }
      },
      onError: (error) {
        if (mounted) {
          setState(() {
            _isListening = false;
            _partialText = '';
          });
          _scaleController.reverse();
          _showErrorSnackBar(error);
        }
      },
    );
  }

  Future<void> _stopListening() async {
    await _voiceService.stopListening();
    if (mounted) {
      setState(() {
        _isListening = false;
        _partialText = '';
      });
      _scaleController.reverse();
    }
  }

  void _showResultSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.blue.shade600,
        duration: const Duration(seconds: 2),
      ),
    );
  }

  Future<void> _testVoiceRecognition() async {
    try {
      // Test Arabic support
      final arabicSupported = await _voiceService.testArabicSupport();
      final status = _voiceService.getVoiceStatus();

      // Show detailed status
      if (mounted) {
        showDialog(
          context: context,
          builder: (context) => AlertDialog(
          title: const Text('🎤 Voice Recognition Status'),
          content: SingleChildScrollView(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisSize: MainAxisSize.min,
              children: [
                Text('Arabic Support: ${arabicSupported ? '✅ Available' : '❌ Not Available'}'),
                const SizedBox(height: 8),
                Text('Initialized: ${status['initialized'] ? '✅' : '❌'}'),
                Text('Available: ${status['available'] ? '✅' : '❌'}'),
                Text('Current Locale: ${status['currentLocale']}'),
                Text('Arabic Locale: ${status['preferredArabic']}'),
                Text('English Locale: ${status['preferredEnglish']}'),
                Text('Auto-detect: ${status['autoDetect'] ? '✅' : '❌'}'),
                const SizedBox(height: 8),
                Text('Total Locales: ${status['totalLocales']}'),
                Text('Arabic Locales: ${status['arabicLocales']}'),
                Text('English Locales: ${status['englishLocales']}'),
                const SizedBox(height: 16),
                const Text(
                  'Try saying:\n• "مرحبا" (Hello in Arabic)\n• "احسب نظام إطفاء" (Calculate fire system)\n• "Hello" (English)',
                  style: TextStyle(fontStyle: FontStyle.italic),
                ),
              ],
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('OK'),
            ),
          ],
        ),
        );
      }
    } catch (e) {
      if (mounted) {
        _showErrorSnackBar('Test failed: $e');
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    if (!_isInitialized) {
      return Container(
        width: 48,
        height: 48,
        decoration: BoxDecoration(
          color: Colors.grey.shade300,
          borderRadius: BorderRadius.circular(24),
        ),
        child: Icon(
          Icons.mic_off,
          color: Colors.grey.shade600,
          size: 24,
        ),
      );
    }

    return GestureDetector(
      onTap: _toggleVoiceInput,
      onLongPress: _showLanguageSelector,
      child: AnimatedBuilder(
        animation: Listenable.merge([_pulseAnimation, _scaleAnimation]),
        builder: (context, child) {
          return Transform.scale(
            scale: _scaleAnimation.value,
            child: Container(
              width: 48,
              height: 48,
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  colors: _isListening
                      ? [Colors.red.shade600, Colors.red.shade800]
                      : [Colors.blue.shade600, Colors.purple.shade600],
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                ),
                borderRadius: BorderRadius.circular(24),
                boxShadow: _isListening
                    ? [
                        BoxShadow(
                          color: Colors.red.withOpacity(0.3 * _pulseAnimation.value),
                          blurRadius: 20 * _pulseAnimation.value,
                          spreadRadius: 5 * _pulseAnimation.value,
                        ),
                      ]
                    : [
                        BoxShadow(
                          color: Colors.black.withOpacity(0.1),
                          blurRadius: 8,
                          offset: const Offset(0, 2),
                        ),
                      ],
              ),
              child: Icon(
                _isListening ? Icons.mic : Icons.mic_none,
                color: Colors.white,
                size: 24,
              ),
            ),
          );
        },
      ),
    );
  }

  @override
  void dispose() {
    _pulseController.dispose();
    _scaleController.dispose();
    _voiceService.dispose();
    super.dispose();
  }
}
