import 'package:flutter/material.dart';
import 'package:syncfusion_flutter_datagrid/datagrid.dart';
import '../models/isar_models.dart';
import '../services/isar_service.dart';

class SystemDataGrid extends StatefulWidget {
  final SystemType systemType;
  final String title;
  final Color themeColor;

  const SystemDataGrid({
    super.key,
    required this.systemType,
    required this.title,
    required this.themeColor,
  });

  @override
  State<SystemDataGrid> createState() => _SystemDataGridState();
}

class _SystemDataGridState extends State<SystemDataGrid> {
  final IsarService _isarService = IsarService.instance;
  List<Section> _sections = [];
  List<SystemItem> _items = [];
  Section? _selectedSection;
  late SystemItemDataSource _dataSource;
  bool _isLoading = true;
  String _searchQuery = '';
  final TextEditingController _searchController = TextEditingController();

  @override
  void initState() {
    super.initState();
    _loadData();
  }

  Future<void> _loadData() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final sections = await _isarService.getSections(widget.systemType);
      final items = await _isarService.getSystemItems(widget.systemType);

      setState(() {
        _sections = sections;
        _items = items;
        _selectedSection = sections.isNotEmpty ? sections.first : null;
        _dataSource = SystemItemDataSource(_items);
        _isLoading = false;
      });

      if (_selectedSection != null) {
        await _loadSectionItems(_selectedSection!);
      }
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error loading data: $e')),
        );
      }
    }
  }

  Future<void> _loadSectionItems(Section section) async {
    try {
      List<SystemItem> items;
      if (_searchQuery.isEmpty) {
        items = await _isarService.getSystemItemsBySection(section.sectionId!);
      } else {
        items = await _isarService.searchSystemItems(widget.systemType, _searchQuery);
        items = items.where((item) => item.sectionId == section.sectionId).toList();
      }

      setState(() {
        _items = items;
        _dataSource = SystemItemDataSource(_items);
      });
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error loading section items: $e')),
        );
      }
    }
  }

  Future<void> _performSearch(String query) async {
    setState(() {
      _searchQuery = query;
    });

    if (_selectedSection != null) {
      await _loadSectionItems(_selectedSection!);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(widget.title),
        backgroundColor: widget.themeColor,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.add),
            onPressed: _addNewItem,
          ),
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _loadData,
          ),
        ],
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : Row(
              children: [
                // Left sidebar with sections
                Container(
                  width: 250,
                  decoration: BoxDecoration(
                    color: Colors.grey.shade100,
                    border: Border(
                      right: BorderSide(color: Colors.grey.shade300),
                    ),
                  ),
                  child: Column(
                    children: [
                      // Search bar
                      Padding(
                        padding: const EdgeInsets.all(8.0),
                        child: TextField(
                          controller: _searchController,
                          decoration: InputDecoration(
                            hintText: 'Search items...',
                            prefixIcon: const Icon(Icons.search),
                            border: OutlineInputBorder(
                              borderRadius: BorderRadius.circular(8),
                            ),
                            contentPadding: const EdgeInsets.symmetric(
                              horizontal: 12,
                              vertical: 8,
                            ),
                          ),
                          onChanged: _performSearch,
                        ),
                      ),
                      // Sections list
                      Expanded(
                        child: ListView.builder(
                          itemCount: _sections.length,
                          itemBuilder: (context, index) {
                            final section = _sections[index];
                            final isSelected = _selectedSection?.id == section.id;
                            
                            return ListTile(
                              leading: Icon(
                                _getIconData(section.icon ?? 'folder'),
                                color: isSelected ? widget.themeColor : Colors.grey,
                              ),
                              title: Text(
                                section.displayName ?? section.name ?? 'Unknown',
                                style: TextStyle(
                                  fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
                                  color: isSelected ? widget.themeColor : Colors.black87,
                                ),
                              ),
                              selected: isSelected,
                              selectedTileColor: widget.themeColor.withOpacity(0.1),
                              onTap: () {
                                setState(() {
                                  _selectedSection = section;
                                });
                                _loadSectionItems(section);
                              },
                            );
                          },
                        ),
                      ),
                    ],
                  ),
                ),
                // Right side with data grid
                Expanded(
                  child: Column(
                    children: [
                      // Header with section name and item count
                      Container(
                        padding: const EdgeInsets.all(16),
                        decoration: BoxDecoration(
                          color: widget.themeColor.withOpacity(0.1),
                          border: Border(
                            bottom: BorderSide(color: Colors.grey.shade300),
                          ),
                        ),
                        child: Row(
                          children: [
                            Text(
                              _selectedSection?.displayName ?? 'No Section Selected',
                              style: const TextStyle(
                                fontSize: 18,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                            const Spacer(),
                            Text(
                              '${_items.length} items',
                              style: TextStyle(
                                color: Colors.grey.shade600,
                                fontSize: 14,
                              ),
                            ),
                          ],
                        ),
                      ),
                      // Data grid
                      Expanded(
                        child: _items.isEmpty
                            ? Center(
                                child: Column(
                                  mainAxisAlignment: MainAxisAlignment.center,
                                  children: [
                                    Icon(
                                      Icons.inbox,
                                      size: 64,
                                      color: Colors.grey.shade400,
                                    ),
                                    const SizedBox(height: 16),
                                    Text(
                                      'No items found',
                                      style: TextStyle(
                                        fontSize: 18,
                                        color: Colors.grey.shade600,
                                      ),
                                    ),
                                    const SizedBox(height: 8),
                                    Text(
                                      _searchQuery.isNotEmpty
                                          ? 'Try adjusting your search'
                                          : 'Add some items to get started',
                                      style: TextStyle(
                                        color: Colors.grey.shade500,
                                      ),
                                    ),
                                  ],
                                ),
                              )
                            : SfDataGrid(
                                source: _dataSource,
                                allowEditing: true,
                                allowSorting: true,
                                allowFiltering: true,
                                showCheckboxColumn: true,
                                selectionMode: SelectionMode.multiple,
                                navigationMode: GridNavigationMode.cell,
                                columnWidthMode: ColumnWidthMode.fill,
                                columns: _buildColumns(),
                              ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
    );
  }

  IconData _getIconData(String iconName) {
    switch (iconName) {
      case 'alarm':
        return Icons.alarm;
      case 'water_drop':
        return Icons.water_drop;
      case 'bubble_chart':
        return Icons.bubble_chart;
      case 'cloud':
        return Icons.cloud;
      case 'air':
        return Icons.air;
      case 'co2':
        return Icons.co2;
      case 'inventory':
        return Icons.inventory;
      default:
        return Icons.folder;
    }
  }

  List<GridColumn> _buildColumns() {
    return [
      GridColumn(
        columnName: 'model',
        label: Container(
          padding: const EdgeInsets.all(8),
          alignment: Alignment.centerLeft,
          child: const Text('Model', style: TextStyle(fontWeight: FontWeight.bold)),
        ),
      ),
      GridColumn(
        columnName: 'description',
        label: Container(
          padding: const EdgeInsets.all(8),
          alignment: Alignment.centerLeft,
          child: const Text('Description', style: TextStyle(fontWeight: FontWeight.bold)),
        ),
      ),
      GridColumn(
        columnName: 'manufacturer',
        label: Container(
          padding: const EdgeInsets.all(8),
          alignment: Alignment.centerLeft,
          child: const Text('Manufacturer', style: TextStyle(fontWeight: FontWeight.bold)),
        ),
      ),
      GridColumn(
        columnName: 'approval',
        label: Container(
          padding: const EdgeInsets.all(8),
          alignment: Alignment.centerLeft,
          child: const Text('Approval', style: TextStyle(fontWeight: FontWeight.bold)),
        ),
      ),
      GridColumn(
        columnName: 'exWorksPrice',
        label: Container(
          padding: const EdgeInsets.all(8),
          alignment: Alignment.centerRight,
          child: const Text('Ex-Works Price', style: TextStyle(fontWeight: FontWeight.bold)),
        ),
      ),
      GridColumn(
        columnName: 'localPrice',
        label: Container(
          padding: const EdgeInsets.all(8),
          alignment: Alignment.centerRight,
          child: const Text('Local Price', style: TextStyle(fontWeight: FontWeight.bold)),
        ),
      ),
      GridColumn(
        columnName: 'installationPrice',
        label: Container(
          padding: const EdgeInsets.all(8),
          alignment: Alignment.centerRight,
          child: const Text('Installation Price', style: TextStyle(fontWeight: FontWeight.bold)),
        ),
      ),
    ];
  }

  void _addNewItem() {
    // TODO: Implement add new item dialog
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Add new item functionality coming soon')),
    );
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }
}

class SystemItemDataSource extends DataGridSource {
  SystemItemDataSource(List<SystemItem> items) {
    _items = items;
    _buildDataGridRows();
  }

  List<SystemItem> _items = [];
  List<DataGridRow> _dataGridRows = [];

  void _buildDataGridRows() {
    _dataGridRows = _items.map<DataGridRow>((item) {
      return DataGridRow(cells: [
        DataGridCell<String>(columnName: 'model', value: item.model ?? ''),
        DataGridCell<String>(columnName: 'description', value: item.description ?? ''),
        DataGridCell<String>(columnName: 'manufacturer', value: item.manufacturer ?? ''),
        DataGridCell<String>(columnName: 'approval', value: item.approval ?? ''),
        DataGridCell<double>(columnName: 'exWorksPrice', value: item.exWorksPrice ?? 0.0),
        DataGridCell<double>(columnName: 'localPrice', value: item.localPrice ?? 0.0),
        DataGridCell<double>(columnName: 'installationPrice', value: item.installationPrice ?? 0.0),
      ]);
    }).toList();
  }

  @override
  List<DataGridRow> get rows => _dataGridRows;

  @override
  DataGridRowAdapter buildRow(DataGridRow row) {
    return DataGridRowAdapter(
      cells: row.getCells().map<Widget>((cell) {
        if (cell.columnName.contains('Price')) {
          return Container(
            alignment: Alignment.centerRight,
            padding: const EdgeInsets.all(8),
            child: Text(
              cell.value != null ? '\$${cell.value.toStringAsFixed(2)}' : '',
              style: const TextStyle(fontFamily: 'monospace'),
            ),
          );
        }
        return Container(
          alignment: Alignment.centerLeft,
          padding: const EdgeInsets.all(8),
          child: Text(cell.value?.toString() ?? ''),
        );
      }).toList(),
    );
  }
}
