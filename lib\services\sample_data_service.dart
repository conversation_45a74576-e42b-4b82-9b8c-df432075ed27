import 'package:uuid/uuid.dart';
import '../models/project.dart';
import 'database_service.dart';

class SampleDataService {
  final DatabaseService _databaseService = DatabaseService();
  final Uuid _uuid = const Uuid();

  Future<void> loadSampleData() async {
    await _loadFireAlarmSystems();
    await _loadFireSprinklerSystems();
    await _loadFoamSystems();
    await _loadFM200Systems();
    await _loadNovecSystems();
    await _loadCO2Systems();
  }

  Future<void> _loadFireAlarmSystems() async {
    // Add Fire Alarm Control Panel
    await _databaseService.insertMaterial(
      MaterialItem(
        id: _uuid.v4(),
        name: 'Fire Alarm Control Panel',
        category: 'Fire Alarm',
        description: 'Addressable fire alarm control panel with 4 loops capacity, supporting up to 1000 devices',
        quantity: 0,
        unit: 'pcs',
        exWorksUnitCost: 2500.00,
        localUnitCost: 1200.00,
        installationUnitCost: 800.00,
        isImported: true,
        vendor: 'Honeywell',
        approval: 'UL, FM, NFPA',
      ),
    );

    // Add Smoke Detector
    await _databaseService.insertMaterial(
      MaterialItem(
        id: _uuid.v4(),
        name: 'Smoke Detector',
        category: 'Fire Alarm',
        description: 'Addressable photoelectric smoke detector with LED indicator',
        quantity: 0,
        unit: 'pcs',
        exWorksUnitCost: 85.00,
        localUnitCost: 50.00,
        installationUnitCost: 75.00,
        isImported: true,
        vendor: 'Siemens',
        approval: 'UL, FM',
      ),
    );

    // Add Manual Call Point
    await _databaseService.insertMaterial(
      MaterialItem(
        id: _uuid.v4(),
        name: 'Manual Call Point',
        category: 'Fire Alarm',
        description: 'Addressable manual call point with resettable element',
        quantity: 0,
        unit: 'pcs',
        exWorksUnitCost: 65.00,
        localUnitCost: 40.00,
        installationUnitCost: 60.00,
        isImported: true,
        vendor: 'Bosch',
        approval: 'EN54, LPCB',
      ),
    );

    // Add Fire Alarm Sounder
    await _databaseService.insertMaterial(
      MaterialItem(
        id: _uuid.v4(),
        name: 'Fire Alarm Sounder',
        category: 'Fire Alarm',
        description: 'Addressable fire alarm sounder with multiple tone options',
        quantity: 0,
        unit: 'pcs',
        exWorksUnitCost: 95.00,
        localUnitCost: 60.00,
        installationUnitCost: 80.00,
        isImported: true,
        vendor: 'Apollo',
        approval: 'UL, FM, EN54',
      ),
    );
  }

  Future<void> _loadFireSprinklerSystems() async {
    // Add Sprinkler Head
    await _databaseService.insertMaterial(
      MaterialItem(
        id: _uuid.v4(),
        name: 'Sprinkler Head',
        category: 'Fire Sprinkler',
        description: 'Quick response pendent sprinkler head, K-factor 5.6',
        quantity: 0,
        unit: 'pcs',
        exWorksUnitCost: 15.00,
        localUnitCost: 10.00,
        installationUnitCost: 25.00,
        isImported: true,
        vendor: 'Viking',
        approval: 'UL, FM, NFPA',
      ),
    );

    // Add Flow Switch
    await _databaseService.insertMaterial(
      MaterialItem(
        id: _uuid.v4(),
        name: 'Flow Switch',
        category: 'Fire Sprinkler',
        description: 'Water flow detector for 4" pipe',
        quantity: 0,
        unit: 'pcs',
        exWorksUnitCost: 120.00,
        localUnitCost: 80.00,
        installationUnitCost: 100.00,
        isImported: true,
        vendor: 'Potter',
        approval: 'UL, FM',
      ),
    );

    // Add Fire Pump
    await _databaseService.insertMaterial(
      MaterialItem(
        id: _uuid.v4(),
        name: 'Fire Pump',
        category: 'Fire Sprinkler',
        description: '1000 GPM electric fire pump with controller',
        quantity: 0,
        unit: 'sets',
        exWorksUnitCost: 15000.00,
        localUnitCost: 5000.00,
        installationUnitCost: 8000.00,
        isImported: true,
        vendor: 'Armstrong',
        approval: 'UL, FM, NFPA 20',
      ),
    );

    // Add Alarm Check Valve
    await _databaseService.insertMaterial(
      MaterialItem(
        id: _uuid.v4(),
        name: 'Alarm Check Valve',
        category: 'Fire Sprinkler',
        description: '6" alarm check valve assembly with trim',
        quantity: 0,
        unit: 'sets',
        exWorksUnitCost: 2200.00,
        localUnitCost: 800.00,
        installationUnitCost: 1200.00,
        isImported: true,
        vendor: 'Tyco',
        approval: 'UL, FM',
      ),
    );
  }

  Future<void> _loadFoamSystems() async {
    // Add Foam Bladder Tank
    await _databaseService.insertMaterial(
      MaterialItem(
        id: _uuid.v4(),
        name: 'Foam Bladder Tank',
        category: 'Foam System',
        description: '1000 gallon foam concentrate bladder tank',
        quantity: 0,
        unit: 'pcs',
        exWorksUnitCost: 12000.00,
        localUnitCost: 4000.00,
        installationUnitCost: 6000.00,
        isImported: true,
        vendor: 'Chemguard',
        approval: 'UL, FM',
      ),
    );

    // Add Foam Proportioner
    await _databaseService.insertMaterial(
      MaterialItem(
        id: _uuid.v4(),
        name: 'Foam Proportioner',
        category: 'Foam System',
        description: '4" balanced pressure proportioner',
        quantity: 0,
        unit: 'pcs',
        exWorksUnitCost: 3500.00,
        localUnitCost: 1200.00,
        installationUnitCost: 1800.00,
        isImported: true,
        vendor: 'Ansul',
        approval: 'UL, FM',
      ),
    );

    // Add Foam Concentrate
    await _databaseService.insertMaterial(
      MaterialItem(
        id: _uuid.v4(),
        name: 'AFFF Foam Concentrate',
        category: 'Foam System',
        description: '3% AFFF foam concentrate',
        quantity: 0,
        unit: 'liters',
        exWorksUnitCost: 8.00,
        localUnitCost: 3.00,
        installationUnitCost: 0.00,
        isImported: true,
        vendor: '3M',
        approval: 'UL, FM',
      ),
    );

    // Add Foam Discharge Device
    await _databaseService.insertMaterial(
      MaterialItem(
        id: _uuid.v4(),
        name: 'Foam Discharge Device',
        category: 'Foam System',
        description: 'High expansion foam generator',
        quantity: 0,
        unit: 'pcs',
        exWorksUnitCost: 1800.00,
        localUnitCost: 600.00,
        installationUnitCost: 900.00,
        isImported: true,
        vendor: 'Fike',
        approval: 'UL, FM',
      ),
    );
  }

  Future<void> _loadFM200Systems() async {
    // Add FM-200 Cylinder
    await _databaseService.insertMaterial(
      MaterialItem(
        id: _uuid.v4(),
        name: 'FM-200 Cylinder',
        category: 'FM-200 System',
        description: '100kg FM-200 cylinder with valve assembly and pressure gauge',
        quantity: 0,
        unit: 'sets',
        exWorksUnitCost: 4200.00,
        localUnitCost: 1500.00,
        installationUnitCost: 2000.00,
        isImported: true,
        vendor: 'Kidde',
        approval: 'UL, FM, EPA',
      ),
    );

    // Add FM-200 Discharge Nozzle
    await _databaseService.insertMaterial(
      MaterialItem(
        id: _uuid.v4(),
        name: 'FM-200 Discharge Nozzle',
        category: 'FM-200 System',
        description: '360° discharge nozzle for FM-200 system',
        quantity: 0,
        unit: 'pcs',
        exWorksUnitCost: 180.00,
        localUnitCost: 60.00,
        installationUnitCost: 90.00,
        isImported: true,
        vendor: 'Kidde',
        approval: 'UL, FM',
      ),
    );
  }

  Future<void> _loadNovecSystems() async {
    // Add Novec 1230 Cylinder
    await _databaseService.insertMaterial(
      MaterialItem(
        id: _uuid.v4(),
        name: 'Novec 1230 Cylinder',
        category: 'Novec System',
        description: '80kg Novec 1230 cylinder with valve assembly and pressure gauge',
        quantity: 0,
        unit: 'sets',
        exWorksUnitCost: 5200.00,
        localUnitCost: 1800.00,
        installationUnitCost: 2200.00,
        isImported: true,
        vendor: '3M',
        approval: 'UL, FM, EPA',
      ),
    );

    // Add Novec 1230 Discharge Nozzle
    await _databaseService.insertMaterial(
      MaterialItem(
        id: _uuid.v4(),
        name: 'Novec 1230 Discharge Nozzle',
        category: 'Novec System',
        description: '360° discharge nozzle for Novec 1230 system',
        quantity: 0,
        unit: 'pcs',
        exWorksUnitCost: 190.00,
        localUnitCost: 70.00,
        installationUnitCost: 95.00,
        isImported: true,
        vendor: '3M',
        approval: 'UL, FM',
      ),
    );
  }

  Future<void> _loadCO2Systems() async {
    // Add CO2 Cylinder
    await _databaseService.insertMaterial(
      MaterialItem(
        id: _uuid.v4(),
        name: 'CO2 Cylinder',
        category: 'CO2 System',
        description: '75kg CO2 cylinder with valve assembly and pressure gauge',
        quantity: 0,
        unit: 'sets',
        exWorksUnitCost: 3800.00,
        localUnitCost: 1300.00,
        installationUnitCost: 1900.00,
        isImported: true,
        vendor: 'Kidde',
        approval: 'UL, FM',
      ),
    );

    // Add CO2 Discharge Nozzle
    await _databaseService.insertMaterial(
      MaterialItem(
        id: _uuid.v4(),
        name: 'CO2 Discharge Nozzle',
        category: 'CO2 System',
        description: 'Directional discharge nozzle for CO2 system',
        quantity: 0,
        unit: 'pcs',
        exWorksUnitCost: 160.00,
        localUnitCost: 50.00,
        installationUnitCost: 80.00,
        isImported: true,
        vendor: 'Kidde',
        approval: 'UL, FM',
      ),
    );
  }
}
