import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'dart:convert';
import '../models/project.dart';
import '../services/dynamic_schema_service.dart';

class SearchableItemDialog extends StatefulWidget {
  final String systemType;
  final Function(MaterialItem) onItemSelected;

  const SearchableItemDialog({
    super.key,
    required this.systemType,
    required this.onItemSelected,
  });

  @override
  State<SearchableItemDialog> createState() => _SearchableItemDialogState();
}

class _SearchableItemDialogState extends State<SearchableItemDialog> {
  final TextEditingController _searchController = TextEditingController();
  final TextEditingController _manualNameController = TextEditingController();
  final TextEditingController _manualDescriptionController = TextEditingController();
  final TextEditingController _manualLocalPriceController = TextEditingController();
  final TextEditingController _manualInstallPriceController = TextEditingController();
  
  List<Map<String, dynamic>> _allItems = [];
  List<Map<String, dynamic>> _filteredItems = [];
  bool _isLoading = true;
  bool _showManualInput = false;
  
  @override
  void initState() {
    super.initState();
    _loadItems();
    _searchController.addListener(_filterItems);
  }

  @override
  void dispose() {
    _searchController.dispose();
    _manualNameController.dispose();
    _manualDescriptionController.dispose();
    _manualLocalPriceController.dispose();
    _manualInstallPriceController.dispose();
    super.dispose();
  }

  Future<void> _loadItems() async {
    try {
      final schemaService = DynamicSchemaService.instance;
      
      // Get all sections to find piping-related tables
      final sections = await schemaService.getAllSections();
      
      List<Map<String, dynamic>> items = [];
      
      for (final section in sections) {
        if (section.name?.toLowerCase().contains('pipe') == true ||
            section.name?.toLowerCase().contains('piping') == true) {
          
          final tables = await schemaService.getTablesForSection(section.sectionId!);
          
          for (final table in tables) {
            final rows = await schemaService.getRowsForTable(table.tableId!);
            final columns = await schemaService.getColumnsForTable(table.tableId!);
            
            for (final row in rows) {
              if (row.data == null) continue;

              // Parse row data from JSON
              final rowData = jsonDecode(row.data!) as Map<String, dynamic>;

              // Extract relevant data
              String description = '';
              String model = '';
              double localPrice = 0.0;
              double installPrice = 0.0;

              for (final column in columns) {
                final value = rowData[column.columnId];
                if (value != null) {
                  final columnName = column.name?.toLowerCase() ?? '';

                  if (columnName.contains('description') || columnName.contains('desc')) {
                    description = value.toString();
                  } else if (columnName.contains('model') || columnName.contains('name')) {
                    model = value.toString();
                  } else if (columnName.contains('local') && columnName.contains('price')) {
                    localPrice = double.tryParse(value.toString()) ?? 0.0;
                  } else if (columnName.contains('install') && columnName.contains('price')) {
                    installPrice = double.tryParse(value.toString()) ?? 0.0;
                  }
                }
              }
              
              if (description.isNotEmpty || model.isNotEmpty) {
                items.add({
                  'model': model,
                  'description': description,
                  'localPrice': localPrice,
                  'installPrice': installPrice,
                  'category': table.name ?? 'Piping',
                  'tableName': table.name ?? '',
                });
              }
            }
          }
        }
      }
      
      setState(() {
        _allItems = items;
        _filteredItems = items;
        _isLoading = false;
      });
    } catch (e) {
      print('Error loading items: $e');
      setState(() {
        _isLoading = false;
      });
    }
  }

  void _filterItems() {
    final query = _searchController.text.toLowerCase();
    setState(() {
      _filteredItems = _allItems.where((item) {
        final model = item['model']?.toString().toLowerCase() ?? '';
        final description = item['description']?.toString().toLowerCase() ?? '';
        return model.contains(query) || description.contains(query);
      }).toList();
    });
  }

  void _selectItem(Map<String, dynamic> item) {
    final material = MaterialItem(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      name: item['model']?.toString() ?? 'Unknown',
      category: item['category']?.toString() ?? 'Piping',
      description: item['description']?.toString() ?? '',
      quantity: 1,
      unit: 'pcs',
      exWorksUnitCost: 0.0, // Not available from database
      localUnitCost: item['localPrice']?.toDouble() ?? 0.0,
      installationUnitCost: item['installPrice']?.toDouble() ?? 0.0,
      isImported: false,
      vendor: '',
      approval: '',
    );
    
    widget.onItemSelected(material);
    Navigator.of(context).pop();
  }

  void _createManualItem() {
    if (_manualNameController.text.trim().isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Please enter a name')),
      );
      return;
    }

    final material = MaterialItem(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      name: _manualNameController.text.trim(),
      category: 'Manual Entry',
      description: _manualDescriptionController.text.trim(),
      quantity: 1,
      unit: 'pcs',
      exWorksUnitCost: 0.0,
      localUnitCost: double.tryParse(_manualLocalPriceController.text) ?? 0.0,
      installationUnitCost: double.tryParse(_manualInstallPriceController.text) ?? 0.0,
      isImported: false,
      vendor: '',
      approval: '',
    );
    
    widget.onItemSelected(material);
    Navigator.of(context).pop();
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      child: Container(
        width: 600,
        height: 700,
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
            // Header
            Row(
              children: [
                Text(
                  'Add ${widget.systemType} Item',
                  style: const TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const Spacer(),
                IconButton(
                  onPressed: () => Navigator.of(context).pop(),
                  icon: const Icon(Icons.close),
                ),
              ],
            ),
            const SizedBox(height: 16),
            
            // Toggle buttons
            Row(
              children: [
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: () => setState(() => _showManualInput = false),
                    icon: const Icon(Icons.search),
                    label: const Text('Search Database'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: !_showManualInput ? Colors.blue : Colors.grey.shade300,
                      foregroundColor: !_showManualInput ? Colors.white : Colors.black,
                    ),
                  ),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: () => setState(() => _showManualInput = true),
                    icon: const Icon(Icons.edit),
                    label: const Text('Manual Entry'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: _showManualInput ? Colors.blue : Colors.grey.shade300,
                      foregroundColor: _showManualInput ? Colors.white : Colors.black,
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            
            // Content
            Expanded(
              child: _showManualInput ? _buildManualInput() : _buildSearchContent(),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSearchContent() {
    return Column(
      children: [
        // Search field
        TextField(
          controller: _searchController,
          decoration: const InputDecoration(
            labelText: 'Search by model or description',
            prefixIcon: Icon(Icons.search),
            border: OutlineInputBorder(),
          ),
        ),
        const SizedBox(height: 16),
        
        // Results
        Expanded(
          child: _isLoading
              ? const Center(child: CircularProgressIndicator())
              : _filteredItems.isEmpty
                  ? const Center(
                      child: Text(
                        'No items found.\nTry a different search term or use manual entry.',
                        textAlign: TextAlign.center,
                        style: TextStyle(color: Colors.grey),
                      ),
                    )
                  : ListView.builder(
                      itemCount: _filteredItems.length,
                      itemBuilder: (context, index) {
                        final item = _filteredItems[index];
                        return Card(
                          child: ListTile(
                            title: Text(
                              item['model']?.toString() ?? 'Unknown',
                              style: const TextStyle(fontWeight: FontWeight.bold),
                            ),
                            subtitle: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                if (item['description']?.toString().isNotEmpty == true)
                                  Text(item['description'].toString()),
                                const SizedBox(height: 4),
                                Text(
                                  'Local: SAR ${item['localPrice']?.toStringAsFixed(2) ?? '0.00'} | '
                                  'Install: SAR ${item['installPrice']?.toStringAsFixed(2) ?? '0.00'}',
                                  style: TextStyle(
                                    fontSize: 12,
                                    color: Colors.green.shade700,
                                  ),
                                ),
                              ],
                            ),
                            trailing: const Icon(Icons.add_circle_outline),
                            onTap: () => _selectItem(item),
                          ),
                        );
                      },
                    ),
        ),
      ],
    );
  }

  Widget _buildManualInput() {
    return Column(
      children: [
        TextField(
          controller: _manualNameController,
          decoration: const InputDecoration(
            labelText: 'Item Name *',
            border: OutlineInputBorder(),
          ),
        ),
        const SizedBox(height: 12),
        TextField(
          controller: _manualDescriptionController,
          decoration: const InputDecoration(
            labelText: 'Description',
            border: OutlineInputBorder(),
          ),
          maxLines: 2,
        ),
        const SizedBox(height: 12),
        TextField(
          controller: _manualLocalPriceController,
          decoration: const InputDecoration(
            labelText: 'Local Price (SAR)',
            border: OutlineInputBorder(),
            prefixText: 'SAR ',
          ),
          keyboardType: TextInputType.number,
          inputFormatters: [FilteringTextInputFormatter.allow(RegExp(r'^\d*\.?\d*'))],
        ),
        const SizedBox(height: 12),
        TextField(
          controller: _manualInstallPriceController,
          decoration: const InputDecoration(
            labelText: 'Installation Price (SAR)',
            border: OutlineInputBorder(),
            prefixText: 'SAR ',
          ),
          keyboardType: TextInputType.number,
          inputFormatters: [FilteringTextInputFormatter.allow(RegExp(r'^\d*\.?\d*'))],
        ),
        const Spacer(),
        SizedBox(
          width: double.infinity,
          child: ElevatedButton(
            onPressed: _createManualItem,
            child: const Text('Add Item'),
          ),
        ),
      ],
    );
  }
}
