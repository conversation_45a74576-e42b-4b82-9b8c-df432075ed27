import 'package:isar/isar.dart';
import 'package:path_provider/path_provider.dart';
import 'dart:math';
import '../models/isar_models.dart';

class IsarService {
  static IsarService? _instance;
  static Isar? _isar;

  IsarService._();

  static IsarService get instance {
    _instance ??= IsarService._();
    return _instance!;
  }

  Future<Isar> get database async {
    if (_isar != null) return _isar!;
    
    final dir = await getApplicationDocumentsDirectory();
    _isar = await Isar.open(
      [
        SystemItemSchema,
        SectionSchema,
        DynamicTableSchema,
        TableColumnSchema,
        TableRowSchema,
        ProjectSchema,
        ProjectSystemSchema,
        SystemItemLinkSchema,
        SidebarSectionSchema,
        FlexibleColumnSchema,
        FlexibleRowSchema,
        FlexibleTableSchema,
      ],
      directory: dir.path,
    );
    
    // Initialize with sample data if empty
    await _initializeSampleData();
    
    return _isar!;
  }

  Future<void> _initializeSampleData() async {
    final isar = await database;
    
    // Check if we already have data
    final itemCount = await isar.systemItems.count();
    if (itemCount > 0) return;

    await isar.writeTxn(() async {
      // Create sections for each system type
      final sections = <Section>[];
      const systemTypes = SystemType.values;
      
      for (int i = 0; i < systemTypes.length; i++) {
        final systemType = systemTypes[i];
        final section = Section.create(
          sectionId: 'section_${systemType.name}',
          name: systemType.name,
          displayName: _getDisplayName(systemType),
          icon: _getIcon(systemType),
          orderIndex: i,
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
          systemType: systemType,
        );
        sections.add(section);
      }
      
      await isar.sections.putAll(sections);
      
      // Create sample items for each system type
      final items = <SystemItem>[];
      final random = Random();
      
      for (final systemType in systemTypes) {
        final section = sections.firstWhere((s) => s.systemType == systemType);
        
        for (int i = 0; i < 20; i++) {
          final item = SystemItem.create(
            itemId: '${systemType.name}_item_$i',
            model: '${_getDisplayName(systemType)} Model ${i + 1}',
            description: 'Sample ${_getDisplayName(systemType).toLowerCase()} item ${i + 1}',
            manufacturer: _getRandomManufacturer(random),
            approval: _getRandomApproval(random),
            exWorksPrice: 100 + random.nextDouble() * 900,
            localPrice: 50 + random.nextDouble() * 450,
            installationPrice: 25 + random.nextDouble() * 225,
            systemType: systemType,
            sectionId: section.sectionId,
            createdAt: DateTime.now(),
          );
          items.add(item);
        }
      }
      
      await isar.systemItems.putAll(items);
    });
  }

  String _getDisplayName(SystemType systemType) {
    switch (systemType) {
      case SystemType.alarm:
        return 'Fire Alarm Systems';
      case SystemType.water:
        return 'Water Systems';
      case SystemType.foam:
        return 'Foam Systems';
      case SystemType.cleanAgent:
        return 'Clean Agent Systems';
      case SystemType.co2:
        return 'CO2 Systems';
      case SystemType.materials:
        return 'Materials';
      case SystemType.custom:
        return 'Custom Section';
    }
  }

  String _getIcon(SystemType systemType) {
    switch (systemType) {
      case SystemType.alarm:
        return 'alarm';
      case SystemType.water:
        return 'water_drop';
      case SystemType.foam:
        return 'bubble_chart';
      case SystemType.cleanAgent:
        return 'cloud';
      case SystemType.co2:
        return 'co2';
      case SystemType.materials:
        return 'inventory';
      case SystemType.custom:
        return 'folder';
    }
  }

  String _getRandomManufacturer(Random random) {
    final manufacturers = [
      'Notifier', 'Simplex', 'Farenhyt', 'Honeywell', 'Siemens',
      'Johnson Controls', 'Tyco', 'Bosch', 'Hochiki', 'Apollo'
    ];
    return manufacturers[random.nextInt(manufacturers.length)];
  }

  String _getRandomApproval(Random random) {
    final approvals = [
      'UL Listed', 'FM Approved', 'CE Marked', 'SASO Approved',
      'NFPA Compliant', 'EN Certified', 'BSI Approved'
    ];
    return approvals[random.nextInt(approvals.length)];
  }

  // CRUD operations for SystemItems
  Future<List<SystemItem>> getSystemItems(SystemType systemType) async {
    final isar = await database;
    return await isar.systemItems
        .filter()
        .systemTypeEqualTo(systemType)
        .findAll();
  }

  Future<List<SystemItem>> getSystemItemsBySection(String sectionId) async {
    final isar = await database;
    return await isar.systemItems
        .filter()
        .sectionIdEqualTo(sectionId)
        .findAll();
  }

  Future<void> addSystemItem(SystemItem item) async {
    final isar = await database;
    await isar.writeTxn(() async {
      await isar.systemItems.put(item);
    });
  }

  Future<void> updateSystemItem(SystemItem item) async {
    final isar = await database;
    await isar.writeTxn(() async {
      await isar.systemItems.put(item);
    });
  }

  Future<void> deleteSystemItem(int id) async {
    final isar = await database;
    await isar.writeTxn(() async {
      await isar.systemItems.delete(id);
    });
  }

  // CRUD operations for Sections
  Future<List<Section>> getSections(SystemType systemType) async {
    final isar = await database;
    return await isar.sections
        .filter()
        .systemTypeEqualTo(systemType)
        .sortByOrderIndex()
        .findAll();
  }

  Future<void> addSection(Section section) async {
    final isar = await database;
    await isar.writeTxn(() async {
      await isar.sections.put(section);
    });
  }

  Future<void> updateSection(Section section) async {
    final isar = await database;
    await isar.writeTxn(() async {
      await isar.sections.put(section);
    });
  }

  Future<void> deleteSection(int id) async {
    final isar = await database;
    await isar.writeTxn(() async {
      await isar.sections.delete(id);
    });
  }

  // Search functionality
  Future<List<SystemItem>> searchSystemItems(
    SystemType systemType,
    String query,
  ) async {
    final isar = await database;
    return await isar.systemItems
        .filter()
        .systemTypeEqualTo(systemType)
        .and()
        .group((q) => q
            .modelContains(query, caseSensitive: false)
            .or()
            .descriptionContains(query, caseSensitive: false)
            .or()
            .manufacturerContains(query, caseSensitive: false))
        .findAll();
  }

  // Dynamic table management
  Future<List<DynamicTable>> getTablesForSection(String sectionId) async {
    final isar = await database;
    return await isar.dynamicTables
        .filter()
        .sectionIdEqualTo(sectionId)
        .sortByOrderIndex()
        .findAll();
  }

  Future<void> createTable(DynamicTable table) async {
    final isar = await database;
    await isar.writeTxn(() async {
      await isar.dynamicTables.put(table);
    });
  }

  Future<void> updateTable(DynamicTable table) async {
    final isar = await database;
    await isar.writeTxn(() async {
      await isar.dynamicTables.put(table);
    });
  }

  Future<void> deleteTable(String tableId) async {
    final isar = await database;
    await isar.writeTxn(() async {
      // Delete table
      await isar.dynamicTables.filter().tableIdEqualTo(tableId).deleteAll();
      // Delete columns
      await isar.tableColumns.filter().tableIdEqualTo(tableId).deleteAll();
      // Delete rows
      await isar.tableRows.filter().tableIdEqualTo(tableId).deleteAll();
    });
  }

  // Column management
  Future<List<TableColumn>> getColumnsForTable(String tableId) async {
    final isar = await database;
    return await isar.tableColumns
        .filter()
        .tableIdEqualTo(tableId)
        .sortByOrderIndex()
        .findAll();
  }

  Future<void> addColumn(TableColumn column) async {
    final isar = await database;
    await isar.writeTxn(() async {
      await isar.tableColumns.put(column);
    });
  }

  Future<void> updateColumn(TableColumn column) async {
    final isar = await database;
    await isar.writeTxn(() async {
      await isar.tableColumns.put(column);
    });
  }

  Future<void> deleteColumn(String columnId) async {
    final isar = await database;
    await isar.writeTxn(() async {
      await isar.tableColumns.filter().columnIdEqualTo(columnId).deleteAll();
    });
  }

  // Row data management
  Future<List<TableRow>> getRowsForTable(String tableId) async {
    final isar = await database;
    return await isar.tableRows
        .filter()
        .tableIdEqualTo(tableId)
        .findAll();
  }

  Future<void> addRow(TableRow row) async {
    final isar = await database;
    await isar.writeTxn(() async {
      await isar.tableRows.put(row);
    });
  }

  Future<void> updateRow(TableRow row) async {
    final isar = await database;
    await isar.writeTxn(() async {
      await isar.tableRows.put(row);
    });
  }

  Future<void> deleteRow(String rowId) async {
    final isar = await database;
    await isar.writeTxn(() async {
      await isar.tableRows.filter().rowIdEqualTo(rowId).deleteAll();
    });
  }

  // Utility methods
  Future<List<Section>> getAllSections() async {
    final isar = await database;
    return await isar.sections.where().sortByOrderIndex().findAll();
  }

  Future<Section?> getSectionById(String sectionId) async {
    final isar = await database;
    return await isar.sections.filter().sectionIdEqualTo(sectionId).findFirst();
  }

  Future<DynamicTable?> getTableById(String tableId) async {
    final isar = await database;
    return await isar.dynamicTables.filter().tableIdEqualTo(tableId).findFirst();
  }

  Future<void> close() async {
    await _isar?.close();
    _isar = null;
  }
}
