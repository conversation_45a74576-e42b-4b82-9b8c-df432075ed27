import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/estimator_provider.dart';
import '../widgets/estimator_form.dart';
import '../widgets/design_results.dart';
import '../widgets/systems_list.dart';
import '../screens/settings_screen.dart';

class HomeScreen extends StatefulWidget {
  const HomeScreen({super.key});

  @override
  State<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> with SingleTickerProviderStateMixin {
  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text(
          'Clean Agent System Estimator',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
          ),
        ),
        actions: [
          IconButton(
            icon: const Icon(Icons.settings),
            onPressed: () {
              Navigator.push(
                context,
                MaterialPageRoute(builder: (context) => const SettingsScreen()),
              );
            },
          ),
        ],
        bottom: TabBar(
          controller: _tabController,
          tabs: [
            Consumer<EstimatorProvider>(
              builder: (context, provider, child) => const Tab(
                text: 'Estimator',
                icon: Icon(Icons.calculate),
              ),
            ),
            Consumer<EstimatorProvider>(
              builder: (context, provider, child) => Tab(
                text: 'My Systems (${provider.systems.length})',
                icon: const Icon(Icons.folder),
              ),
            ),
          ],
        ),
      ),
      body: Column(
        children: [
          // Header section
          Container(
            padding: const EdgeInsets.all(16),
            child: Column(
              children: [
                Text(
                  'Professional tool for calculating NOVEC 1230 and FM-200 fire suppression system requirements',
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: Colors.grey[600],
                  ),
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 8),
                Container(
                  height: 1,
                  color: Colors.grey[300],
                ),
              ],
            ),
          ),
          // Tab content
          Expanded(
            child: TabBarView(
              controller: _tabController,
              children: [
                // Estimator tab
                const EstimatorTab(),
                // Systems tab
                SystemsTab(tabController: _tabController),
              ],
            ),
          ),
        ],
      ),
      // Footer
      bottomNavigationBar: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: Colors.grey[50],
          border: Border(
            top: BorderSide(color: Colors.grey[300]!),
          ),
        ),
        child: Text(
          '© 2025 Clean Agent System Estimator | Professional Fire Protection Engineering Tool',
          style: Theme.of(context).textTheme.bodySmall?.copyWith(
            color: Colors.grey[600],
          ),
          textAlign: TextAlign.center,
        ),
      ),
    );
  }
}

class EstimatorTab extends StatelessWidget {
  const EstimatorTab({super.key});

  @override
  Widget build(BuildContext context) {
    return Consumer<EstimatorProvider>(
      builder: (context, provider, child) {
        return Padding(
          padding: const EdgeInsets.all(16),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Form column - takes 1/3 of the width
              Expanded(
                flex: 1,
                child: Card(
                  child: Padding(
                    padding: const EdgeInsets.all(16),
                    child: EstimatorForm(
                      defaultValues: provider.currentSystem?.inputData,
                    ),
                  ),
                ),
              ),
              const SizedBox(width: 16),
              // Results column - takes 2/3 of the width
              Expanded(
                flex: 2,
                child: provider.calculationPerformed && provider.currentSystem != null
                    ? Column(
                        children: [
                          // Add to systems button
                          Row(
                            mainAxisAlignment: MainAxisAlignment.end,
                            children: [
                              ElevatedButton.icon(
                                onPressed: () {
                                  provider.addCurrentSystemToCollection();
                                  ScaffoldMessenger.of(context).showSnackBar(
                                    const SnackBar(
                                      content: Text('System added successfully'),
                                      backgroundColor: Colors.green,
                                    ),
                                  );
                                },
                                icon: const Icon(Icons.add),
                                label: const Text('Add to My Systems'),
                              ),
                            ],
                          ),
                          const SizedBox(height: 16),
                          // Results display
                          Expanded(
                            child: DesignResultsWidget(
                              results: provider.currentSystem!.designResults,
                              bom: provider.currentSystem!.bom,
                              summary: provider.currentSystem!.summary,
                              quotedSystem: provider.currentSystem!,
                            ),
                          ),
                        ],
                      )
                    : Card(
                        child: Container(
                          height: double.infinity,
                          padding: const EdgeInsets.all(32),
                          child: Column(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Icon(
                                Icons.calculate_outlined,
                                size: 64,
                                color: Colors.grey[400],
                              ),
                              const SizedBox(height: 16),
                              Text(
                                'No Calculations Yet',
                                style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                                  color: Colors.grey[600],
                                ),
                              ),
                              const SizedBox(height: 8),
                              Text(
                                'Fill out the form and click \'Calculate\' to see results.',
                                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                                  color: Colors.grey[500],
                                ),
                                textAlign: TextAlign.center,
                              ),
                            ],
                          ),
                        ),
                      ),
              ),
            ],
          ),
        );
      },
    );
  }
}

class SystemsTab extends StatelessWidget {
  final TabController tabController;

  const SystemsTab({super.key, required this.tabController});

  @override
  Widget build(BuildContext context) {
    return Consumer<EstimatorProvider>(
      builder: (context, provider, child) {
        if (provider.systems.isEmpty) {
          return Center(
            child: Card(
              margin: const EdgeInsets.all(32),
              child: Padding(
                padding: const EdgeInsets.all(32),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Icon(
                      Icons.folder_open_outlined,
                      size: 64,
                      color: Colors.grey[400],
                    ),
                    const SizedBox(height: 16),
                    Text(
                      'No Systems Added Yet',
                      style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                        color: Colors.grey[600],
                      ),
                    ),
                    const SizedBox(height: 8),
                    Text(
                      'Calculate a system and add it to your collection to view it here.',
                      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                        color: Colors.grey[500],
                      ),
                      textAlign: TextAlign.center,
                    ),
                    const SizedBox(height: 16),
                    ElevatedButton(
                      onPressed: () {
                        tabController.animateTo(0);
                      },
                      child: const Text('Go to Estimator'),
                    ),
                  ],
                ),
              ),
            ),
          );
        }

        return Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            children: [
              // Header with create new button
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    'My Systems',
                    style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  ElevatedButton.icon(
                    onPressed: () {
                      provider.createNewQuote();
                      tabController.animateTo(0);
                    },
                    icon: const Icon(Icons.add),
                    label: const Text('Create New System'),
                  ),
                ],
              ),
              const SizedBox(height: 16),
              // Systems list
              Expanded(
                child: SystemsList(
                  systems: provider.systems,
                  onSystemSelected: (system) {
                    provider.selectQuote(system);
                    tabController.animateTo(0);
                  },
                  onSystemDeleted: (systemId) {
                    provider.removeSystem(systemId);
                  },
                ),
              ),
            ],
          ),
        );
      },
    );
  }
}
