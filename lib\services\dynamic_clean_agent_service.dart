import 'dart:convert';
import 'package:isar/isar.dart';
import '../data/estimator_config.dart';
import 'dynamic_schema_service.dart';

/// Service to dynamically read all clean agent data from database tables
/// Replaces hardcoded values with real database lookups
class DynamicCleanAgentService {
  final Isar _isar;
  final DynamicSchemaService _schemaService;

  // Use Clean Agent section name instead of hardcoded ID
  static const String sectionName = 'Clean Agent';

  DynamicCleanAgentService(this._isar) : _schemaService = DynamicSchemaService.instance;

  /// Get component by part number from database tables
  Future<Component?> getComponentByPartNumber(String partNumber) async {
    try {
      // Check if Clean Agent section exists first by trying to get a table
      final testTable = await _schemaService.getTableByName('FM200 Items', sectionName);
      if (testTable == null) {
        print('❌ Clean Agent section not found. Available sections:');
        final allSections = await _schemaService.getAllSections();
        for (final section in allSections) {
          print('  - ${section.name}');
        }
        return null;
      }

      // Search in FM200 Items table
      final fm200Component = await _searchInTable('FM200 Items', partNumber);
      if (fm200Component != null) return fm200Component;

      // Search in NOVEC Items table
      final novecComponent = await _searchInTable('NOVEC Items', partNumber);
      if (novecComponent != null) return novecComponent;

      // Search in Alarm table
      final alarmComponent = await _searchInTable('Alarm', partNumber);
      if (alarmComponent != null) return alarmComponent;

      // Search in Install table
      final installComponent = await _searchInTable('Install', partNumber);
      if (installComponent != null) return installComponent;

      print('Component with part number $partNumber not found in any table');
      return null;
    } catch (e) {
      print('Error getting component $partNumber: $e');
      return null;
    }
  }

  /// Search for component in a specific table
  Future<Component?> _searchInTable(String tableName, String partNumber) async {
    try {
      // Use name-based lookup instead of section ID
      final table = await _schemaService.getTableByName(tableName, sectionName);
      if (table == null) {
        print('❌ Table "$tableName" not found in section "$sectionName"');
        // Debug: List all available sections and tables
        final allSections = await _schemaService.getAllSections();
        print('📋 Available sections: ${allSections.map((s) => s.name).toList()}');
        for (final section in allSections) {
          final tables = await _schemaService.getTablesForSection(section.sectionId!);
          print('  Section "${section.name}": ${tables.map((t) => t.name).toList()}');
        }
        return null;
      }

      // Get columns using the new service
      final columns = await _schemaService.getColumnsForTableByName(tableName, sectionName);

      // Create column mapping
      final columnMap = <String, String>{};
      for (final column in columns) {
        if (column.columnId != null && column.name != null) {
          columnMap[column.columnId!] = column.name!;
        }
      }

      // Get all rows using the new service
      final rows = await _schemaService.getRowsForTableByName(tableName, sectionName);

      // Search for the part number in each row
      for (final row in rows) {
        if (row.data != null) {
          final rowData = jsonDecode(row.data!) as Map<String, dynamic>;
          final convertedData = <String, dynamic>{};
          
          // Convert column IDs to column names
          for (final entry in rowData.entries) {
            final columnName = columnMap[entry.key];
            if (columnName != null) {
              convertedData[columnName] = entry.value;
            }
          }

          // Check if this row contains the part number we're looking for
          final partNoValue = _findPartNumber(convertedData);
          if (partNoValue != null && partNoValue.toString().trim() == partNumber.trim()) {
            // Found the component, extract data
            return _extractComponentFromRow(convertedData, tableName);
          }
        }
      }

      return null;
    } catch (e) {
      print('Error searching in table $tableName: $e');
      return null;
    }
  }

  /// Find part number field in row data (handles different column name variations)
  String? _findPartNumber(Map<String, dynamic> rowData) {
    // Common part number column names
    const partNumberFields = [
      'Part No.',
      'Part No',
      'PartNo',
      'Part Number',
      'P/N',
      'P/N (ANSUL)',
      'PN',
    ];

    for (final field in partNumberFields) {
      if (rowData.containsKey(field) && rowData[field] != null) {
        return rowData[field].toString();
      }
    }

    return null;
  }

  /// Extract component data from database row
  Component _extractComponentFromRow(Map<String, dynamic> rowData, String tableName) {
    final partNo = _findPartNumber(rowData) ?? 'UNKNOWN';
    final description = _findDescription(rowData) ?? 'Unknown Component';
    final unitCost = _findUnitCost(rowData) ?? 0.0;
    final manufacturer = _findManufacturer(rowData) ?? 'Generic';

    return Component(
      partNo: partNo,
      description: description,
      unitCost: unitCost,
      manufacturer: manufacturer,
    );
  }

  /// Find description field in row data
  String? _findDescription(Map<String, dynamic> rowData) {
    const descriptionFields = [
      'Description',
      'DESCRIPTION',
      'Desc',
      'Item Description',
      'Product Description',
    ];

    for (final field in descriptionFields) {
      if (rowData.containsKey(field) && rowData[field] != null) {
        return rowData[field].toString();
      }
    }

    return null;
  }

  /// Find unit cost field in row data
  double? _findUnitCost(Map<String, dynamic> rowData) {
    const costFields = [
      'SICLI Price',
      'List Price',
      'Unit Cost',
      'Price',
      'Cost',
      'Unit Price',
    ];

    for (final field in costFields) {
      if (rowData.containsKey(field) && rowData[field] != null) {
        final value = rowData[field];
        if (value is num) {
          return value.toDouble();
        } else if (value is String) {
          return double.tryParse(value);
        }
      }
    }

    return null;
  }

  /// Find manufacturer field in row data
  String? _findManufacturer(Map<String, dynamic> rowData) {
    const manufacturerFields = [
      'Manufacturer',
      'MANUFACTURER',
      'Mfg',
      'Brand',
      'Supplier',
    ];

    for (final field in manufacturerFields) {
      if (rowData.containsKey(field) && rowData[field] != null) {
        return rowData[field].toString();
      }
    }

    return null;
  }

  /// Get cylinder specifications from Min-Max Filling table
  Future<List<CylinderSpec>> getCylinderSpecs(String agentType) async {
    try {
      // Use name-based lookup
      final table = await _schemaService.getTableByName('Min-Max Filling', sectionName);
      if (table == null) {
        print('Min-Max Filling table not found');
        return [];
      }

      final columns = await _schemaService.getColumnsForTableByName('Min-Max Filling', sectionName);

      final columnMap = <String, String>{};
      for (final column in columns) {
        if (column.columnId != null && column.name != null) {
          columnMap[column.columnId!] = column.name!;
        }
      }

      final rows = await _schemaService.getRowsForTableByName('Min-Max Filling', sectionName);

      final specs = <CylinderSpec>[];
      
      for (final row in rows) {
        if (row.data != null) {
          final rowData = jsonDecode(row.data!) as Map<String, dynamic>;
          final convertedData = <String, dynamic>{};
          
          for (final entry in rowData.entries) {
            final columnName = columnMap[entry.key];
            if (columnName != null) {
              convertedData[columnName] = entry.value;
            }
          }

          // Extract cylinder data
          final cylinderSize = _getNumericValue(convertedData, 'Cyl Size');
          final maxFilling = agentType == 'NOVEC1230' 
              ? _getNumericValue(convertedData, 'Novec maximum filling')
              : _getNumericValue(convertedData, 'FM200 maximum filling');
          final minFilling = _getNumericValue(convertedData, 'Min filling');

          if (cylinderSize != null && maxFilling != null && minFilling != null) {
            specs.add(CylinderSpec(
              excelRow: specs.length + 4, // Starting from row 4 as in React app
              size: cylinderSize,
              maxKg: maxFilling,
              minKg: minFilling,
              partNo: _generateCylinderPartNo(agentType, cylinderSize),
              bracketPart: _generateBracketPartNo(cylinderSize),
              labelPart: _generateLabelPartNo(agentType, cylinderSize),
              bracketQtyRule: _getBracketQtyRule(cylinderSize),
            ));
          }
        }
      }

      print('Loaded ${specs.length} cylinder specs for $agentType from database');
      return specs;
    } catch (e) {
      print('Error loading cylinder specs: $e');
      return [];
    }
  }

  /// Get numeric value from row data
  double? _getNumericValue(Map<String, dynamic> data, String fieldName) {
    if (data.containsKey(fieldName) && data[fieldName] != null) {
      final value = data[fieldName];
      if (value is num) {
        return value.toDouble();
      } else if (value is String) {
        return double.tryParse(value);
      }
    }
    return null;
  }

  /// Generate cylinder part number based on agent type and size
  String _generateCylinderPartNo(String agentType, double size) {
    if (agentType == 'NOVEC1230') {
      switch (size.toInt()) {
        case 4: return '303.207.010';
        case 8: return '303.207.001';
        case 16: return '303.207.002';
        case 32: return '303.207.003';
        case 52: return '303.207.004';
        case 106: return '303.207.005';
        case 147: return '303.207.006';
        case 180: return '303.207.007';
        case 343: return '303.207.008';
        default: return '303.207.001';
      }
    } else { // FM200
      switch (size.toInt()) {
        case 4: return '303.205.026';
        case 8: return '303.205.015';
        case 16: return '303.205.016';
        case 32: return '303.205.017';
        case 52: return '303.205.018';
        case 106: return '303.205.019';
        case 147: return '303.205.020';
        case 180: return '303.205.021';
        case 343: return '303.205.022';
        default: return '303.205.015';
      }
    }
  }

  /// Generate bracket part number based on cylinder size
  String _generateBracketPartNo(double size) {
    if (size <= 4.5) return '311.205.020';
    if (size <= 32) return '311.205.013';
    if (size <= 180) return '311.205.014';
    return '311.205.019'; // For 343L
  }

  /// Generate label part number based on agent type and size
  String _generateLabelPartNo(String agentType, double size) {
    if (agentType == 'NOVEC1230') {
      if (size <= 16 || size == 52) return '314207337';
      return '314207306';
    } else { // FM200
      if (size <= 16) return '314.205.022';
      return '314.205.021';
    }
  }

  /// Get bracket quantity rule based on cylinder size
  int _getBracketQtyRule(double size) {
    if (size <= 32) return 2;
    return 1;
  }

  /// Get pipe data from Pipes flow & prices table
  Future<List<PipeData>> getPipeData() async {
    try {
      // Use name-based lookup
      final table = await _schemaService.getTableByName('Pipes flow & prices', sectionName);
      if (table == null) {
        print('Pipes flow & prices table not found');
        return [];
      }

      final columns = await _schemaService.getColumnsForTableByName('Pipes flow & prices', sectionName);

      final columnMap = <String, String>{};
      for (final column in columns) {
        if (column.columnId != null && column.name != null) {
          columnMap[column.columnId!] = column.name!;
        }
      }

      final rows = await _schemaService.getRowsForTableByName('Pipes flow & prices', sectionName);

      final pipeDataList = <PipeData>[];

      for (final row in rows) {
        if (row.data != null) {
          final rowData = jsonDecode(row.data!) as Map<String, dynamic>;
          final convertedData = <String, dynamic>{};

          for (final entry in rowData.entries) {
            final columnName = columnMap[entry.key];
            if (columnName != null) {
              convertedData[columnName] = entry.value;
            }
          }

          // Extract pipe data
          final sizeDesc = convertedData['Pipe Diameter Inch']?.toString() ?? '';
          final sizeMm = _getNumericValue(convertedData, 'Pipe Diameter mm');
          final minFlow = _getNumericValue(convertedData, 'Min flow Kg/sec');
          final maxFlow = _getNumericValue(convertedData, 'Max Flow Kg/sec');

          if (sizeMm != null && minFlow != null && maxFlow != null) {
            pipeDataList.add(PipeData(
              excelRow: pipeDataList.length + 4,
              sizeDesc: sizeDesc,
              sizeMm: sizeMm.toInt(),
              minFlow: minFlow,
              maxFlow: maxFlow,
            ));
          }
        }
      }

      print('Loaded ${pipeDataList.length} pipe data entries from database');
      return pipeDataList;
    } catch (e) {
      print('Error loading pipe data: $e');
      return [];
    }
  }

  /// Get design concentrations from Design Concentration table
  Future<Map<String, Map<String, Map<String, dynamic>>>> getDesignFactors() async {
    try {
      // Use name-based lookup
      final table = await _schemaService.getTableByName('Design Concentration', sectionName);
      if (table == null) {
        print('Design Concentration table not found');
        return {};
      }

      final columns = await _schemaService.getColumnsForTableByName('Design Concentration', sectionName);

      final columnMap = <String, String>{};
      for (final column in columns) {
        if (column.columnId != null && column.name != null) {
          columnMap[column.columnId!] = column.name!;
        }
      }

      final rows = await _schemaService.getRowsForTableByName('Design Concentration', sectionName);

      final factors = <String, Map<String, Map<String, dynamic>>>{
        'NOVEC1230': {},
        'FM200': {},
      };

      for (final row in rows) {
        if (row.data != null) {
          final rowData = jsonDecode(row.data!) as Map<String, dynamic>;
          final convertedData = <String, dynamic>{};

          for (final entry in rowData.entries) {
            final columnName = columnMap[entry.key];
            if (columnName != null) {
              convertedData[columnName] = entry.value;
            }
          }

          // Extract FM200 data
          final fm200Design = _getNumericValue(convertedData, 'FM200 design');
          final fm200Factor = _getNumericValue(convertedData, 'FM200 Kg/m3');
          final fm200Spacing = _getNumericValue(convertedData, 'Nozzles Spacing FM200');
          final fm200Height = _getNumericValue(convertedData, 'Max Height Nozzle FM200');

          if (fm200Design != null && fm200Factor != null) {
            final percentage = '${(fm200Design * 100).toStringAsFixed(1)}%';
            factors['FM200']![percentage] = {
              'factor': fm200Factor,
              'default': percentage == '7.4%', // Set default
              'maxNozzleSpacing': fm200Spacing ?? 8.7,
              'maxNozzleHeight': fm200Height ?? 4.87,
            };
          }

          // Extract NOVEC data
          final novecDesign = _getNumericValue(convertedData, 'NOVEC design');
          final novecFactor = _getNumericValue(convertedData, 'NOVEC Kg/m3');
          final novecSpacing = _getNumericValue(convertedData, 'Nozzles Spacing Novec');
          final novecHeight = _getNumericValue(convertedData, 'Max Height Nozzle Novec');

          if (novecDesign != null && novecFactor != null) {
            final percentage = '${(novecDesign * 100).toStringAsFixed(1)}%';
            factors['NOVEC1230']![percentage] = {
              'factor': novecFactor,
              'default': percentage == '4.5%', // Set default
              'maxNozzleSpacing': novecSpacing ?? 6.9,
              'maxNozzleHeight': novecHeight ?? 4.3,
            };
          }
        }
      }

      print('Loaded design factors from database:');
      print('FM200: ${factors['FM200']!.keys}');
      print('NOVEC1230: ${factors['NOVEC1230']!.keys}');

      return factors;
    } catch (e) {
      print('Error loading design factors: $e');
      return {};
    }
  }

  /// Get agent cost from FM200/NOVEC Items tables
  Future<Map<String, Map<String, dynamic>>> getAgentData() async {
    final agentData = <String, Map<String, dynamic>>{};

    try {
      // Get NOVEC agent cost
      final novecAgent = await _findAgentInTable('NOVEC Items', 'NOVEC');
      if (novecAgent != null) {
        agentData['NOVEC1230'] = {
          'costPerKg': novecAgent.unitCost,
          'partNumber': novecAgent.partNo,
        };
      }

      // Get FM200 agent cost
      final fm200Agent = await _findAgentInTable('FM200 Items', 'FM200');
      if (fm200Agent != null) {
        agentData['FM200'] = {
          'costPerKg': fm200Agent.unitCost,
          'partNumber': fm200Agent.partNo,
        };
      }

      print('Loaded agent data from database: ${agentData.keys}');
      return agentData;
    } catch (e) {
      print('Error loading agent data: $e');
      return {};
    }
  }

  /// Public getter for Isar instance
  Isar get isar => _isar;

  /// Public method to get numeric value from row data
  double? getNumericValue(Map<String, dynamic> data, String fieldName) {
    return _getNumericValue(data, fieldName);
  }

  /// Get pipe cost per meter from database
  Future<double> getPipeCostPerMeter(int pipeSizeMm) async {
    try {
      // Use name-based lookup
      final table = await _schemaService.getTableByName('Pipes flow & prices', sectionName);
      if (table == null) return 0;

      final columns = await _schemaService.getColumnsForTableByName('Pipes flow & prices', sectionName);

      final columnMap = <String, String>{};
      for (final column in columns) {
        if (column.columnId != null && column.name != null) {
          columnMap[column.columnId!] = column.name!;
        }
      }

      final rows = await _schemaService.getRowsForTableByName('Pipes flow & prices', sectionName);

      for (final row in rows) {
        if (row.data != null) {
          final rowData = jsonDecode(row.data!) as Map<String, dynamic>;
          final convertedData = <String, dynamic>{};

          for (final entry in rowData.entries) {
            final columnName = columnMap[entry.key];
            if (columnName != null) {
              convertedData[columnName] = entry.value;
            }
          }

          final sizeMm = _getNumericValue(convertedData, 'Pipe Diameter mm');
          if (sizeMm != null && sizeMm.toInt() == pipeSizeMm) {
            final cost = _getNumericValue(convertedData, 'Pipe Cost /meter') ??
                        _getNumericValue(convertedData, 'Cost per meter') ??
                        _getNumericValue(convertedData, 'Price per meter');
            return cost ?? 0;
          }
        }
      }

      return 0;
    } catch (e) {
      print('Error getting pipe cost: $e');
      return 0;
    }
  }

  /// Get installation data from Install table
  Future<Map<String, Map<String, dynamic>>> getInstallationData() async {
    try {
      // Use name-based lookup
      final table = await _schemaService.getTableByName('Install', sectionName);
      if (table == null) return {};

      final columns = await _schemaService.getColumnsForTableByName('Install', sectionName);

      final columnMap = <String, String>{};
      for (final column in columns) {
        if (column.columnId != null && column.name != null) {
          columnMap[column.columnId!] = column.name!;
        }
      }

      final rows = await _schemaService.getRowsForTableByName('Install', sectionName);

      final installData = <String, Map<String, dynamic>>{};

      for (final row in rows) {
        if (row.data != null) {
          final rowData = jsonDecode(row.data!) as Map<String, dynamic>;
          final convertedData = <String, dynamic>{};

          for (final entry in rowData.entries) {
            final columnName = columnMap[entry.key];
            if (columnName != null) {
              convertedData[columnName] = entry.value;
            }
          }

          final description = convertedData['DESCRIPTION']?.toString() ?? '';

          // Debug: Print all row data to see what's available
          print('Install table row data: $convertedData');

          // Always check for cable columns in every row (don't rely on description matching)
          final cable15Cost = _getNumericValue(convertedData, '2x1.5mm2') ??
                             _getNumericValue(convertedData, 'Cable 2x1.5') ?? 0;
          final cable25Cost = _getNumericValue(convertedData, '2x2.5mm2') ??
                             _getNumericValue(convertedData, 'Cable 2x2.5') ?? 0;

          print('Cable costs found: 2x1.5mm2=$cable15Cost, 2x2.5mm2=$cable25Cost');

          // Add 2x1.5mm2 cable data if cost is found
          if (cable15Cost > 0) {
            installData['cable2x1.5'] = {
              'description': description.isNotEmpty ? description : 'Fire Alarm Cable 2x1.5mm² - Installation',
              'cost': cable15Cost,
            };
          }

          // Add 2x2.5mm2 cable data if cost is found
          if (cable25Cost > 0) {
            installData['cable2x2.5'] = {
              'description': description.isNotEmpty ? description : 'Fire Alarm Cable 2x2.5mm² - Installation',
              'cost': cable25Cost,
            };
          }

          if (description.toLowerCase().contains('cyl') || description.toLowerCase().contains('cylinder')) {
            installData['cylinderInstall'] = {
              'description': description,
              'cost': _getNumericValue(convertedData, 'Cyl') ??
                     _getNumericValue(convertedData, 'Cylinder') ??
                     _getNumericValue(convertedData, 'Per Cylinder') ?? 0,
            };
          }

          // Add general piping description
          if (description.toLowerCase().contains('piping') || description.toLowerCase().contains('pipe')) {
            installData['piping'] = {
              'description': description,
            };
          }

          // Add general cabling description
          if (description.toLowerCase().contains('cabling') ||
              (description.toLowerCase().contains('cable') && !description.toLowerCase().contains('2x'))) {
            installData['cabling'] = {
              'description': description,
            };
          }

          // Add installation service factors (15% for firefighting, 20% for fire alarm)
          if (description.toLowerCase().contains('firefighting') || description.toLowerCase().contains('suppression')) {
            installData['firefightingInstallFactor'] = {
              'description': description,
              'factor': _getNumericValue(convertedData, 'Factor') ?? 0.15, // 15% default
            };
          }

          if (description.toLowerCase().contains('fire alarm') || description.toLowerCase().contains('detection')) {
            installData['alarmInstallFactor'] = {
              'description': description,
              'factor': _getNumericValue(convertedData, 'Factor') ?? 0.20, // 20% default
            };
          }
        }
      }

      return installData;
    } catch (e) {
      print('Error getting installation data: $e');
      return {};
    }
  }

  /// Find component by description keyword and size using index column
  Future<Component?> findComponentByDescriptionAndSize(String tableName, String descriptionKeyword, int size) async {
    try {
      // Use name-based lookup
      final table = await _schemaService.getTableByName(tableName, sectionName);
      if (table == null) return null;

      final columns = await _schemaService.getColumnsForTableByName(tableName, sectionName);

      final columnMap = <String, String>{};
      for (final column in columns) {
        if (column.columnId != null && column.name != null) {
          columnMap[column.columnId!] = column.name!;
        }
      }

      final rows = await _schemaService.getRowsForTableByName(tableName, sectionName);

      print('Searching for "$descriptionKeyword" with size $size in $tableName table...');

      for (final row in rows) {
        if (row.data != null) {
          final rowData = jsonDecode(row.data!) as Map<String, dynamic>;
          final convertedData = <String, dynamic>{};

          for (final entry in rowData.entries) {
            final columnName = columnMap[entry.key];
            if (columnName != null) {
              convertedData[columnName] = entry.value;
            }
          }

          final description = convertedData['Description']?.toString() ??
                             convertedData['DESCRIPTION']?.toString() ?? '';

          // Check if description contains the keyword
          if (description.toLowerCase().contains(descriptionKeyword.toLowerCase())) {
            // Check if the index column matches the size
            final indexValue = _getNumericValue(convertedData, 'Index') ??
                              _getNumericValue(convertedData, 'index') ??
                              _getNumericValue(convertedData, 'Size') ??
                              _getNumericValue(convertedData, 'size');

            if (indexValue != null && indexValue.toInt() == size) {
              print('Found component: $description (Index: ${indexValue.toInt()})');
              return _extractComponentFromRow(convertedData, tableName);
            }
          }
        }
      }

      print('Component "$descriptionKeyword" with size $size not found in $tableName table');
      return null;
    } catch (e) {
      print('Error finding component by description and size: $e');
      return null;
    }
  }

  /// Find manifold assembly in NOVEC/FM200 Items tables
  Future<Component?> findManifoldAssembly(String tableName, int manifoldSize) async {
    try {
      // Use name-based lookup
      final table = await _schemaService.getTableByName(tableName, sectionName);
      if (table == null) return null;

      final columns = await _schemaService.getColumnsForTableByName(tableName, sectionName);

      final columnMap = <String, String>{};
      for (final column in columns) {
        if (column.columnId != null && column.name != null) {
          columnMap[column.columnId!] = column.name!;
        }
      }

      final rows = await _schemaService.getRowsForTableByName(tableName, sectionName);

      // Search for manifold assembly with the specified size
      print('Searching for manifold bracket assembly ${manifoldSize}mm in $tableName table...');

      for (final row in rows) {
        if (row.data != null) {
          final rowData = jsonDecode(row.data!) as Map<String, dynamic>;
          final convertedData = <String, dynamic>{};

          for (final entry in rowData.entries) {
            final columnName = columnMap[entry.key];
            if (columnName != null) {
              convertedData[columnName] = entry.value;
            }
          }

          final description = convertedData['Description']?.toString() ??
                             convertedData['DESCRIPTION']?.toString() ?? '';

          // Debug: Print all manifold-related items
          if (description.toLowerCase().contains('manifold')) {
            print('Found manifold item: $description');
          }

          // Look for manifold bracket assembly with the specified size
          // Search for patterns like "manifold bracket assembly" and the size
          if (description.toLowerCase().contains('manifold') &&
              description.toLowerCase().contains('bracket') &&
              description.toLowerCase().contains('assembly') &&
              (description.contains('${manifoldSize}mm') ||
               description.contains('$manifoldSize mm') ||
               description.contains('$manifoldSize'))) {

            final partNo = convertedData['Part No.']?.toString() ??
                          convertedData['P/N (ANSUL)']?.toString() ??
                          'MANIFOLD_${manifoldSize}MM';

            final unitCost = _getNumericValue(convertedData, 'SICLI Price') ??
                            _getNumericValue(convertedData, 'List Price') ?? 0;

            print('Found manifold assembly: $description (Part: $partNo, Cost: \$${unitCost.toStringAsFixed(2)})');

            return Component(
              partNo: partNo,
              description: description,
              unitCost: unitCost,
              manufacturer: 'ANSUL',
            );
          }
        }
      }

      // If exact size not found, look for the closest larger size
      for (int searchSize = manifoldSize + 5; searchSize <= 100; searchSize += 5) {
        for (final row in rows) {
          if (row.data != null) {
            final rowData = jsonDecode(row.data!) as Map<String, dynamic>;
            final convertedData = <String, dynamic>{};

            for (final entry in rowData.entries) {
              final columnName = columnMap[entry.key];
              if (columnName != null) {
                convertedData[columnName] = entry.value;
              }
            }

            final description = convertedData['Description']?.toString() ??
                               convertedData['DESCRIPTION']?.toString() ?? '';

            if (description.toLowerCase().contains('manifold') &&
                description.toLowerCase().contains('bracket') &&
                description.toLowerCase().contains('assembly') &&
                (description.contains('${searchSize}mm') ||
                 description.contains('$searchSize mm') ||
                 description.contains('$searchSize'))) {

              final partNo = convertedData['Part No.']?.toString() ??
                            convertedData['P/N (ANSUL)']?.toString() ??
                            'MANIFOLD_${searchSize}MM';

              final unitCost = _getNumericValue(convertedData, 'SICLI Price') ??
                              _getNumericValue(convertedData, 'List Price') ?? 0;

              print('Found closest manifold assembly: $description (Part: $partNo, Cost: \$${unitCost.toStringAsFixed(2)})');

              return Component(
                partNo: partNo,
                description: description,
                unitCost: unitCost,
                manufacturer: 'ANSUL',
              );
            }
          }
        }
      }

      return null;
    } catch (e) {
      print('Error finding manifold assembly: $e');
      return null;
    }
  }

  /// Find agent component in items table
  Future<Component?> _findAgentInTable(String tableName, String agentKeyword) async {
    try {
      // Use name-based lookup
      final table = await _schemaService.getTableByName(tableName, sectionName);
      if (table == null) return null;

      final columns = await _schemaService.getColumnsForTableByName(tableName, sectionName);

      final columnMap = <String, String>{};
      for (final column in columns) {
        if (column.columnId != null && column.name != null) {
          columnMap[column.columnId!] = column.name!;
        }
      }

      final rows = await _schemaService.getRowsForTableByName(tableName, sectionName);

      // Look for agent in the first few rows (agents are usually listed first)
      for (int i = 0; i < rows.length && i < 5; i++) {
        final row = rows[i];
        if (row.data != null) {
          final rowData = jsonDecode(row.data!) as Map<String, dynamic>;
          final convertedData = <String, dynamic>{};

          for (final entry in rowData.entries) {
            final columnName = columnMap[entry.key];
            if (columnName != null) {
              convertedData[columnName] = entry.value;
            }
          }

          final description = _findDescription(convertedData);
          if (description != null) {
            final descUpper = description.toUpperCase().replaceAll('-', '').replaceAll(' ', '');
            final keywordUpper = agentKeyword.toUpperCase().replaceAll('-', '').replaceAll(' ', '');

            // More flexible search: remove hyphens and spaces for comparison
            if (descUpper.contains(keywordUpper) || keywordUpper.contains(descUpper)) {
              print('Found agent in $tableName: $description (keyword: $agentKeyword)');
              return _extractComponentFromRow(convertedData, tableName);
            }
          }
        }
      }

      return null;
    } catch (e) {
      print('Error finding agent in $tableName: $e');
      return null;
    }
  }
}
