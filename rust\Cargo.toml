[package]
name = "firetool_ai_agent"
version = "0.1.0"
edition = "2021"

[lib]
name = "firetool_ai_agent"
crate-type = ["cdylib", "staticlib"]

[dependencies]
# Flutter Rust Bridge
flutter_rust_bridge = "2.0"
anyhow = "1.0"
tokio = { version = "1.0", features = ["rt", "rt-multi-thread", "macros"] }

# LLM Integration - Using llama-cpp-rs for GGUF support
llama-cpp-rs = "0.1"
# Alternative: llama-cpp-2 = "0.1.67"

# Serialization
serde = { version = "1.0", features = ["derive"] }
serde_json = "1.0"

# Utilities
uuid = { version = "1.0", features = ["v4"] }
chrono = { version = "0.4", features = ["serde"] }
log = "0.4"

[build-dependencies]
flutter_rust_bridge_codegen = "2.0"

[features]
default = []
