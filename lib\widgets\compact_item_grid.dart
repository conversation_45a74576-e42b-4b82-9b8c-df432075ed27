import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import '../models/project.dart';

class CompactItemGrid extends StatelessWidget {
  final List<MaterialItem> materials;
  final List<EquipmentItem> equipment;
  final Function(MaterialItem) onMaterialEdit;
  final Function(EquipmentItem) onEquipmentEdit;
  final Function(MaterialItem) onMaterialDelete;
  final Function(EquipmentItem) onEquipmentDelete;
  final Function(MaterialItem, double) onMaterialQuantityChanged;
  final Function(EquipmentItem, double) onEquipmentQuantityChanged;
  final double exchangeRate;

  const CompactItemGrid({
    super.key,
    required this.materials,
    required this.equipment,
    required this.onMaterialEdit,
    required this.onEquipmentEdit,
    required this.onMaterialDelete,
    required this.onEquipmentDelete,
    required this.onMaterialQuantityChanged,
    required this.onEquipmentQuantityChanged,
    this.exchangeRate = 3.75,
  });

  @override
  Widget build(BuildContext context) {
    final allItems = <Widget>[];
    
    // Add material items
    for (final material in materials) {
      allItems.add(_buildMaterialCard(material));
    }
    
    // Add equipment items
    for (final equipment in equipment) {
      allItems.add(_buildEquipmentCard(equipment));
    }

    if (allItems.isEmpty) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.inventory_2_outlined, size: 64, color: Colors.grey),
            SizedBox(height: 16),
            Text(
              'No items added yet',
              style: TextStyle(fontSize: 18, color: Colors.grey),
            ),
            SizedBox(height: 8),
            Text(
              'Tap + to add materials or equipment',
              style: TextStyle(fontSize: 14, color: Colors.grey),
            ),
          ],
        ),
      );
    }

    return Padding(
      padding: const EdgeInsets.all(8),
      child: GridView.builder(
        gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
          crossAxisCount: 3, // 3 items per row for more compact layout
          crossAxisSpacing: 8,
          mainAxisSpacing: 8,
          childAspectRatio: 1.1, // More compact ratio
        ),
        itemCount: allItems.length,
        itemBuilder: (context, index) => allItems[index],
      ),
    );
  }

  Widget _buildMaterialCard(MaterialItem material) {
    final totalCost = material.quantity * material.localUnitCost;

    return Card(
      elevation: 1,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(6)),
      child: Padding(
        padding: const EdgeInsets.all(8),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header with category and actions
            Row(
              children: [
                Expanded(
                  child: Container(
                    padding: const EdgeInsets.symmetric(horizontal: 4, vertical: 1),
                    decoration: BoxDecoration(
                      color: Colors.blue.shade50,
                      borderRadius: BorderRadius.circular(3),
                    ),
                    child: Text(
                      material.category,
                      style: TextStyle(
                        fontSize: 8,
                        color: Colors.blue.shade700,
                        fontWeight: FontWeight.w500,
                      ),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                ),
                PopupMenuButton<String>(
                  padding: EdgeInsets.zero,
                  iconSize: 14,
                  onSelected: (value) {
                    switch (value) {
                      case 'edit':
                        onMaterialEdit(material);
                        break;
                      case 'delete':
                        onMaterialDelete(material);
                        break;
                    }
                  },
                  itemBuilder: (context) => [
                    const PopupMenuItem(
                      value: 'edit',
                      child: Row(
                        children: [
                          Icon(Icons.edit, size: 14),
                          SizedBox(width: 6),
                          Text('Edit', style: TextStyle(fontSize: 12)),
                        ],
                      ),
                    ),
                    const PopupMenuItem(
                      value: 'delete',
                      child: Row(
                        children: [
                          Icon(Icons.delete, size: 14, color: Colors.red),
                          SizedBox(width: 6),
                          Text('Delete', style: TextStyle(color: Colors.red, fontSize: 12)),
                        ],
                      ),
                    ),
                  ],
                ),
              ],
            ),
            const SizedBox(height: 4),

            // Description
            Expanded(
              child: Text(
                material.description.isEmpty ? material.name : material.description,
                style: const TextStyle(
                  fontSize: 10,
                  fontWeight: FontWeight.w600,
                ),
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),
            ),

            const SizedBox(height: 4),

            // Quantity controls
            Row(
              children: [
                GestureDetector(
                  onTap: () {
                    if (material.quantity > 0) {
                      onMaterialQuantityChanged(material, material.quantity - 1);
                    }
                  },
                  child: Container(
                    padding: const EdgeInsets.all(2),
                    decoration: BoxDecoration(
                      color: Colors.grey.shade200,
                      borderRadius: BorderRadius.circular(3),
                    ),
                    child: const Icon(Icons.remove, size: 14),
                  ),
                ),
                Expanded(
                  child: Text(
                    '${material.quantity.toInt()}',
                    textAlign: TextAlign.center,
                    style: const TextStyle(fontWeight: FontWeight.bold, fontSize: 12),
                  ),
                ),
                GestureDetector(
                  onTap: () {
                    onMaterialQuantityChanged(material, material.quantity + 1);
                  },
                  child: Container(
                    padding: const EdgeInsets.all(2),
                    decoration: BoxDecoration(
                      color: Colors.blue.shade100,
                      borderRadius: BorderRadius.circular(3),
                    ),
                    child: const Icon(Icons.add, size: 14, color: Colors.blue),
                  ),
                ),
              ],
            ),

            const SizedBox(height: 4),

            // Cost
            Text(
              'SAR ${NumberFormat("#,##0.00").format(totalCost)}',
              style: TextStyle(
                fontSize: 10,
                fontWeight: FontWeight.bold,
                color: Colors.green.shade700,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildEquipmentCard(EquipmentItem equipment) {
    final totalCost = equipment.quantity * equipment.localUnitCost;

    return Card(
      elevation: 1,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(6)),
      child: Padding(
        padding: const EdgeInsets.all(8),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header with category and actions
            Row(
              children: [
                Expanded(
                  child: Container(
                    padding: const EdgeInsets.symmetric(horizontal: 4, vertical: 1),
                    decoration: BoxDecoration(
                      color: Colors.orange.shade50,
                      borderRadius: BorderRadius.circular(3),
                    ),
                    child: Text(
                      equipment.category,
                      style: TextStyle(
                        fontSize: 8,
                        color: Colors.orange.shade700,
                        fontWeight: FontWeight.w500,
                      ),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                ),
                PopupMenuButton<String>(
                  padding: EdgeInsets.zero,
                  iconSize: 14,
                  onSelected: (value) {
                    switch (value) {
                      case 'edit':
                        onEquipmentEdit(equipment);
                        break;
                      case 'delete':
                        onEquipmentDelete(equipment);
                        break;
                    }
                  },
                  itemBuilder: (context) => [
                    const PopupMenuItem(
                      value: 'edit',
                      child: Row(
                        children: [
                          Icon(Icons.edit, size: 14),
                          SizedBox(width: 6),
                          Text('Edit', style: TextStyle(fontSize: 12)),
                        ],
                      ),
                    ),
                    const PopupMenuItem(
                      value: 'delete',
                      child: Row(
                        children: [
                          Icon(Icons.delete, size: 14, color: Colors.red),
                          SizedBox(width: 6),
                          Text('Delete', style: TextStyle(color: Colors.red, fontSize: 12)),
                        ],
                      ),
                    ),
                  ],
                ),
              ],
            ),
            const SizedBox(height: 4),

            // Name/Description
            Expanded(
              child: Text(
                equipment.name,
                style: const TextStyle(
                  fontSize: 10,
                  fontWeight: FontWeight.w600,
                ),
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),
            ),

            const SizedBox(height: 4),

            // Quantity controls
            Row(
              children: [
                GestureDetector(
                  onTap: () {
                    if (equipment.quantity > 0) {
                      onEquipmentQuantityChanged(equipment, equipment.quantity - 1);
                    }
                  },
                  child: Container(
                    padding: const EdgeInsets.all(2),
                    decoration: BoxDecoration(
                      color: Colors.grey.shade200,
                      borderRadius: BorderRadius.circular(3),
                    ),
                    child: const Icon(Icons.remove, size: 14),
                  ),
                ),
                Expanded(
                  child: Text(
                    '${equipment.quantity.toInt()}',
                    textAlign: TextAlign.center,
                    style: const TextStyle(fontWeight: FontWeight.bold, fontSize: 12),
                  ),
                ),
                GestureDetector(
                  onTap: () {
                    onEquipmentQuantityChanged(equipment, equipment.quantity + 1);
                  },
                  child: Container(
                    padding: const EdgeInsets.all(2),
                    decoration: BoxDecoration(
                      color: Colors.orange.shade100,
                      borderRadius: BorderRadius.circular(3),
                    ),
                    child: const Icon(Icons.add, size: 14, color: Colors.orange),
                  ),
                ),
              ],
            ),

            const SizedBox(height: 4),

            // Cost
            Text(
              'SAR ${NumberFormat("#,##0.00").format(totalCost)}',
              style: TextStyle(
                fontSize: 10,
                fontWeight: FontWeight.bold,
                color: Colors.green.shade700,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }
}
