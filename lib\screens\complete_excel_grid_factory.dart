import 'package:flutter/material.dart';
import '../widgets/system_data_grid.dart';
import '../models/isar_models.dart';

class CompleteExcelGridFactory {
  static Widget createScreen(String systemType) {
    SystemType type;
    String title;
    Color themeColor;

    switch (systemType) {
      case 'alarm':
        type = SystemType.alarm;
        title = 'Fire Alarm Systems';
        themeColor = Colors.red.shade700;
        break;
      case 'water':
        type = SystemType.water;
        title = 'Water Systems';
        themeColor = Colors.blue.shade700;
        break;
      case 'foam':
        type = SystemType.foam;
        title = 'Foam Systems';
        themeColor = Colors.amber.shade700;
        break;
      case 'cleanAgent':
        type = SystemType.cleanAgent;
        title = 'Clean Agent Systems';
        themeColor = Colors.green.shade700;
        break;
      case 'co2':
        type = SystemType.co2;
        title = 'CO2 Systems';
        themeColor = Colors.purple.shade700;
        break;
      default:
        type = SystemType.materials;
        title = 'Materials';
        themeColor = Colors.brown.shade700;
    }

    return SystemDataGrid(
      systemType: type,
      title: title,
      themeColor: themeColor,
    );
  }
}
