import 'models/estimator_types.dart';
import 'utils/estimator_calculations.dart';
import 'utils/bom_generator.dart';
import 'utils/formatters.dart';

void testReactAppLogic() {
  print('\n=== TESTING REACT APP CALCULATION LOGIC ===\n');

  // Test 1: NOVEC 1230 with dimensions
  print('TEST 1: NOVEC 1230 - 5x5x3m room');
  final input1 = EstimatorFormValues(
    agentType: AgentType.novec1230,
    designConcentration: '4.5%',
    inputMode: InputMode.dimensions,
    roomLength: 5.0,
    roomWidth: 5.0,
    roomHeight: 3.0,
    systemType: SystemType.main,
    installationType: InstallationType.supplyOnly,
  );

  final results1 = EstimatorCalculations.calculateDesign(input1);
  final bomData1 = BomGenerator.generateBOM(results1, input1);
  final bom1 = bomData1['bom'] as List<BomItem>;
  final summary1 = bomData1['summary'] as BomSummary;

  print('Room Volume: ${formatNumber(results1.roomData.roomVolume)} m³');
  print('Total Agent Required: ${formatNumber(results1.totalAgentRequired)} kg');
  print('Actual Agent: ${formatNumber(results1.cylinder.actualTotalKg)} kg');
  print('Number of Cylinders: ${results1.cylinder.numCylinders2ndIter}');
  print('Cylinder Size: ${results1.cylinder.cylinderSizeLiters2ndIter.toInt()}L');
  print('Number of Nozzles: ${results1.discharge.nozzleQtyFinal}');
  print('Nozzle Size: ${results1.discharge.nozzleSizeFinal}mm');
  print('Total Cost (SAR): ${formatNumber(summary1.grandTotalSAR)}');
  print('BOM Items: ${bom1.length}');

  print('\n${'='*50}\n');

  // Test 2: FM200 with dimensions
  print('TEST 2: FM200 - 8x6x4m room');
  final input2 = EstimatorFormValues(
    agentType: AgentType.fm200,
    designConcentration: '8.5%',
    inputMode: InputMode.dimensions,
    roomLength: 8.0,
    roomWidth: 6.0,
    roomHeight: 4.0,
    systemType: SystemType.main,
    installationType: InstallationType.supplyOnly,
  );

  final results2 = EstimatorCalculations.calculateDesign(input2);
  final bomData2 = BomGenerator.generateBOM(results2, input2);
  final bom2 = bomData2['bom'] as List<BomItem>;
  final summary2 = bomData2['summary'] as BomSummary;

  print('Room Volume: ${formatNumber(results2.roomData.roomVolume)} m³');
  print('Total Agent Required: ${formatNumber(results2.totalAgentRequired)} kg');
  print('Actual Agent: ${formatNumber(results2.cylinder.actualTotalKg)} kg');
  print('Number of Cylinders: ${results2.cylinder.numCylinders2ndIter}');
  print('Cylinder Size: ${results2.cylinder.cylinderSizeLiters2ndIter.toInt()}L');
  print('Number of Nozzles: ${results2.discharge.nozzleQtyFinal}');
  print('Nozzle Size: ${results2.discharge.nozzleSizeFinal}mm');
  print('Total Cost (SAR): ${formatNumber(summary2.grandTotalSAR)}');
  print('BOM Items: ${bom2.length}');

  print('\n${'='*50}\n');

  // Test 3: Agent quantity mode
  print('TEST 3: NOVEC 1230 - Agent Quantity Mode (100kg)');
  final input3 = EstimatorFormValues(
    agentType: AgentType.novec1230,
    designConcentration: '4.5%',
    inputMode: InputMode.agentQuantity,
    agentQuantity: 100.0,
    roomHeight: 4.0,
    systemType: SystemType.main,
    installationType: InstallationType.supplyOnly,
  );

  final results3 = EstimatorCalculations.calculateDesign(input3);
  final bomData3 = BomGenerator.generateBOM(results3, input3);
  final bom3 = bomData3['bom'] as List<BomItem>;
  final summary3 = bomData3['summary'] as BomSummary;

  print('Room Volume: ${formatNumber(results3.roomData.roomVolume)} m³');
  print('Room Dimensions: ${formatNumber(results3.roomData.roomLength)}m x ${formatNumber(results3.roomData.roomWidth)}m x ${formatNumber(results3.roomData.roomHeight)}m');
  print('Total Agent Required: ${formatNumber(results3.totalAgentRequired)} kg');
  print('Actual Agent: ${formatNumber(results3.cylinder.actualTotalKg)} kg');
  print('Number of Cylinders: ${results3.cylinder.numCylinders2ndIter}');
  print('Cylinder Size: ${results3.cylinder.cylinderSizeLiters2ndIter.toInt()}L');
  print('Number of Nozzles: ${results3.discharge.nozzleQtyFinal}');
  print('Nozzle Size: ${results3.discharge.nozzleSizeFinal}mm');
  print('Total Cost (SAR): ${formatNumber(summary3.grandTotalSAR)}');
  print('BOM Items: ${bom3.length}');

  print('\n=== REACT APP LOGIC TEST COMPLETED ===\n');
}

// Example of how to use in your existing calculator
void integrateWithExistingCalculator() {
  print('\n=== INTEGRATION EXAMPLE ===\n');
  
  // This shows how to replace your existing calculation with React app logic
  final input = EstimatorFormValues(
    agentType: AgentType.fm200,
    designConcentration: '7.4%',
    inputMode: InputMode.dimensions,
    roomLength: 10.0,
    roomWidth: 8.0,
    roomHeight: 3.5,
    systemType: SystemType.main,
    installationType: InstallationType.supplyAndInstall,
  );

  // Use the React app calculation engine
  final designResults = EstimatorCalculations.calculateDesign(input);
  
  // Generate complete BOM with React app logic
  final bomData = BomGenerator.generateBOM(designResults, input);
  final bom = bomData['bom'] as List<BomItem>;
  final summary = bomData['summary'] as BomSummary;

  print('=== CALCULATION RESULTS ===');
  print('Room Volume: ${formatNumber(designResults.roomData.roomVolume)} m³');
  print('Total Agent Required: ${formatNumber(designResults.totalAgentRequired)} kg');
  print('Actual Agent: ${formatNumber(designResults.cylinder.actualTotalKg)} kg');
  print('Number of Cylinders: ${designResults.cylinder.numCylinders2ndIter}');
  print('Cylinder Size: ${designResults.cylinder.cylinderSizeLiters2ndIter.toInt()}L');
  print('Number of Nozzles: ${designResults.discharge.nozzleQtyFinal}');
  print('Nozzle Size: ${designResults.discharge.nozzleSizeFinal}mm');
  print('Total Cost (SAR): ${formatNumber(summary.grandTotalSAR)}');
  print('BOM Items: ${bom.length}');

  print('\n=== BOM SAMPLE (First 5 items) ===');
  for (int i = 0; i < 5 && i < bom.length; i++) {
    final item = bom[i];
    print('${item.partNo}: ${item.description} - Qty: ${item.quantity} - Cost: \$${formatNumber(item.totalCost)}');
  }
}
