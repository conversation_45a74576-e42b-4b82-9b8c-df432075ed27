class ComponentItem {
  final String id;
  final String agentType;
  final String category;
  final String subcategory;
  final String partNumber;
  final String description;
  final String manufacturer;
  final double unitCost;
  final String currency;

  ComponentItem({
    required this.id,
    required this.agentType,
    required this.category,
    required this.subcategory,
    required this.partNumber,
    required this.description,
    required this.manufacturer,
    required this.unitCost,
    required this.currency,
  });
}