class DesignConcentration {
  final String id;
  final String agentType;
  final String percentage;
  final double factor;
  final bool isDefault;
  final double maxNozzleSpacing;
  final double maxNozzleHeight;

  DesignConcentration({
    required this.id,
    required this.agentType,
    required this.percentage,
    required this.factor,
    required this.isDefault,
    required this.maxNozzleSpacing,
    required this.maxNozzleHeight,
  });
}