use serde::{Deserialize, Serialize};
use std::path::Path<PERSON>uf;

/// Model management for local LLM models
#[derive(<PERSON>bug, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct ModelManager {
    pub models_directory: PathBuf,
    pub available_models: Vec<LocalModel>,
    pub active_model: Option<String>,
}

#[derive(<PERSON>bu<PERSON>, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct LocalModel {
    pub id: String,
    pub name: String,
    pub file_path: PathBuf,
    pub size_bytes: u64,
    pub quantization: QuantizationType,
    pub model_type: ModelType,
    pub parameters: ModelParameters,
    pub is_downloaded: bool,
    pub supports_tools: bool,
}

#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub enum QuantizationType {
    Q4KM,   // 4-bit quantization, medium quality
    Q4KS,   // 4-bit quantization, small size
    Q5KM,   // 5-bit quantization, medium quality
    Q8_0,   // 8-bit quantization, high quality
    F16,    // 16-bit float, highest quality
}

#[derive(Debug, <PERSON><PERSON>, Ser<PERSON><PERSON>, Deserialize)]
pub enum ModelType {
    Phi2,
    <PERSON><PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON>2,
    <PERSON><PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON>,
}

#[derive(<PERSON>bu<PERSON>, <PERSON><PERSON>, Serial<PERSON>, Deserialize)]
pub struct ModelParameters {
    pub context_length: u32,
    pub vocab_size: u32,
    pub hidden_size: u32,
    pub num_layers: u32,
    pub num_heads: u32,
}

impl ModelManager {
    pub fn new(models_dir: PathBuf) -> Self {
        Self {
            models_directory: models_dir,
            available_models: Self::get_recommended_models(),
            active_model: None,
        }
    }

    /// Get list of recommended models for FireTool
    pub fn get_recommended_models() -> Vec<LocalModel> {
        vec![
            LocalModel {
                id: "phi2-q4km".to_string(),
                name: "Phi-2 Q4_K_M".to_string(),
                file_path: PathBuf::from("phi-2.Q4_K_M.gguf"),
                size_bytes: 1_600_000_000, // ~1.6GB
                quantization: QuantizationType::Q4KM,
                model_type: ModelType::Phi2,
                parameters: ModelParameters {
                    context_length: 2048,
                    vocab_size: 51200,
                    hidden_size: 2560,
                    num_layers: 32,
                    num_heads: 32,
                },
                is_downloaded: false,
                supports_tools: true,
            },
            LocalModel {
                id: "tinyllama-q4km".to_string(),
                name: "TinyLlama Q4_K_M".to_string(),
                file_path: PathBuf::from("tinyllama-1.1b-chat-v1.0.Q4_K_M.gguf"),
                size_bytes: 800_000_000, // ~800MB
                quantization: QuantizationType::Q4KM,
                model_type: ModelType::TinyLlama,
                parameters: ModelParameters {
                    context_length: 2048,
                    vocab_size: 32000,
                    hidden_size: 2048,
                    num_layers: 22,
                    num_heads: 32,
                },
                is_downloaded: false,
                supports_tools: true,
            },
            LocalModel {
                id: "phi2-q5km".to_string(),
                name: "Phi-2 Q5_K_M (Higher Quality)".to_string(),
                file_path: PathBuf::from("phi-2.Q5_K_M.gguf"),
                size_bytes: 1_900_000_000, // ~1.9GB
                quantization: QuantizationType::Q5KM,
                model_type: ModelType::Phi2,
                parameters: ModelParameters {
                    context_length: 2048,
                    vocab_size: 51200,
                    hidden_size: 2560,
                    num_layers: 32,
                    num_heads: 32,
                },
                is_downloaded: false,
                supports_tools: true,
            },
        ]
    }

    /// Get download URLs for models
    pub fn get_model_download_info(&self, model_id: &str) -> Option<ModelDownloadInfo> {
        match model_id {
            "phi2-q4km" => Some(ModelDownloadInfo {
                url: "https://huggingface.co/microsoft/phi-2/resolve/main/phi-2.Q4_K_M.gguf".to_string(),
                checksum: "".to_string(), // Add actual checksums
                mirror_urls: vec![
                    "https://huggingface.co/TheBloke/phi-2-GGUF/resolve/main/phi-2.Q4_K_M.gguf".to_string(),
                ],
            }),
            "tinyllama-q4km" => Some(ModelDownloadInfo {
                url: "https://huggingface.co/TheBloke/TinyLlama-1.1B-Chat-v1.0-GGUF/resolve/main/tinyllama-1.1b-chat-v1.0.Q4_K_M.gguf".to_string(),
                checksum: "".to_string(),
                mirror_urls: vec![],
            }),
            "phi2-q5km" => Some(ModelDownloadInfo {
                url: "https://huggingface.co/microsoft/phi-2/resolve/main/phi-2.Q5_K_M.gguf".to_string(),
                checksum: "".to_string(),
                mirror_urls: vec![],
            }),
            _ => None,
        }
    }

    /// Check which models are already downloaded
    pub fn scan_downloaded_models(&mut self) {
        for model in &mut self.available_models {
            let full_path = self.models_directory.join(&model.file_path);
            model.is_downloaded = full_path.exists();
            
            if model.is_downloaded {
                // Update actual file size
                if let Ok(metadata) = std::fs::metadata(&full_path) {
                    model.size_bytes = metadata.len();
                }
            }
        }
    }

    /// Get model by ID
    pub fn get_model(&self, model_id: &str) -> Option<&LocalModel> {
        self.available_models.iter().find(|m| m.id == model_id)
    }

    /// Get recommended model for mobile (smallest)
    pub fn get_mobile_model(&self) -> Option<&LocalModel> {
        self.available_models
            .iter()
            .filter(|m| m.is_downloaded)
            .min_by_key(|m| m.size_bytes)
    }

    /// Get recommended model for desktop (best quality available)
    pub fn get_desktop_model(&self) -> Option<&LocalModel> {
        self.available_models
            .iter()
            .filter(|m| m.is_downloaded)
            .max_by_key(|m| match m.quantization {
                QuantizationType::F16 => 5,
                QuantizationType::Q8_0 => 4,
                QuantizationType::Q5KM => 3,
                QuantizationType::Q4KM => 2,
                QuantizationType::Q4KS => 1,
            })
    }
}

#[derive(Debug, Clone)]
pub struct ModelDownloadInfo {
    pub url: String,
    pub checksum: String,
    pub mirror_urls: Vec<String>,
}

impl LocalModel {
    /// Get human-readable size
    pub fn get_size_string(&self) -> String {
        let size_mb = self.size_bytes as f64 / 1_000_000.0;
        if size_mb < 1000.0 {
            format!("{:.0} MB", size_mb)
        } else {
            format!("{:.1} GB", size_mb / 1000.0)
        }
    }

    /// Check if model is suitable for mobile
    pub fn is_mobile_suitable(&self) -> bool {
        self.size_bytes < 1_000_000_000 // Less than 1GB
    }

    /// Get performance rating (1-5)
    pub fn get_performance_rating(&self) -> u8 {
        match (&self.model_type, &self.quantization) {
            (ModelType::Phi2, QuantizationType::Q5KM) => 5,
            (ModelType::Phi2, QuantizationType::Q4KM) => 4,
            (ModelType::TinyLlama, QuantizationType::Q4KM) => 3,
            (ModelType::TinyLlama, QuantizationType::Q4KS) => 2,
            _ => 3,
        }
    }

    /// Get quality description
    pub fn get_quality_description(&self) -> &'static str {
        match self.get_performance_rating() {
            5 => "Excellent - Best quality, larger size",
            4 => "Very Good - Balanced quality and size",
            3 => "Good - Fast and efficient",
            2 => "Fair - Compact for mobile",
            _ => "Basic - Minimal resource usage",
        }
    }
}
