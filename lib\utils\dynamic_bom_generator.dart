import 'package:flutter/foundation.dart';
import '../models/estimator_types.dart';
import '../data/estimator_config.dart';
import '../services/dynamic_clean_agent_service.dart';
import '../services/isar_service.dart';
import 'estimator_calculations.dart';

class DynamicBomGenerator {
  static DynamicCleanAgentService? _dynamicService;

  /// Initialize the dynamic service
  static Future<void> initialize() async {
    if (_dynamicService == null) {
      final isarService = IsarService.instance;
      final isar = await isarService.database;
      _dynamicService = DynamicCleanAgentService(isar);
    }
  }



  /// Helper function to add item to BOM using database lookup
  /// Returns true if item was found and added, false otherwise
  static Future<bool> addBomItemFromDatabase(
    List<BomItem> bom,
    String partNo,
    int quantity,
    String category, {
    String? subcategory,
    String currency = 'USD',
  }) async {
    if (partNo.isEmpty || quantity <= 0) return false;

    await initialize();
    final component = await _dynamicService!.getComponentByPartNumber(partNo);

    if (component == null) {
      debugPrint('Component $partNo not found in database, skipping...');
      return false;
    }
    
    bom.add(BomItem(
      partNo: partNo,
      description: component.description,
      quantity: quantity,
      unitCost: component.unitCost,
      totalCost: component.unitCost * quantity,
      manufacturer: component.manufacturer,
      category: category,
      subcategory: subcategory,
      currency: currency,
    ));
    return true;
  }

  /// Generate Bill of Materials using database data
  static Future<Map<String, dynamic>> generateBOM(DesignResults design, EstimatorFormValues input, {double? installationFactor}) async {
    await initialize();
    
    final bom = <BomItem>[];
    final agentType = input.agentType == AgentType.novec1230 ? 'NOVEC1230' : 'FM200';
    final isMainAndReserve = input.systemType == SystemType.mainAndReserve;
    final cylinderSize = design.cylinder.cylinderSizeLiters2ndIter;
    final cylinderQty = design.cylinder.numCylinders2ndIter;
    final roomArea = design.roomData.roomArea;
    final roomLength = design.roomData.roomLength;
    final roomWidth = design.roomData.roomWidth;
    final roomHeight = design.roomData.roomHeight;
    
    // Multiplier for main+reserve systems
    final systemMultiplier = isMainAndReserve ? 2 : 1;
    
    // Get dynamic data from database
    final cylinderSpecs = await _dynamicService!.getCylinderSpecs(agentType);
    final agentData = await _dynamicService!.getAgentData();

    // SECTION 1: SUPPRESSION SYSTEM COMPONENTS
    
    // 1. Add cylinders
    CylinderSpec? cylinderSpec;
    try {
      cylinderSpec = cylinderSpecs.where((c) => c.size == cylinderSize).first;
    } catch (e) {
      cylinderSpec = null;
    }
    
    if (cylinderSpec != null) {
      // Add cylinder from database
      await addBomItemFromDatabase(bom, cylinderSpec.partNo, cylinderQty * systemMultiplier, 'Extinguishing System (Hygood - UL/FM – UK)', subcategory: 'Cylinders');
      
      // Add labels from database
      await addBomItemFromDatabase(bom, cylinderSpec.labelPart, cylinderQty * systemMultiplier, 'Extinguishing System (Hygood - UL/FM – UK)', subcategory: 'Cylinder Accessories');

      // Add brackets from database - 2 per cylinder as requested
      await addBomItemFromDatabase(bom, cylinderSpec.bracketPart, 2 * cylinderQty * systemMultiplier, 'Extinguishing System (Hygood - UL/FM – UK)', subcategory: 'Cylinder Accessories');
    }

    // 2. Add actuators from database
    final eActuatorQty = cylinderQty < 9 ? 1 : (cylinderQty >= 9 && cylinderQty <= 15 ? 2 : 3);
    await addBomItemFromDatabase(bom, '*********', eActuatorQty * systemMultiplier, 'Extinguishing System (Hygood - UL/FM – UK)', subcategory: 'Actuators');
    await addBomItemFromDatabase(bom, '304.209.002', eActuatorQty * systemMultiplier, 'Extinguishing System (Hygood - UL/FM – UK)', subcategory: 'Actuators');
    
    // Pneumatic Actuator - (n-1) per bank where n is cylinder count
    if (cylinderQty > 1) {
      await addBomItemFromDatabase(bom, '304.209.004', (cylinderQty - 1) * systemMultiplier, 'Extinguishing System (Hygood - UL/FM – UK)', subcategory: 'Actuators');
    }

    // 3. Add discharge components from database
    if (cylinderQty > 1) {
      await addBomItemFromDatabase(bom, '306.205.003', (cylinderQty - 1) * systemMultiplier, 'Extinguishing System (Hygood - UL/FM – UK)', subcategory: 'Discharge Components');
    } else {
      await addBomItemFromDatabase(bom, '306.205.003', 1, 'Extinguishing System (Hygood - UL/FM – UK)', subcategory: 'Discharge Components');
    }

    // Add pressure switches from database
    await addBomItemFromDatabase(bom, '304.205.006', cylinderQty * systemMultiplier, 'Extinguishing System (Hygood - UL/FM – UK)', subcategory: 'Switches');
    await addBomItemFromDatabase(bom, '437900', 1, 'Extinguishing System (Hygood - UL/FM – UK)', subcategory: 'Switches');
    
    // 3. Add discharge hose and check valve based on flow per cylinder (not manifold size)
    // Calculate flow per cylinder: Total Agent Weight ÷ Number of Cylinders ÷ 10 seconds
    final totalAgentKg = design.cylinder.actualTotalKg;
    final flowPerCylinder = totalAgentKg / cylinderQty / 10.0; // kg/s per cylinder

    // Find the correct pipe size using database pipe data
    final pipeData = await _dynamicService!.getPipeData();
    int dischargePipeSize = 15; // Default fallback

    // Find the smallest pipe that can handle the flow per cylinder
    for (final pipe in pipeData) {
      if (flowPerCylinder >= pipe.minFlow && flowPerCylinder <= pipe.maxFlow) {
        dischargePipeSize = pipe.sizeMm;
        break;
      }
    }

    // If flow is greater than largest pipe, use largest
    if (pipeData.isNotEmpty && flowPerCylinder > pipeData.last.maxFlow) {
      dischargePipeSize = pipeData.last.sizeMm;
    }

    // Choose nearest larger available discharge component size (25mm, 50mm, 80mm)
    String dischargeHosePart;
    String checkValvePart;

    if (dischargePipeSize <= 25) {
      // Use 25mm components
      dischargeHosePart = '306.207.002'; // 25mm discharge hose
      checkValvePart = '302.209.004';    // 25mm manifold check valve
    } else if (dischargePipeSize <= 50) {
      // Use 50mm components
      dischargeHosePart = '306.207.003'; // 50mm discharge hose
      checkValvePart = '302.209.005';    // 50mm manifold check valve
    } else {
      // Use 80mm components for larger flows
      dischargeHosePart = '306.205.005'; // 80mm valve discharge hose
      checkValvePart = '306.205.006';    // 80mm discharge hose/check valve assembly
    }

    debugPrint('Flow per cylinder: ${flowPerCylinder.toStringAsFixed(2)} kg/s → Required pipe size: ${dischargePipeSize}mm → Using discharge components: $dischargeHosePart, $checkValvePart');

    // Add discharge hose from database
    await addBomItemFromDatabase(bom, dischargeHosePart, cylinderQty * systemMultiplier, 'Extinguishing System (Hygood - UL/FM – UK)', subcategory: 'Discharge Components');

    // Add check valve from database
    await addBomItemFromDatabase(bom, checkValvePart, cylinderQty * systemMultiplier, 'Extinguishing System (Hygood - UL/FM – UK)', subcategory: 'Discharge Components');
    
    // 4. Add Liquid Level Indicator if applicable from database
    debugPrint('=== LLI SECTION STARTED ===');
    debugPrint('Agent Type: $agentType');
    debugPrint('Cylinder Size: ${cylinderSize}L');
    debugPrint('Cylinder Qty: $cylinderQty');

    final cylinderToLLI = {
      'NOVEC1230': {
        '4.5': null, '8': null, '16': null, '32': null, '52': null,
        '106': '300.015.127', '147': '300.015.128', '180': '300.015.129', '343': '300.015.128'
      },
      'FM200': {
        '4.5': null, '8': null, '16': null, '32': null, '52': null,
        '106': '300.015.127', '147': '300.015.128', '180': '300.015.128', '343': '300.015.128'
      }
    };

    final cylinderSizeKey = cylinderSize.toInt().toString(); // Convert 106.0 -> 106 -> '106'
    final lliPart = cylinderToLLI[agentType]?[cylinderSizeKey];
    debugPrint('LLI Check: Agent=$agentType, CylinderSize=${cylinderSize}L, Key=$cylinderSizeKey, LLI Part=$lliPart');
    debugPrint('Available agent types in LLI map: ${cylinderToLLI.keys.toList()}');
    debugPrint('Available cylinder sizes for $agentType: ${cylinderToLLI[agentType]?.keys.toList()}');

    if (lliPart != null) {
      debugPrint('Adding LLI: $lliPart');
      await addBomItemFromDatabase(bom, lliPart, cylinderQty * systemMultiplier, 'Extinguishing System (Hygood - UL/FM – UK)', subcategory: 'Cylinder Accessories');
      debugPrint('LLI added successfully');
    } else {
      debugPrint('No LLI required for ${cylinderSize}L cylinder');
    }
    debugPrint('=== LLI SECTION ENDED ===');

    // 5. Add agent from database
    final agentInfo = agentData[agentType];
    if (agentInfo != null && agentInfo['partNumber'] != null) {
      await addBomItemFromDatabase(bom, agentInfo['partNumber'], design.cylinder.actualTotalKg.ceil() * systemMultiplier, 'Extinguishing System (Hygood - UL/FM – UK)', subcategory: 'Agent');
    }

    // 6. Add nozzles from database
    final nozzleSize = design.discharge.nozzleSizeFinal;
    final nozzleQty = design.discharge.nozzleQtyFinal;

    final nozzleMapping = {
      'NOVEC1230': {
        15: '310.207.214', 20: '310.207.216', 25: '310.207.218',
        32: '310.207.220', 40: '310.207.222', 50: '310.207.224'
      },
      'FM200': {
        10: '310.205.216', 15: '310.205.218', 20: '310.205.220',
        25: '310.205.222', 32: '310.205.224', 40: '310.205.226', 50: '310.205.228'
      }
    };

    final nozzlePart = nozzleMapping[agentType]?[nozzleSize];
    if (nozzlePart != null) {
      // Try to add from database first
      bool nozzleAdded = await addBomItemFromDatabase(bom, nozzlePart, nozzleQty * systemMultiplier, 'Extinguishing System (Hygood - UL/FM – UK)', subcategory: 'Nozzles');

      // If not found in database, add with fallback pricing
      if (!nozzleAdded) {
        debugPrint('Nozzle $nozzlePart not found in database, adding with fallback pricing...');
        final fallbackCost = agentType == 'NOVEC1230' ? 190.0 : 180.0; // USD
        bom.add(BomItem(
          partNo: nozzlePart,
          description: '${nozzleSize}mm Discharge Nozzle for $agentType',
          quantity: nozzleQty * systemMultiplier,
          unitCost: fallbackCost,
          totalCost: fallbackCost * (nozzleQty * systemMultiplier),
          manufacturer: agentType == 'NOVEC1230' ? '3M' : 'Kidde',
          category: 'Extinguishing System (Hygood - UL/FM – UK)',
          subcategory: 'Nozzles',
          currency: 'USD',
        ));
      }
    }

    // 7. Add manifold assembly from database (search in NOVEC/FM200 Items tables)
    final manifoldAssemblySize = design.discharge.manifoldAssemblySize;
    const minManifoldSize = 65; // Minimum manifold size
    final actualManifoldSize = manifoldAssemblySize < minManifoldSize ? minManifoldSize : manifoldAssemblySize;

    // Search for manifold assembly in the appropriate items table
    final manifoldTableName = agentType == 'NOVEC1230' ? 'NOVEC Items' : 'FM200 Items';
    final manifoldComponent = await _findManifoldInItemsTable(manifoldTableName, actualManifoldSize.toInt());

    if (manifoldComponent != null) {
      bom.add(BomItem(
        partNo: manifoldComponent.partNo,
        description: manifoldComponent.description,
        quantity: 1 * systemMultiplier,
        unitCost: manifoldComponent.unitCost,
        totalCost: manifoldComponent.unitCost * (1 * systemMultiplier),
        manufacturer: manifoldComponent.manufacturer,
        category: 'Extinguishing System (Hygood - UL/FM – UK)',
        subcategory: 'Manifold',
        currency: 'USD', // Manifold assemblies are in USD
      ));
    } else {
      print('Manifold assembly ${actualManifoldSize}mm not found in $manifoldTableName table');
    }

    // 8. Add caution plates from database
    final doorCautionPart = agentType == 'NOVEC1230' ? '314.207.001' : '314.205.002';
    final manualCautionPart = agentType == 'NOVEC1230' ? '314.207.003' : '314.205.003';
    await addBomItemFromDatabase(bom, doorCautionPart, 1, 'Extinguishing System (Hygood - UL/FM – UK)', subcategory: 'Signage');
    await addBomItemFromDatabase(bom, manualCautionPart, 1, 'Extinguishing System (Hygood - UL/FM – UK)', subcategory: 'Signage');

    // SECTION 2: ALARM & DETECTION SYSTEM
    final detectorQty = EstimatorCalculations.calculateDetectors(roomArea);

    // Control panel from database
    await addBomItemFromDatabase(bom, 'RP-2002E', 1, 'Fire Alarm (Notifier - UL/FM)', subcategory: 'Control Panel');

    // Detectors from database
    await addBomItemFromDatabase(bom, '2W-B', detectorQty, 'Fire Alarm (Notifier - UL/FM)', subcategory: 'Detectors');
    await addBomItemFromDatabase(bom, '5151-CH', detectorQty, 'Fire Alarm (Notifier - UL/FM)', subcategory: 'Detectors');

    // Notification devices from database
    await addBomItemFromDatabase(bom, 'P2GRKLED', 2, 'Fire Alarm (Notifier - UL/FM)', subcategory: 'Notification');
    await addBomItemFromDatabase(bom, 'SSM24-6', 1, 'Fire Alarm (Notifier - UL/FM)', subcategory: 'Notification');

    // Manual stations from database
    await addBomItemFromDatabase(bom, 'NBG-12LR', 1, 'Fire Alarm (Notifier - UL/FM)', subcategory: 'Manual Stations');
    await addBomItemFromDatabase(bom, '2080-9057', 1, 'Fire Alarm (Notifier - UL/FM)', subcategory: 'Manual Stations');

    // Selector switch for main+reserve from database
    if (isMainAndReserve) {
      await addBomItemFromDatabase(bom, '76496', 1, 'Fire Alarm (Notifier - UL/FM)', subcategory: 'Control');
    }

    // Disconnect switch from database
    await addBomItemFromDatabase(bom, '2080-9070', 1, 'Fire Alarm (Notifier - UL/FM)', subcategory: 'Control');

    // SECTION 3: INSTALLATION ITEMS (if supply & install)
    if (input.installationType == InstallationType.supplyAndInstall) {
      await _addInstallationItems(bom, roomLength, roomWidth, roomHeight, nozzleQty, detectorQty, cylinderQty, design.discharge.manifoldPipeSize, design);
    }

    return {
      'bom': bom,
      'summary': await _calculateSummary(bom, design, input, installationFactor: installationFactor),
    };
  }

  /// Add installation items from database tables
  static Future<void> _addInstallationItems(
    List<BomItem> bom,
    double roomLength,
    double roomWidth,
    double roomHeight,
    int nozzleQty,
    int detectorQty,
    int cylinderQty,
    int manifoldPipeSize,
    DesignResults design,
  ) async {
    await initialize();

    // Get cable pricing and descriptions from "Install" table
    final installData = await _dynamicService!.getInstallationData();
    print('=== INSTALLATION DATA DEBUG ===');
    print('Install data keys: ${installData.keys.toList()}');
    installData.forEach((key, value) {
      print('Key: $key, Value: $value');
    });
    print('=== END INSTALLATION DATA DEBUG ===');

    // Calculate pipe lengths using correct React app formulas
    final pipeLengths = calculatePipeLengths(roomLength, roomWidth, roomHeight, nozzleQty, cylinderQty);
    final nozzlePipeLength = pipeLengths['nozzlePipeLength']!;
    final manifoldPipeLength = pipeLengths['manifoldPipeLength']!;

    // Calculate cable lengths for fire alarm system
    final cableLengths = calculateCableLengths(roomLength, roomWidth, roomHeight, cylinderQty);
    final loopCableLength = cableLengths['loopCableLength']!;
    final powerCableLength = cableLengths['powerCableLength']!;

    // Get pipe costs per meter from database (in SAR)
    final nozzlePipeSize = design.discharge.nozzleSizeFinal; // Use nozzle size for nozzle piping
    final nozzlePipeCostPerMeter = await _dynamicService!.getPipeCostPerMeter(nozzlePipeSize);
    final manifoldPipeCostPerMeter = await _dynamicService!.getPipeCostPerMeter(manifoldPipeSize);

    // Get piping description from Install table
    final pipingData = installData['piping'];
    final pipingDescription = pipingData?['description'] ?? 'Piping Installation';

    // Add nozzle piping installation items (in SAR)
    if (nozzlePipeCostPerMeter > 0) {
      print('Adding nozzle pipe: ${nozzlePipeLength.ceil()}m × SAR ${nozzlePipeCostPerMeter.toStringAsFixed(2)} = SAR ${(nozzlePipeCostPerMeter * nozzlePipeLength.ceil()).toStringAsFixed(2)}');
      bom.add(BomItem(
        partNo: 'NOZZLE_PIPE_${nozzlePipeSize}MM',
        description: '$pipingDescription - Nozzle Pipe ${nozzlePipeSize}mm',
        quantity: nozzlePipeLength.ceil(),
        unitCost: nozzlePipeCostPerMeter,
        totalCost: nozzlePipeCostPerMeter * nozzlePipeLength.ceil(),
        manufacturer: 'Generic',
        category: 'Installation Items',
        subcategory: 'Piping',
        currency: 'SAR', // Force SAR currency
      ));
    }

    // Add manifold piping installation items (in SAR)
    if (manifoldPipeCostPerMeter > 0) {
      print('Adding manifold pipe: ${manifoldPipeLength.ceil()}m × SAR ${manifoldPipeCostPerMeter.toStringAsFixed(2)} = SAR ${(manifoldPipeCostPerMeter * manifoldPipeLength.ceil()).toStringAsFixed(2)}');
      bom.add(BomItem(
        partNo: 'MANIFOLD_PIPE_${manifoldPipeSize}MM',
        description: '$pipingDescription - Manifold Pipe ${manifoldPipeSize}mm',
        quantity: manifoldPipeLength.ceil(),
        unitCost: manifoldPipeCostPerMeter,
        totalCost: manifoldPipeCostPerMeter * manifoldPipeLength.ceil(),
        manufacturer: 'Generic',
        category: 'Installation Items',
        subcategory: 'Piping',
        currency: 'SAR', // Force SAR currency
      ));
    }

    // Add cable installation items with proper descriptions (in SAR)
    final cablingData = installData['cabling'];
    final cable15Data = installData['cable2x1.5'];
    if (cable15Data != null) {
      final cable15Cost = cable15Data['cost'] ?? 0;
      print('Adding loop cable: ${loopCableLength.ceil()}m × SAR ${cable15Cost.toStringAsFixed(2)} = SAR ${(cable15Cost * loopCableLength.ceil()).toStringAsFixed(2)}');
      bom.add(BomItem(
        partNo: 'CABLE_2X1.5MM2',
        description: cablingData?['description'] ?? cable15Data['description'] ?? 'Fire Alarm Cable 2x1.5mm² - Installation',
        quantity: loopCableLength.ceil(),
        unitCost: cable15Cost,
        totalCost: cable15Cost * loopCableLength.ceil(),
        manufacturer: 'Generic',
        category: 'Installation Items',
        subcategory: 'Cabling',
        currency: 'SAR', // Force SAR currency
      ));
    }

    final cable25Data = installData['cable2x2.5'];
    if (cable25Data != null) {
      final cable25Cost = cable25Data['cost'] ?? 0;
      print('Adding power cable: ${powerCableLength.ceil()}m × SAR ${cable25Cost.toStringAsFixed(2)} = SAR ${(cable25Cost * powerCableLength.ceil()).toStringAsFixed(2)}');
      bom.add(BomItem(
        partNo: 'CABLE_2X2.5MM2',
        description: cablingData?['description'] ?? cable25Data['description'] ?? 'Fire Alarm Cable 2x2.5mm² - Installation',
        quantity: powerCableLength.ceil(),
        unitCost: cable25Cost,
        totalCost: cable25Cost * powerCableLength.ceil(),
        manufacturer: 'Generic',
        category: 'Installation Items',
        subcategory: 'Cabling',
        currency: 'SAR', // Force SAR currency
      ));
    } else {
      print('WARNING: 2x2.5mm² cable data not found in Install table');
    }

    // Add cylinder installation cost per cylinder
    final cylinderInstallData = installData['cylinderInstall'];
    if (cylinderInstallData != null) {
      bom.add(BomItem(
        partNo: 'CYL_INSTALL',
        description: cylinderInstallData['description'] ?? 'Cylinder Installation Service',
        quantity: cylinderQty,
        unitCost: cylinderInstallData['cost'] ?? 0,
        totalCost: (cylinderInstallData['cost'] ?? 0) * cylinderQty,
        manufacturer: 'Service',
        category: 'Installation Services',
        subcategory: 'Cylinder Installation',
        currency: 'SAR',
      ));
    }
  }



  /// Calculate summary costs with proper installation logic from database
  static Future<BomSummary> _calculateSummary(List<BomItem> bom, DesignResults design, EstimatorFormValues input, {double? installationFactor}) async {
    await initialize();

    double suppressionCostUSD = 0;
    double alarmCostUSD = 0;
    double installationItemsCostSAR = 0; // Installation items are in SAR
    double suppressionInstallCost = 0;
    double alarmInstallCost = 0;
    double installationServicesInstallCost = 0;

    // Calculate costs by category and currency
    for (final item in bom) {
      switch (item.category) {
        case 'Suppression System':
        case 'Extinguishing System (Hygood - UL/FM – UK)':
          if (item.currency == 'USD') {
            suppressionCostUSD += item.totalCost;
          } else if (item.currency == 'SAR') {
            // This shouldn't happen for suppression system, but handle it
            print('WARNING: Suppression item ${item.partNo} is in SAR, should be USD');
          }
          break;
        case 'Alarm & Detection':
        case 'Fire Alarm (Notifier - UL/FM)':
          if (item.currency == 'USD') {
            alarmCostUSD += item.totalCost;
          } else if (item.currency == 'SAR') {
            // This shouldn't happen for alarm system, but handle it
            print('WARNING: Alarm item ${item.partNo} is in SAR, should be USD');
          }
          break;
        case 'Installation Items':
          if (item.currency == 'SAR') {
            installationItemsCostSAR += item.totalCost;
          } else if (item.currency == 'USD') {
            print('WARNING: Installation item ${item.partNo} is in USD, should be SAR');
            // Convert to SAR for installation items
            installationItemsCostSAR += item.totalCost * AppConfig.dollarRateSarUsd;
          }
          break;
        case 'Suppression Installation':
          suppressionInstallCost += item.totalCost;
          break;
        case 'Alarm Installation':
          alarmInstallCost += item.totalCost;
          break;
        case 'Installation Services':
          installationServicesInstallCost += item.totalCost;
          break;
      }
    }

    // Apply shipping factor to USD items (not installation factor - that's for labor)
    const shippingFactor = AppConfig.shippingExFactor;
    suppressionCostUSD *= shippingFactor;
    alarmCostUSD *= shippingFactor;
    // Installation items in local currency don't get shipping factor

    // Calculate totals - keep USD and SAR separate (TODO: Support other currencies)
    final totalSupplyCostUSD = suppressionCostUSD + alarmCostUSD;
    final totalSupplyCostSAR = (totalSupplyCostUSD * AppConfig.dollarRateSarUsd) + installationItemsCostSAR;

    // Calculate installation costs based on supply costs (if supply & install)
    double calculatedSuppressionInstallCost = 0;
    double calculatedAlarmInstallCost = 0;

    if (input.installationType == InstallationType.supplyAndInstall) {
      // Use unified installation factor for labor cost calculation
      final unifiedInstallationFactor = installationFactor ?? 0.15; // Default 15%

      // Apply installation factor to total system cost (suppression + alarm) for labor
      final totalSystemCostSAR = (suppressionCostUSD + alarmCostUSD) * AppConfig.dollarRateSarUsd;
      final totalLaborCostSAR = totalSystemCostSAR * unifiedInstallationFactor;

      // Split labor cost proportionally between suppression and alarm
      final totalSystemCostUSD = suppressionCostUSD + alarmCostUSD;
      if (totalSystemCostUSD > 0) {
        final suppressionRatio = suppressionCostUSD / totalSystemCostUSD;
        final alarmRatio = alarmCostUSD / totalSystemCostUSD;

        calculatedSuppressionInstallCost = totalLaborCostSAR * suppressionRatio;
        calculatedAlarmInstallCost = totalLaborCostSAR * alarmRatio;
      }

      print('=== INSTALLATION COST CALCULATION ===');
      print('Suppression Cost USD: ${suppressionCostUSD.toStringAsFixed(2)}');
      print('Alarm Cost USD: ${alarmCostUSD.toStringAsFixed(2)}');
      print('Total System Cost SAR: ${totalSystemCostSAR.toStringAsFixed(2)}');
      print('Installation Factor: ${(unifiedInstallationFactor * 100).toStringAsFixed(1)}%');
      print('Total Labor Cost SAR: ${totalLaborCostSAR.toStringAsFixed(2)}');
      print('Calculated Suppression Install Cost SAR: ${calculatedSuppressionInstallCost.toStringAsFixed(2)}');
      print('Calculated Alarm Install Cost SAR: ${calculatedAlarmInstallCost.toStringAsFixed(2)}');
    }

    // Use the higher of calculated vs actual installation costs
    final finalSuppressionInstallCost = suppressionInstallCost > 0 ? suppressionInstallCost : calculatedSuppressionInstallCost;
    final finalAlarmInstallCost = alarmInstallCost > 0 ? alarmInstallCost : calculatedAlarmInstallCost;

    final totalInstallCostSAR = finalSuppressionInstallCost + finalAlarmInstallCost + installationServicesInstallCost;
    final grandTotalSAR = totalSupplyCostSAR + totalInstallCostSAR;

    // Apply margin
    const marginFactor = AppConfig.defaultMarginFactor;
    final marginAmountSAR = grandTotalSAR * (marginFactor - 1);

    // Calculate the new structure costs
    final exWorksItemsUSD = totalSupplyCostUSD;
    final landedCostSAR = totalSupplyCostUSD * AppConfig.dollarRateSarUsd;
    final installationMaterialsSAR = installationItemsCostSAR;
    final installationLaborSAR = totalInstallCostSAR;

    print('=== IMPROVED COST SUMMARY ===');
    print('Suppression System: \$${suppressionCostUSD.toStringAsFixed(2)}');
    print('Alarm & Detection: \$${alarmCostUSD.toStringAsFixed(2)}');
    print('Ex-works Total: \$${exWorksItemsUSD.toStringAsFixed(2)}');
    print('Landed Cost: SAR ${landedCostSAR.toStringAsFixed(2)}');
    print('Installation Materials: SAR ${installationMaterialsSAR.toStringAsFixed(2)}');
    print('Installation Labor Cost: SAR ${installationLaborSAR.toStringAsFixed(2)}');
    print('Total: SAR ${grandTotalSAR.toStringAsFixed(2)}');

    return BomSummary(
      // New structure
      exWorksItemsUSD: exWorksItemsUSD,
      landedCostSAR: landedCostSAR,
      installationMaterialsSAR: installationMaterialsSAR,
      installationLaborSAR: installationLaborSAR,
      // Legacy fields for backward compatibility
      suppressionCost: suppressionCostUSD,
      alarmCost: alarmCostUSD,
      installationItemsCost: installationItemsCostSAR,
      suppressionInstallCost: finalSuppressionInstallCost,
      alarmInstallCost: finalAlarmInstallCost,
      installationServicesInstallCost: installationServicesInstallCost,
      totalSupplyCostUSD: totalSupplyCostUSD,
      totalSupplyCostSAR: totalSupplyCostSAR,
      totalInstallCostSAR: totalInstallCostSAR,
      grandTotalSAR: grandTotalSAR + marginAmountSAR,
      marginFactor: marginFactor,
      marginAmountSAR: marginAmountSAR,
    );
  }

  /// Calculate pipe lengths for installation using correct React app formulas
  static Map<String, double> calculatePipeLengths(double roomLength, double roomWidth, double roomHeight, int nozzleQty, int cylinderQty) {
    // User-provided formulas:
    // Nozzle pipes = L × (nozzle qty)
    // Manifold = (H-1.3) + 0.5×(no of cyl) + W
    final nozzlePipeLength = roomLength * nozzleQty;
    final manifoldPipeLength = (roomHeight - 1.3) + (0.5 * cylinderQty) + roomWidth;

    print('=== PIPE LENGTH CALCULATION ===');
    print('Room: ${roomLength.toStringAsFixed(1)}L × ${roomWidth.toStringAsFixed(1)}W × ${roomHeight.toStringAsFixed(1)}H');
    print('Nozzles: $nozzleQty, Cylinders: $cylinderQty');
    print('Nozzle pipe formula: ${roomLength.toStringAsFixed(1)} × $nozzleQty = ${nozzlePipeLength.toStringAsFixed(1)}m');
    print('Manifold pipe formula: (${roomHeight.toStringAsFixed(1)} - 1.3) + 0.5×$cylinderQty + ${roomWidth.toStringAsFixed(1)} = ${manifoldPipeLength.toStringAsFixed(1)}m');

    return {
      'manifoldPipeLength': manifoldPipeLength,
      'nozzlePipeLength': nozzlePipeLength,
    };
  }

  /// Calculate cable lengths for installation using correct React app formulas
  static Map<String, double> calculateCableLengths(double roomLength, double roomWidth, double roomHeight, int cylinderQty) {
    // User-provided formulas:
    // Cables 2x1.5mm² = ((H-1.5)+L+W)×2+6+4+4+4
    // Cables 2x2.5mm² = 5+Cyl qty×2+3+3+5
    final loopCableLength = ((roomHeight - 1.5) + roomLength + roomWidth) * 2 + 6 + 4 + 4 + 4;
    final powerCableLength = 5.0 + (cylinderQty * 2.0) + 3 + 3 + 5;

    print('=== CABLE LENGTH CALCULATION ===');
    print('Room: ${roomLength.toStringAsFixed(1)}L × ${roomWidth.toStringAsFixed(1)}W × ${roomHeight.toStringAsFixed(1)}H');
    print('Cylinders: $cylinderQty');
    print('Loop cable formula (2x1.5mm²): ((${roomHeight.toStringAsFixed(1)} - 1.5) + ${roomLength.toStringAsFixed(1)} + ${roomWidth.toStringAsFixed(1)}) × 2 + 6 + 4 + 4 + 4 = ${loopCableLength.toStringAsFixed(1)}m');
    print('Power cable formula (2x2.5mm²): 5 + $cylinderQty × 2 + 3 + 3 + 5 = ${powerCableLength.toStringAsFixed(1)}m');

    return {
      'loopCableLength': loopCableLength,
      'powerCableLength': powerCableLength,
    };
  }

  /// Find manifold assembly in NOVEC/FM200 Items tables
  static Future<Component?> _findManifoldInItemsTable(String tableName, int manifoldSize) async {
    await initialize();
    return await _dynamicService!.findManifoldAssembly(tableName, manifoldSize);
  }
}
