import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:uuid/uuid.dart';
import 'dart:convert';
import '../models/estimator_types.dart';
import '../utils/estimator_calculations.dart';
import '../utils/bom_generator.dart';

class EstimatorProvider extends ChangeNotifier {
  static const String _systemsKey = 'clean-agent-systems';
  
  QuotedSystem? _currentSystem;
  bool _calculationPerformed = false;
  List<QuotedSystem> _systems = [];
  bool _isLoading = false;
  String? _errorMessage;

  // Getters
  QuotedSystem? get currentSystem => _currentSystem;
  bool get calculationPerformed => _calculationPerformed;
  List<QuotedSystem> get systems => List.from(_systems);
  bool get isLoading => _isLoading;
  String? get errorMessage => _errorMessage;

  EstimatorProvider() {
    _loadSystems();
  }

  Future<void> _loadSystems() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final systemsJson = prefs.getString(_systemsKey);
      
      if (systemsJson != null) {
        final systemsList = json.decode(systemsJson) as List;
        _systems = systemsList.map((item) => QuotedSystem.fromJson(item)).toList();
        notifyListeners();
      }
    } catch (e) {
      print('Error loading systems: $e');
      _errorMessage = 'Failed to load saved systems';
      notifyListeners();
    }
  }

  Future<void> _saveSystems() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final systemsList = _systems.map((system) => system.toJson()).toList();
      await prefs.setString(_systemsKey, json.encode(systemsList));
    } catch (e) {
      print('Error saving systems: $e');
      _errorMessage = 'Failed to save systems';
      notifyListeners();
    }
  }

  // Handle form submission for calculations
  Future<void> calculateSystem(EstimatorFormValues values) async {
    _isLoading = true;
    _errorMessage = null;
    notifyListeners();

    try {
      // Calculate design based on input values
      final designResults = EstimatorCalculations.calculateDesign(values);
      
      // Generate bill of materials
      final bomData = BomGenerator.generateBOM(designResults, values);
      
      // Create a new system with calculated results
      final newSystem = QuotedSystem(
        id: const Uuid().v4(),
        name: 'New System (${values.agentType.name.toUpperCase()} ${designResults.cylinder.actualTotalKg.toStringAsFixed(0)} kg)',
        date: DateTime.now().toIso8601String(),
        inputData: values,
        designResults: designResults,
        bom: bomData['bom'] as List<BomItem>,
        summary: bomData['summary'] as BomSummary,
      );
      
      // Update current system with results
      _currentSystem = newSystem;
      _calculationPerformed = true;
      _isLoading = false;
      notifyListeners();
    } catch (error) {
      _isLoading = false;
      _errorMessage = 'An error occurred during calculation: $error';
      print('Calculation error: $error');
      notifyListeners();
    }
  }

  // Handle adding the current system to the collection
  void addCurrentSystemToCollection() {
    if (_currentSystem == null) {
      _errorMessage = 'No system available to add';
      notifyListeners();
      return;
    }
    
    // Check if system already exists
    final exists = _systems.any((sys) => sys.id == _currentSystem!.id);
    if (!exists) {
      _systems.add(_currentSystem!);
      _saveSystems();
      _errorMessage = null;
      notifyListeners();
    } else {
      _errorMessage = 'System already added';
      notifyListeners();
    }
  }

  // Handle creating a new quote
  void createNewQuote() {
    _currentSystem = null;
    _calculationPerformed = false;
    _errorMessage = null;
    notifyListeners();
  }

  // Handle selecting a saved quote
  void selectQuote(QuotedSystem quote) {
    _currentSystem = quote;
    _calculationPerformed = true;
    _errorMessage = null;
    notifyListeners();
  }

  // Remove a system from the collection
  void removeSystem(String systemId) {
    _systems.removeWhere((system) => system.id == systemId);
    _saveSystems();
    notifyListeners();
  }

  // Update system name
  void updateSystemName(String systemId, String newName) {
    final systemIndex = _systems.indexWhere((system) => system.id == systemId);
    if (systemIndex != -1) {
      final updatedSystem = QuotedSystem(
        id: _systems[systemIndex].id,
        name: newName,
        date: _systems[systemIndex].date,
        inputData: _systems[systemIndex].inputData,
        designResults: _systems[systemIndex].designResults,
        bom: _systems[systemIndex].bom,
        summary: _systems[systemIndex].summary,
      );
      
      _systems[systemIndex] = updatedSystem;
      
      // Update current system if it's the same one
      if (_currentSystem?.id == systemId) {
        _currentSystem = updatedSystem;
      }
      
      _saveSystems();
      notifyListeners();
    }
  }

  // Clear all systems
  void clearAllSystems() {
    _systems.clear();
    _saveSystems();
    notifyListeners();
  }

  // Clear error message
  void clearError() {
    _errorMessage = null;
    notifyListeners();
  }

  // Get systems by agent type
  List<QuotedSystem> getSystemsByAgentType(AgentType agentType) {
    return _systems.where((system) => system.inputData.agentType == agentType).toList();
  }

  // Get systems by system type
  List<QuotedSystem> getSystemsBySystemType(SystemType systemType) {
    return _systems.where((system) => system.inputData.systemType == systemType).toList();
  }

  // Get total cost of all systems
  double getTotalCostOfAllSystems() {
    return _systems.fold(0.0, (sum, system) => sum + system.summary.grandTotalSAR);
  }

  // Export data for all systems
  Map<String, dynamic> exportAllSystemsData() {
    return {
      'systems': _systems.map((system) => system.toJson()).toList(),
      'exportDate': DateTime.now().toIso8601String(),
      'totalSystems': _systems.length,
      'totalCost': getTotalCostOfAllSystems(),
    };
  }
}
