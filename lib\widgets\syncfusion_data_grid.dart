import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:syncfusion_flutter_datagrid/datagrid.dart';
import '../models/isar_models.dart';
import '../services/dynamic_schema_service.dart';
import '../services/data_import_export_service.dart';
import '../services/supabase_sync_service.dart';
import '../services/undo_redo_service.dart';
import '../config/supabase_config.dart';
import 'column_management_dialog.dart';
import 'import_preview_dialog.dart';

// Intent classes for keyboard shortcuts
class UndoIntent extends Intent {
  const UndoIntent();
}

class RedoIntent extends Intent {
  const RedoIntent();
}

class SyncfusionDataGrid extends StatefulWidget {
  final FlexibleTable table;
  final Color? themeColor;

  const SyncfusionDataGrid({
    super.key,
    required this.table,
    this.themeColor,
  });

  @override
  State<SyncfusionDataGrid> createState() => _SyncfusionDataGridState();
}

class _SyncfusionDataGridState extends State<SyncfusionDataGrid> {
  final DynamicSchemaService _schemaService = DynamicSchemaService.instance;
  final DataImportExportService _importExportService = DataImportExportService.instance;
  final UndoRedoService _undoRedoService = UndoRedoService.instance;
  final DataGridController _dataGridController = DataGridController();
  late final SupabaseSyncService _supabaseSyncService;

  List<FlexibleColumn> _columns = [];
  List<FlexibleRow> _rows = [];
  late TableDataSource _dataSource;
  bool _isLoading = true;
  String? _error;
  double _rowHeight = 48.0;
  final Map<String, double> _columnWidths = {};
  List<DataGridRow> _selectedRows = [];
  bool _autoResizeColumns = true; // Default to auto-resize
  bool _isCardView = false; // Toggle between card and table view
  RowColumnIndex? _focusedCell; // Track the currently focused cell for highlighting
  late CustomSelectionManager _selectionManager;


  @override
  void initState() {
    super.initState();
    _selectionManager = CustomSelectionManager();
    _supabaseSyncService = SupabaseSyncService(_schemaService);
    _dataSource = TableDataSource([], [], _schemaService, widget.table.tableId!, [], _undoRedoService);
    _loadData();
  }

  void _autoResizeAllColumns() {
    setState(() {
      _autoResizeColumns = true;
      _columnWidths.clear(); // Clear manual widths to allow auto-sizing

      // Recalculate optimal widths for all columns
      for (final column in _columns) {
        if (column.columnId != null) {
          final optimalWidth = _calculateOptimalColumnWidth(column, isMobile: false);
          _columnWidths[column.columnId!] = optimalWidth;
        }
      }

      // Force rebuild of grid columns
      _dataSource = TableDataSource(_buildDataRows(), _columns, _schemaService, widget.table.tableId!, _rows, _undoRedoService);
    });
  }

  double _calculateOptimalColumnWidth(FlexibleColumn column, {bool isMobile = false}) {
    // Calculate header text width
    final headerText = column.name ?? 'Column';
    final headerWidth = _calculateTextWidth(headerText, TextStyle(
      fontFamily: 'Inter',
      fontWeight: FontWeight.w600,
      fontSize: isMobile ? 12 : 14,
    )) + (isMobile ? 60 : 80); // Less padding on mobile

    // Calculate content width by sampling data
    double maxContentWidth = isMobile ? 60.0 : 80.0; // Smaller minimum width on mobile

    if (_rows.isNotEmpty) {
      // Sample up to 10 rows to determine content width
      final sampleSize = _rows.length > 10 ? 10 : _rows.length;
      for (int i = 0; i < sampleSize; i++) {
        final row = _rows[i];
        final rowData = row.data != null ? jsonDecode(row.data!) as Map<String, dynamic> : <String, dynamic>{};
        final cellValue = rowData[column.columnId] ?? '';

        double contentWidth = _calculateTextWidth(cellValue.toString(), TextStyle(
          fontFamily: 'Inter',
          fontSize: isMobile ? 12 : 14,
        )) + (isMobile ? 16 : 24); // Less padding on mobile

        if (contentWidth > maxContentWidth) {
          maxContentWidth = contentWidth;
        }
      }
    }

    // Return the larger of header width or content width, with reasonable limits
    final optimalWidth = headerWidth > maxContentWidth ? headerWidth : maxContentWidth;

    if (isMobile) {
      // More aggressive limits for mobile
      return optimalWidth.clamp(80.0, 200.0); // Min 80px, max 200px
    } else {
      return optimalWidth.clamp(100.0, 300.0); // Min 100px, Max 300px
    }
  }

  double _calculateTextWidth(String text, TextStyle style) {
    final textPainter = TextPainter(
      text: TextSpan(text: text, style: style),
      textDirection: TextDirection.ltr,
    );
    textPainter.layout();
    return textPainter.width;
  }

  @override
  void didUpdateWidget(SyncfusionDataGrid oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.table.tableId != widget.table.tableId) {
      _loadData();
    }
  }

  Future<void> _loadData() async {
    if (mounted) {
      setState(() {
        _isLoading = true;
        _error = null;
      });
    }

    try {
      _columns = await _schemaService.getColumnsForTable(widget.table.tableId!);
      _rows = await _schemaService.getRowsForTable(widget.table.tableId!);

      _dataSource = TableDataSource(_buildDataRows(), _columns, _schemaService, widget.table.tableId!, _rows, _undoRedoService);

      // Auto-resize columns on initial load
      if (_autoResizeColumns) {
        _columnWidths.clear();
        for (final column in _columns) {
          if (column.columnId != null) {
            final optimalWidth = _calculateOptimalColumnWidth(column, isMobile: false);
            _columnWidths[column.columnId!] = optimalWidth;
          }
        }
      }

      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _error = e.toString();
          _isLoading = false;
        });
      }
    }
  }

  List<DataGridRow> _buildDataRows() {
    return _rows.map((row) {
      final rowData = row.data != null ? jsonDecode(row.data!) as Map<String, dynamic> : <String, dynamic>{};
      
      return DataGridRow(
        cells: _columns.map((column) {
          final value = rowData[column.columnId] ?? '';
          return DataGridCell<String>(
            columnName: column.columnId!,
            value: value.toString(),
          );
        }).toList(),
      );
    }).toList();
  }

  List<GridColumn> _buildGridColumns({bool isMobile = false}) {
    return _columns.map((column) {
      // Use stored width or calculate optimal width
      double columnWidth;
      if (_columnWidths.containsKey(column.columnId!)) {
        columnWidth = _columnWidths[column.columnId!]!;
      } else {
        columnWidth = _calculateOptimalColumnWidth(column, isMobile: isMobile);
        if (_autoResizeColumns) {
          _columnWidths[column.columnId!] = columnWidth;
        }
      }

      return GridColumn(
        columnName: column.columnId!,
        width: columnWidth,
        allowSorting: true,
        allowFiltering: false,
        label: Container(
          padding: EdgeInsets.symmetric(
            horizontal: isMobile ? 8 : 12,
            vertical: isMobile ? 6 : 8
          ),
          alignment: Alignment.centerLeft,
          decoration: BoxDecoration(
            color: widget.themeColor?.withValues(alpha: 0.1) ?? Colors.blue.withValues(alpha: 0.1),
            border: Border(
              right: BorderSide(
                color: widget.themeColor?.withValues(alpha: 0.2) ?? Colors.blue.withValues(alpha: 0.2),
                width: 1,
              ),
            ),
          ),
          child: Row(
            children: [
              if (!isMobile) _buildColumnTypeIcon(column.dataType),
              if (!isMobile) const SizedBox(width: 8),
              Expanded(
                child: Text(
                  column.name ?? 'Column',
                  style: TextStyle(
                    fontFamily: 'Inter',
                    fontWeight: FontWeight.w600,
                    fontSize: isMobile ? 12 : 14,
                    color: widget.themeColor ?? Colors.blue.shade700,
                  ),
                  overflow: TextOverflow.visible,
                  softWrap: true,
                  maxLines: 2,
                ),
              ),
              if (column.isRequired == true)
                Container(
                  margin: const EdgeInsets.only(left: 4),
                  child: Icon(
                    Icons.star,
                    size: 12,
                    color: Colors.red.shade600,
                  ),
                ),
              PopupMenuButton<String>(
                icon: Icon(
                  Icons.more_vert,
                  size: 16,
                  color: widget.themeColor ?? Colors.blue.shade700,
                ),
                itemBuilder: (context) => [
                  const PopupMenuItem(
                    value: 'filter',
                    child: Row(
                      children: [
                        Icon(Icons.filter_list, size: 16, color: Colors.blue),
                        SizedBox(width: 8),
                        Text('Filter'),
                      ],
                    ),
                  ),
                  const PopupMenuItem(
                    value: 'sort_asc',
                    child: Row(
                      children: [
                        Icon(Icons.arrow_upward, size: 16, color: Colors.green),
                        SizedBox(width: 8),
                        Text('Sort Ascending'),
                      ],
                    ),
                  ),
                  const PopupMenuItem(
                    value: 'sort_desc',
                    child: Row(
                      children: [
                        Icon(Icons.arrow_downward, size: 16, color: Colors.orange),
                        SizedBox(width: 8),
                        Text('Sort Descending'),
                      ],
                    ),
                  ),
                  const PopupMenuDivider(),
                  const PopupMenuItem(
                    value: 'edit',
                    child: Row(
                      children: [
                        Icon(Icons.edit, size: 16),
                        SizedBox(width: 8),
                        Text('Edit Column'),
                      ],
                    ),
                  ),
                  const PopupMenuItem(
                    value: 'delete',
                    child: Row(
                      children: [
                        Icon(Icons.delete, size: 16, color: Colors.red),
                        SizedBox(width: 8),
                        Text('Delete Column', style: TextStyle(color: Colors.red)),
                      ],
                    ),
                  ),
                ],
                onSelected: (value) => _handleColumnMenuAction(value, column),
              ),
            ],
          ),
        ),
      );
    }).toList();
  }

  Widget _buildColumnTypeIcon(ColumnDataType dataType) {
    IconData iconData;
    Color iconColor = Colors.grey.shade600;
    
    switch (dataType) {
      case ColumnDataType.text:
        iconData = Icons.text_fields;
        break;
      case ColumnDataType.number:
        iconData = Icons.numbers;
        iconColor = Colors.blue;
        break;
      case ColumnDataType.currency:
        iconData = Icons.attach_money;
        iconColor = Colors.green;
        break;
      case ColumnDataType.date:
        iconData = Icons.calendar_today;
        iconColor = Colors.orange;
        break;
      case ColumnDataType.boolean:
        iconData = Icons.check_box;
        iconColor = Colors.purple;
        break;
      case ColumnDataType.dropdown:
        iconData = Icons.arrow_drop_down;
        iconColor = Colors.teal;
        break;
    }
    
    return Icon(
      iconData,
      size: 16,
      color: iconColor,
    );
  }

  void _showColumnHeaderMenu(DataGridCellTapDetails details) {
    final columnIndex = details.rowColumnIndex.columnIndex;

    // Skip if this is the checkbox column (first column)
    if (columnIndex == 0) return;

    // Adjust for checkbox column offset
    final adjustedColumnIndex = columnIndex - 1;
    if (adjustedColumnIndex >= _columns.length || adjustedColumnIndex < 0) return;

    final column = _columns[adjustedColumnIndex];

    showMenu(
      context: context,
      position: RelativeRect.fromLTRB(
        details.globalPosition.dx,
        details.globalPosition.dy,
        details.globalPosition.dx + 200,
        details.globalPosition.dy + 300,
      ),
      items: <PopupMenuEntry<String>>[
        PopupMenuItem<String>(
          value: 'filter',
          child: Row(
            children: [
              Icon(Icons.filter_list, size: 16, color: Colors.blue.shade600),
              const SizedBox(width: 8),
              const Text('Filter'),
            ],
          ),
        ),
        PopupMenuItem<String>(
          value: 'sort_asc',
          child: Row(
            children: [
              Icon(Icons.arrow_upward, size: 16, color: Colors.green.shade600),
              const SizedBox(width: 8),
              const Text('Sort Ascending'),
            ],
          ),
        ),
        PopupMenuItem<String>(
          value: 'sort_desc',
          child: Row(
            children: [
              Icon(Icons.arrow_downward, size: 16, color: Colors.orange.shade600),
              const SizedBox(width: 8),
              const Text('Sort Descending'),
            ],
          ),
        ),
        const PopupMenuDivider(),
        PopupMenuItem<String>(
          value: 'edit',
          child: Row(
            children: [
              Icon(Icons.edit, size: 16, color: Colors.blue.shade600),
              const SizedBox(width: 8),
              const Text('Edit Column'),
            ],
          ),
        ),
        PopupMenuItem<String>(
          value: 'delete',
          child: Row(
            children: [
              Icon(Icons.delete, size: 16, color: Colors.red.shade600),
              const SizedBox(width: 8),
              const Text('Delete Column'),
            ],
          ),
        ),
      ],
    ).then((value) {
      if (value != null) {
        _handleColumnMenuAction(value, column);
      }
    });
  }

  void _handleColumnMenuAction(String action, FlexibleColumn column) {
    switch (action) {
      case 'filter':
        _showColumnFilter(column);
        break;
      case 'sort_asc':
        _sortColumn(column, true);
        break;
      case 'sort_desc':
        _sortColumn(column, false);
        break;
      case 'edit':
        _editSpecificColumn(column);
        break;
      case 'delete':
        _deleteColumn(column);
        break;
    }
  }

  void _showColumnFilter(FlexibleColumn column) {
    // Get unique values from this column
    final uniqueValues = <String>{};
    for (final row in _rows) {
      if (row.data != null) {
        final rowData = jsonDecode(row.data!) as Map<String, dynamic>;
        final value = rowData[column.columnId]?.toString() ?? '';
        if (value.isNotEmpty) {
          uniqueValues.add(value);
        }
      }
    }

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('Filter ${column.name}'),
        content: SizedBox(
          width: 300,
          height: 400,
          child: Column(
            children: [
              TextField(
                decoration: const InputDecoration(
                  labelText: 'Search values...',
                  prefixIcon: Icon(Icons.search),
                ),
                onChanged: (value) {
                  // TODO: Implement search filtering
                },
              ),
              const SizedBox(height: 16),
              Expanded(
                child: ListView(
                  children: uniqueValues.map((value) {
                    return CheckboxListTile(
                      title: Text(value),
                      value: true, // TODO: Track selected filters
                      onChanged: (selected) {
                        // TODO: Implement filter selection
                      },
                    );
                  }).toList(),
                ),
              ),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              // TODO: Apply filters
              Navigator.of(context).pop();
            },
            child: const Text('Apply Filter'),
          ),
        ],
      ),
    );
  }

  void _sortColumn(FlexibleColumn column, bool ascending) {
    setState(() {
      _rows.sort((a, b) {
        final aData = a.data != null ? jsonDecode(a.data!) as Map<String, dynamic> : <String, dynamic>{};
        final bData = b.data != null ? jsonDecode(b.data!) as Map<String, dynamic> : <String, dynamic>{};

        final aValue = aData[column.columnId]?.toString() ?? '';
        final bValue = bData[column.columnId]?.toString() ?? '';

        int comparison;

        // Try to parse as numbers for numeric sorting
        final aNum = double.tryParse(aValue);
        final bNum = double.tryParse(bValue);

        if (aNum != null && bNum != null) {
          comparison = aNum.compareTo(bNum);
        } else {
          comparison = aValue.compareTo(bValue);
        }

        return ascending ? comparison : -comparison;
      });

      _dataSource = TableDataSource(_buildDataRows(), _columns, _schemaService, widget.table.tableId!, _rows, _undoRedoService);
    });

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Sorted ${column.name} ${ascending ? 'ascending' : 'descending'}'),
        backgroundColor: Colors.green,
      ),
    );
  }

  void _handleColumnAction(String action, FlexibleColumn column) {
    switch (action) {
      case 'edit':
        _editSpecificColumn(column);
        break;
      case 'delete':
        _deleteColumn(column);
        break;
    }
  }

  void _editColumn(FlexibleColumn column) {
    showDialog(
      context: context,
      builder: (context) => ColumnManagementDialog(
        table: widget.table,
        onColumnsChanged: _loadData,
      ),
    );
  }

  void _editSpecificColumn(FlexibleColumn column) {
    final nameController = TextEditingController(text: column.name);
    final defaultValueController = TextEditingController(text: column.defaultValue);
    ColumnDataType selectedDataType = column.dataType;
    CurrencyType selectedCurrencyType = column.currencyType;
    bool isRequired = column.isRequired ?? false;

    showDialog(
      context: context,
      builder: (context) => StatefulBuilder(
        builder: (context, setState) => AlertDialog(
          title: Row(
            children: [
              Icon(
                Icons.edit,
                color: widget.themeColor ?? Colors.blue,
                size: 20,
              ),
              const SizedBox(width: 8),
              Text(
                'Edit Column: ${column.name}',
                style: const TextStyle(
                  fontFamily: 'Inter',
                  fontSize: 18,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),
          content: SizedBox(
            width: 400,
            child: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                TextField(
                  controller: nameController,
                  decoration: const InputDecoration(
                    labelText: 'Column Name',
                    border: OutlineInputBorder(),
                  ),
                ),
                const SizedBox(height: 16),
                DropdownButtonFormField<ColumnDataType>(
                  value: selectedDataType,
                  decoration: const InputDecoration(
                    labelText: 'Data Type',
                    border: OutlineInputBorder(),
                  ),
                  items: ColumnDataType.values.map((type) {
                    return DropdownMenuItem(
                      value: type,
                      child: Text(_getDataTypeDisplayName(type)),
                    );
                  }).toList(),
                  onChanged: (value) {
                    if (value != null) {
                      setState(() {
                        selectedDataType = value;
                      });
                    }
                  },
                ),
                const SizedBox(height: 16),
                // Currency type dropdown (only show for currency data type)
                if (selectedDataType == ColumnDataType.currency) ...[
                  DropdownButtonFormField<CurrencyType>(
                    value: selectedCurrencyType,
                    decoration: const InputDecoration(
                      labelText: 'Currency Type',
                      border: OutlineInputBorder(),
                    ),
                    items: CurrencyType.values.map((type) {
                      return DropdownMenuItem(
                        value: type,
                        child: Row(
                          children: [
                            Text(type.symbol),
                            const SizedBox(width: 8),
                            Text('${type.code} - ${type.name}'),
                          ],
                        ),
                      );
                    }).toList(),
                    onChanged: (value) {
                      if (value != null) {
                        setState(() {
                          selectedCurrencyType = value;
                        });
                      }
                    },
                  ),
                  const SizedBox(height: 16),
                ],
                TextField(
                  controller: defaultValueController,
                  decoration: const InputDecoration(
                    labelText: 'Default Value (Optional)',
                    border: OutlineInputBorder(),
                  ),
                ),
                const SizedBox(height: 16),
                CheckboxListTile(
                  title: const Text('Required Field'),
                  subtitle: const Text('Users must provide a value for this column'),
                  value: isRequired,
                  onChanged: (value) {
                    setState(() {
                      isRequired = value ?? false;
                    });
                  },
                  contentPadding: EdgeInsets.zero,
                ),
              ],
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('Cancel'),
            ),
            ElevatedButton(
              onPressed: () async {
                if (nameController.text.trim().isNotEmpty) {
                  try {
                    // Update the column using the schema service
                    await _schemaService.updateColumn(
                      column.columnId!,
                      name: nameController.text.trim(),
                      dataType: selectedDataType,
                      currencyType: selectedCurrencyType,
                      isRequired: isRequired,
                      defaultValue: defaultValueController.text.trim().isEmpty
                          ? null
                          : defaultValueController.text.trim(),
                    );

                    if (mounted) {
                      Navigator.of(context).pop();
                      _loadData(); // Reload the data to reflect changes

                      ScaffoldMessenger.of(context).showSnackBar(
                        SnackBar(
                          content: Text('Column "${nameController.text.trim()}" updated successfully'),
                          backgroundColor: Colors.green,
                        ),
                      );
                    }
                  } catch (e) {
                    if (mounted) {
                      ScaffoldMessenger.of(context).showSnackBar(
                        SnackBar(
                          content: Text('Error updating column: $e'),
                          backgroundColor: Colors.red,
                        ),
                      );
                    }
                  }
                }
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: widget.themeColor ?? Colors.blue,
                foregroundColor: Colors.white,
              ),
              child: const Text('Update Column'),
            ),
          ],
        ),
      ),
    );
  }

  String _getDataTypeDisplayName(ColumnDataType dataType) {
    switch (dataType) {
      case ColumnDataType.text:
        return 'Text';
      case ColumnDataType.number:
        return 'Number';
      case ColumnDataType.currency:
        return 'Currency';
      case ColumnDataType.date:
        return 'Date';
      case ColumnDataType.boolean:
        return 'Yes/No';
      case ColumnDataType.dropdown:
        return 'Dropdown';
    }
  }

  void _showCellEditDialog(RowColumnIndex rowColumnIndex) {
    debugPrint('_showCellEditDialog called: row=${rowColumnIndex.rowIndex}, col=${rowColumnIndex.columnIndex}');
    final rowIndex = rowColumnIndex.rowIndex - 1; // Subtract 1 for header
    final columnIndex = rowColumnIndex.columnIndex;

    // Adjust column index if checkbox column is present
    final adjustedColumnIndex = columnIndex > 0 ? columnIndex - 1 : columnIndex;

    if (rowIndex < 0 || rowIndex >= _rows.length || adjustedColumnIndex >= _columns.length || adjustedColumnIndex < 0) {
      debugPrint('Invalid indices: rowIndex=$rowIndex, columnIndex=$columnIndex, adjustedColumnIndex=$adjustedColumnIndex, _rows.length=${_rows.length}, _columns.length=${_columns.length}');
      return;
    }

    final row = _rows[rowIndex];
    final column = _columns[adjustedColumnIndex];
    final rowData = row.data != null ? jsonDecode(row.data!) as Map<String, dynamic> : <String, dynamic>{};
    final currentValue = rowData[column.columnId] ?? '';

    final controller = TextEditingController(text: currentValue.toString());

    debugPrint('About to show dialog for editing cell...');
    showDialog(
      context: context,
      builder: (context) {
        debugPrint('Dialog builder called - dialog should be visible now');
        return AlertDialog(
        title: Row(
          children: [
            Icon(
              Icons.edit,
              color: widget.themeColor ?? Colors.blue,
              size: 20,
            ),
            const SizedBox(width: 8),
            Text(
              'Edit ${column.name}',
              style: const TextStyle(
                fontFamily: 'Inter',
                fontSize: 18,
                fontWeight: FontWeight.w600,
              ),
            ),
          ],
        ),
        content: SizedBox(
          width: 400,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Row ${rowIndex + 1}',
                style: TextStyle(
                  fontSize: 14,
                  color: Colors.grey.shade600,
                  fontFamily: 'Inter',
                ),
              ),
              const SizedBox(height: 16),
              _buildCellEditWidget(column, controller),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () async {
              try {
                final newValue = _convertValueByType(controller.text, column.dataType);
                rowData[column.columnId!] = newValue;

                await _schemaService.updateRowData(row.rowId!, rowData);
                await _loadData();

                if (mounted) {
                  Navigator.of(context).pop();
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(
                      content: Text('Cell updated successfully'),
                      backgroundColor: Colors.green,
                    ),
                  );
                }
              } catch (e) {
                if (mounted) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: Text('Error updating cell: $e'),
                      backgroundColor: Colors.red,
                    ),
                  );
                }
              }
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: widget.themeColor ?? Colors.blue,
              foregroundColor: Colors.white,
            ),
            child: const Text('Save'),
          ),
        ],
      );
      },
    );
  }

  Widget _buildCellEditWidget(FlexibleColumn column, TextEditingController controller) {
    switch (column.dataType) {
      case ColumnDataType.boolean:
        bool currentValue = controller.text.toLowerCase() == 'true';
        return StatefulBuilder(
          builder: (context, setState) => CheckboxListTile(
            title: Text('${column.name} Value'),
            value: currentValue,
            onChanged: (value) {
              setState(() {
                currentValue = value ?? false;
                controller.text = currentValue.toString();
              });
            },
            contentPadding: EdgeInsets.zero,
          ),
        );
      case ColumnDataType.date:
        return TextField(
          controller: controller,
          decoration: const InputDecoration(
            labelText: 'Date (YYYY-MM-DD)',
            border: OutlineInputBorder(),
            hintText: '2024-01-01',
          ),
        );
      case ColumnDataType.number:
        return TextField(
          controller: controller,
          keyboardType: TextInputType.number,
          decoration: const InputDecoration(
            labelText: 'Number',
            border: OutlineInputBorder(),
          ),
        );
      case ColumnDataType.currency:
        return TextField(
          controller: controller,
          keyboardType: TextInputType.number,
          decoration: InputDecoration(
            labelText: 'Amount (${column.currencyType.symbol})',
            border: const OutlineInputBorder(),
            prefixText: column.currencyType.symbol,
          ),
        );
      default:
        return TextField(
          controller: controller,
          decoration: const InputDecoration(
            labelText: 'Text',
            border: OutlineInputBorder(),
          ),
        );
    }
  }

  dynamic _convertValueByType(String value, ColumnDataType dataType) {
    switch (dataType) {
      case ColumnDataType.number:
      case ColumnDataType.currency:
        return double.tryParse(value) ?? 0.0;
      case ColumnDataType.boolean:
        return value.toLowerCase() == 'true';
      default:
        return value;
    }
  }



  Future<void> _deleteColumn(FlexibleColumn column) async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text(
          'Delete Column',
          style: TextStyle(
            fontFamily: 'Inter',
            fontWeight: FontWeight.w600,
          ),
        ),
        content: Text(
          'Are you sure you want to delete the column "${column.name}"? This will remove all data in this column.',
          style: const TextStyle(fontFamily: 'Inter'),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text(
              'Cancel',
              style: TextStyle(
                fontFamily: 'Inter',
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
          ElevatedButton(
            onPressed: () => Navigator.of(context).pop(true),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red.shade600,
              foregroundColor: Colors.white,
            ),
            child: const Text(
              'Delete',
              style: TextStyle(
                fontFamily: 'Inter',
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      try {
        await _schemaService.deleteColumn(column.columnId!);
        await _loadData();
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(
                'Column "${column.name}" deleted successfully',
                style: const TextStyle(fontFamily: 'Inter'),
              ),
              backgroundColor: Colors.green.shade600,
            ),
          );
        }
      } catch (e) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(
                'Error deleting column: $e',
                style: const TextStyle(fontFamily: 'Inter'),
              ),
              backgroundColor: Colors.red.shade600,
            ),
          );
        }
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return const Center(
        child: CircularProgressIndicator(),
      );
    }

    if (_error != null) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.error_outline,
              size: 64,
              color: Colors.red.shade300,
            ),
            const SizedBox(height: 16),
            Text(
              'Error loading table data',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: Colors.red.shade700,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              _error!,
              textAlign: TextAlign.center,
              style: TextStyle(
                color: Colors.red.shade600,
              ),
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: _loadData,
              child: const Text('Retry'),
            ),
          ],
        ),
      );
    }

    return FocusableActionDetector(
      shortcuts: {
        LogicalKeySet(LogicalKeyboardKey.control, LogicalKeyboardKey.keyZ): const UndoIntent(),
        LogicalKeySet(LogicalKeyboardKey.control, LogicalKeyboardKey.keyY): const RedoIntent(),
      },
      actions: {
        UndoIntent: CallbackAction<UndoIntent>(
          onInvoke: (intent) => _undoRedoService.canUndo ? _undo() : null,
        ),
        RedoIntent: CallbackAction<RedoIntent>(
          onInvoke: (intent) => _undoRedoService.canRedo ? _redo() : null,
        ),
      },
      child: Column(
        children: [
          _buildToolbar(),
          Expanded(
            child: _columns.isEmpty
                ? _buildEmptyState()
                : Container(
                    decoration: BoxDecoration(
                      border: Border.all(color: (widget.themeColor ?? Colors.blue).withValues(alpha: 0.3)),
                      borderRadius: BorderRadius.circular(8),
                      boxShadow: [
                        BoxShadow(
                          color: Colors.black.withValues(alpha: 0.05),
                          blurRadius: 4,
                          offset: const Offset(0, 2),
                        ),
                      ],
                    ),
                    child: ClipRRect(
                      borderRadius: BorderRadius.circular(8),
                      child: Theme(
                        data: Theme.of(context).copyWith(
                          scrollbarTheme: ScrollbarThemeData(
                            thumbColor: WidgetStateProperty.all(
                              (widget.themeColor ?? Colors.blue).withValues(alpha: 0.7),
                            ),
                            trackColor: WidgetStateProperty.all(
                              Colors.grey.shade200,
                            ),
                            thickness: WidgetStateProperty.all(12), // Made bigger
                            radius: const Radius.circular(6),
                            crossAxisMargin: 4,
                            mainAxisMargin: 4,
                          ),
                        ),
                        child: LayoutBuilder(
                          builder: (context, constraints) {
                            final isNarrow = constraints.maxWidth < 600;

                            Widget dataGrid = SfDataGrid(
                              source: _dataSource,
                              controller: _dataGridController,
                              columns: _buildGridColumns(isMobile: isNarrow),
                              allowSorting: false,
                              allowFiltering: false,
                              allowEditing: true,
                              allowColumnsResizing: !isNarrow, // Disable column resizing on mobile
                              columnResizeMode: ColumnResizeMode.onResize,
                              selectionMode: SelectionMode.multiple, // Enable multiple row selection via checkboxes
                              navigationMode: GridNavigationMode.cell, // Cell navigation for editing
                              editingGestureType: EditingGestureType.doubleTap, // Double-tap to edit cells
                              selectionManager: _selectionManager, // Use custom selection manager
                              columnWidthMode: isNarrow ? ColumnWidthMode.auto : ColumnWidthMode.none, // Auto-fit on mobile
                              gridLinesVisibility: isNarrow ? GridLinesVisibility.horizontal : GridLinesVisibility.both,
                              headerGridLinesVisibility: GridLinesVisibility.both,
                              headerRowHeight: isNarrow ? 44.0 : 56.0, // Smaller header on mobile
                              rowHeight: isNarrow ? 36.0 : _rowHeight, // Smaller rows on mobile
                              showCheckboxColumn: !isNarrow, // Hide checkbox column on mobile to save space
                              checkboxColumnSettings: const DataGridCheckboxColumnSettings(
                                showCheckboxOnHeader: true,
                                width: 50,
                              ),
                              frozenColumnsCount: 0,
                              frozenRowsCount: 0,
                          onColumnResizeUpdate: (ColumnResizeUpdateDetails details) {
                            // Handle column resize - this works from the right border by default
                            setState(() {
                              // Adjust for checkbox column offset (checkbox is column 0, data columns start at 1)
                              final adjustedColumnIndex = details.columnIndex > 0 ? details.columnIndex - 1 : 0;
                              if (adjustedColumnIndex < _columns.length) {
                                final columnId = _columns[adjustedColumnIndex].columnId!;
                                _columnWidths[columnId] = details.width;
                                _autoResizeColumns = false; // Disable auto-resize when manually resizing
                              }
                            });
                            return true;
                          },

                          onSelectionChanging: (addedRows, removedRows) {
                            // Allow selection changes only when they come from checkbox interactions
                            // This is a simple approach: if we're in checkbox mode, allow all selection changes
                            // The checkbox column will handle its own selection logic properly
                            return true;
                          },
                          onSelectionChanged: (addedRows, removedRows) {
                            setState(() {
                              _selectedRows = _dataGridController.selectedRows;
                            });
                          },
                          onCellTap: (details) {
                            if (details.rowColumnIndex.rowIndex == 0) {
                              // Header tapped - show column menu
                              _showColumnHeaderMenu(details);
                              return;
                            }

                            // For data cells: set cell focus and allow normal DataGrid behavior
                            if (details.rowColumnIndex.columnIndex > 0) {
                              setState(() {
                                _focusedCell = details.rowColumnIndex;
                              });
                              // Let the DataGrid handle cell focus naturally
                            }
                          },
                          onCellDoubleTap: (details) {
                            // Double-tap on any data cell (except checkbox column) should start editing
                            if (details.rowColumnIndex.rowIndex > 0 && details.rowColumnIndex.columnIndex > 0) {
                              debugPrint('REAL Double-tap detected on cell: ${details.rowColumnIndex.rowIndex}, ${details.rowColumnIndex.columnIndex}');

                              // Show edit dialog instead of inline editing since inline isn't working
                              _showCellEditDialog(details.rowColumnIndex);
                            }
                          },
                            );

                            // Return the appropriate widget based on screen size and view mode
                            if (isNarrow && (_isCardView || constraints.maxWidth < 400)) {
                              // Show card view on mobile when toggled or on very small screens
                              return _buildMobileCardView();
                            } else if (isNarrow) {
                              // On mobile, wrap in horizontal scroll for better UX
                              return SingleChildScrollView(
                                scrollDirection: Axis.horizontal,
                                child: SizedBox(
                                  width: constraints.maxWidth < 800 ? 800.0 : constraints.maxWidth,
                                  child: dataGrid,
                                ),
                              );
                            } else {
                              return dataGrid;
                            }
                          },
                        ),
                      ),
                    ),
                  ),
          ),
        ],
      ),
    );
  }

  Widget _buildMobileCardView() {
    if (_rows.isEmpty) {
      return const Center(
        child: Text(
          'No data available',
          style: TextStyle(
            fontFamily: 'Inter',
            fontSize: 14,
            color: Colors.grey,
          ),
        ),
      );
    }

    return ListView.builder(
      itemCount: _rows.length,
      itemBuilder: (context, index) {
        final row = _rows[index];
        final rowData = row.data != null ? jsonDecode(row.data!) as Map<String, dynamic> : <String, dynamic>{};

        return Card(
          margin: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
          elevation: 2,
          child: InkWell(
            onTap: () => _showCardEditDialog(row, index),
            borderRadius: BorderRadius.circular(8),
            child: Padding(
              padding: const EdgeInsets.all(12),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Add a header with row number and edit icon
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        'Row ${index + 1}',
                        style: TextStyle(
                          fontFamily: 'Inter',
                          fontSize: 13,
                          fontWeight: FontWeight.w700,
                          color: widget.themeColor ?? Colors.blue.shade700,
                        ),
                      ),
                      Icon(
                        Icons.edit,
                        size: 16,
                        color: Colors.grey.shade600,
                      ),
                    ],
                  ),
                  const SizedBox(height: 8),
                  const Divider(height: 1),
                  const SizedBox(height: 8),
                  // Field-value pairs
                  ...(_columns.map((column) {
                    final value = rowData[column.columnId] ?? '';
                    if (value.toString().isEmpty) return const SizedBox.shrink();

                    return Padding(
                      padding: const EdgeInsets.only(bottom: 8),
                      child: Row(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          SizedBox(
                            width: 80,
                            child: Text(
                              column.name ?? 'Field',
                              style: const TextStyle(
                                fontFamily: 'Inter',
                                fontSize: 11,
                                fontWeight: FontWeight.w600,
                                color: Colors.grey,
                              ),
                            ),
                          ),
                          const SizedBox(width: 8),
                          Expanded(
                            child: Text(
                              value.toString(),
                              style: const TextStyle(
                                fontFamily: 'Inter',
                                fontSize: 12,
                              ),
                            ),
                          ),
                        ],
                      ),
                    );
                  }).where((widget) => widget is! SizedBox).toList()),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  void _showCardEditDialog(FlexibleRow row, int rowIndex) {
    final rowData = row.data != null ? jsonDecode(row.data!) as Map<String, dynamic> : <String, dynamic>{};
    final controllers = <String, TextEditingController>{};

    // Create controllers for each column
    for (final column in _columns) {
      final value = rowData[column.columnId] ?? '';
      controllers[column.columnId!] = TextEditingController(text: value.toString());
    }

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(
          'Edit Row ${rowIndex + 1}',
          style: TextStyle(
            fontFamily: 'Inter',
            fontWeight: FontWeight.w600,
            color: widget.themeColor ?? Colors.blue.shade700,
          ),
        ),
        content: SizedBox(
          width: double.maxFinite,
          child: SingleChildScrollView(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: _columns.map((column) {
                final controller = controllers[column.columnId!]!;

                return Padding(
                  padding: const EdgeInsets.only(bottom: 16),
                  child: TextFormField(
                    controller: controller,
                    decoration: InputDecoration(
                      labelText: column.name ?? 'Field',
                      border: const OutlineInputBorder(),
                      contentPadding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                    ),
                    keyboardType: _getKeyboardType(column.dataType),
                  ),
                );
              }).toList(),
            ),
          ),
        ),
        actions: [
          TextButton(
            onPressed: () {
              // Dispose controllers
              for (final controller in controllers.values) {
                controller.dispose();
              }
              Navigator.of(context).pop();
            },
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () async {
              // Save changes
              final updatedData = <String, dynamic>{};
              for (final column in _columns) {
                final value = controllers[column.columnId!]!.text;
                updatedData[column.columnId!] = value;
              }

              try {
                await _schemaService.updateRowData(
                  row.rowId!,
                  updatedData,
                );

                // Refresh data
                await _loadData();

                // Dispose controllers
                for (final controller in controllers.values) {
                  controller.dispose();
                }

                if (mounted) {
                  Navigator.of(context).pop();
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(content: Text('Row updated successfully')),
                  );
                }
              } catch (e) {
                if (mounted) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(content: Text('Error updating row: $e')),
                  );
                }
              }
            },
            child: const Text('Save'),
          ),
        ],
      ),
    );
  }

  TextInputType _getKeyboardType(ColumnDataType dataType) {
    switch (dataType) {
      case ColumnDataType.number:
      case ColumnDataType.currency:
        return TextInputType.number;
      case ColumnDataType.date:
        return TextInputType.datetime;
      default:
        return TextInputType.text;
    }
  }

  Widget _buildToolbar() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: widget.themeColor?.withValues(alpha: 0.05) ?? Colors.blue.withValues(alpha: 0.05),
        border: Border(
          bottom: BorderSide(color: Colors.grey.shade200, width: 1),
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: LayoutBuilder(
        builder: (context, constraints) {
          final isNarrow = constraints.maxWidth < 600;

          return Column(
            children: [
              // First Row - Table Info and Primary Actions
              isNarrow ? _buildMobileToolbar() : _buildDesktopToolbar(),

          const SizedBox(height: 12),

          // Second Row - Tools and Controls (Responsive Wrap)
          Wrap(
            spacing: 8,
            runSpacing: 8,
            children: [
              // Row Management
              _buildIconOnlyButton(
                onPressed: _addRow,
                icon: Icons.add,
                tooltip: 'Add a new row to the table',
                color: widget.themeColor ?? Colors.blue,
              ),
              _buildIconOnlyButton(
                onPressed: _dataGridController.selectedRows.isNotEmpty ? _deleteSelectedRows : null,
                icon: Icons.remove,
                tooltip: 'Delete selected rows',
                color: Colors.red.shade600,
              ),

              // History Controls
              _buildIconButton(
                onPressed: _undoRedoService.canUndo ? _undo : null,
                icon: Icons.undo,
                tooltip: _getUndoTooltip(),
                color: widget.themeColor ?? Colors.blue,
              ),
              _buildIconButton(
                onPressed: _undoRedoService.canRedo ? _redo : null,
                icon: Icons.redo,
                tooltip: _getRedoTooltip(),
                color: widget.themeColor ?? Colors.blue,
              ),

              // Auto Resize
              _buildIconOnlyButton(
                onPressed: _autoResizeAllColumns,
                icon: Icons.fit_screen,
                tooltip: 'Auto-resize all columns to fit content',
                color: Colors.purple.shade600,
              ),

              // View Toggle (only show on mobile)
              if (isNarrow) _buildIconOnlyButton(
                onPressed: () {
                  setState(() {
                    _isCardView = !_isCardView;
                  });
                },
                icon: _isCardView ? Icons.table_view : Icons.view_agenda,
                tooltip: _isCardView ? 'Switch to table view' : 'Switch to card view',
                color: Colors.indigo.shade600,
              ),

              // Row Height Control
              _buildRowHeightControl(),

              // Import/Export
              _buildIconOnlyButton(
                onPressed: _importData,
                icon: Icons.upload_file,
                tooltip: 'Import data from Excel/CSV',
                color: Colors.green.shade600,
              ),
              _buildIconOnlyButton(
                onPressed: _exportData,
                icon: Icons.download,
                tooltip: 'Export data to Excel/CSV',
                color: Colors.orange.shade600,
              ),

              // Supabase Sync Buttons
              const SizedBox(width: 8),
              _buildIconOnlyButton(
                onPressed: SupabaseConfig.isConfigured ? _uploadToSupabase : null,
                icon: Icons.cloud_upload,
                tooltip: 'Upload to Supabase',
                color: Colors.blue.shade600,
              ),
              _buildIconOnlyButton(
                onPressed: SupabaseConfig.isConfigured ? _downloadFromSupabase : null,
                icon: Icons.cloud_download,
                tooltip: 'Download from Supabase',
                color: Colors.purple.shade600,
              ),


              // Column Management
              _buildIconOnlyButton(
                onPressed: _showColumnManagement,
                icon: Icons.view_week,
                tooltip: 'Manage table columns',
                color: widget.themeColor ?? Colors.blue,
              ),

              // Delete Table
              _buildIconOnlyButton(
                onPressed: _deleteTable,
                icon: Icons.delete_forever,
                tooltip: 'Delete this table and all its data',
                color: Colors.red.shade600,
              ),
            ],
          ),
        ],
      );
        },
      ),
    );
  }

  Widget _buildMobileToolbar() {
    return Column(
      children: [
        // Table info
        Row(
          children: [
            Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: widget.themeColor ?? Colors.blue,
                borderRadius: BorderRadius.circular(8),
              ),
              child: const Icon(
                Icons.table_chart,
                color: Colors.white,
                size: 20,
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    widget.table.name ?? 'Untitled Table',
                    style: const TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.w600,
                      fontFamily: 'Inter',
                    ),
                  ),
                  if (widget.table.description != null && widget.table.description!.isNotEmpty)
                    Text(
                      widget.table.description!,
                      style: TextStyle(
                        fontSize: 12,
                        color: Colors.grey.shade600,
                        fontFamily: 'Inter',
                      ),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                ],
              ),
            ),
          ],
        ),
        const SizedBox(height: 12),
        // Mobile action buttons in a scrollable row
        SingleChildScrollView(
          scrollDirection: Axis.horizontal,
          child: Row(
            children: [
              _buildIconOnlyButton(
                onPressed: _addRow,
                icon: Icons.add,
                tooltip: 'Add Row',
                color: widget.themeColor ?? Colors.blue,
              ),
              const SizedBox(width: 8),
              _buildIconOnlyButton(
                onPressed: _selectedRows.isNotEmpty ? _deleteSelectedRows : null,
                icon: Icons.delete,
                tooltip: 'Delete Selected',
                color: Colors.red,
              ),
              const SizedBox(width: 8),
              _buildIconOnlyButton(
                onPressed: _showColumnManagement,
                icon: Icons.view_column,
                tooltip: 'Add Column',
                color: widget.themeColor ?? Colors.blue,
              ),
              const SizedBox(width: 8),
              _buildIconOnlyButton(
                onPressed: _autoResizeAllColumns,
                icon: Icons.fit_screen,
                tooltip: 'Auto Resize',
                color: widget.themeColor ?? Colors.blue,
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildDesktopToolbar() {
    return Row(
      children: [
        // Table icon and info
        Container(
          padding: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: widget.themeColor ?? Colors.blue,
            borderRadius: BorderRadius.circular(8),
          ),
          child: const Icon(
            Icons.table_chart,
            color: Colors.white,
            size: 20,
          ),
        ),
        const SizedBox(width: 16),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                widget.table.name ?? 'Untitled Table',
                style: const TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.w600,
                  fontFamily: 'Inter',
                ),
              ),
              if (widget.table.description != null && widget.table.description!.isNotEmpty)
                Padding(
                  padding: const EdgeInsets.only(top: 4),
                  child: Text(
                    widget.table.description!,
                    style: TextStyle(
                      fontSize: 14,
                      color: Colors.grey.shade600,
                      fontFamily: 'Inter',
                    ),
                  ),
                ),
            ],
          ),
        ),
        const SizedBox(width: 16),
        // Desktop action buttons
        Row(
          children: [
            _buildIconOnlyButton(
              onPressed: _addRow,
              icon: Icons.add,
              tooltip: 'Add Row',
              color: widget.themeColor ?? Colors.blue,
            ),
            const SizedBox(width: 8),
            _buildIconOnlyButton(
              onPressed: _selectedRows.isNotEmpty ? _deleteSelectedRows : null,
              icon: Icons.delete,
              tooltip: 'Delete Selected',
              color: Colors.red,
            ),
            const SizedBox(width: 8),
            _buildIconOnlyButton(
              onPressed: _showColumnManagement,
              icon: Icons.view_column,
              tooltip: 'Add Column',
              color: widget.themeColor ?? Colors.blue,
            ),
            const SizedBox(width: 8),
            _buildIconOnlyButton(
              onPressed: _autoResizeAllColumns,
              icon: Icons.fit_screen,
              tooltip: 'Auto Resize',
              color: widget.themeColor ?? Colors.blue,
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildButtonGroup(List<Widget> buttons) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: buttons.map((button) {
        final index = buttons.indexOf(button);
        return Padding(
          padding: EdgeInsets.only(right: index < buttons.length - 1 ? 8 : 0),
          child: button,
        );
      }).toList(),
    );
  }



  Widget _buildRowHeightControl() {
    return Tooltip(
      message: 'Adjust row height',
      child: Container(
        height: 36, // Match icon button height
        padding: const EdgeInsets.symmetric(horizontal: 8),
        decoration: BoxDecoration(
          border: Border.all(color: (widget.themeColor ?? Colors.blue).withValues(alpha: 0.3)),
          borderRadius: BorderRadius.circular(6),
          color: (widget.themeColor ?? Colors.blue).withValues(alpha: 0.1),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(Icons.height, size: 16, color: Colors.grey.shade600),
            const SizedBox(width: 8),
            SizedBox(
              width: 80,
              child: Slider(
                value: _rowHeight,
                min: 32.0,
                max: 80.0,
                divisions: 12,
                onChanged: (value) {
                  setState(() {
                    _rowHeight = value;
                  });
                },
                activeColor: widget.themeColor ?? Colors.blue,
              ),
            ),
            const SizedBox(width: 4),
            Container(
              width: 24,
              alignment: Alignment.center,
              child: Text(
                '${_rowHeight.round()}',
                style: TextStyle(
                  fontSize: 12,
                  color: Colors.grey.shade600,
                  fontFamily: 'Inter',
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.table_chart,
            size: 64,
            color: Colors.grey.shade400,
          ),
          const SizedBox(height: 16),
          Text(
            'No columns defined',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: Colors.grey.shade600,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'Add columns to start building your table',
            style: TextStyle(
              color: Colors.grey.shade500,
            ),
          ),
          const SizedBox(height: 16),
          ElevatedButton.icon(
            onPressed: _showColumnManagement,
            icon: const Icon(Icons.add),
            label: const Text('Add Columns'),
            style: ElevatedButton.styleFrom(
              backgroundColor: widget.themeColor ?? Colors.blue,
              foregroundColor: Colors.white,
            ),
          ),
        ],
      ),
    );
  }

  void _addRow() async {
    try {
      final rowId = await _schemaService.createRow(widget.table.tableId!);

      // Add undo action
      final undoAction = _undoRedoService.createRowAddAction(
        tableId: widget.table.tableId!,
        rowId: rowId,
      );
      _undoRedoService.addAction(undoAction);

      await _loadData();
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error adding row: ${e.toString()}'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Future<void> _deleteSelectedRows() async {
    final selectedRows = _dataGridController.selectedRows;
    if (selectedRows.isEmpty) return;

    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text(
          'Delete Rows',
          style: TextStyle(
            fontFamily: 'Inter',
            fontWeight: FontWeight.w600,
          ),
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Are you sure you want to delete ${selectedRows.length} row${selectedRows.length != 1 ? 's' : ''}?',
              style: const TextStyle(fontFamily: 'Inter'),
            ),
            const SizedBox(height: 8),
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.red.shade50,
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.red.shade200),
              ),
              child: Row(
                children: [
                  Icon(Icons.warning, color: Colors.red.shade600, size: 20),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      'This action cannot be undone.',
                      style: TextStyle(
                        fontFamily: 'Inter',
                        fontSize: 13,
                        color: Colors.red.shade700,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text(
              'Cancel',
              style: TextStyle(fontFamily: 'Inter'),
            ),
          ),
          ElevatedButton(
            onPressed: () => Navigator.of(context).pop(true),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red.shade600,
              foregroundColor: Colors.white,
            ),
            child: const Text(
              'Delete',
              style: TextStyle(fontFamily: 'Inter'),
            ),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      try {
        // Get the row IDs from selected rows and create undo actions
        for (final selectedRow in selectedRows) {
          final rowIndex = _dataSource.rows.indexOf(selectedRow);
          if (rowIndex >= 0 && rowIndex < _rows.length) {
            final row = _rows[rowIndex];
            if (row.rowId != null) {
              // Get row data for undo
              final rowData = row.data != null
                  ? Map<String, dynamic>.from(jsonDecode(row.data!))
                  : <String, dynamic>{};

              // Create undo action before deletion
              final undoAction = _undoRedoService.createRowDeleteAction(
                tableId: widget.table.tableId!,
                rowId: row.rowId!,
                rowData: rowData,
              );
              _undoRedoService.addAction(undoAction);

              await _schemaService.deleteRow(row.rowId!);
            }
          }
        }

        await _loadData();

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(
                '${selectedRows.length} row${selectedRows.length != 1 ? 's' : ''} deleted successfully',
                style: const TextStyle(fontFamily: 'Inter'),
              ),
              backgroundColor: Colors.green.shade600,
            ),
          );
        }
      } catch (e) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(
                'Error deleting rows: $e',
                style: const TextStyle(fontFamily: 'Inter'),
              ),
              backgroundColor: Colors.red.shade600,
            ),
          );
        }
      }
    }
  }

  void _showColumnManagement() {
    showDialog(
      context: context,
      builder: (context) => ColumnManagementDialog(
        table: widget.table,
        onColumnsChanged: () => _loadData(),
      ),
    );
  }

  void _importData() async {
    try {
      // Show import preview dialog
      final previewData = await _importExportService.previewImport();
      if (previewData == null) return;

      if (!mounted) return;

      await showDialog(
        context: context,
        builder: (context) => ImportPreviewDialog(
          previewData: previewData,
          onConfirm: (mappings) async {
            try {
              final messenger = ScaffoldMessenger.of(context);

              final importResult = await _importExportService.importDataWithMappings(
                widget.table.tableId!,
                mappings,
              );

              if (mounted) {
                if (importResult.success) {
                  messenger.showSnackBar(
                    SnackBar(
                      content: Text(
                        'Successfully imported ${importResult.successfulRows} of ${importResult.totalRows} rows',
                        style: const TextStyle(fontFamily: 'Inter'),
                      ),
                      backgroundColor: Colors.green,
                    ),
                  );
                  await _loadData();
                } else {
                  _showImportErrorDialog(importResult);
                }
              }
            } catch (e) {
              if (mounted) {
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content: Text(
                      'Error importing data: $e',
                      style: const TextStyle(fontFamily: 'Inter'),
                    ),
                    backgroundColor: Colors.red,
                  ),
                );
              }
            }
          },
        ),
      );
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Import failed: ${e.toString()}'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  void _exportData() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Export Data'),
        content: const Text('Choose export format:'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () async {
              Navigator.of(context).pop();
              await _exportToExcel();
            },
            child: const Text('Excel'),
          ),
          ElevatedButton(
            onPressed: () async {
              Navigator.of(context).pop();
              await _exportToCsv();
            },
            child: const Text('CSV'),
          ),
        ],
      ),
    );
  }

  Future<void> _exportToExcel() async {
    try {
      final filePath = await _importExportService.exportToExcel(
        widget.table.tableId!,
        widget.table.name ?? 'table',
      );

      if (mounted) {
        if (filePath != null) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Data exported to: $filePath'),
              backgroundColor: Colors.green,
            ),
          );
        } else {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Export cancelled'),
              backgroundColor: Colors.orange,
            ),
          );
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Export failed: ${e.toString()}'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Future<void> _exportToCsv() async {
    try {
      final filePath = await _importExportService.exportToCsv(
        widget.table.tableId!,
        widget.table.name ?? 'table',
      );

      if (mounted) {
        if (filePath != null) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Data exported to: $filePath'),
              backgroundColor: Colors.green,
            ),
          );
        } else {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Export cancelled'),
              backgroundColor: Colors.orange,
            ),
          );
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Export failed: ${e.toString()}'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  // Supabase Sync Methods
  Future<void> _uploadToSupabase() async {
    if (!SupabaseConfig.isConfigured) {
      _showError('Please configure company settings first');
      return;
    }

    try {
      setState(() => _isLoading = true);
      await _supabaseSyncService.uploadTableToSupabase(widget.table.tableId!);

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Data uploaded to Supabase successfully'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Upload failed: ${e.toString()}'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) setState(() => _isLoading = false);
    }
  }

  Future<void> _downloadFromSupabase() async {
    if (!SupabaseConfig.isConfigured) {
      _showError('Please configure company settings first');
      return;
    }

    try {
      setState(() => _isLoading = true);
      await _supabaseSyncService.downloadTableFromSupabase(widget.table.tableId!);
      await _loadData(); // Reload the data to show downloaded content

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Data downloaded from Supabase successfully'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Download failed: ${e.toString()}'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) setState(() => _isLoading = false);
    }
  }

  void _showError(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text(message), backgroundColor: Colors.red),
    );
  }



  Widget _buildModernButton({
    required VoidCallback? onPressed,
    required IconData icon,
    required String label,
    required Color color,
    required String tooltip,
    bool isPrimary = false,
  }) {
    return Tooltip(
      message: tooltip,
      child: isPrimary
          ? ElevatedButton.icon(
              onPressed: onPressed,
              icon: Icon(icon, size: 16),
              label: Text(
                label,
                style: const TextStyle(
                  fontFamily: 'Inter',
                  fontWeight: FontWeight.w500,
                ),
              ),
              style: ElevatedButton.styleFrom(
                backgroundColor: color,
                foregroundColor: Colors.white,
                elevation: 2,
                shadowColor: color.withValues(alpha: 0.3),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
                padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
              ),
            )
          : OutlinedButton.icon(
              onPressed: onPressed,
              icon: Icon(icon, size: 16),
              label: Text(
                label,
                style: const TextStyle(
                  fontFamily: 'Inter',
                  fontWeight: FontWeight.w500,
                ),
              ),
              style: OutlinedButton.styleFrom(
                foregroundColor: color,
                side: BorderSide(color: color, width: 1.5),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
                padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
              ),
            ),
    );
  }

  Widget _buildIconButton({
    required VoidCallback? onPressed,
    required IconData icon,
    required String tooltip,
    required Color color,
  }) {
    return Tooltip(
      message: tooltip,
      child: IconButton(
        onPressed: onPressed,
        icon: Icon(icon, size: 18),
        color: onPressed != null ? color : Colors.grey.shade400,
        style: IconButton.styleFrom(
          backgroundColor: onPressed != null
              ? color.withValues(alpha: 0.1)
              : Colors.grey.shade100,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(6),
          ),
          padding: const EdgeInsets.all(8),
        ),
      ),
    );
  }

  Widget _buildIconOnlyButton({
    required VoidCallback? onPressed,
    required IconData icon,
    required String tooltip,
    required Color color,
  }) {
    return Tooltip(
      message: tooltip,
      child: Container(
        height: 36, // Match row height control height
        width: 36,
        decoration: BoxDecoration(
          color: color.withValues(alpha: 0.1),
          borderRadius: BorderRadius.circular(6),
          border: Border.all(color: color.withValues(alpha: 0.3)),
        ),
        child: IconButton(
          onPressed: onPressed,
          icon: Icon(icon, size: 18),
          color: color,
          padding: EdgeInsets.zero,
          constraints: const BoxConstraints(),
          style: IconButton.styleFrom(
            backgroundColor: Colors.transparent,
            hoverColor: color.withValues(alpha: 0.1),
          ),
        ),
      ),
    );
  }

  String _getUndoTooltip() {
    if (!_undoRedoService.canUndo) {
      return 'Undo (Ctrl+Z)';
    }
    final lastAction = _undoRedoService.undoStack.last;
    return 'Undo: ${lastAction.description} (Ctrl+Z)';
  }

  String _getRedoTooltip() {
    if (!_undoRedoService.canRedo) {
      return 'Redo (Ctrl+Y)';
    }
    final lastAction = _undoRedoService.redoStack.last;
    return 'Redo: ${lastAction.description} (Ctrl+Y)';
  }

  Future<void> _undo() async {
    try {
      await _undoRedoService.undo();
      await _loadData();
      if (mounted) {
        setState(() {});
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error undoing action: $e'),
            backgroundColor: Colors.red.shade600,
          ),
        );
      }
    }
  }

  Future<void> _redo() async {
    try {
      await _undoRedoService.redo();
      await _loadData();
      if (mounted) {
        setState(() {});
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error redoing action: $e'),
            backgroundColor: Colors.red.shade600,
          ),
        );
      }
    }
  }

  Future<void> _deleteTable() async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text(
          'Delete Table',
          style: TextStyle(
            fontFamily: 'Inter',
            fontWeight: FontWeight.w600,
          ),
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Are you sure you want to delete "${widget.table.name}"?',
              style: const TextStyle(
                fontFamily: 'Inter',
              ),
            ),
            const SizedBox(height: 8),
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.red.shade50,
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.red.shade200),
              ),
              child: Row(
                children: [
                  Icon(Icons.warning, color: Colors.red.shade600, size: 20),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      'This action cannot be undone. All data in this table will be permanently deleted.',
                      style: TextStyle(
                        fontFamily: 'Inter',
                        fontSize: 13,
                        color: Colors.red.shade700,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text(
              'Cancel',
              style: TextStyle(
                fontFamily: 'Inter',
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
          ElevatedButton(
            onPressed: () => Navigator.of(context).pop(true),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red.shade600,
              foregroundColor: Colors.white,
            ),
            child: const Text(
              'Delete',
              style: TextStyle(
                fontFamily: 'Inter',
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      try {
        await _schemaService.deleteTable(widget.table.tableId!);
        if (mounted) {
          Navigator.of(context).pop(); // Go back to previous screen
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(
                'Table "${widget.table.name}" deleted successfully',
                style: const TextStyle(fontFamily: 'Inter'),
              ),
              backgroundColor: Colors.green.shade600,
            ),
          );
        }
      } catch (e) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(
                'Error deleting table: $e',
                style: const TextStyle(fontFamily: 'Inter'),
              ),
              backgroundColor: Colors.red.shade600,
            ),
          );
        }
      }
    }
  }

  void _showImportErrorDialog(result) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Import Results'),
        content: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              Text('Total rows: ${result.totalRows}'),
              Text('Successful: ${result.successfulRows}'),
              Text('Errors: ${result.errors.length}'),
              if (result.errors.isNotEmpty) ...[
                const SizedBox(height: 16),
                const Text('Errors:', style: TextStyle(fontWeight: FontWeight.bold)),
                ...result.errors.take(5).map<Widget>((error) => Text('• $error')),
                if (result.errors.length > 5)
                  Text('... and ${result.errors.length - 5} more'),
              ],
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('OK'),
          ),
        ],
      ),
    );
  }
}

class TableDataSource extends DataGridSource {
  TableDataSource(List<DataGridRow> dataGridRows, this._columns, this._schemaService, this._tableId, this._rows, this._undoRedoService) {
    _dataGridRows = dataGridRows;
  }

  List<DataGridRow> _dataGridRows = [];
  final List<FlexibleColumn> _columns;
  final DynamicSchemaService _schemaService;
  final String _tableId;
  final List<FlexibleRow> _rows;
  final UndoRedoService _undoRedoService;

  /// Helps to hold the new value of all editable widgets.
  /// Based on the new value we will commit the new value into the corresponding
  /// DataGridCell on the onCellSubmit method.
  dynamic newCellValue;

  /// Helps to control the editable text in the [TextField] widget.
  TextEditingController editingController = TextEditingController();

  @override
  List<DataGridRow> get rows => _dataGridRows;

  @override
  Future<bool> canSubmitCell(DataGridRow dataGridRow, RowColumnIndex rowColumnIndex, GridColumn column) async {
    return true; // Allow all cell edits
  }

  @override
  Future<void> onCellSubmit(DataGridRow dataGridRow, RowColumnIndex rowColumnIndex, GridColumn column) async {
    // Get the old value using column name (Syncfusion recommended approach)
    DataGridCell? targetCell;
    try {
      targetCell = dataGridRow
              .getCells()
              .firstWhere((DataGridCell dataGridCell) =>
                  dataGridCell.columnName == column.columnName);
    } catch (e) {
      targetCell = null;
    }
    final dynamic oldValue = targetCell?.value ?? '';

    final int dataRowIndex = _dataGridRows.indexOf(dataGridRow);

    // If no new value or same as old value, skip
    if (newCellValue == null || oldValue == newCellValue) {
      return;
    }

    try {
      // Find the cell by column name and update it (Syncfusion recommended approach)
      final cellIndex = dataGridRow.getCells().indexWhere(
        (cell) => cell.columnName == column.columnName
      );

      if (cellIndex >= 0) {
        // Update the DataGridCell using the correct approach from Syncfusion docs
        dataGridRow.getCells()[cellIndex] = DataGridCell(
          columnName: column.columnName,
          value: newCellValue,
        );

        // Update the underlying data and save to database
        if (dataRowIndex >= 0 && dataRowIndex < _rows.length) {
          final row = _rows[dataRowIndex];
          final rowData = row.data != null ? jsonDecode(row.data!) as Map<String, dynamic> : <String, dynamic>{};
          rowData[column.columnName] = newCellValue;

          // Save to database
          await _schemaService.updateRowData(row.rowId!, rowData);

          // Add undo action
          final undoAction = _undoRedoService.createCellEditAction(
            tableId: _tableId,
            rowId: row.rowId!,
            columnId: column.columnName,
            newValue: newCellValue,
            oldValue: oldValue,
          );
          _undoRedoService.addAction(undoAction);
        }
      }
    } catch (e) {
      debugPrint('Error updating cell: $e');
    }
  }



  @override
  Widget? buildEditWidget(DataGridRow dataGridRow, RowColumnIndex rowColumnIndex, GridColumn column, CellSubmit submitCell) {
    debugPrint('buildEditWidget called: row=${rowColumnIndex.rowIndex}, col=${rowColumnIndex.columnIndex}, columnName=${column.columnName}');
    debugPrint('Creating edit widget for cell...');

    // Skip editing for checkbox column (first column)
    if (rowColumnIndex.columnIndex == 0) {
      debugPrint('Skipping edit for checkbox column');
      return null;
    }

    final columnId = column.columnName;
    final flexColumn = _columns.firstWhere(
      (col) => col.columnId == columnId,
      orElse: () => FlexibleColumn(),
    );

    // Get current value using column name (Syncfusion recommended approach)
    DataGridCell? targetCell;
    try {
      targetCell = dataGridRow
              .getCells()
              .firstWhere((DataGridCell dataGridCell) =>
                  dataGridCell.columnName == column.columnName);
    } catch (e) {
      targetCell = null;
    }

    final String displayText = targetCell?.value?.toString() ?? '';

    // The new cell value must be reset.
    // To avoid committing the [DataGridCell] value that was previously edited
    // into the current non-modified [DataGridCell].
    newCellValue = null;

    final bool isNumericType = flexColumn.dataType == ColumnDataType.number || flexColumn.dataType == ColumnDataType.currency;

    // Create a subtle editing widget with standard styling
    return Container(
      width: double.infinity,
      height: double.infinity,
      padding: const EdgeInsets.all(4.0),
      decoration: BoxDecoration(
        border: Border.all(color: Colors.blue.shade300, width: 1),
        borderRadius: BorderRadius.circular(4),
        color: Colors.blue.shade50,
      ),
      child: TextField(
        autofocus: true,
        controller: editingController..text = displayText,
        textAlign: isNumericType ? TextAlign.right : TextAlign.left,
        style: const TextStyle(
          fontFamily: 'Inter',
          fontSize: 14,
          fontWeight: FontWeight.normal,
        ),
        decoration: const InputDecoration(
          border: InputBorder.none,
          contentPadding: EdgeInsets.symmetric(horizontal: 8, vertical: 4),
          isDense: true,
        ),
        keyboardType: isNumericType ? TextInputType.number : TextInputType.text,
        onChanged: (String value) {
          if (value.isNotEmpty) {
            if (isNumericType) {
              newCellValue = double.tryParse(value) ?? 0.0;
            } else {
              newCellValue = value;
            }
          } else {
            newCellValue = null;
          }
        },
        onSubmitted: (String value) {
          submitCell();
        },
      ),
    );
  }

  Widget _buildEnhancedTextEditor(dynamic value, CellSubmit submitCell, DataGridRow dataGridRow, RowColumnIndex rowColumnIndex, GridColumn column) {
    final controller = TextEditingController(text: value?.toString() ?? '');

    return TextField(
      controller: controller,
      autofocus: true,
      style: const TextStyle(fontFamily: 'Inter', fontSize: 14),
      decoration: const InputDecoration(
        border: InputBorder.none,
        contentPadding: EdgeInsets.symmetric(horizontal: 12, vertical: 8),
      ),
      onSubmitted: (newValue) {
        _updateCellValue(dataGridRow, rowColumnIndex, column, newValue);
        submitCell();
      },
      onEditingComplete: () {
        _updateCellValue(dataGridRow, rowColumnIndex, column, controller.text);
        submitCell();
      },
    );
  }

  void _updateCellValue(DataGridRow dataGridRow, RowColumnIndex rowColumnIndex, GridColumn column, dynamic newValue) {
    // Add bounds checking before updating
    if (rowColumnIndex.columnIndex >= 0 && rowColumnIndex.columnIndex < dataGridRow.getCells().length) {
      // Update the cell in the data grid
      dataGridRow.getCells()[rowColumnIndex.columnIndex] = DataGridCell(
        columnName: column.columnName,
        value: newValue,
      );

      // Also update the underlying data and save to database
      final rowIndex = rowColumnIndex.rowIndex - 1; // Subtract 1 for header
      if (rowIndex >= 0 && rowIndex < _rows.length) {
        final row = _rows[rowIndex];
        final rowData = row.data != null ? jsonDecode(row.data!) as Map<String, dynamic> : <String, dynamic>{};
        rowData[column.columnName] = newValue;

        // Save to database
        _schemaService.updateRowData(row.rowId!, rowData).then((_) {
          // Refresh the data source
          notifyListeners();
        }).catchError((error) {
          debugPrint('Error saving cell data: $error');
        });
      }
    }
  }

  Widget _buildEnhancedNumberEditor(dynamic value, CellSubmit submitCell, DataGridRow dataGridRow, RowColumnIndex rowColumnIndex, GridColumn column) {
    final controller = TextEditingController(text: value?.toString() ?? '');

    return TextField(
      controller: controller,
      autofocus: true,
      keyboardType: TextInputType.number,
      style: const TextStyle(fontFamily: 'Inter', fontSize: 14),
      decoration: const InputDecoration(
        border: InputBorder.none,
        contentPadding: EdgeInsets.symmetric(horizontal: 12, vertical: 8),
        hintText: 'Enter number',
      ),
      onSubmitted: (newValue) {
        final numValue = double.tryParse(newValue) ?? 0.0;
        _updateCellValue(dataGridRow, rowColumnIndex, column, numValue);
        submitCell();
      },
      onEditingComplete: () {
        final numValue = double.tryParse(controller.text) ?? 0.0;
        _updateCellValue(dataGridRow, rowColumnIndex, column, numValue);
        submitCell();
      },
    );
  }

  Widget _buildEnhancedCurrencyEditor(dynamic value, CellSubmit submitCell, DataGridRow dataGridRow, RowColumnIndex rowColumnIndex, GridColumn column) {
    final controller = TextEditingController(text: value?.toString() ?? '');

    return TextField(
      controller: controller,
      autofocus: true,
      keyboardType: TextInputType.number,
      style: const TextStyle(fontFamily: 'Inter', fontSize: 14),
      decoration: const InputDecoration(
        border: InputBorder.none,
        contentPadding: EdgeInsets.symmetric(horizontal: 12, vertical: 8),
        prefixText: '\$',
        hintText: '0.00',
      ),
      onSubmitted: (newValue) {
        final numValue = double.tryParse(newValue) ?? 0.0;
        _updateCellValue(dataGridRow, rowColumnIndex, column, numValue);
        submitCell();
      },
      onEditingComplete: () {
        final numValue = double.tryParse(controller.text) ?? 0.0;
        _updateCellValue(dataGridRow, rowColumnIndex, column, numValue);
        submitCell();
      },
    );
  }

  Widget _buildEnhancedBooleanEditor(dynamic value, CellSubmit submitCell, DataGridRow dataGridRow, RowColumnIndex rowColumnIndex, GridColumn column) {
    bool currentValue = value.toString().toLowerCase() == 'true';

    return StatefulBuilder(
      builder: (context, setState) {
        return Checkbox(
          value: currentValue,
          onChanged: (newValue) {
            setState(() {
              currentValue = newValue ?? false;
            });
            _updateCellValue(dataGridRow, rowColumnIndex, column, currentValue);
            submitCell();
          },
        );
      },
    );
  }

  Widget _buildEnhancedDateEditor(dynamic value, CellSubmit submitCell, DataGridRow dataGridRow, RowColumnIndex rowColumnIndex, GridColumn column) {
    final controller = TextEditingController(text: value?.toString() ?? '');

    return TextField(
      controller: controller,
      autofocus: true,
      style: const TextStyle(fontFamily: 'Inter', fontSize: 14),
      decoration: const InputDecoration(
        border: InputBorder.none,
        contentPadding: EdgeInsets.symmetric(horizontal: 12, vertical: 8),
        hintText: 'YYYY-MM-DD',
      ),
      onSubmitted: (newValue) {
        _updateCellValue(dataGridRow, rowColumnIndex, column, newValue);
        submitCell();
      },
      onEditingComplete: () {
        _updateCellValue(dataGridRow, rowColumnIndex, column, controller.text);
        submitCell();
      },
    );
  }

  @override
  DataGridRowAdapter buildRow(DataGridRow row) {
    return DataGridRowAdapter(
      cells: row.getCells().map<Widget>((dataGridCell) {
        final columnName = dataGridCell.columnName;
        final column = _columns.firstWhere(
          (col) => col.columnId == columnName,
          orElse: () => FlexibleColumn(),
        );

        return Container(
          alignment: _getCellAlignment(column.dataType),
          padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 6),
          child: _buildCellContent(dataGridCell.value, column),
        );
      }).toList(),
    );
  }

  Alignment _getCellAlignment(ColumnDataType dataType) {
    switch (dataType) {
      case ColumnDataType.number:
      case ColumnDataType.currency:
        return Alignment.centerRight;
      case ColumnDataType.boolean:
        return Alignment.center;
      default:
        return Alignment.centerLeft;
    }
  }

  Widget _buildCellContent(dynamic value, FlexibleColumn column) {
    if (value == null || value.toString().isEmpty) {
      return Text(
        '-',
        style: TextStyle(
          fontFamily: 'Inter',
          fontSize: 12,
          color: Colors.grey.shade500,
          fontStyle: FontStyle.italic,
        ),
      );
    }

    switch (column.dataType) {
      case ColumnDataType.currency:
        return _buildCurrencyCell(value, column.currencyType);
      case ColumnDataType.number:
        return _buildNumberCell(value);
      case ColumnDataType.boolean:
        return _buildBooleanCell(value);
      case ColumnDataType.date:
        return _buildDateCell(value);
      default:
        return Text(
          value.toString(),
          style: const TextStyle(
            fontFamily: 'Inter',
            fontSize: 12,
          ),
          overflow: TextOverflow.ellipsis,
        );
    }
  }

  Widget _buildCurrencyCell(dynamic value, CurrencyType currencyType) {
    final numValue = double.tryParse(value.toString()) ?? 0.0;
    final formattedValue = numValue.toStringAsFixed(2);

    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        Text(
          currencyType.symbol,
          style: TextStyle(
            fontFamily: 'Inter',
            fontSize: 12,
            fontWeight: FontWeight.w500,
            color: Colors.green.shade700,
          ),
        ),
        const SizedBox(width: 2),
        Flexible(
          child: Text(
            formattedValue,
            style: const TextStyle(
              fontFamily: 'Inter',
              fontSize: 12,
              fontWeight: FontWeight.w500,
            ),
            overflow: TextOverflow.ellipsis,
          ),
        ),
      ],
    );
  }

  Widget _buildNumberCell(dynamic value) {
    final numValue = double.tryParse(value.toString());
    if (numValue != null) {
      return Text(
        numValue % 1 == 0 ? numValue.toInt().toString() : numValue.toString(),
        style: const TextStyle(
          fontFamily: 'Inter',
          fontSize: 12,
          fontWeight: FontWeight.w500,
        ),
        overflow: TextOverflow.ellipsis,
      );
    }
    return Text(
      value.toString(),
      style: const TextStyle(
        fontFamily: 'Inter',
        fontSize: 14,
      ),
    );
  }

  Widget _buildBooleanCell(dynamic value) {
    final boolValue = value.toString().toLowerCase() == 'true';
    return Icon(
      boolValue ? Icons.check_circle : Icons.cancel,
      color: boolValue ? Colors.green.shade600 : Colors.red.shade600,
      size: 20,
    );
  }

  Widget _buildDateCell(dynamic value) {
    try {
      final date = DateTime.parse(value.toString());
      return Text(
        '${date.day}/${date.month}/${date.year}',
        style: const TextStyle(
          fontFamily: 'Inter',
          fontSize: 14,
        ),
      );
    } catch (e) {
      return Text(
        value.toString(),
        style: const TextStyle(
          fontFamily: 'Inter',
          fontSize: 14,
        ),
      );
    }
  }
}

/// Custom selection manager to control DataGrid selection behavior
/// This prevents row selection when clicking on data cells while allowing checkbox selection
class CustomSelectionManager extends RowSelectionManager {
  @override
  void handleTap(RowColumnIndex rowColumnIndex) {
    // Only allow row selection if it's from checkbox column (column 0)
    if (rowColumnIndex.columnIndex == 0) {
      super.handleTap(rowColumnIndex);
    }
    // For data cells (column > 0), don't call super.handleTap to prevent row selection
    // This allows cell focus and editing to work without triggering row selection
  }

  @override
  Future<void> handleKeyEvent(KeyEvent keyEvent) async {
    // Allow keyboard navigation and selection
    await super.handleKeyEvent(keyEvent);
  }
}


