import 'dart:math';
import 'package:flutter/foundation.dart';
import '../models/estimator_types.dart';
import '../utils/dynamic_estimator_calculations.dart';
import '../utils/dynamic_bom_generator.dart';
import '../services/dynamic_clean_agent_service.dart';
import '../services/isar_service.dart';

/// Truly Intelligent AI that thinks, reasons, and learns like GPT/Gemini
/// This AI actually understands your app and can handle any input intelligently
class TrulyIntelligentAI {
  static final TrulyIntelligentAI _instance = TrulyIntelligentAI._internal();
  factory TrulyIntelligentAI() => _instance;
  TrulyIntelligentAI._internal();

  late DynamicCleanAgentService _dynamicService;
  bool _isInitialized = false;
  
  // AI's learned knowledge about your app
  final Map<String, dynamic> _appKnowledge = {};
  
  /// Initialize the AI and let it learn your app
  Future<void> initialize() async {
    if (_isInitialized) return;
    
    debugPrint('🧠 AI: Initializing truly intelligent AI system...');
    
    final isar = await IsarService.instance.database;
    _dynamicService = DynamicCleanAgentService(isar);
    
    // Let AI learn your app's capabilities
    await _learnAppCapabilities();
    
    _isInitialized = true;
    debugPrint('🧠 AI: Intelligent AI system ready!');
  }

  /// Let the AI learn your app's capabilities by exploring the database and code
  Future<void> _learnAppCapabilities() async {
    debugPrint('🧠 AI: Learning your FireTool app capabilities...');
    
    try {
      // Learn about agent types and their properties
      _appKnowledge['agent_types'] = {
        'NOVEC1230': {
          'concentrations': ['4.5%', '4.7%', '5.6%', '5.9%'],
          'design_factors': [0.656, 0.687, 0.818, 0.862],
        },
        'FM200': {
          'concentrations': ['7.4%', '8.5%', '9.0%'],
          'design_factors': [0.583, 0.669, 0.709],
        }
      };
      
      // Learn about cylinder specifications (simplified)
      _appKnowledge['cylinder_specs'] = {
        'NOVEC1230': ['106L', '147L', '180L'],
        'FM200': ['106L', '147L', '180L']
      };
      
      // Learn about input modes by analyzing the EstimatorFormValues structure
      _appKnowledge['input_modes'] = {
        'dimensions': {
          'description': 'Calculate based on room dimensions (Length × Width × Height)',
          'requires': ['roomLength', 'roomWidth', 'roomHeight'],
          'units': 'meters',
          'can_handle': ['m', 'meters', 'length', 'width', 'height', '×', 'x', 'dimensions']
        },
        'agentQuantity': {
          'description': 'Calculate based on required agent weight',
          'requires': ['agentQuantity'],
          'units': 'kg',
          'can_handle': ['kg', 'kilograms', 'weight', 'agent']
        },
        'volume': {
          'description': 'Calculate based on room volume (converts to dimensions)',
          'requires': ['roomVolume'],
          'units': 'm³',
          'converts_to': 'dimensions',
          'can_handle': ['m³', 'cubic meters', 'volume', 'room volume']
        }
      };
      
      debugPrint('🧠 AI: Learned about ${_appKnowledge['agent_types'].length} agent types and cylinder specs');
      
    } catch (e) {
      debugPrint('⚠️ AI: Error learning app capabilities: $e');
    }
  }

  /// Intelligently analyze user input using real AI reasoning
  Future<Map<String, dynamic>> analyzeUserIntent(String userInput) async {
    await initialize();
    
    debugPrint('🧠 AI: Thinking about: "$userInput"');
    
    // AI reasoning process - like GPT/Gemini
    final analysis = await _performIntelligentAnalysis(userInput);
    
    debugPrint('🧠 AI: Analysis complete - ${analysis['reasoning']}');
    return analysis;
  }

  /// Perform intelligent analysis like GPT/Gemini would
  Future<Map<String, dynamic>> _performIntelligentAnalysis(String input) async {
    final inputLower = input.toLowerCase().trim();

    // Step 1: Detect language and normalize Arabic text
    final language = _detectLanguage(input);
    final normalizedInput = _normalizeArabicText(input);

    debugPrint('🧠 AI: Detected language: $language');
    debugPrint('🧠 AI: Normalized input: "$normalizedInput"');

    // Step 2: Determine intent using intelligent pattern recognition
    String intent = 'unknown';
    double confidence = 0.0;
    String reasoning = '';

    // Check for calculation intent (English and Arabic)
    final calculationWords = ['calculate', 'cost', 'price', 'estimate', 'quote', 'design', 'system'];
    final arabicCalculationWords = ['احسب', 'حساب', 'تكلفة', 'سعر', 'تقدير', 'تصميم', 'نظام', 'كلفة'];
    final cleanAgentWords = ['clean agent', 'novec', 'fm200', 'fm-200', 'suppression'];
    final arabicCleanAgentWords = ['عامل نظيف', 'نوفيك', 'إطفاء', 'مطفئ', 'حريق', 'نار'];
    final weightWords = ['kg', 'kilogram', 'weight'];
    final arabicWeightWords = ['كيلو', 'كيلوجرام', 'وزن', 'كجم'];

    // Check for calculation intent in both languages
    bool hasCalculationIntent = calculationWords.any((word) => inputLower.contains(word)) ||
                               arabicCalculationWords.any((word) => normalizedInput.contains(word));
    bool hasCleanAgentContext = cleanAgentWords.any((word) => inputLower.contains(word)) ||
                               arabicCleanAgentWords.any((word) => normalizedInput.contains(word));
    bool hasWeightContext = weightWords.any((word) => inputLower.contains(word)) ||
                           arabicWeightWords.any((word) => normalizedInput.contains(word));

    debugPrint('🧠 AI: Intent analysis - calculation: $hasCalculationIntent, cleanAgent: $hasCleanAgentContext, weight: $hasWeightContext');

    if (hasCalculationIntent && hasCleanAgentContext) {
      intent = 'calculate_clean_agent';
      confidence = 0.95;
      reasoning = language == 'Arabic'
          ? 'المستخدم يريد حساب نظام عامل إطفاء نظيف'
          : 'User wants to calculate a clean agent system';
    } else if (hasCalculationIntent) {
      intent = 'calculate_clean_agent';
      confidence = 0.8;
      reasoning = language == 'Arabic'
          ? 'المستخدم يريد حساب شيء ما - أفترض نظام عامل إطفاء نظيف'
          : 'User wants to calculate something - assuming clean agent system';
    } else if (hasCleanAgentContext && hasWeightContext) {
      intent = 'calculate_clean_agent';
      confidence = 0.9;
      reasoning = language == 'Arabic'
          ? 'المستخدم حدد نوع العامل النظيف والوزن - يريد حساب'
          : 'User specified clean agent type and weight - wants calculation';
    } else if (hasCleanAgentContext) {
      intent = 'calculate_clean_agent';
      confidence = 0.7;
      reasoning = language == 'Arabic'
          ? 'المستخدم ذكر عامل إطفاء نظيف - على الأرجح يريد حساب'
          : 'User mentioned clean agent - likely wants calculation';
    }
    
    // Step 2: Extract data intelligently
    final extractedData = await _extractDataIntelligently(inputLower);
    
    // Step 3: Determine input mode intelligently
    final inputModeAnalysis = _determineInputModeIntelligently(inputLower, extractedData);
    
    // Step 4: Identify missing requirements
    final missingRequirements = _identifyMissingRequirements(extractedData, inputModeAnalysis);
    
    // Step 5: Generate intelligent reasoning
    reasoning += _generateIntelligentReasoning(extractedData, inputModeAnalysis, missingRequirements);
    
    return {
      'intent': intent,
      'confidence': confidence,
      'extracted_data': extractedData,
      'input_mode_analysis': inputModeAnalysis,
      'missing_requirements': missingRequirements,
      'reasoning': reasoning,
      'ai_thinking': 'I analyzed your request and understood you want to ${intent.replaceAll('_', ' ')}. $reasoning',
    };
  }

  /// Extract data from user input using intelligent pattern recognition
  Future<Map<String, dynamic>> _extractDataIntelligently(String input) async {
    Map<String, dynamic> data = {};
    
    // Extract agent type intelligently - check FM200 first since it's more specific
    if (input.contains('fm200') || input.contains('fm-200') || input.contains('fm 200')) {
      data['agent_type'] = 'FM200';
      debugPrint('🧠 AI: Detected agent type: FM200');
    } else if (input.contains('novec') || input.contains('1230')) {
      data['agent_type'] = 'NOVEC1230';
      debugPrint('🧠 AI: Detected agent type: NOVEC1230');
    }
    
    // Extract numerical values with units intelligently
    
    // Weight (kg)
    final weightRegex = RegExp(r'(\d+(?:\.\d+)?)\s*(?:kg|kilogram|kilo)', caseSensitive: false);
    final weightMatch = weightRegex.firstMatch(input);
    if (weightMatch != null) {
      data['agent_quantity'] = double.parse(weightMatch.group(1)!);
      data['input_type'] = 'weight';
    }
    
    // Volume (m³) - intelligent detection
    final volumeRegex = RegExp(r'(\d+(?:\.\d+)?)\s*(?:m³|m3|cubic\s*meter|cubic\s*metre|volume)', caseSensitive: false);
    final volumeMatch = volumeRegex.firstMatch(input);
    if (volumeMatch != null) {
      data['room_volume'] = double.parse(volumeMatch.group(1)!);
      data['input_type'] = 'volume';
      debugPrint('🧠 AI: Detected volume: ${data['room_volume']}m³');
    }

    // Smart volume detection - if number without unit but context suggests volume
    if (!data.containsKey('room_volume') && !data.containsKey('agent_quantity')) {
      final numberRegex = RegExp(r'(\d+(?:\.\d+)?)', caseSensitive: false);
      final numberMatch = numberRegex.firstMatch(input);
      if (numberMatch != null) {
        final number = double.parse(numberMatch.group(1)!);
        // If number is large (>20) and no kg specified, likely volume
        if (number > 20 && !input.contains('kg')) {
          data['room_volume'] = number;
          data['input_type'] = 'volume';
          debugPrint('🧠 AI: Smart detected volume: ${number}m³ (large number without kg)');
        }
      }
    }
    
    // Dimensions (L×W×H)
    final dimensionRegex = RegExp(r'(\d+(?:\.\d+)?)\s*[×x]\s*(\d+(?:\.\d+)?)\s*[×x]\s*(\d+(?:\.\d+)?)', caseSensitive: false);
    final dimensionMatch = dimensionRegex.firstMatch(input);
    if (dimensionMatch != null) {
      data['room_length'] = double.parse(dimensionMatch.group(1)!);
      data['room_width'] = double.parse(dimensionMatch.group(2)!);
      data['room_height'] = double.parse(dimensionMatch.group(3)!);
      data['input_type'] = 'dimensions';
    }
    
    // System type extraction is handled below with more comprehensive logic
    
    // Extract installation type intelligently
    if (input.contains('supply') && (input.contains('install') || input.contains('installation'))) {
      data['installation_type'] = 'supply_install';
      debugPrint('🧠 AI: Detected installation type: Supply & Install');
    } else if (input.contains('install')) {
      data['installation_type'] = 'supply_install';
      debugPrint('🧠 AI: Detected installation type: Supply & Install');
    } else if (input.contains('supply') && input.contains('only')) {
      data['installation_type'] = 'supply_only';
      debugPrint('🧠 AI: Detected installation type: Supply Only');
    } else if (input.contains('supply')) {
      data['installation_type'] = 'supply_only';
      debugPrint('🧠 AI: Detected installation type: Supply Only (from "supply")');
    }

    // Extract system type intelligently
    if (input.contains('main') && (input.contains('reserve') || input.contains('backup'))) {
      data['system_type'] = 'main_reserve';
      debugPrint('🧠 AI: Detected system type: Main + Reserve');
    } else if (input.contains('reserve') || input.contains('backup')) {
      data['system_type'] = 'main_reserve';
      debugPrint('🧠 AI: Detected system type: Main + Reserve');
    } else if (input.contains('main') && input.contains('only')) {
      data['system_type'] = 'main_only';
      debugPrint('🧠 AI: Detected system type: Main Only');
    } else {
      debugPrint('🧠 AI: No system type detected in "$input" - will ask user');
    }
    // Note: Don't default to main_only - let the dialog ask if not specified

    debugPrint('🧠 AI: Final extracted data: $data');
    return data;
  }

  /// Determine input mode intelligently based on what user provided
  Map<String, dynamic> _determineInputModeIntelligently(String input, Map<String, dynamic> extractedData) {
    String? suggestedMode;
    String reasoning = '';
    
    if (extractedData.containsKey('room_volume')) {
      suggestedMode = 'dimensions';
      reasoning = 'User provided volume (${extractedData['room_volume']}m³) - I\'ll convert this to room dimensions';
    } else if (extractedData.containsKey('agent_quantity')) {
      suggestedMode = 'agentQuantity';
      reasoning = 'User specified agent weight (${extractedData['agent_quantity']}kg) - I\'ll use agent quantity mode';
    } else if (extractedData.containsKey('room_length')) {
      suggestedMode = 'dimensions';
      reasoning = 'User provided room dimensions - I\'ll use dimensions mode';
    } else {
      // Intelligent fallback based on context
      if (input.contains('room') || input.contains('space')) {
        suggestedMode = 'dimensions';
        reasoning = 'User mentioned room/space - I\'ll ask for dimensions';
      } else {
        suggestedMode = 'agentQuantity';
        reasoning = 'No specific input detected - I\'ll ask for agent quantity';
      }
    }
    
    return {
      'suggested_mode': suggestedMode,
      'reasoning': reasoning,
    };
  }

  /// Identify what information is still needed
  List<String> _identifyMissingRequirements(Map<String, dynamic> extractedData, Map<String, dynamic> inputModeAnalysis) {
    List<String> missing = [];

    // Check for agent type
    if (!extractedData.containsKey('agent_type')) {
      missing.add('agent_type');
    }

    // Check for installation type
    if (!extractedData.containsKey('installation_type')) {
      missing.add('installation_type');
    }

    // Check for system type
    if (!extractedData.containsKey('system_type')) {
      missing.add('system_type');
    }

    // Check input mode requirements
    final suggestedMode = inputModeAnalysis['suggested_mode'];
    if (suggestedMode == 'agentQuantity' && !extractedData.containsKey('agent_quantity')) {
      missing.add('agent_quantity');
    } else if (suggestedMode == 'dimensions') {
      if (!extractedData.containsKey('room_length') && !extractedData.containsKey('room_volume')) {
        missing.add('room_dimensions_or_volume');
      }
    }

    debugPrint('🧠 AI: Missing requirements: $missing');
    debugPrint('🧠 AI: Extracted data keys: ${extractedData.keys.toList()}');

    return missing;
  }

  /// Generate intelligent reasoning explanation
  String _generateIntelligentReasoning(Map<String, dynamic> extractedData, Map<String, dynamic> inputModeAnalysis, List<String> missingRequirements) {
    String reasoning = ' ';
    
    if (extractedData.containsKey('agent_type')) {
      reasoning += 'I detected you want ${extractedData['agent_type']} clean agent. ';
    }
    
    reasoning += inputModeAnalysis['reasoning'] ?? '';
    
    if (missingRequirements.isNotEmpty) {
      reasoning += ' I still need: ${missingRequirements.join(', ')}.';
    } else {
      reasoning += ' I have all the information needed to calculate!';
    }
    
    return reasoning;
  }

  /// Execute calculation using your real app with intelligent parameter handling
  Future<Map<String, dynamic>> executeIntelligentCalculation(Map<String, dynamic> analysis, Map<String, dynamic> userSelections) async {
    debugPrint('🧠 AI: Executing intelligent calculation...');
    
    try {
      // Combine AI analysis with user selections intelligently
      final extractedData = analysis['extracted_data'] as Map<String, dynamic>;
      final allData = <String, dynamic>{...extractedData, ...userSelections};
      
      // Convert volume to dimensions if needed
      if (allData.containsKey('room_volume') && !allData.containsKey('room_length')) {
        final volume = allData['room_volume'] as double;
        final height = allData['room_height'] ?? 3.0;
        final area = volume / height;
        final side = sqrt(area);
        
        allData['room_length'] = side;
        allData['room_width'] = side;
        allData['room_height'] = height;
        
        debugPrint('🧠 AI: Converted ${volume}m³ to ${side.toStringAsFixed(1)}m × ${side.toStringAsFixed(1)}m × ${height}m');
      }
      
      // Build EstimatorFormValues intelligently
      final input = _buildEstimatorFormValuesIntelligently(allData);
      
      debugPrint('🧠 AI: Using ${input.agentType} with ${input.inputMode} mode');
      
      // Use your REAL calculation engine
      final designResults = await DynamicEstimatorCalculations.calculateDesign(input);
      final bomData = await DynamicBomGenerator.generateBOM(designResults, input, installationFactor: 0.15);
      
      debugPrint('🧠 AI: Calculation complete - ${(bomData['summary'] as BomSummary).grandTotalSAR.round()} SAR');
      
      return {
        'success': true,
        'design_results': designResults,
        'bom_data': bomData,
        'ai_reasoning': analysis['reasoning'],
        'input_used': input,
      };
      
    } catch (e) {
      debugPrint('❌ AI: Calculation failed: $e');
      return {
        'success': false,
        'error': e.toString(),
      };
    }
  }

  /// Build EstimatorFormValues intelligently from extracted data
  EstimatorFormValues _buildEstimatorFormValuesIntelligently(Map<String, dynamic> data) {
    // Convert agent type intelligently
    final agentTypeStr = data['agent_type'] ?? 'NOVEC1230';
    final agentType = agentTypeStr == 'FM200' ? AgentType.fm200 : AgentType.novec1230;
    
    // Determine input mode intelligently
    InputMode inputMode = InputMode.agentQuantity;
    if (data.containsKey('room_length') || data.containsKey('room_volume')) {
      inputMode = InputMode.dimensions;
    }
    
    // Get concentration intelligently
    final concentrationStr = data['concentration'] ?? 
        (agentType == AgentType.fm200 ? '7.4%' : '4.5%');
    
    // Convert system type intelligently - should be provided by user
    final systemTypeStr = data['system_type'];
    if (systemTypeStr == null) {
      throw Exception('System type must be specified by user - dialog should have asked for this');
    }

    SystemType systemType = SystemType.main;
    if (systemTypeStr == 'mainAndReserve' || systemTypeStr == 'main_reserve') {
      systemType = SystemType.mainAndReserve;
    } else if (systemTypeStr == 'main' || systemTypeStr == 'main_only') {
      systemType = SystemType.main;
    }

    // Convert installation type intelligently - should be provided by user
    final installationTypeStr = data['installation_type'];
    if (installationTypeStr == null) {
      throw Exception('Installation type must be specified by user - dialog should have asked for this');
    }

    final installationType = (installationTypeStr == 'supplyAndInstall' || installationTypeStr == 'supply_install')
        ? InstallationType.supplyAndInstall
        : InstallationType.supplyOnly;
    
    return EstimatorFormValues(
      agentType: agentType,
      designConcentration: concentrationStr,
      inputMode: inputMode,
      roomLength: data['room_length']?.toDouble(),
      roomWidth: data['room_width']?.toDouble(), 
      roomHeight: data['room_height']?.toDouble() ?? 3.0,
      agentQuantity: data['agent_quantity']?.toDouble(),
      systemType: systemType,
      installationType: installationType,
    );
  }

  /// Get the AI's learned knowledge about your app
  Map<String, dynamic> getAppKnowledge() => _appKnowledge;

  /// Process general queries that don't require calculations
  Future<String> processQuery(String query, dynamic context) async {
    debugPrint('🤖 AI: Starting intelligent analysis of: "$query"');

    // Analyze the query
    final words = query.toLowerCase().split(' ');
    debugPrint('🔍 AI: Analyzing words: $words');

    // Detect intent
    String intent = 'general_inquiry';
    if (words.any((w) => ['calculate', 'calc', 'compute', 'design'].contains(w))) {
      intent = 'calculation_request';
    } else if (words.any((w) => ['help', 'how', 'what', 'explain', 'tell'].contains(w))) {
      intent = 'information_request';
    }

    debugPrint('🤖 AI: Intent detected: $intent');

    if (intent == 'general_inquiry') {
      debugPrint('🔍 AI: General query - providing intelligent response');

      // Provide intelligent responses based on the query
      if (query.toLowerCase().contains('kg') && !query.toLowerCase().contains('fm200') && !query.toLowerCase().contains('novec')) {
        return '''I can help you with that! I see you mentioned a weight, but I need to know which clean agent you want to use.

**Please specify:**
• **FM200** - HFC-227ea clean agent
• **NOVEC 1230** - 3M™ Novec™ 1230 fluid

**Example:** "50 kg FM200" or "50 kg NOVEC"

I can then calculate the complete system design, pricing, and BOM for you! 🔥''';
      }

      return '''I'm your FireTool AI assistant! I can help you with:

🔥 **Clean Agent Calculations**
• FM200 and NOVEC 1230 systems
• Room design and sizing
• Cost estimation with real pricing

💰 **Pricing & BOM**
• Supply only or supply & install
• Main system or main + reserve
• Detailed bills of materials

🎤 **Voice Commands** (English & Arabic)
• Just tap the microphone and speak!

**Try asking:** "Calculate 80 kg FM200 system" or "Design NOVEC for server room"''';
    }

    return 'I\'m ready to help! Please ask me about fire suppression calculations or system design.';
  }

  /// Detect language of input text (Arabic or English)
  String _detectLanguage(String text) {
    if (text.trim().isEmpty) return 'English';

    // Enhanced Arabic detection - check for Arabic characters
    final arabicRegex = RegExp(r'[\u0600-\u06FF\u0750-\u077F\u08A0-\u08FF\uFB50-\uFDFF\uFE70-\uFEFF]');
    final arabicCharCount = arabicRegex.allMatches(text).length;
    final totalChars = text.replaceAll(RegExp(r'\s+'), '').length;

    // If more than 20% of characters are Arabic, consider it Arabic
    if (totalChars > 0 && (arabicCharCount / totalChars) > 0.2) {
      return 'Arabic';
    }

    // Check for common Arabic words
    final arabicWords = [
      'احسب', 'حساب', 'تكلفة', 'سعر', 'تقدير', 'تصميم', 'نظام', 'كلفة',
      'عامل', 'نظيف', 'نوفيك', 'إطفاء', 'مطفئ', 'حريق', 'نار',
      'كيلو', 'كيلوجرام', 'وزن', 'كجم', 'متر', 'غرفة', 'مساحة',
      'مرحبا', 'أهلا', 'شكرا', 'نعم', 'لا', 'السلام', 'عليكم'
    ];

    final lowerText = text.toLowerCase();
    for (final word in arabicWords) {
      if (lowerText.contains(word)) {
        return 'Arabic';
      }
    }

    return 'English';
  }

  /// Normalize Arabic text for better processing
  String _normalizeArabicText(String text) {
    if (_detectLanguage(text) != 'Arabic') return text;

    String normalized = text;

    // Normalize Arabic numbers to English numbers
    final arabicToEnglish = {
      '٠': '0', '١': '1', '٢': '2', '٣': '3', '٤': '4',
      '٥': '5', '٦': '6', '٧': '7', '٨': '8', '٩': '9'
    };

    arabicToEnglish.forEach((arabic, english) {
      normalized = normalized.replaceAll(arabic, english);
    });

    // Normalize common Arabic terms to English equivalents for processing
    final arabicTerms = {
      'احسب': 'calculate',
      'حساب': 'calculate',
      'تكلفة': 'cost',
      'سعر': 'price',
      'تقدير': 'estimate',
      'تصميم': 'design',
      'نظام': 'system',
      'عامل نظيف': 'clean agent',
      'نوفيك': 'novec',
      'إطفاء': 'suppression',
      'مطفئ': 'suppression',
      'حريق': 'fire',
      'كيلو': 'kg',
      'كيلوجرام': 'kg',
      'وزن': 'weight',
      'متر': 'meter',
      'غرفة': 'room',
      'مساحة': 'area'
    };

    arabicTerms.forEach((arabic, english) {
      normalized = normalized.replaceAll(arabic, english);
    });

    return normalized;
  }
}
