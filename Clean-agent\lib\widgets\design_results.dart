import 'package:flutter/material.dart';
import '../models/estimator_types.dart';
import '../utils/formatters.dart';

class DesignResultsWidget extends StatefulWidget {
  final DesignResults results;
  final List<BomItem> bom;
  final BomSummary summary;
  final QuotedSystem quotedSystem;

  const DesignResultsWidget({
    super.key,
    required this.results,
    required this.bom,
    required this.summary,
    required this.quotedSystem,
  });

  @override
  State<DesignResultsWidget> createState() => _DesignResultsWidgetState();
}

class _DesignResultsWidgetState extends State<DesignResultsWidget> with SingleTickerProviderStateMixin {
  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 5, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Card(
      child: Column(
        children: [
          // Export buttons
          Padding(
            padding: const EdgeInsets.all(16),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                OutlinedButton.icon(
                  onPressed: () {
                    // TODO: Implement PDF export
                    ScaffoldMessenger.of(context).showSnackBar(
                      const SnackBar(content: Text('PDF export coming soon')),
                    );
                  },
                  icon: const Icon(Icons.picture_as_pdf),
                  label: const Text('Export PDF'),
                ),
                const SizedBox(width: 8),
                OutlinedButton.icon(
                  onPressed: () {
                    // TODO: Implement Excel export
                    ScaffoldMessenger.of(context).showSnackBar(
                      const SnackBar(content: Text('Excel export coming soon')),
                    );
                  },
                  icon: const Icon(Icons.table_chart),
                  label: const Text('Export Excel'),
                ),
              ],
            ),
          ),
          
          // Tab bar
          TabBar(
            controller: _tabController,
            isScrollable: true,
            tabs: const [
              Tab(text: 'Summary'),
              Tab(text: 'Cylinder Details'),
              Tab(text: 'Discharge System'),
              Tab(text: 'Bill of Materials'),
              Tab(text: 'Cost Summary'),
            ],
          ),
          
          // Tab content
          Expanded(
            child: TabBarView(
              controller: _tabController,
              children: [
                _buildSummaryTab(),
                _buildCylinderTab(),
                _buildDischargeTab(),
                _buildBomTab(),
                _buildCostSummaryTab(),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSummaryTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'System Design Summary',
            style: Theme.of(context).textTheme.headlineSmall?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 16),
          
          Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Room Parameters
              Expanded(
                child: Card(
                  child: Padding(
                    padding: const EdgeInsets.all(16),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'Room Parameters',
                          style: Theme.of(context).textTheme.titleMedium?.copyWith(
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        const SizedBox(height: 12),
                        _buildInfoRow('Length', '${formatNumber(widget.results.roomData.roomLength)} m'),
                        _buildInfoRow('Width', '${formatNumber(widget.results.roomData.roomWidth)} m'),
                        _buildInfoRow('Height', '${formatNumber(widget.results.roomData.roomHeight)} m'),
                        _buildInfoRow('Area', '${formatNumber(widget.results.roomData.roomArea)} m²'),
                        _buildInfoRow('Volume', '${formatNumber(widget.results.roomData.roomVolume)} m³'),
                      ],
                    ),
                  ),
                ),
              ),
              const SizedBox(width: 16),
              
              // System Results
              Expanded(
                child: Card(
                  child: Padding(
                    padding: const EdgeInsets.all(16),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'System Results',
                          style: Theme.of(context).textTheme.titleMedium?.copyWith(
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        const SizedBox(height: 12),
                        _buildInfoRow('Design Factor', '${formatNumber(widget.results.designFactor)} kg/m³'),
                        _buildInfoRow('Required Agent', '${formatNumber(widget.results.totalAgentRequired)} kg'),
                        _buildInfoRow('Actual Agent', '${formatNumber(widget.results.cylinder.actualTotalKg)} kg'),
                        _buildInfoRow('Cylinder Size', '${widget.results.cylinder.cylinderSizeLiters2ndIter.toInt()}L'),
                        _buildInfoRow('Number of Cylinders', '${widget.results.cylinder.numCylinders2ndIter}'),
                        _buildInfoRow('Agent per Cylinder', '${formatNumber(widget.results.cylinder.qtyPerCylinder)} kg'),
                        _buildInfoRow('Filling Ratio', '${formatNumber(widget.results.cylinder.fillingRatio * 100, 1)}%'),
                        _buildInfoRow('Number of Nozzles', '${widget.results.discharge.nozzleQtyFinal}'),
                        _buildInfoRow('Nozzle Size', '${widget.results.discharge.nozzleSizeFinal} mm'),
                        _buildInfoRow('System Type', widget.results.systemType.toUpperCase()),
                      ],
                    ),
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildCylinderTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Cylinder Calculations',
            style: Theme.of(context).textTheme.headlineSmall?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 16),
          
          // First Iteration
          Text(
            'First Iteration',
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 8),
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Table(
                border: TableBorder.all(color: Colors.grey[300]!),
                children: [
                  TableRow(
                    decoration: BoxDecoration(color: Colors.grey[100]),
                    children: const [
                      Padding(padding: EdgeInsets.all(8), child: Text('Target Fill (kg)', style: TextStyle(fontWeight: FontWeight.bold))),
                      Padding(padding: EdgeInsets.all(8), child: Text('Cylinder Size (L)', style: TextStyle(fontWeight: FontWeight.bold))),
                      Padding(padding: EdgeInsets.all(8), child: Text('Number of Cylinders', style: TextStyle(fontWeight: FontWeight.bold))),
                      Padding(padding: EdgeInsets.all(8), child: Text('Qty per Cylinder (kg)', style: TextStyle(fontWeight: FontWeight.bold))),
                    ],
                  ),
                  TableRow(
                    children: [
                      Padding(padding: const EdgeInsets.all(8), child: Text(formatNumber(widget.results.cylinder.targetFillSingleCyl))),
                      Padding(padding: const EdgeInsets.all(8), child: Text(widget.results.cylinder.cylinderSizeLiters1stIter.toInt().toString())),
                      Padding(padding: const EdgeInsets.all(8), child: Text(widget.results.cylinder.numCylinders1stIter.toString())),
                      Padding(padding: const EdgeInsets.all(8), child: Text(formatNumber(widget.results.cylinder.qtyPerCylinder1stIter))),
                    ],
                  ),
                ],
              ),
            ),
          ),
          const SizedBox(height: 24),
          
          // Final Results
          Text(
            'Final Results',
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 8),
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Table(
                border: TableBorder.all(color: Colors.grey[300]!),
                children: [
                  TableRow(
                    decoration: BoxDecoration(color: Colors.grey[100]),
                    children: const [
                      Padding(padding: EdgeInsets.all(8), child: Text('Cylinder Size (L)', style: TextStyle(fontWeight: FontWeight.bold))),
                      Padding(padding: EdgeInsets.all(8), child: Text('Number of Cylinders', style: TextStyle(fontWeight: FontWeight.bold))),
                      Padding(padding: EdgeInsets.all(8), child: Text('Agent per Cylinder (kg)', style: TextStyle(fontWeight: FontWeight.bold))),
                      Padding(padding: EdgeInsets.all(8), child: Text('Total Agent (kg)', style: TextStyle(fontWeight: FontWeight.bold))),
                      Padding(padding: EdgeInsets.all(8), child: Text('Filling Ratio (%)', style: TextStyle(fontWeight: FontWeight.bold))),
                    ],
                  ),
                  TableRow(
                    children: [
                      Padding(padding: const EdgeInsets.all(8), child: Text(widget.results.cylinder.cylinderSizeLiters2ndIter.toInt().toString())),
                      Padding(padding: const EdgeInsets.all(8), child: Text(widget.results.cylinder.numCylinders2ndIter.toString())),
                      Padding(padding: const EdgeInsets.all(8), child: Text(formatNumber(widget.results.cylinder.qtyPerCylinder))),
                      Padding(padding: const EdgeInsets.all(8), child: Text(formatNumber(widget.results.cylinder.actualTotalKg))),
                      Padding(padding: const EdgeInsets.all(8), child: Text(formatNumber(widget.results.cylinder.fillingRatio * 100, 1))),
                    ],
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDischargeTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Discharge System Details',
            style: Theme.of(context).textTheme.headlineSmall?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 16),

          // Flow Calculations
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Flow Calculations',
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 12),
                  _buildInfoRow('Total Flow Rate', '${formatNumber(widget.results.discharge.totalFlowRate)} kg/s'),
                  _buildInfoRow('Discharge Time', '10 seconds'),
                ],
              ),
            ),
          ),
          const SizedBox(height: 16),

          // Nozzle Calculations
          Text(
            'Nozzle Sizing',
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 8),
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Table(
                border: TableBorder.all(color: Colors.grey[300]!),
                children: [
                  TableRow(
                    decoration: BoxDecoration(color: Colors.grey[100]),
                    children: const [
                      Padding(padding: EdgeInsets.all(8), child: Text('Trial', style: TextStyle(fontWeight: FontWeight.bold))),
                      Padding(padding: EdgeInsets.all(8), child: Text('Nozzle Qty', style: TextStyle(fontWeight: FontWeight.bold))),
                      Padding(padding: EdgeInsets.all(8), child: Text('Flow per Nozzle (kg/s)', style: TextStyle(fontWeight: FontWeight.bold))),
                      Padding(padding: EdgeInsets.all(8), child: Text('Nozzle Size (mm)', style: TextStyle(fontWeight: FontWeight.bold))),
                    ],
                  ),
                  TableRow(
                    children: [
                      const Padding(padding: EdgeInsets.all(8), child: Text('First Trial')),
                      Padding(padding: const EdgeInsets.all(8), child: Text(widget.results.discharge.nozzleQty1stTrial.toString())),
                      Padding(padding: const EdgeInsets.all(8), child: Text(formatNumber(widget.results.discharge.flowPerNozzle1stTrial))),
                      Padding(padding: const EdgeInsets.all(8), child: Text(widget.results.discharge.nozzleSize1stTrial.toString())),
                    ],
                  ),
                  TableRow(
                    children: [
                      const Padding(padding: EdgeInsets.all(8), child: Text('Final')),
                      Padding(padding: const EdgeInsets.all(8), child: Text(widget.results.discharge.nozzleQtyFinal.toString())),
                      Padding(padding: const EdgeInsets.all(8), child: Text(formatNumber(widget.results.discharge.flowPerNozzleFinal))),
                      Padding(padding: const EdgeInsets.all(8), child: Text(widget.results.discharge.nozzleSizeFinal.toString())),
                    ],
                  ),
                ],
              ),
            ),
          ),
          const SizedBox(height: 16),

          // Manifold Information
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Manifold System',
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 12),
                  _buildInfoRow('Manifold Pipe Size', '${widget.results.discharge.manifoldPipeSize} mm'),
                  _buildInfoRow('Manifold Assembly Size', '${widget.results.discharge.manifoldAssemblySize} mm'),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildBomTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Bill of Materials',
            style: Theme.of(context).textTheme.headlineSmall?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 16),

          // Group BOM items by category
          ...widget.bom.fold<Map<String, List<BomItem>>>({}, (map, item) {
            final category = item.category;
            if (!map.containsKey(category)) {
              map[category] = [];
            }
            map[category]!.add(item);
            return map;
          }).entries.map((entry) => _buildBomCategory(entry.key, entry.value)),
        ],
      ),
    );
  }

  Widget _buildBomCategory(String category, List<BomItem> items) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          category,
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.bold,
            color: Theme.of(context).primaryColor,
          ),
        ),
        const SizedBox(height: 8),
        Card(
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Table(
              border: TableBorder.all(color: Colors.grey[300]!),
              columnWidths: const {
                0: FlexColumnWidth(2),
                1: FlexColumnWidth(3),
                2: FlexColumnWidth(1),
                3: FlexColumnWidth(1.5),
                4: FlexColumnWidth(1.5),
              },
              children: [
                TableRow(
                  decoration: BoxDecoration(color: Colors.grey[100]),
                  children: const [
                    Padding(padding: EdgeInsets.all(8), child: Text('Part No.', style: TextStyle(fontWeight: FontWeight.bold))),
                    Padding(padding: EdgeInsets.all(8), child: Text('Description', style: TextStyle(fontWeight: FontWeight.bold))),
                    Padding(padding: EdgeInsets.all(8), child: Text('Qty', style: TextStyle(fontWeight: FontWeight.bold))),
                    Padding(padding: EdgeInsets.all(8), child: Text('Unit Cost', style: TextStyle(fontWeight: FontWeight.bold))),
                    Padding(padding: EdgeInsets.all(8), child: Text('Total Cost', style: TextStyle(fontWeight: FontWeight.bold))),
                  ],
                ),
                ...items.map((item) => TableRow(
                  children: [
                    Padding(padding: const EdgeInsets.all(8), child: Text(item.partNo, style: const TextStyle(fontSize: 12))),
                    Padding(padding: const EdgeInsets.all(8), child: Text(item.description, style: const TextStyle(fontSize: 12))),
                    Padding(padding: const EdgeInsets.all(8), child: Text(item.quantity.toString(), style: const TextStyle(fontSize: 12))),
                    Padding(padding: const EdgeInsets.all(8), child: Text('\$${formatNumber(item.unitCost)}', style: const TextStyle(fontSize: 12))),
                    Padding(padding: const EdgeInsets.all(8), child: Text('\$${formatNumber(item.totalCost)}', style: const TextStyle(fontSize: 12, fontWeight: FontWeight.w500))),
                  ],
                )),
                // Category total
                TableRow(
                  decoration: BoxDecoration(color: Colors.grey[50]),
                  children: [
                    const Padding(padding: EdgeInsets.all(8), child: Text('')),
                    const Padding(padding: EdgeInsets.all(8), child: Text('')),
                    const Padding(padding: EdgeInsets.all(8), child: Text('')),
                    const Padding(
                      padding: EdgeInsets.all(8),
                      child: Text(
                        'Subtotal:',
                        style: TextStyle(fontWeight: FontWeight.bold, fontSize: 12),
                      ),
                    ),
                    Padding(
                      padding: const EdgeInsets.all(8),
                      child: Text(
                        '\$${formatNumber(items.fold(0.0, (sum, item) => sum + item.totalCost))}',
                        style: const TextStyle(fontWeight: FontWeight.bold, fontSize: 12),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ),
        const SizedBox(height: 16),
      ],
    );
  }

  Widget _buildCostSummaryTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Cost Summary',
            style: Theme.of(context).textTheme.headlineSmall?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 16),

          // Supply Costs
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Supply Costs (USD)',
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 12),
                  _buildInfoRow('Suppression System', '\$${formatNumber(widget.summary.suppressionCost)}'),
                  _buildInfoRow('Alarm & Detection', '\$${formatNumber(widget.summary.alarmCost)}'),
                  _buildInfoRow('Installation Items', '\$${formatNumber(widget.summary.installationItemsCost)}'),
                  const Divider(),
                  _buildInfoRow('Total Supply Cost (USD)', '\$${formatNumber(widget.summary.totalSupplyCostUSD)}', isTotal: true),
                  _buildInfoRow('Total Supply Cost (SAR)', 'SAR ${formatNumber(widget.summary.totalSupplyCostSAR)}', isTotal: true),
                ],
              ),
            ),
          ),
          const SizedBox(height: 16),

          // Installation Costs
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Installation Costs (SAR)',
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 12),
                  _buildInfoRow('Suppression Installation', 'SAR ${formatNumber(widget.summary.suppressionInstallCost)}'),
                  _buildInfoRow('Alarm Installation', 'SAR ${formatNumber(widget.summary.alarmInstallCost)}'),
                  _buildInfoRow('Installation Services', 'SAR ${formatNumber(widget.summary.installationServicesInstallCost)}'),
                  const Divider(),
                  _buildInfoRow('Total Installation Cost (SAR)', 'SAR ${formatNumber(widget.summary.totalInstallCostSAR)}', isTotal: true),
                ],
              ),
            ),
          ),
          const SizedBox(height: 16),

          // Grand Total
          Card(
            color: Theme.of(context).primaryColor.withOpacity(0.1),
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Project Total',
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                      color: Theme.of(context).primaryColor,
                    ),
                  ),
                  const SizedBox(height: 12),
                  _buildInfoRow('Subtotal (SAR)', 'SAR ${formatNumber(widget.summary.totalSupplyCostSAR + widget.summary.totalInstallCostSAR)}'),
                  _buildInfoRow('Margin Factor', '${formatNumber(widget.summary.marginFactor, 2)}x'),
                  _buildInfoRow('Margin Amount (SAR)', 'SAR ${formatNumber(widget.summary.marginAmountSAR)}'),
                  const Divider(thickness: 2),
                  _buildInfoRow(
                    'GRAND TOTAL (SAR)',
                    'SAR ${formatNumber(widget.summary.grandTotalSAR)}',
                    isTotal: true,
                    isGrandTotal: true,
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildInfoRow(String label, String value, {bool isTotal = false, bool isGrandTotal = false}) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: TextStyle(
              fontWeight: isTotal || isGrandTotal ? FontWeight.bold : FontWeight.w500,
              fontSize: isGrandTotal ? 16 : 14,
              color: isGrandTotal ? Theme.of(context).primaryColor : null,
            ),
          ),
          Text(
            value,
            style: TextStyle(
              fontWeight: isTotal || isGrandTotal ? FontWeight.bold : FontWeight.normal,
              fontSize: isGrandTotal ? 16 : 14,
              color: isGrandTotal ? Theme.of(context).primaryColor : null,
            ),
          ),
        ],
      ),
    );
  }
}
