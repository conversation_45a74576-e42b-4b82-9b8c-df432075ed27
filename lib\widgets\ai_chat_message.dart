import 'package:flutter/material.dart';
import '../models/chat_message.dart';

/// Enhanced chat message widget with inline action buttons
class AIChatMessage extends StatelessWidget {
  final ChatMessage message;
  final bool isUser;
  final Map<String, dynamic>? calculationResults;
  final VoidCallback? onShowBOM;
  final VoidCallback? onShowCalculation;
  final VoidCallback? onRecalculate;

  const AIChatMessage({
    super.key,
    required this.message,
    required this.isUser,
    this.calculationResults,
    this.onShowBOM,
    this.onShowCalculation,
    this.onRecalculate,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.symmetric(vertical: 8, horizontal: 16),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          if (!isUser) ...[
            CircleAvatar(
              radius: 16,
              backgroundColor: Colors.red.shade700,
              child: const Icon(
                Icons.smart_toy,
                size: 16,
                color: Colors.white,
              ),
            ),
            const SizedBox(width: 12),
          ],
          Expanded(
            child: Column(
              crossAxisAlignment: isUser ? CrossAxisAlignment.end : CrossAxisAlignment.start,
              children: [
                // Message bubble
                Container(
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    color: isUser 
                        ? Colors.red.shade700
                        : Colors.grey.shade100,
                    borderRadius: BorderRadius.circular(16),
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        message.content,
                        style: TextStyle(
                          color: isUser ? Colors.white : Colors.black87,
                          fontSize: 14,
                        ),
                      ),
                      
                      // Show calculation summary if available
                      if (!isUser && calculationResults != null) ...[
                        const SizedBox(height: 12),
                        _buildCalculationSummary(context),
                      ],
                    ],
                  ),
                ),
                
                // Action buttons for AI messages with calculations
                if (!isUser && calculationResults != null) ...[
                  const SizedBox(height: 8),
                  _buildActionButtons(context),
                ],
                
                // Timestamp
                const SizedBox(height: 4),
                Text(
                  _formatTime(message.timestamp),
                  style: TextStyle(
                    fontSize: 10,
                    color: Colors.grey.shade600,
                  ),
                ),
              ],
            ),
          ),
          if (isUser) ...[
            const SizedBox(width: 12),
            CircleAvatar(
              radius: 16,
              backgroundColor: Colors.blue.shade700,
              child: const Icon(
                Icons.person,
                size: 16,
                color: Colors.white,
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildCalculationSummary(BuildContext context) {
    if (calculationResults == null) return const SizedBox.shrink();

    final results = calculationResults!;
    
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.blue.shade50,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.blue.shade200),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(Icons.calculate, size: 16, color: Colors.blue.shade700),
              const SizedBox(width: 8),
              Text(
                'Calculation Complete',
                style: TextStyle(
                  fontWeight: FontWeight.w600,
                  color: Colors.blue.shade700,
                  fontSize: 12,
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          
          // Quick summary
          if (results.containsKey('bom_summary'))
            _buildQuickSummary(results),
        ],
      ),
    );
  }

  Widget _buildQuickSummary(Map<String, dynamic> results) {
    final bomSummary = results['bom_summary'] as Map<String, dynamic>;
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Total Cost: ${bomSummary['grand_total_sar']} SAR',
          style: const TextStyle(
            fontWeight: FontWeight.bold,
            fontSize: 14,
          ),
        ),
        const SizedBox(height: 4),
        Text(
          'Agent: ${results['form_inputs']?['agent_type'] ?? 'N/A'} • '
          'Weight: ${results['design_results']?['agent_weight_required'] ?? 'N/A'}kg',
          style: TextStyle(
            fontSize: 12,
            color: Colors.grey.shade700,
          ),
        ),
      ],
    );
  }

  Widget _buildActionButtons(BuildContext context) {
    return Wrap(
      spacing: 8,
      runSpacing: 4,
      children: [
        _buildActionButton(
          context,
          icon: Icons.list_alt,
          label: 'Show BOM',
          color: Colors.blue,
          onPressed: onShowBOM,
        ),
        _buildActionButton(
          context,
          icon: Icons.calculate,
          label: 'Show Calculation',
          color: Colors.green,
          onPressed: onShowCalculation,
        ),
        _buildActionButton(
          context,
          icon: Icons.refresh,
          label: 'Recalculate',
          color: Colors.orange,
          onPressed: onRecalculate,
        ),
      ],
    );
  }

  Widget _buildActionButton(
    BuildContext context, {
    required IconData icon,
    required String label,
    required MaterialColor color,
    VoidCallback? onPressed,
  }) {
    return ElevatedButton.icon(
      onPressed: onPressed,
      icon: Icon(icon, size: 14),
      label: Text(
        label,
        style: const TextStyle(fontSize: 12),
      ),
      style: ElevatedButton.styleFrom(
        backgroundColor: color.shade700,
        foregroundColor: Colors.white,
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
        minimumSize: const Size(0, 32),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16),
        ),
      ),
    );
  }

  String _formatTime(DateTime timestamp) {
    return '${timestamp.hour.toString().padLeft(2, '0')}:'
           '${timestamp.minute.toString().padLeft(2, '0')}';
  }
}

/// BOM Details Dialog
class BOMDetailsDialog extends StatelessWidget {
  final Map<String, dynamic> calculationResults;

  const BOMDetailsDialog({
    super.key,
    required this.calculationResults,
  });

  @override
  Widget build(BuildContext context) {
    return Dialog(
      child: Container(
        width: 800,
        height: 600,
        padding: const EdgeInsets.all(24),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.list_alt, color: Colors.blue.shade700),
                const SizedBox(width: 12),
                const Text(
                  'Bill of Materials',
                  style: TextStyle(fontSize: 20, fontWeight: FontWeight.w600),
                ),
                const Spacer(),
                IconButton(
                  onPressed: () => Navigator.of(context).pop(),
                  icon: const Icon(Icons.close),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Expanded(
              child: _buildBOMContent(),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildBOMContent() {
    if (!calculationResults.containsKey('bom_items')) {
      return const Center(child: Text('No BOM data available'));
    }

    final bomItems = calculationResults['bom_items'] as List<Map<String, dynamic>>;
    final bomSummary = calculationResults['bom_summary'] as Map<String, dynamic>;

    return SingleChildScrollView(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Detailed BOM Items Table
          _buildBOMTable(bomItems),
          const SizedBox(height: 24),

          // Summary Section
          _buildBOMSection('Cost Summary', [
            _buildBOMRow('Ex-works Total (USD)', '\$${bomSummary['ex_works_total_usd']}'),
            _buildBOMRow('Landed Cost (SAR)', '${bomSummary['landed_cost_sar']} SAR'),
            _buildBOMRow('Installation Materials (SAR)', '${bomSummary['installation_materials_sar']} SAR'),
            _buildBOMRow('Installation Labor (SAR)', '${bomSummary['installation_labor_sar']} SAR'),
            const Divider(),
            _buildBOMRow('Grand Total (SAR)', '${bomSummary['grand_total_sar']} SAR', isTotal: true),
          ]),
        ],
      ),
    );
  }

  Widget _buildBOMTable(List<Map<String, dynamic>> bomItems) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Bill of Materials',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),

            // Table Header
            Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: Colors.grey.shade200,
                borderRadius: BorderRadius.circular(4),
              ),
              child: const Row(
                children: [
                  Expanded(flex: 2, child: Text('Part No.', style: TextStyle(fontWeight: FontWeight.bold))),
                  Expanded(flex: 4, child: Text('Description', style: TextStyle(fontWeight: FontWeight.bold))),
                  Expanded(flex: 1, child: Text('Qty', style: TextStyle(fontWeight: FontWeight.bold))),
                  Expanded(flex: 2, child: Text('Unit Cost', style: TextStyle(fontWeight: FontWeight.bold))),
                  Expanded(flex: 2, child: Text('Total', style: TextStyle(fontWeight: FontWeight.bold))),
                ],
              ),
            ),

            // Table Rows
            ...bomItems.map((item) => Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                border: Border(bottom: BorderSide(color: Colors.grey.shade300)),
              ),
              child: Row(
                children: [
                  Expanded(flex: 2, child: Text(item['part_no'] ?? '')),
                  Expanded(flex: 4, child: Text(item['description'] ?? '')),
                  Expanded(flex: 1, child: Text('${item['quantity']}')),
                  Expanded(flex: 2, child: Text('\$${item['unit_cost']}')),
                  Expanded(flex: 2, child: Text('\$${item['total_cost']}')),
                ],
              ),
            )),
          ],
        ),
      ),
    );
  }

  Widget _buildBOMSection(String title, List<Widget> rows) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              title,
              style: const TextStyle(fontSize: 16, fontWeight: FontWeight.w600),
            ),
            const SizedBox(height: 12),
            ...rows,
          ],
        ),
      ),
    );
  }

  Widget _buildBOMRow(String label, String value, {bool isTotal = false}) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        children: [
          Expanded(
            child: Text(
              label,
              style: TextStyle(
                fontWeight: isTotal ? FontWeight.bold : FontWeight.normal,
                fontSize: isTotal ? 16 : 14,
              ),
            ),
          ),
          Text(
            value,
            style: TextStyle(
              fontWeight: isTotal ? FontWeight.bold : FontWeight.normal,
              fontSize: isTotal ? 16 : 14,
              color: isTotal ? Colors.red.shade700 : null,
            ),
          ),
        ],
      ),
    );
  }
}

/// Calculation Details Dialog
class CalculationDetailsDialog extends StatelessWidget {
  final Map<String, dynamic> calculationResults;

  const CalculationDetailsDialog({
    super.key,
    required this.calculationResults,
  });

  @override
  Widget build(BuildContext context) {
    return Dialog(
      child: Container(
        width: 800,
        height: 600,
        padding: const EdgeInsets.all(24),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.calculate, color: Colors.green.shade700),
                const SizedBox(width: 12),
                const Text(
                  'Calculation Details',
                  style: TextStyle(fontSize: 20, fontWeight: FontWeight.w600),
                ),
                const Spacer(),
                IconButton(
                  onPressed: () => Navigator.of(context).pop(),
                  icon: const Icon(Icons.close),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Expanded(
              child: _buildCalculationContent(),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildCalculationContent() {
    if (!calculationResults.containsKey('design_results')) {
      return const Center(child: Text('No calculation data available'));
    }

    final designResults = calculationResults['design_results'] as Map<String, dynamic>;
    final formInputs = calculationResults['form_inputs'] as Map<String, dynamic>? ?? {};
    
    return SingleChildScrollView(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildCalcSection('Input Parameters', [
            _buildCalcRow('Agent Type', formInputs['agent_type']?.toString() ?? 'N/A'),
            _buildCalcRow('Design Concentration', formInputs['design_concentration']?.toString() ?? 'N/A'),
            _buildCalcRow('Agent Quantity', '${formInputs['agent_quantity']}kg'),
            _buildCalcRow('System Type', formInputs['system_type']?.toString() ?? 'N/A'),
          ]),
          const SizedBox(height: 16),
          _buildCalcSection('Design Results', [
            _buildCalcRow('Agent Weight Required', '${designResults['agent_weight_required']}kg'),
            _buildCalcRow('Room Volume', '${designResults['room_volume']}m³'),
            if (designResults.containsKey('room_dimensions')) ...[
              () {
                final dimensions = designResults['room_dimensions'] as Map<String, dynamic>;
                return _buildCalcRow('Room Dimensions', '${dimensions['length']}m × ${dimensions['width']}m × ${dimensions['height']}m');
              }(),
            ],
          ]),
        ],
      ),
    );
  }

  Widget _buildCalcSection(String title, List<Widget> rows) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              title,
              style: const TextStyle(fontSize: 16, fontWeight: FontWeight.w600),
            ),
            const SizedBox(height: 12),
            ...rows,
          ],
        ),
      ),
    );
  }

  Widget _buildCalcRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        children: [
          Expanded(
            flex: 2,
            child: Text(
              label,
              style: const TextStyle(fontWeight: FontWeight.w500),
            ),
          ),
          Expanded(
            flex: 3,
            child: Text(value),
          ),
        ],
      ),
    );
  }
}
