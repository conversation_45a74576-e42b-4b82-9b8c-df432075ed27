import 'dart:convert';
import 'package:supabase_flutter/supabase_flutter.dart';
import '../config/supabase_config.dart';
import '../models/isar_models.dart';
import 'dynamic_schema_service.dart';

class SupabaseSyncService {
  final DynamicSchemaService _schemaService;
  SupabaseClient? _supabase;

  SupabaseSyncService(this._schemaService);

  // Initialize Supabase client
  Future<void> initialize() async {
    if (!SupabaseConfig.isConfigured) {
      throw Exception('Supabase not configured. Please set up company settings first.');
    }

    try {
      await Supabase.initialize(
        url: SupabaseConfig.supabaseUrl!,
        anonKey: SupabaseConfig.supabaseAnonKey!,
      );
      _supabase = Supabase.instance.client;
    } catch (e) {
      // If already initialized, just get the client
      _supabase = Supabase.instance.client;
    }
  }

  // Upload table to Supabase (create if doesn't exist)
  Future<void> uploadTableToSupabase(String tableId) async {
    await _ensureInitialized();

    final table = await _schemaService.getTable(tableId);
    if (table == null) {
      throw Exception('Table not found');
    }

    // Get section information for enhanced naming
    String? sectionName;
    if (table.sectionId != null) {
      final sections = await _schemaService.getAllSections();
      final section = sections.where((s) => s.sectionId == table.sectionId).firstOrNull;
      sectionName = section?.name;
    }

    final columns = await _schemaService.getColumnsForTable(tableId);
    final rows = await _schemaService.getRowsForTable(tableId);

    // Use enhanced table naming with section hierarchy
    final supabaseTableName = SupabaseConfig.getEnhancedTableName(
      table.tableId!,
      sectionName,
      table.name,
    );

    print('DEBUG: Upload table name: $supabaseTableName');
    print('DEBUG: Company: ${SupabaseConfig.currentCompany}');
    print('DEBUG: Section: $sectionName');
    print('DEBUG: Table: ${table.name}');

    // Check if table exists, create if not
    try {
      await _createTableIfNotExists(supabaseTableName, columns);
      print('DEBUG: Table creation/check completed successfully');
    } catch (e) {
      print('DEBUG: Table creation failed: $e');
      throw Exception('Failed to create table: $e');
    }

    // Clear existing data (delete all rows)
    try {
      await _supabase!.from(supabaseTableName).delete().gte('id', 0);
    } catch (e) {
      print('DEBUG: Clear table failed (table might not exist yet): $e');
    }

    // Upload data
    if (rows.isNotEmpty) {
      print('DEBUG: Found ${rows.length} rows to upload');

      final supabaseRows = rows.map((row) {
        // Parse row data from JSON string
        final Map<String, dynamic> originalRowData = row.data != null
            ? jsonDecode(row.data!) as Map<String, dynamic>
            : <String, dynamic>{};

        print('DEBUG: Original row data: $originalRowData');

        // Create new row data with readable column names
        final Map<String, dynamic> rowData = <String, dynamic>{};

        // Add metadata with cleaner names
        rowData['row_id'] = row.rowId;
        rowData['sync_created'] = row.createdAt?.toIso8601String() ?? DateTime.now().toIso8601String();
        rowData['sync_updated'] = row.updatedAt?.toIso8601String() ?? DateTime.now().toIso8601String();

        // Map original column data to readable column names
        for (final column in columns) {
          final originalColumnId = column.columnId!;
          final readableColumnName = _createReadableColumnName(column);
          final value = originalRowData[originalColumnId];

          print('DEBUG: Column ${column.name} ($originalColumnId) -> $readableColumnName = $value');

          if (value != null) {
            // Convert value to appropriate type for Supabase
            rowData[readableColumnName] = _convertValueForSupabase(value, column.dataType);
          } else {
            // Provide default value based on column type
            rowData[readableColumnName] = _getDefaultValue(column.dataType);
          }
        }

        print('DEBUG: Final row data: $rowData');
        return rowData;
      }).toList();

      print('DEBUG: About to insert ${supabaseRows.length} rows into $supabaseTableName');
      await _supabase!.from(supabaseTableName).insert(supabaseRows);
      print('DEBUG: Insert completed successfully');
    } else {
      print('DEBUG: No rows to upload');
    }
  }

  // Download table from Supabase
  Future<void> downloadTableFromSupabase(String tableId) async {
    await _ensureInitialized();

    final table = await _schemaService.getTable(tableId);
    if (table == null) {
      throw Exception('Table not found');
    }

    // Get section information for enhanced naming
    String? sectionName;
    if (table.sectionId != null) {
      final sections = await _schemaService.getAllSections();
      final section = sections.where((s) => s.sectionId == table.sectionId).firstOrNull;
      sectionName = section?.name;
    }

    // Use enhanced table naming with section hierarchy
    final supabaseTableName = SupabaseConfig.getEnhancedTableName(
      table.tableId!,
      sectionName,
      table.name,
    );

    try {
      // Fetch data from Supabase
      final response = await _supabase!.from(supabaseTableName).select();

      if (response.isEmpty) return;

      // Clear local data
      await _schemaService.deleteAllRowsForTable(tableId);

      // Get columns for mapping back to original column IDs
      final columns = await _schemaService.getColumnsForTable(tableId);

      // Insert downloaded data
      for (final supabaseRow in response) {
        final Map<String, dynamic> supabaseData = Map.from(supabaseRow);

        // Remove metadata fields
        supabaseData.remove('row_id');
        supabaseData.remove('sync_created');
        supabaseData.remove('sync_updated');
        supabaseData.remove('id'); // Remove Supabase auto-generated ID

        // Map readable column names back to original column IDs
        final Map<String, dynamic> originalRowData = <String, dynamic>{};

        for (final column in columns) {
          final originalColumnId = column.columnId!;
          final readableColumnName = _createReadableColumnName(column);

          if (supabaseData.containsKey(readableColumnName)) {
            originalRowData[originalColumnId] = supabaseData[readableColumnName];
          }
        }

        // Create local row using the service method
        final rowId = await _schemaService.createRow(tableId);
        await _schemaService.updateRowData(rowId, originalRowData);
      }
    } catch (e) {
      if (e.toString().contains('relation') && e.toString().contains('does not exist')) {
        throw Exception('Table does not exist in Supabase. Please upload first.');
      }
      rethrow;
    }
  }

  // Create table in Supabase if it doesn't exist
  Future<void> _createTableIfNotExists(String tableName, List<FlexibleColumn> columns) async {
    try {
      // Try to query the table to see if it exists
      await _supabase!.from(tableName).select().limit(1);
    } catch (e) {
      if (e.toString().contains('relation') && e.toString().contains('does not exist')) {
        // Table doesn't exist, create it
        await _createSupabaseTable(tableName, columns);
      } else {
        rethrow;
      }
    }
  }

  // Create table in Supabase using SQL
  Future<void> _createSupabaseTable(String tableName, List<FlexibleColumn> columns) async {
    final columnDefinitions = <String>[];

    // Add auto-increment ID column
    columnDefinitions.add('id BIGSERIAL PRIMARY KEY');

    // Add metadata columns (cleaner names)
    columnDefinitions.add('row_id TEXT UNIQUE'); // Maps to ISAR row ID
    columnDefinitions.add('sync_created TIMESTAMPTZ DEFAULT NOW()');
    columnDefinitions.add('sync_updated TIMESTAMPTZ DEFAULT NOW()');

    // Add user-defined columns with readable names
    for (final column in columns) {
      final sqlType = _getSupabaseColumnType(column.dataType);
      final readableColumnName = _createReadableColumnName(column);
      columnDefinitions.add('$readableColumnName $sqlType');
    }

    final createTableSQL = '''
      CREATE TABLE IF NOT EXISTS "$tableName" (
        ${columnDefinitions.join(',\n        ')}
      );
    ''';

    // Try to use exec_sql function first, fallback to direct table creation
    try {
      await _supabase!.rpc('exec_sql', params: {'sql': createTableSQL});
    } catch (e) {
      // If exec_sql doesn't exist, try alternative approach
      // Create a simple table structure that Supabase can handle
      try {
        // First, try to insert a dummy row to trigger table creation
        final dummyData = <String, dynamic>{
          'row_id': 'dummy',
          'sync_created': DateTime.now().toIso8601String(),
          'sync_updated': DateTime.now().toIso8601String(),
        };

        // Add dummy values for user columns with readable names
        for (final column in columns) {
          dummyData[_createReadableColumnName(column)] = _getDefaultValue(column.dataType);
        }

        await _supabase!.from(tableName).insert(dummyData);

        // Delete the dummy row
        await _supabase!.from(tableName).delete().eq('isar_id', 'dummy');

      } catch (insertError) {
        throw Exception('Unable to create table in Supabase. Please set up the exec_sql function or create the table manually. Original error: $e, Insert error: $insertError');
      }
    }
  }

  // Get default value for column type
  dynamic _getDefaultValue(ColumnDataType dataType) {
    switch (dataType) {
      case ColumnDataType.text:
      case ColumnDataType.dropdown:
        return '';
      case ColumnDataType.number:
      case ColumnDataType.currency:
        return 0;
      case ColumnDataType.boolean:
        return false;
      case ColumnDataType.date:
        return DateTime.now().toIso8601String().split('T')[0]; // YYYY-MM-DD format
    }
  }

  // Convert ISAR column type to Supabase/PostgreSQL type
  String _getSupabaseColumnType(ColumnDataType? type) {
    switch (type) {
      case ColumnDataType.text:
        return 'TEXT';
      case ColumnDataType.number:
        return 'NUMERIC';
      case ColumnDataType.currency:
        return 'NUMERIC(15,2)';
      case ColumnDataType.boolean:
        return 'BOOLEAN';
      case ColumnDataType.date:
        return 'DATE';
      case ColumnDataType.dropdown:
        return 'TEXT';
      default:
        return 'TEXT';
    }
  }

  // Sanitize column name for SQL
  String _sanitizeColumnName(String columnName) {
    // Replace invalid characters with underscores and ensure it starts with letter or underscore
    String sanitized = columnName.replaceAll(RegExp(r'[^a-zA-Z0-9_]'), '_');

    // Ensure column name starts with letter or underscore (PostgreSQL requirement)
    if (sanitized.isNotEmpty && !RegExp(r'^[a-zA-Z_]').hasMatch(sanitized)) {
      sanitized = 'col_$sanitized';
    }

    // Ensure it's not empty
    if (sanitized.isEmpty) {
      sanitized = 'unnamed_column';
    }

    return sanitized.toLowerCase(); // PostgreSQL prefers lowercase
  }

  // Create readable column name from FlexibleColumn
  String _createReadableColumnName(FlexibleColumn column) {
    // Use column name if available, otherwise use sanitized column ID
    String baseName = column.name?.isNotEmpty == true
        ? column.name!
        : column.columnId!;

    // Sanitize for SQL
    return _sanitizeColumnName(baseName);
  }

  // Convert value to appropriate type for Supabase
  dynamic _convertValueForSupabase(dynamic value, ColumnDataType dataType) {
    if (value == null) return null;

    try {
      // Handle different data types
      switch (dataType) {
        case ColumnDataType.number:
        case ColumnDataType.currency:
          // Convert to double for numeric types
          if (value is String) {
            final parsed = double.tryParse(value);
            return parsed ?? 0.0;
          } else if (value is int) {
            return value.toDouble();
          } else if (value is BigInt) {
            return value.toDouble();
          } else if (value is double) {
            return value;
          } else if (value is num) {
            return value.toDouble();
          }
          return 0.0;

        case ColumnDataType.text:
        default:
          // Convert everything else to string, handling special types
          if (value is BigInt) {
            return value.toString();
          } else if (value is DateTime) {
            return value.toIso8601String();
          } else {
            return value.toString();
          }
      }
    } catch (e) {
      print('DEBUG: Error converting value $value of type ${value.runtimeType}: $e');
      // Return safe default
      switch (dataType) {
        case ColumnDataType.number:
        case ColumnDataType.currency:
          return 0.0;
        case ColumnDataType.text:
        default:
          return value?.toString() ?? '';
      }
    }
  }

  // Ensure Supabase is initialized
  Future<void> _ensureInitialized() async {
    if (_supabase == null) {
      await initialize();
    }
  }

  // Get sync status for a table
  Future<Map<String, dynamic>> getTableSyncStatus(String tableId) async {
    await _ensureInitialized();

    final table = await _schemaService.getTable(tableId);
    if (table == null) {
      return {'error': 'Table not found'};
    }

    // Get section information for enhanced naming
    String? sectionName;
    if (table.sectionId != null) {
      final sections = await _schemaService.getAllSections();
      final section = sections.where((s) => s.sectionId == table.sectionId).firstOrNull;
      sectionName = section?.name;
    }

    final localRows = await _schemaService.getRowsForTable(tableId);

    // Use enhanced table naming with section hierarchy
    final supabaseTableName = SupabaseConfig.getEnhancedTableName(
      table.tableId!,
      sectionName,
      table.name,
    );

    try {
      final response = await _supabase!.from(supabaseTableName).select('id').count();
      final remoteCount = response.count;

      return {
        'local_count': localRows.length,
        'remote_count': remoteCount,
        'table_exists': true,
      };
    } catch (e) {
      return {
        'local_count': localRows.length,
        'remote_count': 0,
        'table_exists': false,
        'error': e.toString(),
      };
    }
  }
}
