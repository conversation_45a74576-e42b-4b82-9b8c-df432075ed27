import React, { useState } from 'react';
import {
  ThemeProvider,
  createTheme,
  CssBaseline,
  AppBar,
  Toolbar,
  Typography,
  Container,
  Grid,
  Card,
  CardContent,
  CardActions,
  Button,
  Box,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  MenuItem,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  IconButton,
  Fab,
  Chip,
} from '@mui/material';
import {
  Fire,
  Add,
  FolderOpen,
  Calculator,
  SmartToy,
  Settings,
  Dashboard,
  Water,
  Cloud,
  Shield,
  Build,
  Edit,
  Delete,
  Home,
} from '@mui/icons-material';

const theme = createTheme({
  palette: {
    primary: { main: '#0078D4' },
    secondary: { main: '#FFB900' },
    background: { default: '#F3F2F1', paper: '#FFFFFF' },
  },
  typography: { fontFamily: '"Segoe UI", "Roboto", "Arial", sans-serif' },
});

const sampleItems = [
  {
    id: 1, model: 'FM200-25KG', description: 'FM200 Fire Suppression Cylinder 25kg',
    manufacturer: 'Kidde', approval: 'UL Listed', exWorksPrice: 1200,
    localPrice: 1500, installationPrice: 200, systemType: 'cleanAgent',
  },
  {
    id: 2, model: 'NOVEC-50KG', description: 'NOVEC 1230 Fire Suppression Cylinder 50kg',
    manufacturer: '3M', approval: 'FM Approved', exWorksPrice: 2000,
    localPrice: 2500, installationPrice: 300, systemType: 'cleanAgent',
  },
];

function App() {
  const [currentView, setCurrentView] = useState('home');
  const [items, setItems] = useState(sampleItems);
  const [openDialog, setOpenDialog] = useState(false);
  const [newItem, setNewItem] = useState({
    model: '', description: '', manufacturer: '', approval: '',
    exWorksPrice: 0, localPrice: 0, installationPrice: 0, systemType: 'cleanAgent',
  });

  const handleAddItem = () => {
    setItems([...items, { ...newItem, id: items.length + 1 }]);
    setOpenDialog(false);
    setNewItem({
      model: '', description: '', manufacturer: '', approval: '',
      exWorksPrice: 0, localPrice: 0, installationPrice: 0, systemType: 'cleanAgent',
    });
  };

  const renderHome = () => (
    <Container maxWidth="lg" sx={{ mt: 4, mb: 4 }}>
      <Box textAlign="center" mb={4}>
        <Fire sx={{ fontSize: 80, color: theme.palette.primary.main, mb: 2 }} />
        <Typography variant="h3" component="h1" gutterBottom>
          FireTool Web
        </Typography>
        <Typography variant="h6" color="text.secondary">
          Fire System Estimating Tool - Complete Windows-Style Interface
        </Typography>
      </Box>

      <Grid container spacing={3}>
        {[
          { icon: Add, title: 'New Project', desc: 'Start a new fire system project', view: 'newProject' },
          { icon: FolderOpen, title: 'Open Project', desc: 'Continue working on existing projects', view: 'projects' },
          { icon: Calculator, title: 'Quick Quote', desc: 'Generate fast estimates without projects', view: 'calculator' },
          { icon: SmartToy, title: 'AI Assistant', desc: 'Get intelligent help with calculations', view: 'ai' },
          { icon: Dashboard, title: 'Admin Dashboard', desc: 'Manage database, settings, and backups', view: 'admin' },
          { icon: Settings, title: 'Settings', desc: 'Configure app preferences and defaults', view: 'settings' },
        ].map((item, index) => (
          <Grid item xs={12} sm={6} md={4} key={index}>
            <Card sx={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
              <CardContent sx={{ flexGrow: 1, textAlign: 'center' }}>
                <item.icon sx={{ fontSize: 48, color: theme.palette.primary.main, mb: 2 }} />
                <Typography variant="h6" component="h2">{item.title}</Typography>
                <Typography color="text.secondary">{item.desc}</Typography>
              </CardContent>
              <CardActions>
                <Button size="large" fullWidth onClick={() => setCurrentView(item.view)}>
                  {item.title === 'New Project' ? 'Create Project' : 
                   item.title === 'Open Project' ? 'Browse Projects' :
                   item.title === 'Quick Quote' ? 'Calculate' :
                   item.title === 'AI Assistant' ? 'Chat with AI' :
                   item.title === 'Admin Dashboard' ? 'Manage System' : 'Open Settings'}
                </Button>
              </CardActions>
            </Card>
          </Grid>
        ))}
      </Grid>
    </Container>
  );

  const renderDatabase = () => (
    <Container maxWidth="lg" sx={{ mt: 4, mb: 4 }}>
      <Box display="flex" justifyContent="space-between" alignItems="center" mb={3}>
        <Typography variant="h4">Database Management</Typography>
        <Button variant="contained" startIcon={<Add />} onClick={() => setOpenDialog(true)}>
          Add Item
        </Button>
      </Box>

      <TableContainer component={Paper}>
        <Table>
          <TableHead>
            <TableRow>
              <TableCell>Model</TableCell>
              <TableCell>Description</TableCell>
              <TableCell>Manufacturer</TableCell>
              <TableCell>Approval</TableCell>
              <TableCell align="right">Ex-Works Price</TableCell>
              <TableCell align="right">Local Price</TableCell>
              <TableCell align="right">Installation Price</TableCell>
              <TableCell>System Type</TableCell>
              <TableCell>Actions</TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {items.map((item) => (
              <TableRow key={item.id}>
                <TableCell>{item.model}</TableCell>
                <TableCell>{item.description}</TableCell>
                <TableCell>{item.manufacturer}</TableCell>
                <TableCell>
                  <Chip label={item.approval} size="small" color="primary" />
                </TableCell>
                <TableCell align="right">${item.exWorksPrice.toLocaleString()}</TableCell>
                <TableCell align="right">${item.localPrice.toLocaleString()}</TableCell>
                <TableCell align="right">${item.installationPrice.toLocaleString()}</TableCell>
                <TableCell>
                  <Chip label={item.systemType} size="small" />
                </TableCell>
                <TableCell>
                  <IconButton size="small"><Edit /></IconButton>
                  <IconButton size="small"><Delete /></IconButton>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </TableContainer>
    </Container>
  );

  const renderCalculator = () => (
    <Container maxWidth="md" sx={{ mt: 4, mb: 4 }}>
      <Typography variant="h4" gutterBottom>Quick Quote Calculator</Typography>
      <Card sx={{ p: 3 }}>
        <Grid container spacing={3}>
          <Grid item xs={12} sm={6}>
            <TextField fullWidth label="Room Volume (m³)" type="number" />
          </Grid>
          <Grid item xs={12} sm={6}>
            <TextField fullWidth label="Agent Type" select defaultValue="FM200">
              <MenuItem value="FM200">FM200</MenuItem>
              <MenuItem value="NOVEC">NOVEC 1230</MenuItem>
              <MenuItem value="CO2">CO2</MenuItem>
            </TextField>
          </Grid>
          <Grid item xs={12}>
            <Button variant="contained" size="large" fullWidth>
              Calculate System Requirements
            </Button>
          </Grid>
        </Grid>
      </Card>
    </Container>
  );

  const renderAI = () => (
    <Container maxWidth="md" sx={{ mt: 4, mb: 4 }}>
      <Typography variant="h4" gutterBottom>AI Assistant</Typography>
      <Card sx={{ p: 3, minHeight: 400 }}>
        <Typography variant="body1" color="text.secondary" textAlign="center" sx={{ mt: 10 }}>
          AI Assistant interface would be implemented here with chat functionality,
          voice input, and intelligent system recommendations.
        </Typography>
      </Card>
    </Container>
  );

  return (
    <ThemeProvider theme={theme}>
      <CssBaseline />
      <AppBar position="static">
        <Toolbar>
          <Fire sx={{ mr: 2 }} />
          <Typography variant="h6" component="div" sx={{ flexGrow: 1 }}>
            FireTool Web - Fire System Estimating Tool
          </Typography>
          {currentView !== 'home' && (
            <Button color="inherit" startIcon={<Home />} onClick={() => setCurrentView('home')}>
              Home
            </Button>
          )}
        </Toolbar>
      </AppBar>

      {currentView === 'home' && renderHome()}
      {currentView === 'admin' && renderDatabase()}
      {currentView === 'calculator' && renderCalculator()}
      {currentView === 'ai' && renderAI()}
      {(currentView === 'projects' || currentView === 'newProject' || currentView === 'settings') && (
        <Container maxWidth="md" sx={{ mt: 4, mb: 4 }}>
          <Typography variant="h4" gutterBottom>
            {currentView === 'projects' ? 'Projects' : 
             currentView === 'newProject' ? 'New Project' : 'Settings'}
          </Typography>
          <Card sx={{ p: 3 }}>
            <Typography variant="body1" color="text.secondary" textAlign="center">
              {currentView} interface would be implemented here with full functionality.
            </Typography>
          </Card>
        </Container>
      )}

      <Dialog open={openDialog} onClose={() => setOpenDialog(false)} maxWidth="md" fullWidth>
        <DialogTitle>Add New Item</DialogTitle>
        <DialogContent>
          <Grid container spacing={2} sx={{ mt: 1 }}>
            <Grid item xs={12} sm={6}>
              <TextField fullWidth label="Model" value={newItem.model}
                onChange={(e) => setNewItem({...newItem, model: e.target.value})} />
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField fullWidth label="Manufacturer" value={newItem.manufacturer}
                onChange={(e) => setNewItem({...newItem, manufacturer: e.target.value})} />
            </Grid>
            <Grid item xs={12}>
              <TextField fullWidth label="Description" value={newItem.description}
                onChange={(e) => setNewItem({...newItem, description: e.target.value})} />
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField fullWidth label="Approval" value={newItem.approval}
                onChange={(e) => setNewItem({...newItem, approval: e.target.value})} />
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField fullWidth label="System Type" select value={newItem.systemType}
                onChange={(e) => setNewItem({...newItem, systemType: e.target.value})}>
                <MenuItem value="cleanAgent">Clean Agent</MenuItem>
                <MenuItem value="water">Water Sprinkler</MenuItem>
                <MenuItem value="foam">Foam System</MenuItem>
              </TextField>
            </Grid>
            <Grid item xs={12} sm={4}>
              <TextField fullWidth label="Ex-Works Price" type="number" value={newItem.exWorksPrice}
                onChange={(e) => setNewItem({...newItem, exWorksPrice: Number(e.target.value)})} />
            </Grid>
            <Grid item xs={12} sm={4}>
              <TextField fullWidth label="Local Price" type="number" value={newItem.localPrice}
                onChange={(e) => setNewItem({...newItem, localPrice: Number(e.target.value)})} />
            </Grid>
            <Grid item xs={12} sm={4}>
              <TextField fullWidth label="Installation Price" type="number" value={newItem.installationPrice}
                onChange={(e) => setNewItem({...newItem, installationPrice: Number(e.target.value)})} />
            </Grid>
          </Grid>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setOpenDialog(false)}>Cancel</Button>
          <Button onClick={handleAddItem} variant="contained">Add Item</Button>
        </DialogActions>
      </Dialog>
    </ThemeProvider>
  );
}

export default App;
