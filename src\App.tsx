import React, { useState } from 'react';
import {
  ThemeProvider,
  createTheme,
  CssBaseline,
  AppBar,
  Too<PERSON>bar,
  Typography,
  Container,
  Grid,
  Card,
  CardContent,
  CardActions,
  Button,
  Box,
} from '@mui/material';

const theme = createTheme({
  palette: {
    primary: { main: '#0078D4' },
    secondary: { main: '#FFB900' },
    background: { default: '#F3F2F1', paper: '#FFFFFF' },
  },
  typography: { fontFamily: '"Segoe UI", "Roboto", "Arial", sans-serif' },
});

function App() {
  const [currentView, setCurrentView] = useState('home');

  return (
    <ThemeProvider theme={theme}>
      <CssBaseline />
      <AppBar position="static">
        <Toolbar>
          <Typography variant="h6" component="div" sx={{ flexGrow: 1 }}>
            🔥 FireTool Web - Fire System Estimating Tool
          </Typography>
        </Toolbar>
      </AppBar>

      <Container maxWidth="lg" sx={{ mt: 4, mb: 4 }}>
        <Box textAlign="center" mb={4}>
          <Typography variant="h2" component="h1" gutterBottom sx={{ color: '#0078D4' }}>
            🔥 FireTool Web App
          </Typography>
          <Typography variant="h5" color="text.secondary">
            Fire System Estimating Tool - Complete Windows-Style Interface
          </Typography>
        </Box>

        <Grid container spacing={3}>
          {[
            { title: 'New Project', desc: 'Start a new fire system project' },
            { title: 'Open Project', desc: 'Continue working on existing projects' },
            { title: 'Quick Quote', desc: 'Generate fast estimates without projects' },
            { title: 'AI Assistant', desc: 'Get intelligent help with calculations' },
            { title: 'Admin Dashboard', desc: 'Manage database, settings, and backups' },
            { title: 'Settings', desc: 'Configure app preferences and defaults' },
          ].map((item, index) => (
            <Grid item xs={12} sm={6} md={4} key={index}>
              <Card sx={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
                <CardContent sx={{ flexGrow: 1, textAlign: 'center' }}>
                  <Typography variant="h6" component="h2" sx={{ mb: 2 }}>
                    {item.title}
                  </Typography>
                  <Typography color="text.secondary">{item.desc}</Typography>
                </CardContent>
                <CardActions>
                  <Button size="large" fullWidth variant="contained">
                    Open {item.title}
                  </Button>
                </CardActions>
              </Card>
            </Grid>
          ))}
        </Grid>

        <Box sx={{ mt: 6, p: 3, backgroundColor: '#f5f5f5', borderRadius: 2 }}>
          <Typography variant="h4" gutterBottom>
            Sample Database Items
          </Typography>
          <Grid container spacing={2}>
            <Grid item xs={12} md={6}>
              <Card sx={{ p: 2 }}>
                <Typography variant="h6">FM200-25KG</Typography>
                <Typography color="text.secondary">FM200 Fire Suppression Cylinder 25kg</Typography>
                <Typography>Manufacturer: Kidde | Approval: UL Listed</Typography>
                <Typography>Price: $1,500 + $200 installation</Typography>
              </Card>
            </Grid>
            <Grid item xs={12} md={6}>
              <Card sx={{ p: 2 }}>
                <Typography variant="h6">NOVEC-50KG</Typography>
                <Typography color="text.secondary">NOVEC 1230 Fire Suppression Cylinder 50kg</Typography>
                <Typography>Manufacturer: 3M | Approval: FM Approved</Typography>
                <Typography>Price: $2,500 + $300 installation</Typography>
              </Card>
            </Grid>
          </Grid>
        </Box>
      </Container>
    </ThemeProvider>
  );
}

export default App;
