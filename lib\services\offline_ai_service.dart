import 'dart:convert';
import 'dart:io';
import 'package:path_provider/path_provider.dart';
import 'package:flutter/foundation.dart';
import '../bridge_generated.dart';

/// Offline AI Service using local LLM via Rust backend
class OfflineAIService {
  static final OfflineAIService _instance = OfflineAIService._internal();
  factory OfflineAIService() => _instance;
  OfflineAIService._internal();

  FireToolAIAgent? _aiAgent;
  bool _isInitialized = false;
  String? _modelsDirectory;

  /// Initialize the offline AI service
  Future<bool> initialize() async {
    if (_isInitialized) return _aiAgent != null;

    try {
      // Initialize Rust backend
      await RustLib.init();
      
      // Create AI agent instance
      _aiAgent = FireToolAIAgent();
      
      // Setup models directory
      await _setupModelsDirectory();
      
      _isInitialized = true;
      debugPrint('✅ Offline AI Service initialized');
      return true;
    } catch (e) {
      debugPrint('❌ Failed to initialize Offline AI Service: $e');
      return false;
    }
  }

  /// Setup models directory for storing LLM files
  Future<void> _setupModelsDirectory() async {
    final appDir = await getApplicationDocumentsDirectory();
    _modelsDirectory = '${appDir.path}/ai_models';
    
    final modelsDir = Directory(_modelsDirectory!);
    if (!await modelsDir.exists()) {
      await modelsDir.create(recursive: true);
    }
  }

  /// Check if AI is ready to use
  bool get isReady {
    return _aiAgent?.isReady() ?? false;
  }

  /// Get available models
  List<ModelInfo> get availableModels {
    return _aiAgent?.getAvailableModels() ?? [];
  }

  /// Load a specific model
  Future<bool> loadModel(String modelPath) async {
    if (_aiAgent == null) return false;

    try {
      // modelPath is already the full path from ModelManager.getModelPath()
      final result = _aiAgent!.loadModel(modelPath: modelPath);

      if (result) {
        debugPrint('✅ Model loaded: $modelPath');
      } else {
        debugPrint('❌ Failed to load model: $modelPath');
      }

      return result;
    } catch (e) {
      debugPrint('❌ Error loading model: $e');
      return false;
    }
  }

  /// Update app context for AI
  Future<void> updateContext(AppContextData context) async {
    if (_aiAgent == null) return;

    try {
      final contextJson = jsonEncode(context.toJson());
      _aiAgent!.updateContext(contextJson: contextJson);
    } catch (e) {
      debugPrint('❌ Error updating context: $e');
    }
  }

  /// Process user query with AI
  Future<AIResponseData?> processQuery(String query) async {
    if (_aiAgent == null || !isReady) {
      return AIResponseData(
        message: 'AI agent not ready. Please load a model first.',
        toolCalls: [],
        contextUsed: [],
        processingTimeMs: 0,
      );
    }

    try {
      final response = await _aiAgent!.processQuery(query: query);
      return AIResponseData.fromRustResponse(response);
    } catch (e) {
      debugPrint('❌ Error processing query: $e');
      return AIResponseData(
        message: 'Sorry, I encountered an error processing your request: $e',
        toolCalls: [],
        contextUsed: [],
        processingTimeMs: 0,
      );
    }
  }

  /// Clear conversation history
  void clearHistory() {
    _aiAgent?.clearHistory();
  }

  /// Get conversation history
  List<ChatMessageData> getHistory() {
    if (_aiAgent == null) return [];
    
    try {
      final history = _aiAgent!.getHistory();
      return history.map((msg) => ChatMessageData.fromRustMessage(msg)).toList();
    } catch (e) {
      debugPrint('❌ Error getting history: $e');
      return [];
    }
  }

  /// Download a model (placeholder for future implementation)
  Future<bool> downloadModel(String modelId, {Function(double)? onProgress}) async {
    // This will be implemented to download models from HuggingFace
    debugPrint('Model download not yet implemented: $modelId');
    return false;
  }

  /// Get models directory path
  String? get modelsDirectory => _modelsDirectory;
}

/// App context data for AI
class AppContextData {
  final String? currentScreen;
  final String? projectName;
  final String? projectCurrency;
  final List<String> availableData;
  final Map<String, String> userInput;
  final DatabaseStateData databaseState;
  final CalculationContextData calculationContext;

  AppContextData({
    this.currentScreen,
    this.projectName,
    this.projectCurrency,
    this.availableData = const [],
    this.userInput = const {},
    required this.databaseState,
    required this.calculationContext,
  });

  Map<String, dynamic> toJson() => {
    'current_screen': currentScreen,
    'project_name': projectName,
    'project_currency': projectCurrency,
    'available_data': availableData,
    'user_input': userInput,
    'database_state': databaseState.toJson(),
    'calculation_context': calculationContext.toJson(),
  };
}

/// Database state data
class DatabaseStateData {
  final List<String> sections;
  final List<String> tables;
  final List<String> recentItems;
  final int totalRecords;

  DatabaseStateData({
    this.sections = const [],
    this.tables = const [],
    this.recentItems = const [],
    this.totalRecords = 0,
  });

  Map<String, dynamic> toJson() => {
    'sections': sections,
    'tables': tables,
    'recent_items': recentItems,
    'total_records': totalRecords,
  };
}

/// Calculation context data
class CalculationContextData {
  final String? lastCalculationType;
  final Map<String, double> currentValues;
  final List<String> availableFormulas;
  final List<CalculationResultData> recentResults;

  CalculationContextData({
    this.lastCalculationType,
    this.currentValues = const {},
    this.availableFormulas = const [],
    this.recentResults = const [],
  });

  Map<String, dynamic> toJson() => {
    'last_calculation_type': lastCalculationType,
    'current_values': currentValues,
    'available_formulas': availableFormulas,
    'recent_results': recentResults.map((r) => r.toJson()).toList(),
  };
}

/// Calculation result data
class CalculationResultData {
  final String calculationType;
  final Map<String, double> inputs;
  final double result;
  final int timestamp;

  CalculationResultData({
    required this.calculationType,
    required this.inputs,
    required this.result,
    required this.timestamp,
  });

  Map<String, dynamic> toJson() => {
    'calculation_type': calculationType,
    'inputs': inputs,
    'result': result,
    'timestamp': timestamp,
  };
}

/// AI response data
class AIResponseData {
  final String message;
  final List<ToolCallData> toolCalls;
  final List<String> contextUsed;
  final int processingTimeMs;

  AIResponseData({
    required this.message,
    required this.toolCalls,
    required this.contextUsed,
    required this.processingTimeMs,
  });

  static AIResponseData fromRustResponse(AiResponse response) {
    return AIResponseData(
      message: response.message,
      toolCalls: response.toolCalls.map((tc) => ToolCallData.fromRustToolCall(tc)).toList(),
      contextUsed: response.contextUsed,
      processingTimeMs: response.processingTimeMs.toInt(),
    );
  }
}

/// Tool call data
class ToolCallData {
  final String id;
  final String functionName;
  final Map<String, dynamic> parameters;
  final ToolResultData? result;

  ToolCallData({
    required this.id,
    required this.functionName,
    required this.parameters,
    this.result,
  });

  static ToolCallData fromRustToolCall(ToolCall toolCall) {
    return ToolCallData(
      id: toolCall.id,
      functionName: toolCall.functionName,
      parameters: Map<String, dynamic>.from(toolCall.parameters),
      result: toolCall.result != null ? ToolResultData.fromRustResult(toolCall.result!) : null,
    );
  }
}

/// Tool result data
class ToolResultData {
  final bool success;
  final dynamic data;
  final String message;
  final int executionTimeMs;

  ToolResultData({
    required this.success,
    required this.data,
    required this.message,
    required this.executionTimeMs,
  });

  static ToolResultData fromRustResult(ToolResult result) {
    return ToolResultData(
      success: result.success,
      data: result.data,
      message: result.message,
      executionTimeMs: result.executionTimeMs.toInt(),
    );
  }
}

/// Chat message data
class ChatMessageData {
  final String role;
  final String content;
  final DateTime timestamp;
  final List<ToolCallData>? toolCalls;

  ChatMessageData({
    required this.role,
    required this.content,
    required this.timestamp,
    this.toolCalls,
  });

  static ChatMessageData fromRustMessage(ChatMessage message) {
    return ChatMessageData(
      role: message.role,
      content: message.content,
      timestamp: DateTime.fromMillisecondsSinceEpoch(message.timestamp * 1000),
      toolCalls: message.toolCalls?.map((tc) => ToolCallData.fromRustToolCall(tc)).toList(),
    );
  }
}
