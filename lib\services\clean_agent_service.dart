import 'dart:math';
import '../models/clean_agent/design_concentration.dart';
import '../models/clean_agent/filling_ratio.dart';
import '../models/clean_agent/cylinder_spec.dart';
import '../models/clean_agent/pipe_data.dart';
import '../models/clean_agent/component_item.dart';

// Configuration constants from React app
class AppConfig {
  static const double shippingExFactor = 1.15;
  static const double dollarRateSarUsd = 3.75;
  static const double defaultRoomHeightM = 4.0;
  static const double agentDischargeTimeSeconds = 10.0;
  static const double nozzleSizeThresholdMm = 50.0;
  static const double detectorCoverageAreaM2 = 49.0;
  static const bool no343LCylinder = true;
  static const double marginFactor = 1.0;

  static const Map<String, double> maxFillingRatio = {
    'NOVEC1230': 0.88, // 88% of cylinder volume
    'FM200': 0.80,     // 80% of cylinder volume
  };
}

// Design concentration factors from React app
class DesignFactors {
  static const Map<String, Map<String, double>> factors = {
    'NOVEC1230': {
      '4.5%': 0.656,
      '4.7%': 0.687,
      '5.6%': 0.826,
      '5.9%': 0.873,
    },
    'FM200': {
      '7.4%': 0.583,
      '8.5%': 0.678,
      '9.0%': 0.722,
    }
  };
}

// Agent data from React app
class AgentData {
  static const Map<String, Map<String, dynamic>> data = {
    'NOVEC1230': {
      'costPerKg': 32.2582,
      'partNumber': '300.207.001'
    },
    'FM200': {
      'costPerKg': 20.3595,
      'partNumber': '300.205.001'
    }
  };
}

// Input data structure - matching React app
class InputData {
    final String agentType;
    final String designConcentration;
    final String inputMode;
    final double? roomLength;
    final double? roomWidth;
    final double? roomHeight;
    final double? agentQuantity;
    final String systemType;
    final String installationType;
    
    InputData({
      required this.agentType,
      required this.designConcentration,
      required this.inputMode,
      this.roomLength,
      this.roomWidth,
      this.roomHeight,
      this.agentQuantity,
      required this.systemType,
      required this.installationType,
    });
}

// Results data structures
class RoomData {
    final double roomLength;
    final double roomWidth;
    final double roomHeight;
    final double roomArea;
    final double roomVolume;

    RoomData({
      required this.roomLength,
      required this.roomWidth,
      required this.roomHeight,
      required this.roomArea,
      required this.roomVolume,
    });
  }

class CylinderData {
    final double targetFillSingleCyl;
    final int cylinderSizeLiters1stIter;
    final int numCylinders1stIter;
    final double qtyPerCylinder1stIter;
    final int cylinderSizeLiters2ndIter;
    final int numCylinders2ndIter;
    final double qtyPerCylinder;
    final double actualTotalKg;
    final double fillingRatio;

    CylinderData({
      required this.targetFillSingleCyl,
      required this.cylinderSizeLiters1stIter,
      required this.numCylinders1stIter,
      required this.qtyPerCylinder1stIter,
      required this.cylinderSizeLiters2ndIter,
      required this.numCylinders2ndIter,
      required this.qtyPerCylinder,
      required this.actualTotalKg,
      required this.fillingRatio,
    });
  }

class DischargeData {
    final double totalFlowRate;
    final int nozzleQty1stTrial;
    final double flowPerNozzle1stTrial;
    final double nozzleSize1stTrial;
    final int nozzleQtyFinal;
    final double flowPerNozzleFinal;
    final double nozzleSizeFinal;
    final double manifoldPipeSize;
    final double manifoldAssemblySize;

    DischargeData({
      required this.totalFlowRate,
      required this.nozzleQty1stTrial,
      required this.flowPerNozzle1stTrial,
      required this.nozzleSize1stTrial,
      required this.nozzleQtyFinal,
      required this.flowPerNozzleFinal,
      required this.nozzleSizeFinal,
      required this.manifoldPipeSize,
      required this.manifoldAssemblySize,
    });
  }

class DesignResults {
    final RoomData roomData;
    final double designFactor;
    final double totalAgentRequired;
    final CylinderData cylinder;
    final DischargeData discharge;
    final String systemType;

    DesignResults({
      required this.roomData,
      required this.designFactor,
      required this.totalAgentRequired,
      required this.cylinder,
      required this.discharge,
      required this.systemType,
    });
  }

class BomItem {
    final String partNo;
    final String description;
    final double quantity;
    final double unitCost;
    final double totalCost;
    final String manufacturer;
    final String category;
    final String subcategory;
    final String currency;

    BomItem({
      required this.partNo,
      required this.description,
      required this.quantity,
      required this.unitCost,
      required this.totalCost,
      required this.manufacturer,
      required this.category,
      required this.subcategory,
      required this.currency,
    });
  }

class BomSummary {
    final double suppressionCost;
    final double alarmCost;
    final double installationItemsCost;
    final double suppressionInstallCost;
    final double alarmInstallCost;
    final double installationServicesInstallCost;
    final double totalSupplyCostUSD;
    final double totalSupplyCostSAR;
    final double totalInstallCostSAR;
    final double grandTotalSAR;
    final double marginFactor;
    final double marginAmountSAR;
    final double agentCost;
    final double cylinderCost;
    final double valvesCost;
    final double pipingCost;
    final double nozzlesCost;
    final double detectionCost;
    final double miscCost;
    final double subtotalSAR;
    final double shippingFactorSAR;
    final double taxSAR;
    final double marginAmount;
    final double priceAfterMargin;

    BomSummary({
      required this.suppressionCost,
      required this.alarmCost,
      required this.installationItemsCost,
      required this.suppressionInstallCost,
      required this.alarmInstallCost,
      required this.installationServicesInstallCost,
      required this.totalSupplyCostUSD,
      required this.totalSupplyCostSAR,
      required this.totalInstallCostSAR,
      required this.grandTotalSAR,
      required this.marginFactor,
      required this.marginAmountSAR,
      required this.agentCost,
      required this.cylinderCost,
      required this.valvesCost,
      required this.pipingCost,
      required this.nozzlesCost,
      required this.detectionCost,
      required this.miscCost,
      required this.subtotalSAR,
      required this.shippingFactorSAR,
      required this.taxSAR,
      required this.marginAmount,
      required this.priceAfterMargin,
    });
}

// Main service class with calculation logic from React app
class CleanAgentService {

  // Main calculation methods
  Future<DesignResults> calculateDesign(
    InputData input,
    List<DesignConcentration> designConcentrations,
    List<CylinderSpec> cylinderSpecs,
    List<FillingRatio> fillingRatios,
    List<PipeData> pipeData,
  ) async {
    // Get design factor based on agent type and concentration
    final designFactor = _getDesignFactor(input.agentType, input.designConcentration, designConcentrations);
    
    // Calculate room dimensions based on input mode
    double roomLength = 0, roomWidth = 0, roomHeight = 0, roomVolume = 0, totalAgentRequired = 0;
    
    if (input.inputMode == 'dimensions' && input.roomLength != null && input.roomWidth != null && input.roomHeight != null) {
      // Using dimensions
      roomLength = input.roomLength!;
      roomWidth = input.roomWidth!;
      roomHeight = input.roomHeight!;
      roomVolume = roomLength * roomWidth * roomHeight;
      totalAgentRequired = roomVolume * designFactor;
    } else if (input.inputMode == 'agentQuantity' && input.agentQuantity != null) {
      // Using agent quantity
      totalAgentRequired = input.agentQuantity!;
      roomHeight = input.roomHeight ?? AppConfig.defaultRoomHeightM;
      roomVolume = totalAgentRequired / designFactor;
      
      // Derive room dimensions (assuming square room)
      final floorArea = roomVolume / roomHeight;
      roomLength = sqrt(floorArea);
      roomWidth = sqrt(floorArea);
    }
    
    // Create room data object
    final roomData = RoomData(
      roomLength: roomLength,
      roomWidth: roomWidth,
      roomHeight: roomHeight,
      roomArea: roomLength * roomWidth,
      roomVolume: roomVolume,
    );
    
    // Calculate cylinder data
    final cylinderData = _calculateCylinder(
      totalAgentRequired, 
      input.agentType,
      cylinderSpecs,
      fillingRatios,
    );
    
    // Calculate discharge system
    final dischargeData = _calculateDischarge(
      cylinderData.actualTotalKg,
      roomLength,
      roomWidth,
      roomHeight,
      pipeData,
    );
    
    // Return design results
    return DesignResults(
      roomData: roomData,
      designFactor: designFactor,
      totalAgentRequired: totalAgentRequired,
      cylinder: cylinderData,
      discharge: dischargeData,
      systemType: input.systemType,
    );
  }

  // Helper methods
  double _getDesignFactor(
    String agentType, 
    String designConcentration,
    List<DesignConcentration> designConcentrations,
  ) {
    final concentration = designConcentrations.firstWhere(
      (c) => c.agentType == agentType && c.percentage == designConcentration,
      orElse: () => designConcentrations.firstWhere(
        (c) => c.agentType == agentType && c.isDefault,
      ),
    );
    
    return concentration.factor;
  }

  CylinderData _calculateCylinder(
    double totalAgentRequired,
    String agentType,
    List<CylinderSpec> cylinderSpecs,
    List<FillingRatio> fillingRatios,
  ) {
    // Get filling ratio for agent type (not used in current calculation but available for future use)
    // final fillingRatio = fillingRatios.firstWhere(
    //   (fr) => fr.agentType == agentType,
    // );
    
    // Filter cylinder specs for this agent type
    final agentCylinders = cylinderSpecs
        .where((cs) => cs.agentType == agentType)
        .toList()
      ..sort((a, b) => a.sizeLiters.compareTo(b.sizeLiters));
    
    // First iteration - try with a single cylinder
    double targetFillSingleCyl = totalAgentRequired;
    int cylinderSizeLiters1stIter = 0;
    
    // Find the smallest cylinder that can hold the required agent
    for (final cylinder in agentCylinders) {
      if (targetFillSingleCyl <= cylinder.maxCapacityKg) {
        cylinderSizeLiters1stIter = cylinder.sizeLiters;
        break;
      }
    }
    
    // If no single cylinder can hold it, use the largest available
    if (cylinderSizeLiters1stIter == 0 && agentCylinders.isNotEmpty) {
      cylinderSizeLiters1stIter = agentCylinders.last.sizeLiters;
    }
    
    // Calculate number of cylinders needed
    final maxCapacityPerCylinder = agentCylinders
        .firstWhere((c) => c.sizeLiters == cylinderSizeLiters1stIter)
        .maxCapacityKg;
    
    final numCylinders1stIter = (totalAgentRequired / maxCapacityPerCylinder).ceil();
    final qtyPerCylinder1stIter = totalAgentRequired / numCylinders1stIter;
    
    // Second iteration - optimize cylinder size
    int cylinderSizeLiters2ndIter = cylinderSizeLiters1stIter;
    int numCylinders2ndIter = numCylinders1stIter;
    double qtyPerCylinder = qtyPerCylinder1stIter;
    
    // Try to find a smaller cylinder that can still hold the per-cylinder quantity
    for (final cylinder in agentCylinders) {
      if (qtyPerCylinder <= cylinder.maxCapacityKg && 
          qtyPerCylinder >= cylinder.minCapacityKg) {
        cylinderSizeLiters2ndIter = cylinder.sizeLiters;
        break;
      }
    }
    
    // Calculate actual total kg and filling ratio
    final actualTotalKg = qtyPerCylinder * numCylinders2ndIter;
    final cylinderVolume = cylinderSizeLiters2ndIter.toDouble();
    final fillingRatioValue = qtyPerCylinder / cylinderVolume;
    
    return CylinderData(
      targetFillSingleCyl: targetFillSingleCyl,
      cylinderSizeLiters1stIter: cylinderSizeLiters1stIter,
      numCylinders1stIter: numCylinders1stIter,
      qtyPerCylinder1stIter: qtyPerCylinder1stIter,
      cylinderSizeLiters2ndIter: cylinderSizeLiters2ndIter,
      numCylinders2ndIter: numCylinders2ndIter,
      qtyPerCylinder: qtyPerCylinder,
      actualTotalKg: actualTotalKg,
      fillingRatio: fillingRatioValue,
    );
  }

  DischargeData _calculateDischarge(
    double actualTotalKg,
    double roomLength,
    double roomWidth,
    double roomHeight,
    List<PipeData> pipeData,
  ) {
    // Calculate total flow rate (kg/sec)
    final totalFlowRate = actualTotalKg / AppConfig.agentDischargeTimeSeconds;
    
    // Calculate room area
    final roomArea = roomLength * roomWidth;
    
    // Calculate nozzle quantity based on room dimensions and coverage area
    int nozzleQty1stTrial;
    if (totalFlowRate > 25) {
      // For large flow rates, start with fewer nozzles
      nozzleQty1stTrial = max(4, (roomArea / 36).ceil()); // 36m² coverage per nozzle for large systems
    } else if (totalFlowRate > 10) {
      // Medium systems
      nozzleQty1stTrial = max(2, (roomArea / 25).ceil()); // 25m² coverage per nozzle
    } else {
      // Small systems
      const coverageLength = 6.0; // meters per nozzle
      const coverageWidth = 6.0;  // meters per nozzle
      
      final nozzlesLength = (roomLength / coverageLength).ceil();
      final nozzlesWidth = (roomWidth / coverageWidth).ceil();
      nozzleQty1stTrial = nozzlesLength * nozzlesWidth;
    }
    
    // Calculate flow per nozzle
    final flowPerNozzle1stTrial = totalFlowRate / nozzleQty1stTrial;
    
    // Determine nozzle size based on flow rate
    double nozzleSize1stTrial = 0;
    
    // Sort pipe data by size
    final sortedPipeData = List<PipeData>.from(pipeData)
      ..sort((a, b) => a.sizeMm.compareTo(b.sizeMm));
    
    // Find appropriate nozzle size
    for (final pipe in sortedPipeData) {
      if (flowPerNozzle1stTrial <= pipe.maxFlowKgPerSec) {
        nozzleSize1stTrial = pipe.sizeMm;
        break;
      }
    }
    
    // If no suitable size found, use the largest
    if (nozzleSize1stTrial == 0 && sortedPipeData.isNotEmpty) {
      nozzleSize1stTrial = sortedPipeData.last.sizeMm;
    }
    
    // Final nozzle quantity - double if nozzle size is large
    int nozzleQtyFinal = nozzleQty1stTrial;
    if (nozzleSize1stTrial > AppConfig.nozzleSizeThresholdMm) {
      nozzleQtyFinal = nozzleQty1stTrial * 2;
    }
    
    // Recalculate flow per nozzle
    final flowPerNozzleFinal = totalFlowRate / nozzleQtyFinal;
    
    // Determine final nozzle size
    double nozzleSizeFinal = 0;
    for (final pipe in sortedPipeData) {
      if (flowPerNozzleFinal <= pipe.maxFlowKgPerSec) {
        nozzleSizeFinal = pipe.sizeMm;
        break;
      }
    }
    
    // If no suitable size found, use the largest
    if (nozzleSizeFinal == 0 && sortedPipeData.isNotEmpty) {
      nozzleSizeFinal = sortedPipeData.last.sizeMm;
    }
    
    // Calculate manifold pipe size (one size larger than nozzle)
    double manifoldPipeSize = nozzleSizeFinal;
    int manifoldIndex = sortedPipeData.indexWhere((p) => p.sizeMm == nozzleSizeFinal);
    if (manifoldIndex < sortedPipeData.length - 1) {
      manifoldPipeSize = sortedPipeData[manifoldIndex + 1].sizeMm;
    }
    
    // Calculate manifold assembly size (one size larger than manifold)
    double manifoldAssemblySize = manifoldPipeSize;
    manifoldIndex = sortedPipeData.indexWhere((p) => p.sizeMm == manifoldPipeSize);
    if (manifoldIndex < sortedPipeData.length - 1) {
      manifoldAssemblySize = sortedPipeData[manifoldIndex + 1].sizeMm;
    }
    
    return DischargeData(
      totalFlowRate: totalFlowRate,
      nozzleQty1stTrial: nozzleQty1stTrial,
      flowPerNozzle1stTrial: flowPerNozzle1stTrial,
      nozzleSize1stTrial: nozzleSize1stTrial,
      nozzleQtyFinal: nozzleQtyFinal,
      flowPerNozzleFinal: flowPerNozzleFinal,
      nozzleSizeFinal: nozzleSizeFinal,
      manifoldPipeSize: manifoldPipeSize,
      manifoldAssemblySize: manifoldAssemblySize,
    );
  }

  // Generate Bill of Materials
  Future<Map<String, dynamic>> generateBOM(
    DesignResults designResults,
    String agentType,
    List<ComponentItem> components,
    List<CylinderSpec> cylinderSpecs,
    double exchangeRate,
    double shippingFactor,
    double marginFactor,
  ) async {
    final List<BomItem> bom = [];
    double agentCost = 0;
    double cylinderCost = 0;
    double valvesCost = 0;
    double pipingCost = 0;
    double nozzlesCost = 0;
    double detectionCost = 0;
    double miscCost = 0;
    
    // 1. Add agent
    final agentComponents = components.where(
      (c) => c.agentType == agentType && c.category == 'Agent'
    ).toList();
    
    if (agentComponents.isNotEmpty) {
      final agent = agentComponents.first;
      final agentQty = designResults.cylinder.actualTotalKg;
      final agentTotalCost = agent.unitCost * agentQty;
      
      bom.add(BomItem(
        partNo: agent.partNumber,
        description: agent.description,
        quantity: agentQty,
        unitCost: agent.unitCost,
        totalCost: agentTotalCost,
        manufacturer: agent.manufacturer,
        category: 'Suppression',
        subcategory: 'Agent',
        currency: agent.currency,
      ));
      
      agentCost = agentTotalCost;
    }
    
    // 2. Add cylinders
    final cylinderSize = designResults.cylinder.cylinderSizeLiters2ndIter;
    final cylinderQty = designResults.cylinder.numCylinders2ndIter;
    
    final cylinderSpec = cylinderSpecs.firstWhere(
      (c) => c.agentType == agentType && c.sizeLiters == cylinderSize,
      orElse: () => cylinderSpecs.firstWhere(
        (c) => c.agentType == agentType,
        orElse: () => CylinderSpec(
          id: 'default',
          agentType: agentType,
          sizeLiters: cylinderSize,
          minCapacityKg: 0,
          maxCapacityKg: 0,
          partNumber: 'CYL-$cylinderSize',
          price: 0,
        ),
      ),
    );
    
    final cylinderTotalCost = cylinderSpec.price * cylinderQty;
    bom.add(BomItem(
      partNo: cylinderSpec.partNumber,
      description: '$cylinderSize L Cylinder',
      quantity: cylinderQty.toDouble(),
      unitCost: cylinderSpec.price,
      totalCost: cylinderTotalCost,
      manufacturer: 'Manufacturer',
      category: 'Suppression',
      subcategory: 'Cylinder',
      currency: 'USD',
    ));
    
    cylinderCost = cylinderTotalCost;
    
    // 3. Add discharge valve
    final valveComponents = components.where(
      (c) => c.agentType == agentType && c.category == 'Valve'
    ).toList();
    
    if (valveComponents.isNotEmpty) {
      final valve = valveComponents.first;
      final valveTotalCost = valve.unitCost * cylinderQty;
      
      bom.add(BomItem(
        partNo: valve.partNumber,
        description: valve.description,
        quantity: cylinderQty.toDouble(),
        unitCost: valve.unitCost,
        totalCost: valveTotalCost,
        manufacturer: valve.manufacturer,
        category: 'Suppression',
        subcategory: 'Valve',
        currency: valve.currency,
      ));
      
      valvesCost = valveTotalCost;
    }
    
    // 4. Add nozzles
    final nozzleComponents = components.where(
      (c) => c.agentType == agentType && c.category == 'Nozzle'
    ).toList();
    
    if (nozzleComponents.isNotEmpty) {
      // Find nozzle closest to calculated size
      final targetSize = designResults.discharge.nozzleSizeFinal;
      nozzleComponents.sort((a, b) {
        final sizeA = double.tryParse(a.description.replaceAll(RegExp(r'[^\d.]'), '')) ?? 0;
        final sizeB = double.tryParse(b.description.replaceAll(RegExp(r'[^\d.]'), '')) ?? 0;
        return (sizeA - targetSize).abs().compareTo((sizeB - targetSize).abs());
      });
      
      final nozzle = nozzleComponents.first;
      final nozzleQty = designResults.discharge.nozzleQtyFinal;
      final nozzleTotalCost = nozzle.unitCost * nozzleQty;
      
      bom.add(BomItem(
        partNo: nozzle.partNumber,
        description: nozzle.description,
        quantity: nozzleQty.toDouble(),
        unitCost: nozzle.unitCost,
        totalCost: nozzleTotalCost,
        manufacturer: nozzle.manufacturer,
        category: 'Suppression',
        subcategory: 'Nozzle',
        currency: nozzle.currency,
      ));
      
      nozzlesCost = nozzleTotalCost;
    }
    
    // 5. Add piping (estimate based on room size)
    final roomVolume = designResults.roomData.roomVolume;
    final estimatedPipingLength = sqrt(roomVolume) * 3; // Rough estimate
    
    final pipingComponents = components.where(
      (c) => c.category == 'Piping'
    ).toList();
    
    if (pipingComponents.isNotEmpty) {
      final piping = pipingComponents.first;
      final pipingTotalCost = piping.unitCost * estimatedPipingLength;
      
      bom.add(BomItem(
        partNo: piping.partNumber,
        description: piping.description,
        quantity: estimatedPipingLength,
        unitCost: piping.unitCost,
        totalCost: pipingTotalCost,
        manufacturer: piping.manufacturer,
        category: 'Suppression',
        subcategory: 'Piping',
        currency: piping.currency,
      ));
      
      pipingCost = pipingTotalCost;
    }
    
    // 6. Add detection components
    final roomArea = designResults.roomData.roomArea;
    final detectorQty = (roomArea / AppConfig.detectorCoverageAreaM2).ceil();
    
    final detectionComponents = components.where(
      (c) => c.category == 'Detection'
    ).toList();
    
    double detectionTotalCost = 0;
    for (final detector in detectionComponents) {
      double qty = 1;
      if (detector.subcategory == 'Detector') {
        qty = detectorQty.toDouble();
      }
      
      final itemTotalCost = detector.unitCost * qty;
      detectionTotalCost += itemTotalCost;
      
      bom.add(BomItem(
        partNo: detector.partNumber,
        description: detector.description,
        quantity: qty,
        unitCost: detector.unitCost,
        totalCost: itemTotalCost,
        manufacturer: detector.manufacturer,
        category: 'Alarm',
        subcategory: detector.subcategory,
        currency: detector.currency,
      ));
    }
    
    detectionCost = detectionTotalCost;
    
    // 7. Add installation items
    final installComponents = components.where(
      (c) => c.category == 'Installation'
    ).toList();
    
    double installTotalCost = 0;
    for (final item in installComponents) {
      // Quantity depends on subcategory and room size
      double qty = 1;
      if (item.subcategory == 'Labor') {
        qty = roomVolume / 10; // Rough estimate: 1 labor unit per 10m³
      }
      
      final itemTotalCost = item.unitCost * qty;
      installTotalCost += itemTotalCost;
      
      bom.add(BomItem(
        partNo: item.partNumber,
        description: item.description,
        quantity: qty,
        unitCost: item.unitCost,
        totalCost: itemTotalCost,
        manufacturer: item.manufacturer,
        category: 'Installation',
        subcategory: item.subcategory,
        currency: item.currency,
      ));
    }
    
    miscCost = installTotalCost;
    
    // Calculate summary
    final suppressionCost = agentCost + cylinderCost + valvesCost + pipingCost + nozzlesCost;
    final alarmCost = detectionCost;
    final installationItemsCost = miscCost;
    
    // Installation costs (estimated as percentage of equipment cost)
    final suppressionInstallCost = suppressionCost * 0.15; // 15% of suppression cost
    final alarmInstallCost = alarmCost * 0.20; // 20% of alarm cost
    const installationServicesInstallCost = 0; // Already included in installation items
    
    // Total costs
    final totalSupplyCostUSD = suppressionCost + alarmCost + installationItemsCost;
    final totalSupplyCostSAR = totalSupplyCostUSD * exchangeRate;
    final totalInstallCostSAR = (suppressionInstallCost + alarmInstallCost + installationServicesInstallCost) * exchangeRate;
    
    // Shipping and tax
    final shippingFactorSAR = totalSupplyCostSAR * shippingFactor;
    final subtotalSAR = totalSupplyCostSAR + shippingFactorSAR;
    const taxSAR = 0; // No tax by default, can be added if needed
    
    // Margin
    final marginAmount = subtotalSAR * marginFactor;
    final priceAfterMargin = subtotalSAR + marginAmount;
    
    // Grand total
    final grandTotalSAR = priceAfterMargin + totalInstallCostSAR;
    
    // Create summary
    final summary = BomSummary(
      suppressionCost: suppressionCost,
      alarmCost: alarmCost,
      installationItemsCost: installationItemsCost,
      suppressionInstallCost: suppressionInstallCost,
      alarmInstallCost: alarmInstallCost,
      installationServicesInstallCost: installationServicesInstallCost.toDouble(),
      totalSupplyCostUSD: totalSupplyCostUSD,
      totalSupplyCostSAR: totalSupplyCostSAR,
      totalInstallCostSAR: totalInstallCostSAR,
      grandTotalSAR: grandTotalSAR,
      marginFactor: marginFactor,
      marginAmountSAR: marginAmount,
      agentCost: agentCost,
      cylinderCost: cylinderCost,
      valvesCost: valvesCost,
      pipingCost: pipingCost,
      nozzlesCost: nozzlesCost,
      detectionCost: detectionCost,
      miscCost: miscCost,
      subtotalSAR: subtotalSAR,
      shippingFactorSAR: shippingFactorSAR,
      taxSAR: taxSAR.toDouble(),
      marginAmount: marginAmount,
      priceAfterMargin: priceAfterMargin,
    );
    
    return {
      'bom': bom,
      'summary': summary,
    };
  }
}
