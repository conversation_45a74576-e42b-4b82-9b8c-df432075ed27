import 'package:flutter/material.dart';
import '../models/isar_models.dart';
import '../services/dynamic_schema_service.dart';

class ColumnManagementDialog extends StatefulWidget {
  final FlexibleTable table;
  final VoidCallback? onColumnsChanged;

  const ColumnManagementDialog({
    super.key,
    required this.table,
    this.onColumnsChanged,
  });

  @override
  State<ColumnManagementDialog> createState() => _ColumnManagementDialogState();
}

class _ColumnManagementDialogState extends State<ColumnManagementDialog> {
  final DynamicSchemaService _schemaService = DynamicSchemaService.instance;
  List<FlexibleColumn> _columns = [];
  bool _isLoading = true;
  String? _error;

  @override
  void initState() {
    super.initState();
    _loadColumns();
  }

  Future<void> _loadColumns() async {
    setState(() {
      _isLoading = true;
      _error = null;
    });

    try {
      _columns = await _schemaService.getColumnsForTable(widget.table.tableId!);
      setState(() {
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _error = e.toString();
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      child: Container(
        width: 600,
        height: 500,
        padding: const EdgeInsets.all(24),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header
            Row(
              children: [
                const Icon(Icons.view_column, size: 24),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Text(
                        'Manage Columns',
                        style: TextStyle(
                          fontSize: 20,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      Text(
                        'Table: ${widget.table.name}',
                        style: TextStyle(
                          fontSize: 14,
                          color: Colors.grey.shade600,
                        ),
                      ),
                    ],
                  ),
                ),
                IconButton(
                  onPressed: () => Navigator.of(context).pop(),
                  icon: const Icon(Icons.close),
                ),
              ],
            ),
            
            const Divider(),
            
            // Add column button
            Padding(
              padding: const EdgeInsets.symmetric(vertical: 8),
              child: ElevatedButton.icon(
                onPressed: () => _showAddColumnDialog(),
                icon: const Icon(Icons.add),
                label: const Text('Add Column'),
              ),
            ),
            
            // Columns list
            Expanded(
              child: _buildColumnsList(),
            ),
            
            // Footer
            const Divider(),
            Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                TextButton(
                  onPressed: () => Navigator.of(context).pop(),
                  child: const Text('Close'),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildColumnsList() {
    if (_isLoading) {
      return const Center(child: CircularProgressIndicator());
    }

    if (_error != null) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.error, size: 48, color: Colors.red.shade300),
            const SizedBox(height: 16),
            Text('Error: $_error'),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: _loadColumns,
              child: const Text('Retry'),
            ),
          ],
        ),
      );
    }

    if (_columns.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.view_column, size: 48, color: Colors.grey.shade400),
            const SizedBox(height: 16),
            Text(
              'No columns yet',
              style: TextStyle(
                fontSize: 16,
                color: Colors.grey.shade600,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'Add your first column to get started',
              style: TextStyle(
                color: Colors.grey.shade500,
              ),
            ),
          ],
        ),
      );
    }

    return ReorderableListView.builder(
      itemCount: _columns.length,
      onReorder: _reorderColumns,
      itemBuilder: (context, index) {
        final column = _columns[index];
        return _buildColumnTile(column, index);
      },
    );
  }

  Widget _buildColumnTile(FlexibleColumn column, int index) {
    return Card(
      key: ValueKey(column.columnId),
      margin: const EdgeInsets.symmetric(vertical: 4),
      child: ListTile(
        leading: _buildColumnTypeIcon(column.dataType),
        title: Text(
          column.name ?? 'Unnamed Column',
          style: const TextStyle(fontWeight: FontWeight.bold),
        ),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(_getDataTypeDisplayName(column.dataType)),
            if (column.isRequired == true)
              const Text(
                'Required',
                style: TextStyle(
                  color: Colors.red,
                  fontSize: 12,
                ),
              ),
          ],
        ),
        trailing: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            IconButton(
              icon: const Icon(Icons.edit, size: 20),
              onPressed: () => _showEditColumnDialog(column),
              tooltip: 'Edit Column',
            ),
            IconButton(
              icon: const Icon(Icons.delete, size: 20, color: Colors.red),
              onPressed: () => _showDeleteConfirmation(column),
              tooltip: 'Delete Column',
            ),
            const Icon(Icons.drag_handle, color: Colors.grey),
          ],
        ),
      ),
    );
  }

  Widget _buildColumnTypeIcon(ColumnDataType dataType) {
    IconData iconData;
    Color iconColor;
    
    switch (dataType) {
      case ColumnDataType.text:
        iconData = Icons.text_fields;
        iconColor = Colors.grey.shade600;
        break;
      case ColumnDataType.number:
        iconData = Icons.numbers;
        iconColor = Colors.blue;
        break;
      case ColumnDataType.currency:
        iconData = Icons.attach_money;
        iconColor = Colors.green;
        break;
      case ColumnDataType.date:
        iconData = Icons.calendar_today;
        iconColor = Colors.orange;
        break;
      case ColumnDataType.boolean:
        iconData = Icons.check_box;
        iconColor = Colors.purple;
        break;
      case ColumnDataType.dropdown:
        iconData = Icons.arrow_drop_down;
        iconColor = Colors.teal;
        break;
    }
    
    return Container(
      width: 40,
      height: 40,
      decoration: BoxDecoration(
        color: iconColor.withOpacity(0.1),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Icon(
        iconData,
        color: iconColor,
        size: 20,
      ),
    );
  }

  String _getDataTypeDisplayName(ColumnDataType dataType) {
    switch (dataType) {
      case ColumnDataType.text:
        return 'Text';
      case ColumnDataType.number:
        return 'Number';
      case ColumnDataType.currency:
        return 'Currency';
      case ColumnDataType.date:
        return 'Date';
      case ColumnDataType.boolean:
        return 'Yes/No';
      case ColumnDataType.dropdown:
        return 'Dropdown';
    }
  }

  void _reorderColumns(int oldIndex, int newIndex) {
    if (newIndex > oldIndex) {
      newIndex -= 1;
    }
    
    setState(() {
      final column = _columns.removeAt(oldIndex);
      _columns.insert(newIndex, column);
    });
    
    // Update order in database
    final columnIds = _columns.map((c) => c.columnId!).toList();
    _schemaService.reorderColumns(widget.table.tableId!, columnIds);
  }

  void _showAddColumnDialog() {
    showDialog(
      context: context,
      builder: (context) => _ColumnEditDialog(
        tableId: widget.table.tableId!,
        onSaved: () {
          _loadColumns();
          widget.onColumnsChanged?.call();
        },
      ),
    );
  }

  void _showEditColumnDialog(FlexibleColumn column) {
    showDialog(
      context: context,
      builder: (context) => _ColumnEditDialog(
        tableId: widget.table.tableId!,
        column: column,
        onSaved: () {
          _loadColumns();
          widget.onColumnsChanged?.call();
        },
      ),
    );
  }

  void _showDeleteConfirmation(FlexibleColumn column) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete Column'),
        content: Text(
          'Are you sure you want to delete the column "${column.name}"?\n\n'
          'This will permanently remove all data in this column. '
          'This action cannot be undone.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () async {
              Navigator.of(context).pop();
              await _deleteColumn(column);
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red,
              foregroundColor: Colors.white,
            ),
            child: const Text('Delete'),
          ),
        ],
      ),
    );
  }

  Future<void> _deleteColumn(FlexibleColumn column) async {
    try {
      await _schemaService.deleteColumn(column.columnId!);
      await _loadColumns();
      widget.onColumnsChanged?.call();
      
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Column "${column.name}" deleted successfully'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error deleting column: ${e.toString()}'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }
}

class _ColumnEditDialog extends StatefulWidget {
  final String tableId;
  final FlexibleColumn? column;
  final VoidCallback? onSaved;

  const _ColumnEditDialog({
    required this.tableId,
    this.column,
    this.onSaved,
  });

  @override
  State<_ColumnEditDialog> createState() => _ColumnEditDialogState();
}

class _ColumnEditDialogState extends State<_ColumnEditDialog> {
  final DynamicSchemaService _schemaService = DynamicSchemaService.instance;
  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  final _defaultValueController = TextEditingController();
  
  ColumnDataType _selectedDataType = ColumnDataType.text;
  CurrencyType _selectedCurrencyType = CurrencyType.usd;
  bool _isRequired = false;
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    if (widget.column != null) {
      _nameController.text = widget.column!.name ?? '';
      _defaultValueController.text = widget.column!.defaultValue ?? '';
      _selectedDataType = widget.column!.dataType;
      _selectedCurrencyType = widget.column!.currencyType;
      _isRequired = widget.column!.isRequired ?? false;
    }
  }

  @override
  void dispose() {
    _nameController.dispose();
    _defaultValueController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final isEditing = widget.column != null;
    
    return AlertDialog(
      title: Text(isEditing ? 'Edit Column' : 'Add Column'),
      content: SizedBox(
        width: 400,
        child: Form(
          key: _formKey,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              TextFormField(
                controller: _nameController,
                decoration: const InputDecoration(
                  labelText: 'Column Name',
                  border: OutlineInputBorder(),
                ),
                validator: (value) {
                  if (value == null || value.trim().isEmpty) {
                    return 'Please enter a column name';
                  }
                  return null;
                },
              ),
              
              const SizedBox(height: 16),
              
              DropdownButtonFormField<ColumnDataType>(
                value: _selectedDataType,
                decoration: const InputDecoration(
                  labelText: 'Data Type',
                  border: OutlineInputBorder(),
                ),
                items: ColumnDataType.values.map((type) {
                  return DropdownMenuItem(
                    value: type,
                    child: Text(_getDataTypeDisplayName(type)),
                  );
                }).toList(),
                onChanged: (value) {
                  if (value != null) {
                    setState(() {
                      _selectedDataType = value;
                    });
                  }
                },
              ),

              const SizedBox(height: 16),

              // Currency type dropdown (only show for currency data type)
              if (_selectedDataType == ColumnDataType.currency) ...[
                DropdownButtonFormField<CurrencyType>(
                  value: _selectedCurrencyType,
                  decoration: const InputDecoration(
                    labelText: 'Currency Type',
                    border: OutlineInputBorder(),
                  ),
                  items: CurrencyType.values.map((type) {
                    return DropdownMenuItem(
                      value: type,
                      child: Row(
                        children: [
                          Text(type.symbol),
                          const SizedBox(width: 8),
                          Text('${type.code} - ${type.name}'),
                        ],
                      ),
                    );
                  }).toList(),
                  onChanged: (value) {
                    if (value != null) {
                      setState(() {
                        _selectedCurrencyType = value;
                      });
                    }
                  },
                ),
                const SizedBox(height: 16),
              ],

              TextFormField(
                controller: _defaultValueController,
                decoration: const InputDecoration(
                  labelText: 'Default Value (Optional)',
                  border: OutlineInputBorder(),
                ),
              ),
              
              const SizedBox(height: 16),
              
              CheckboxListTile(
                title: const Text('Required Field'),
                value: _isRequired,
                onChanged: (value) {
                  setState(() {
                    _isRequired = value ?? false;
                  });
                },
                controlAffinity: ListTileControlAffinity.leading,
              ),
            ],
          ),
        ),
      ),
      actions: [
        TextButton(
          onPressed: _isLoading ? null : () => Navigator.of(context).pop(),
          child: const Text('Cancel'),
        ),
        ElevatedButton(
          onPressed: _isLoading ? null : _handleSave,
          child: _isLoading
              ? const SizedBox(
                  width: 16,
                  height: 16,
                  child: CircularProgressIndicator(strokeWidth: 2),
                )
              : Text(isEditing ? 'Update' : 'Add'),
        ),
      ],
    );
  }

  String _getDataTypeDisplayName(ColumnDataType dataType) {
    switch (dataType) {
      case ColumnDataType.text:
        return 'Text';
      case ColumnDataType.number:
        return 'Number';
      case ColumnDataType.currency:
        return 'Currency';
      case ColumnDataType.date:
        return 'Date';
      case ColumnDataType.boolean:
        return 'Yes/No';
      case ColumnDataType.dropdown:
        return 'Dropdown';
    }
  }

  Future<void> _handleSave() async {
    if (!_formKey.currentState!.validate()) return;

    setState(() => _isLoading = true);

    try {
      if (widget.column != null) {
        // Update existing column
        await _schemaService.updateColumn(
          widget.column!.columnId!,
          name: _nameController.text.trim(),
          dataType: _selectedDataType,
          currencyType: _selectedCurrencyType,
          isRequired: _isRequired,
          defaultValue: _defaultValueController.text.trim().isEmpty
              ? null
              : _defaultValueController.text.trim(),
        );
      } else {
        // Create new column
        await _schemaService.createColumn(
          name: _nameController.text.trim(),
          tableId: widget.tableId,
          dataType: _selectedDataType,
          currencyType: _selectedCurrencyType,
          isRequired: _isRequired,
          defaultValue: _defaultValueController.text.trim().isEmpty
              ? null
              : _defaultValueController.text.trim(),
        );
      }

      widget.onSaved?.call();
      
      if (mounted) {
        Navigator.of(context).pop();
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error: ${e.toString()}'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() => _isLoading = false);
      }
    }
  }
}
