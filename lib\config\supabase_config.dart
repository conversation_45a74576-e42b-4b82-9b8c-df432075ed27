class SupabaseConfig {
  // These will be set from settings/company configuration
  static String? _supabaseUrl;
  static String? _supabaseAnonKey;
  static String? _currentCompany;

  // Getters
  static String? get supabaseUrl => _supabaseUrl;
  static String? get supabaseAnonKey => _supabaseAnonKey;
  static String? get currentCompany => _currentCompany;

  // Setters
  static void setCredentials({
    required String url,
    required String anonKey,
    required String company,
  }) {
    _supabaseUrl = url;
    _supabaseAnonKey = anonKey;
    _currentCompany = company;
  }

  // Check if configuration is valid
  static bool get isConfigured => 
      _supabaseUrl != null && 
      _supabaseAnonKey != null && 
      _currentCompany != null;

  // Get table name with company prefix
  static String getTableName(String tableId) {
    if (_currentCompany == null) {
      throw Exception('Company not set');
    }

    // Sanitize company name for use in table names
    final sanitizedCompanyName = _sanitizeName(_currentCompany!);
    return '${sanitizedCompanyName}_$tableId';
  }

  // Generate enhanced table name with section hierarchy
  static String getEnhancedTableName(String tableId, String? sectionName, String? tableName) {
    if (_currentCompany == null) {
      throw Exception('Company not set');
    }

    // Sanitize all components
    final sanitizedCompanyName = _sanitizeName(_currentCompany!);
    final sanitizedSectionName = sectionName != null ? _sanitizeName(sectionName) : 'general';
    final sanitizedTableName = tableName != null ? _sanitizeName(tableName) : 'table';

    // Generate 3-digit numeric suffix from table ID hash
    final numericSuffix = _generateNumericSuffix(tableId);

    // Format: company_section_tablename001
    return '${sanitizedCompanyName}_${sanitizedSectionName}_$sanitizedTableName$numericSuffix';
  }

  // Generate 3-digit numeric suffix from table ID
  static String _generateNumericSuffix(String tableId) {
    // Use hash code to generate consistent 3-digit number
    final hash = tableId.hashCode.abs();
    final suffix = (hash % 900) + 100; // Ensures 3-digit number (100-999)
    return suffix.toString().padLeft(3, '0');
  }

  // Helper method to sanitize names
  static String _sanitizeName(String name) {
    return name
        .replaceAll(RegExp(r'[^a-zA-Z0-9]'), '_')
        .replaceAll(RegExp(r'_+'), '_') // Replace multiple underscores with single
        .replaceAll(RegExp(r'^_|_$'), '') // Remove leading/trailing underscores
        .toLowerCase();
  }

  // Clear configuration (for switching companies)
  static void clear() {
    _supabaseUrl = null;
    _supabaseAnonKey = null;
    _currentCompany = null;
  }
}
