import 'dart:io';
import 'dart:convert';
import 'package:file_picker/file_picker.dart';
import 'package:excel/excel.dart';
import 'package:csv/csv.dart';
import '../models/isar_models.dart';
import '../widgets/import_preview_dialog.dart';
import 'dynamic_schema_service.dart';

class DataImportExportService {
  static DataImportExportService? _instance;
  static DataImportExportService get instance {
    _instance ??= DataImportExportService._();
    return _instance!;
  }

  DataImportExportService._();

  final DynamicSchemaService _schemaService = DynamicSchemaService.instance;

  // Store the selected file data for import
  List<List<dynamic>>? _cachedImportData;
  String? _cachedFileName;

  // Preview import data before actual import
  Future<ImportPreviewData?> previewImport() async {
    try {
      final result = await FilePicker.platform.pickFiles(
        type: FileType.custom,
        allowedExtensions: ['xlsx', 'xls', 'csv'],
        allowMultiple: false,
      );

      if (result == null || result.files.isEmpty) return null;

      final file = File(result.files.first.path!);
      final fileName = result.files.first.name;
      final extension = fileName.split('.').last.toLowerCase();

      List<List<dynamic>> rows;
      if (extension == 'csv') {
        final contents = await file.readAsString();
        rows = const CsvToListConverter().convert(contents);
      } else {
        final bytes = await file.readAsBytes();
        final excel = Excel.decodeBytes(bytes);
        final sheet = excel.tables.values.first;
        rows = [];
        for (final row in sheet.rows) {
          final rowData = row.map((cell) => cell?.value?.toString() ?? '').toList();
          rows.add(rowData);
        }
      }

      if (rows.isEmpty) return null;

      // Cache the data for later import
      _cachedImportData = rows;
      _cachedFileName = fileName;

      final headers = rows.first.map((h) => h.toString().trim()).toList();
      final dataRows = rows.skip(1).take(5).toList(); // Take first 5 rows as sample
      final detectedTypes = _analyzeDataTypes(headers, rows.skip(1).toList());

      return ImportPreviewData(
        headers: headers,
        sampleRows: dataRows,
        detectedTypes: detectedTypes,
        fileName: fileName,
      );
    } catch (e) {
      throw Exception('Error previewing import file: $e');
    }
  }

  // Import data with custom mappings using cached data
  Future<ImportResult> importDataWithMappings(String tableId, List<ColumnMapping> mappings) async {
    try {
      if (_cachedImportData == null) {
        return ImportResult(
          success: false,
          message: 'No cached import data found. Please select a file first.',
          totalRows: 0,
          successfulRows: 0,
          errors: [],
        );
      }

      return await _processImportDataWithMappings(tableId, _cachedImportData!, mappings);
    } catch (e) {
      return ImportResult(
        success: false,
        message: 'Import failed: $e',
        totalRows: 0,
        successfulRows: 0,
        errors: [e.toString()],
      );
    } finally {
      // Clear cached data after import
      _cachedImportData = null;
      _cachedFileName = null;
    }
  }

  // Import from Excel/CSV
  Future<ImportResult> importFromFile(String tableId) async {
    try {
      final result = await FilePicker.platform.pickFiles(
        type: FileType.custom,
        allowedExtensions: ['xlsx', 'xls', 'csv'],
        allowMultiple: false,
      );

      if (result == null || result.files.isEmpty) {
        return ImportResult(
          success: false,
          message: 'No file selected',
          totalRows: 0,
          successfulRows: 0,
          errors: [],
        );
      }

      final file = File(result.files.first.path!);
      final extension = result.files.first.extension?.toLowerCase();

      if (extension == 'csv') {
        return await _importFromCsv(tableId, file);
      } else if (extension == 'xlsx' || extension == 'xls') {
        return await _importFromExcel(tableId, file);
      } else {
        return ImportResult(
          success: false,
          message: 'Unsupported file format',
          totalRows: 0,
          successfulRows: 0,
          errors: [],
        );
      }
    } catch (e) {
      return ImportResult(
        success: false,
        message: 'Error importing file: ${e.toString()}',
        totalRows: 0,
        successfulRows: 0,
        errors: [e.toString()],
      );
    }
  }

  Future<ImportResult> _importFromCsv(String tableId, File file) async {
    try {
      final contents = await file.readAsString();
      final rows = const CsvToListConverter().convert(contents);
      
      if (rows.isEmpty) {
        return ImportResult(
          success: false,
          message: 'CSV file is empty',
          totalRows: 0,
          successfulRows: 0,
          errors: [],
        );
      }

      return await _processImportData(tableId, rows);
    } catch (e) {
      return ImportResult(
        success: false,
        message: 'Error reading CSV file: ${e.toString()}',
        totalRows: 0,
        successfulRows: 0,
        errors: [e.toString()],
      );
    }
  }

  Future<ImportResult> _importFromExcel(String tableId, File file) async {
    try {
      final bytes = await file.readAsBytes();
      final excel = Excel.decodeBytes(bytes);
      
      if (excel.tables.isEmpty) {
        return ImportResult(
          success: false,
          message: 'Excel file has no sheets',
          totalRows: 0,
          successfulRows: 0,
          errors: [],
        );
      }

      // Use the first sheet
      final sheet = excel.tables.values.first;
      final rows = <List<dynamic>>[];
      
      for (final row in sheet.rows) {
        final rowData = row.map((cell) => cell?.value?.toString() ?? '').toList();
        rows.add(rowData);
      }

      if (rows.isEmpty) {
        return ImportResult(
          success: false,
          message: 'Excel sheet is empty',
          totalRows: 0,
          successfulRows: 0,
          errors: [],
        );
      }

      return await _processImportData(tableId, rows);
    } catch (e) {
      return ImportResult(
        success: false,
        message: 'Error reading Excel file: ${e.toString()}',
        totalRows: 0,
        successfulRows: 0,
        errors: [e.toString()],
      );
    }
  }

  Future<ImportResult> _processImportDataWithMappings(String tableId, List<List<dynamic>> rows, List<ColumnMapping> mappings) async {
    try {
      var columns = await _schemaService.getColumnsForTable(tableId);

      // Create columns based on mappings if table is empty
      if (columns.isEmpty && rows.isNotEmpty) {
        for (final mapping in mappings) {
          if (mapping.shouldImport) {
            await _schemaService.createColumn(
              tableId: tableId,
              name: mapping.header,
              dataType: mapping.dataType,
              isRequired: false,
            );
          }
        }
        // Refresh columns list
        columns = await _schemaService.getColumnsForTable(tableId);
      }

      if (columns.isEmpty) {
        return ImportResult(
          success: false,
          message: 'No columns to import',
          totalRows: 0,
          successfulRows: 0,
          errors: [],
        );
      }

      final errors = <String>[];
      int successfulRows = 0;

      // Skip header row
      final dataRows = rows.skip(1).toList();
      final headers = rows.first.map((h) => h.toString().trim()).toList();

      // Create mapping from header index to column
      final columnMapping = <int, FlexibleColumn>{};
      for (int i = 0; i < headers.length; i++) {
        final header = headers[i];
        final mapping = mappings.where((m) => m.header == header && m.shouldImport).firstOrNull;
        if (mapping != null) {
          final column = columns.where((c) => c.name?.toLowerCase() == header.toLowerCase()).firstOrNull;
          if (column != null) {
            columnMapping[i] = column;
          }
        }
      }

      // Process each data row
      for (int rowIndex = 0; rowIndex < dataRows.length; rowIndex++) {
        try {
          final row = dataRows[rowIndex];
          final rowData = <String, dynamic>{};

          // Map data to columns
          for (final entry in columnMapping.entries) {
            final cellIndex = entry.key;
            final column = entry.value;

            if (cellIndex < row.length) {
              final cellValue = row[cellIndex]?.toString().trim() ?? '';

              // Validate and convert data based on column type
              final convertedValue = _convertCellValue(cellValue, column.dataType);
              if (convertedValue != null) {
                rowData[column.columnId!] = convertedValue;
              }
            }
          }

          // Create row in database
          final rowId = await _schemaService.createRow(tableId);
          await _schemaService.updateRowData(rowId, rowData);
          successfulRows++;

        } catch (e) {
          errors.add('Row ${rowIndex + 2}: ${e.toString()}');
        }
      }

      return ImportResult(
        success: successfulRows > 0,
        message: successfulRows == dataRows.length
            ? 'All rows imported successfully'
            : 'Partial import completed',
        totalRows: dataRows.length,
        successfulRows: successfulRows,
        errors: errors,
      );

    } catch (e) {
      return ImportResult(
        success: false,
        message: 'Error processing import data: ${e.toString()}',
        totalRows: 0,
        successfulRows: 0,
        errors: [e.toString()],
      );
    }
  }

  Future<ImportResult> _processImportData(String tableId, List<List<dynamic>> rows) async {
    try {
      var columns = await _schemaService.getColumnsForTable(tableId);

      // If table has no columns, auto-create them from the first row (headers)
      if (columns.isEmpty && rows.isNotEmpty) {
        final headers = rows.first.map((h) => h.toString().trim()).toList();
        final dataRows = rows.skip(1).toList();

        // Analyze data types from sample data
        final detectedTypes = _analyzeDataTypes(headers, dataRows);

        // Create columns automatically
        for (int i = 0; i < headers.length; i++) {
          final header = headers[i];
          if (header.isNotEmpty) {
            await _schemaService.createColumn(
              tableId: tableId,
              name: header,
              dataType: detectedTypes[i],
              isRequired: false,
            );

            // Refresh columns list
            columns = await _schemaService.getColumnsForTable(tableId);
          }
        }
      }

      if (columns.isEmpty) {
        return ImportResult(
          success: false,
          message: 'Table has no columns defined and could not auto-create from import data',
          totalRows: 0,
          successfulRows: 0,
          errors: [],
        );
      }

      final errors = <String>[];
      int successfulRows = 0;
      
      // Assume first row contains headers
      final headers = rows.first.map((h) => h.toString().trim()).toList();
      final dataRows = rows.skip(1).toList();

      // Create column mapping
      final columnMapping = <int, FlexibleColumn>{};
      for (int i = 0; i < headers.length; i++) {
        final header = headers[i];
        final column = columns.where((c) => 
            c.name?.toLowerCase() == header.toLowerCase()).firstOrNull;
        if (column != null) {
          columnMapping[i] = column;
        }
      }

      if (columnMapping.isEmpty) {
        return ImportResult(
          success: false,
          message: 'No matching columns found between file and table',
          totalRows: dataRows.length,
          successfulRows: 0,
          errors: ['Headers: ${headers.join(', ')}', 'Table columns: ${columns.map((c) => c.name).join(', ')}'],
        );
      }

      // Process each data row
      for (int rowIndex = 0; rowIndex < dataRows.length; rowIndex++) {
        try {
          final row = dataRows[rowIndex];
          final rowData = <String, dynamic>{};

          // Map data to columns
          for (final entry in columnMapping.entries) {
            final cellIndex = entry.key;
            final column = entry.value;
            
            if (cellIndex < row.length) {
              final cellValue = row[cellIndex]?.toString().trim() ?? '';
              
              // Validate and convert data based on column type
              final convertedValue = _convertCellValue(cellValue, column.dataType);
              if (convertedValue != null) {
                rowData[column.columnId!] = convertedValue;
              }
            }
          }

          // Create row in database
          final rowId = await _schemaService.createRow(tableId);
          await _schemaService.updateRowData(rowId, rowData);
          successfulRows++;
          
        } catch (e) {
          errors.add('Row ${rowIndex + 2}: ${e.toString()}');
        }
      }

      return ImportResult(
        success: successfulRows > 0,
        message: successfulRows == dataRows.length 
            ? 'All rows imported successfully'
            : 'Partial import completed',
        totalRows: dataRows.length,
        successfulRows: successfulRows,
        errors: errors,
      );

    } catch (e) {
      return ImportResult(
        success: false,
        message: 'Error processing import data: ${e.toString()}',
        totalRows: 0,
        successfulRows: 0,
        errors: [e.toString()],
      );
    }
  }

  List<ColumnDataType> _analyzeDataTypes(List<String> headers, List<List<dynamic>> dataRows) {
    final types = <ColumnDataType>[];

    for (int colIndex = 0; colIndex < headers.length; colIndex++) {
      final columnValues = <String>[];

      // Collect sample values from this column (up to 10 rows)
      for (int rowIndex = 0; rowIndex < dataRows.length && rowIndex < 10; rowIndex++) {
        if (colIndex < dataRows[rowIndex].length) {
          final value = dataRows[rowIndex][colIndex]?.toString().trim() ?? '';
          if (value.isNotEmpty) {
            columnValues.add(value);
          }
        }
      }

      // Analyze the values to determine data type
      types.add(_detectDataType(columnValues));
    }

    return types;
  }

  ColumnDataType _detectDataType(List<String> values) {
    if (values.isEmpty) return ColumnDataType.text;

    int numberCount = 0;
    int currencyCount = 0;
    int dateCount = 0;
    int booleanCount = 0;

    for (final value in values) {
      final lowerValue = value.toLowerCase();

      // Check for boolean
      if (['true', 'false', 'yes', 'no', '1', '0', 'on', 'off'].contains(lowerValue)) {
        booleanCount++;
        continue;
      }

      // Check for currency (contains currency symbols)
      if (RegExp(r'[\$£€¥₹₽₩₪₨₦₡₵₴₸₼₾₿]').hasMatch(value) ||
          value.contains('USD') || value.contains('EUR') || value.contains('GBP')) {
        currencyCount++;
        continue;
      }

      // Check for number
      if (double.tryParse(value.replaceAll(RegExp(r'[,\s]'), '')) != null) {
        numberCount++;
        continue;
      }

      // Check for date
      try {
        DateTime.parse(value);
        dateCount++;
        continue;
      } catch (e) {
        // Try other date formats
        if (RegExp(r'^\d{1,2}[/-]\d{1,2}[/-]\d{2,4}$').hasMatch(value) ||
            RegExp(r'^\d{4}[/-]\d{1,2}[/-]\d{1,2}$').hasMatch(value)) {
          dateCount++;
          continue;
        }
      }
    }

    final totalValues = values.length;

    // Determine type based on majority (>= 70%)
    if (booleanCount >= (totalValues * 0.7)) {
      return ColumnDataType.boolean;
    } else if (currencyCount >= (totalValues * 0.7)) {
      return ColumnDataType.currency;
    } else if (dateCount >= (totalValues * 0.7)) {
      return ColumnDataType.date;
    } else if (numberCount >= (totalValues * 0.7)) {
      return ColumnDataType.number;
    } else {
      return ColumnDataType.text;
    }
  }

  dynamic _convertCellValue(String value, ColumnDataType dataType) {
    if (value.isEmpty) return null;

    try {
      switch (dataType) {
        case ColumnDataType.text:
          return value;
        case ColumnDataType.number:
          return double.tryParse(value) ?? value;
        case ColumnDataType.currency:
          // Remove currency symbols and parse as number
          final cleanValue = value.replaceAll(RegExp(r'[^\d.-]'), '');
          return double.tryParse(cleanValue) ?? value;
        case ColumnDataType.date:
          // Try to parse as date, return as string if fails
          try {
            DateTime.parse(value);
            return value;
          } catch (e) {
            return value;
          }
        case ColumnDataType.boolean:
          final lowerValue = value.toLowerCase();
          if (['true', 'yes', '1', 'on'].contains(lowerValue)) {
            return true;
          } else if (['false', 'no', '0', 'off'].contains(lowerValue)) {
            return false;
          }
          return value;
        case ColumnDataType.dropdown:
          return value;
      }
    } catch (e) {
      return value;
    }
  }

  // Export to Excel/CSV with user-selected destination
  Future<String?> exportToExcel(String tableId, String tableName) async {
    try {
      // Let user choose save location
      final String? outputFile = await FilePicker.platform.saveFile(
        dialogTitle: 'Save Excel file',
        fileName: '${tableName}_export_${DateTime.now().millisecondsSinceEpoch}.xlsx',
        type: FileType.custom,
        allowedExtensions: ['xlsx'],
      );

      if (outputFile == null) {
        return null; // User cancelled
      }

      final columns = await _schemaService.getColumnsForTable(tableId);
      final rows = await _schemaService.getRowsForTable(tableId);

      if (columns.isEmpty) {
        throw Exception('Table has no columns');
      }

      final excel = Excel.createExcel();
      final sheet = excel['Sheet1'];

      // Add headers
      for (int i = 0; i < columns.length; i++) {
        sheet.cell(CellIndex.indexByColumnRow(columnIndex: i, rowIndex: 0))
            .value = TextCellValue(columns[i].name ?? 'Column ${i + 1}');
      }

      // Add data rows
      for (int rowIndex = 0; rowIndex < rows.length; rowIndex++) {
        final row = rows[rowIndex];
        final rowData = row.data != null
            ? jsonDecode(row.data!) as Map<String, dynamic>
            : <String, dynamic>{};

        for (int colIndex = 0; colIndex < columns.length; colIndex++) {
          final column = columns[colIndex];
          final value = rowData[column.columnId] ?? '';

          sheet.cell(CellIndex.indexByColumnRow(
            columnIndex: colIndex,
            rowIndex: rowIndex + 1
          )).value = TextCellValue(value.toString());
        }
      }

      // Save file to user-selected location
      final file = File(outputFile);
      await file.writeAsBytes(excel.encode()!);
      return outputFile;

    } catch (e) {
      throw Exception('Error exporting to Excel: ${e.toString()}');
    }
  }

  Future<String?> exportToCsv(String tableId, String tableName) async {
    try {
      // Let user choose save location
      final String? outputFile = await FilePicker.platform.saveFile(
        dialogTitle: 'Save CSV file',
        fileName: '${tableName}_export_${DateTime.now().millisecondsSinceEpoch}.csv',
        type: FileType.custom,
        allowedExtensions: ['csv'],
      );

      if (outputFile == null) {
        return null; // User cancelled
      }

      final columns = await _schemaService.getColumnsForTable(tableId);
      final rows = await _schemaService.getRowsForTable(tableId);

      if (columns.isEmpty) {
        throw Exception('Table has no columns');
      }

      final csvData = <List<String>>[];

      // Add headers
      csvData.add(columns.map((c) => c.name ?? '').toList());

      // Add data rows
      for (final row in rows) {
        final rowData = row.data != null
            ? jsonDecode(row.data!) as Map<String, dynamic>
            : <String, dynamic>{};

        final csvRow = columns.map((column) {
          final value = rowData[column.columnId] ?? '';
          return value.toString();
        }).toList();

        csvData.add(csvRow);
      }

      // Convert to CSV string
      final csvString = const ListToCsvConverter().convert(csvData);

      // Save file to user-selected location
      final file = File(outputFile);
      await file.writeAsString(csvString);
      return outputFile;

    } catch (e) {
      throw Exception('Error exporting to CSV: ${e.toString()}');
    }
  }
}

class ImportResult {
  final bool success;
  final String message;
  final int totalRows;
  final int successfulRows;
  final List<String> errors;

  ImportResult({
    required this.success,
    required this.message,
    required this.totalRows,
    required this.successfulRows,
    required this.errors,
  });
}
