import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Platform,
} from 'react-native';
import { Card, Button, Title, Paragraph } from 'react-native-paper';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';
import { AppConstants } from '../constants/AppConstants';

interface HomeScreenProps {
  navigation: any;
}

const HomeScreen: React.FC<HomeScreenProps> = ({ navigation }) => {
  const handleNewProject = () => {
    // navigation.navigate('NewProject');
    console.log('New Project pressed');
  };

  const handleOpenProject = () => {
    // navigation.navigate('ProjectList');
    console.log('Open Project pressed');
  };

  const handleQuickQuote = () => {
    console.log('Quick Quote pressed');
  };

  const handleAIAssistant = () => {
    // navigation.navigate('AIAssistant');
    console.log('AI Assistant pressed');
  };

  const handleAdminDashboard = () => {
    // navigation.navigate('AdminDashboard');
    console.log('Admin Dashboard pressed');
  };

  return (
    <ScrollView style={styles.container} contentContainerStyle={styles.content}>
      {/* Header Section */}
      <View style={styles.header}>
        <Icon
          name="fire"
          size={80}
          color={AppConstants.COLORS.PRIMARY}
          style={styles.headerIcon}
        />
        <Title style={styles.title}>Fire System Estimating Tool</Title>
        <Paragraph style={styles.subtitle}>
          Create accurate estimates for fire alarm, sprinkler, and suppression systems
        </Paragraph>
      </View>

      {/* Main Action Cards */}
      <View style={styles.cardContainer}>
        <Card style={styles.card} elevation={2}>
          <TouchableOpacity onPress={handleNewProject} style={styles.cardButton}>
            <Card.Content style={styles.cardContent}>
              <Icon name="plus" size={32} color={AppConstants.COLORS.PRIMARY} />
              <Title style={styles.cardTitle}>New Project</Title>
              <Paragraph style={styles.cardDescription}>
                Start a new fire system project
              </Paragraph>
            </Card.Content>
          </TouchableOpacity>
        </Card>

        <Card style={styles.card} elevation={2}>
          <TouchableOpacity onPress={handleOpenProject} style={styles.cardButton}>
            <Card.Content style={styles.cardContent}>
              <Icon name="folder-open" size={32} color={AppConstants.COLORS.PRIMARY} />
              <Title style={styles.cardTitle}>Open Project</Title>
              <Paragraph style={styles.cardDescription}>
                Continue working on existing projects
              </Paragraph>
            </Card.Content>
          </TouchableOpacity>
        </Card>

        <Card style={styles.card} elevation={2}>
          <TouchableOpacity onPress={handleQuickQuote} style={styles.cardButton}>
            <Card.Content style={styles.cardContent}>
              <Icon name="calculator" size={32} color={AppConstants.COLORS.ACCENT} />
              <Title style={styles.cardTitle}>Quick Quote</Title>
              <Paragraph style={styles.cardDescription}>
                Generate fast estimates without projects
              </Paragraph>
            </Card.Content>
          </TouchableOpacity>
        </Card>

        <Card style={styles.card} elevation={2}>
          <TouchableOpacity onPress={handleAIAssistant} style={styles.cardButton}>
            <Card.Content style={styles.cardContent}>
              <Icon name="robot" size={32} color={AppConstants.COLORS.SUCCESS} />
              <Title style={styles.cardTitle}>AI Assistant</Title>
              <Paragraph style={styles.cardDescription}>
                Get intelligent help with calculations
              </Paragraph>
            </Card.Content>
          </TouchableOpacity>
        </Card>

        <Card style={styles.card} elevation={2}>
          <TouchableOpacity onPress={handleAdminDashboard} style={styles.cardButton}>
            <Card.Content style={styles.cardContent}>
              <Icon name="cog" size={32} color={AppConstants.COLORS.TEXT_SECONDARY} />
              <Title style={styles.cardTitle}>Admin Dashboard</Title>
              <Paragraph style={styles.cardDescription}>
                Manage database, settings, and backups
              </Paragraph>
            </Card.Content>
          </TouchableOpacity>
        </Card>
      </View>

      {/* Footer */}
      <View style={styles.footer}>
        <Text style={styles.footerText}>
          {AppConstants.APP_NAME} v{AppConstants.APP_VERSION}
        </Text>
        <Text style={styles.footerText}>
          Platform: {Platform.OS === 'windows' ? 'Windows' : Platform.OS}
        </Text>
      </View>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: AppConstants.COLORS.BACKGROUND,
  },
  content: {
    padding: AppConstants.SPACING.MD,
  },
  header: {
    alignItems: 'center',
    marginBottom: AppConstants.SPACING.XL,
    paddingVertical: AppConstants.SPACING.LG,
  },
  headerIcon: {
    marginBottom: AppConstants.SPACING.MD,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    color: AppConstants.COLORS.TEXT_PRIMARY,
    textAlign: 'center',
    marginBottom: AppConstants.SPACING.SM,
  },
  subtitle: {
    fontSize: 16,
    color: AppConstants.COLORS.TEXT_SECONDARY,
    textAlign: 'center',
    paddingHorizontal: AppConstants.SPACING.LG,
  },
  cardContainer: {
    gap: AppConstants.SPACING.MD,
  },
  card: {
    backgroundColor: AppConstants.COLORS.CARD,
    borderRadius: AppConstants.BORDER_RADIUS.LG,
  },
  cardButton: {
    borderRadius: AppConstants.BORDER_RADIUS.LG,
  },
  cardContent: {
    alignItems: 'center',
    padding: AppConstants.SPACING.LG,
  },
  cardTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: AppConstants.COLORS.TEXT_PRIMARY,
    marginTop: AppConstants.SPACING.SM,
    marginBottom: AppConstants.SPACING.XS,
  },
  cardDescription: {
    fontSize: 14,
    color: AppConstants.COLORS.TEXT_SECONDARY,
    textAlign: 'center',
  },
  footer: {
    alignItems: 'center',
    marginTop: AppConstants.SPACING.XL,
    paddingVertical: AppConstants.SPACING.LG,
  },
  footerText: {
    fontSize: 12,
    color: AppConstants.COLORS.TEXT_DISABLED,
    marginBottom: AppConstants.SPACING.XS,
  },
});

export default HomeScreen;
