// Fix for ISAR namespace issue with AGP 8.2.1+
// This script adds the missing namespace to the ISAR plugin and removes package attribute

subprojects { project ->
    afterEvaluate {
        if (project.name == 'isar_flutter_libs') {
            if (project.hasProperty('android')) {
                project.android {
                    namespace = 'dev.isar.isar_flutter_libs'
                }

                // Remove package attribute from AndroidManifest.xml
                project.tasks.whenTaskAdded { task ->
                    if (task.name.contains('processDebugManifest') || task.name.contains('processReleaseManifest')) {
                        task.doFirst {
                            def manifestFile = new File("${project.projectDir}/src/main/AndroidManifest.xml")
                            if (manifestFile.exists()) {
                                def manifestContent = manifestFile.text
                                if (manifestContent.contains('package=')) {
                                    manifestContent = manifestContent.replaceAll(/package="[^"]*"/, '')
                                    manifestFile.text = manifestContent
                                    println "Removed package attribute from ISAR AndroidManifest.xml"
                                }
                            }
                        }
                    }
                }

                println "Added namespace to ISAR plugin: ${project.name}"
            }
        }
    }
}
