import { Platform } from 'react-native';

export const AppConstants = {
  // App Information
  APP_NAME: 'FireTool RN',
  APP_VERSION: '1.0.0',
  
  // Colors - Windows-appropriate theme
  COLORS: {
    PRIMARY: '#0078D4', // Microsoft Blue
    PRIMARY_DARK: '#106EBE',
    PRIMARY_LIGHT: '#40E0FF',
    SECONDARY: '#00BCF2',
    ACCENT: '#FFB900',
    
    // Background colors
    BACKGROUND: '#FFFFFF',
    SURFACE: '#F3F2F1',
    CARD: '#FFFFFF',
    
    // Text colors
    TEXT_PRIMARY: '#323130',
    TEXT_SECONDARY: '#605E5C',
    TEXT_DISABLED: '#A19F9D',
    
    // Status colors
    SUCCESS: '#107C10',
    WARNING: '#FFB900',
    ERROR: '#D13438',
    INFO: '#0078D4',
    
    // Border colors
    BORDER: '#EDEBE9',
    DIVIDER: '#E1DFDD',
    
    // Windows specific
    WINDOWS_ACCENT: Platform.OS === 'windows' ? '#0078D4' : '#0078D4',
  },
  
  // Typography
  FONTS: {
    REGULAR: Platform.select({
      windows: 'Segoe UI',
      default: 'System',
    }),
    MEDIUM: Platform.select({
      windows: 'Segoe UI Semibold',
      default: 'System',
    }),
    BOLD: Platform.select({
      windows: 'Segoe UI Bold',
      default: 'System',
    }),
  },
  
  // Spacing
  SPACING: {
    XS: 4,
    SM: 8,
    MD: 16,
    LG: 24,
    XL: 32,
    XXL: 48,
  },
  
  // Border radius
  BORDER_RADIUS: {
    SM: 2,
    MD: 4,
    LG: 8,
    XL: 12,
  },
  
  // Shadows (Windows-style)
  SHADOWS: {
    SMALL: Platform.select({
      windows: {
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 1 },
        shadowOpacity: 0.1,
        shadowRadius: 2,
        elevation: 2,
      },
      default: {
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 1 },
        shadowOpacity: 0.1,
        shadowRadius: 2,
        elevation: 2,
      },
    }),
    MEDIUM: Platform.select({
      windows: {
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.15,
        shadowRadius: 4,
        elevation: 4,
      },
      default: {
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.15,
        shadowRadius: 4,
        elevation: 4,
      },
    }),
    LARGE: Platform.select({
      windows: {
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 4 },
        shadowOpacity: 0.2,
        shadowRadius: 8,
        elevation: 8,
      },
      default: {
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 4 },
        shadowOpacity: 0.2,
        shadowRadius: 8,
        elevation: 8,
      },
    }),
  },
  
  // Layout
  LAYOUT: {
    HEADER_HEIGHT: 56,
    SIDEBAR_WIDTH: 280,
    SIDEBAR_COLLAPSED_WIDTH: 48,
    CARD_MIN_HEIGHT: 120,
    BUTTON_HEIGHT: 40,
    INPUT_HEIGHT: 40,
  },
  
  // Animation durations
  ANIMATION: {
    FAST: 150,
    NORMAL: 250,
    SLOW: 350,
  },
  
  // Database
  DATABASE: {
    NAME: 'FireToolRN.db',
    VERSION: 1,
  },
  
  // System Types with Icons (Windows-style icons)
  SYSTEM_TYPES: {
    ALARM: {
      name: 'Fire Alarm',
      icon: 'bell',
      color: '#D13438',
    },
    WATER: {
      name: 'Water Sprinkler',
      icon: 'water',
      color: '#0078D4',
    },
    FOAM: {
      name: 'Foam System',
      icon: 'cloud',
      color: '#00BCF2',
    },
    CLEAN_AGENT: {
      name: 'Clean Agent',
      icon: 'shield-check',
      color: '#107C10',
    },
    CO2: {
      name: 'CO2 System',
      icon: 'molecule-co2',
      color: '#605E5C',
    },
    MATERIALS: {
      name: 'Materials',
      icon: 'package-variant',
      color: '#FFB900',
    },
    CUSTOM: {
      name: 'Custom',
      icon: 'cog',
      color: '#8764B8',
    },
  },
  
  // Currency configurations
  CURRENCIES: {
    USD: { symbol: '$', name: 'US Dollar', code: 'USD' },
    GBP: { symbol: '£', name: 'British Pound', code: 'GBP' },
    SAR: { symbol: 'ر.س', name: 'Saudi Riyal', code: 'SAR' },
    EGP: { symbol: 'ج.م', name: 'Egyptian Pound', code: 'EGP' },
    AED: { symbol: 'د.إ', name: 'UAE Dirham', code: 'AED' },
  },
  
  // Default values
  DEFAULTS: {
    CURRENCY: 'SAR',
    EXCHANGE_RATE: 3.75,
    SHIPPING_RATE: 1.15,
    MARGIN_RATE: 1.0,
    INCLUDE_INSTALLATION: true,
  },
  
  // Windows-specific configurations
  WINDOWS: {
    TITLE_BAR_HEIGHT: Platform.OS === 'windows' ? 32 : 0,
    MENU_BAR_HEIGHT: Platform.OS === 'windows' ? 24 : 0,
    WINDOW_CONTROLS_WIDTH: Platform.OS === 'windows' ? 138 : 0,
  },
  
  // File extensions for import/export
  FILE_EXTENSIONS: {
    EXCEL: ['.xlsx', '.xls'],
    CSV: ['.csv'],
    JSON: ['.json'],
    PDF: ['.pdf'],
  },
  
  // Grid and table configurations
  GRID: {
    DEFAULT_ROW_HEIGHT: 40,
    HEADER_HEIGHT: 48,
    MIN_COLUMN_WIDTH: 100,
    MAX_COLUMN_WIDTH: 400,
    RESIZE_HANDLE_WIDTH: 4,
  },
  
  // AI Assistant configurations
  AI: {
    MAX_MESSAGES: 100,
    TYPING_DELAY: 50,
    RESPONSE_TIMEOUT: 30000,
  },
};
