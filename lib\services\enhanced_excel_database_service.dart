import 'dart:async';
import 'dart:io';
import 'dart:convert';
import 'package:sqflite/sqflite.dart';
import 'package:sqflite_common_ffi/sqflite_ffi.dart';
import 'package:path/path.dart';
import 'package:excel/excel.dart';
import 'package:uuid/uuid.dart';
import 'package:path_provider/path_provider.dart';

class EnhancedExcelDatabaseService {
  static final EnhancedExcelDatabaseService _instance = EnhancedExcelDatabaseService._internal();
  static Database? _database;
  const _uuid = Uuid();
  
  factory EnhancedExcelDatabaseService() {
    return _instance;
  }
  
  EnhancedExcelDatabaseService._internal();
  
  Future<Database> get database async {
    if (_database != null) return _database!;
    _database = await _initDatabase();
    return _database!;
  }
  
  Future<Database> _initDatabase() async {
    final dbPath = await getDatabasesPath();
    final path = join(dbPath, 'enhanced_excel_database.db');
    
    return await openDatabase(
      path,
      version: 1,
      onCreate: _createDatabase,
      onUpgrade: _upgradeDatabase,
    );
  }
  
  Future<void> _createDatabase(Database db, int version) async {
    // Create metadata tables
    await db.execute('''
      CREATE TABLE sections (
        id TEXT PRIMARY KEY,
        name TEXT NOT NULL,
        icon TEXT,
        order_index INTEGER,
        created_at TEXT,
        updated_at TEXT
      )
    ''');
    
    await db.execute('''
      CREATE TABLE subsections (
        id TEXT PRIMARY KEY,
        section_id TEXT NOT NULL,
        name TEXT NOT NULL,
        table_name TEXT NOT NULL,
        order_index INTEGER,
        created_at TEXT,
        updated_at TEXT,
        FOREIGN KEY (section_id) REFERENCES sections (id) ON DELETE CASCADE
      )
    ''');
    
    await db.execute('''
      CREATE TABLE table_schemas (
        id TEXT PRIMARY KEY,
        table_name TEXT NOT NULL UNIQUE,
        schema_json TEXT NOT NULL,
        created_at TEXT,
        updated_at TEXT
      )
    ''');
    
    await db.execute('''
      CREATE TABLE sync_metadata (
        id TEXT PRIMARY KEY,
        table_name TEXT NOT NULL,
        row_id TEXT NOT NULL,
        last_synced TEXT,
        sync_status TEXT,
        conflict_data TEXT
      )
    ''');
    
    // Create default sections
    await _createDefaultSections(db);
  }
  
  Future<void> _upgradeDatabase(Database db, int oldVersion, int newVersion) async {
    // Handle database upgrades
  }
  
  Future<void> _createDefaultSections(Database db) async {
    final defaultSections = [
      {'name': 'Fire Alarm', 'icon': 'alarm', 'order_index': 0},
      {'name': 'Firefighting', 'icon': 'water_drop', 'order_index': 1},
      {'name': 'Clean Agent', 'icon': 'air', 'order_index': 2},
      {'name': 'Foam', 'icon': 'bubble_chart', 'order_index': 3},
      {'name': 'CO2', 'icon': 'cloud', 'order_index': 4},
      {'name': 'Fire Pumps', 'icon': 'settings', 'order_index': 5},
      {'name': 'Foam Pumps', 'icon': 'pump', 'order_index': 6},
      {'name': 'Civil Works', 'icon': 'construction', 'order_index': 7},
    ];
    
    for (final section in defaultSections) {
      final sectionId = _uuid.v4();
      await db.insert('sections', {
        'id': sectionId,
        'name': section['name'],
        'icon': section['icon'],
        'order_index': section['order_index'],
        'created_at': DateTime.now().toIso8601String(),
        'updated_at': DateTime.now().toIso8601String(),
      });
      
      // Create default subsections for each section
      await _createDefaultSubsections(db, sectionId, section['name'] as String);
    }
  }
  
  Future<void> _createDefaultSubsections(Database db, String sectionId, String sectionName) async {
    List<String> subsectionNames = [];
    
    switch (sectionName) {
      case 'Fire Alarm':
        subsectionNames = ['Notifier', 'Simplex', 'Conduits', 'Ex-Devices', 'Cables'];
        break;
      case 'Firefighting':
        subsectionNames = ['Sprinklers', 'Pipes', 'Valves', 'Pumps', 'Tanks'];
        break;
      case 'Clean Agent':
        subsectionNames = ['FM200', 'Novec', 'Inergen', 'Cylinders', 'Piping'];
        break;
      case 'Foam':
        subsectionNames = ['Generators', 'Concentrate', 'Proportioners', 'Monitors'];
        break;
      case 'CO2':
        subsectionNames = ['Cylinders', 'Valves', 'Nozzles', 'Detection'];
        break;
      case 'Fire Pumps':
        subsectionNames = ['Electric', 'Diesel', 'Jockey', 'Controllers'];
        break;
      case 'Foam Pumps':
        subsectionNames = ['Centrifugal', 'Positive Displacement', 'Skid Units'];
        break;
      case 'Civil Works':
        subsectionNames = ['Excavation', 'Concrete', 'Steel Work', 'Finishing'];
        break;
    }
    
    for (int i = 0; i < subsectionNames.length; i++) {
      final subsectionId = _uuid.v4();
      final tableName = '${sectionName.toLowerCase().replaceAll(' ', '_')}_${subsectionNames[i].toLowerCase().replaceAll(' ', '_')}';
      
      await db.insert('subsections', {
        'id': subsectionId,
        'section_id': sectionId,
        'name': subsectionNames[i],
        'table_name': tableName,
        'order_index': i,
        'created_at': DateTime.now().toIso8601String(),
        'updated_at': DateTime.now().toIso8601String(),
      });
      
      // Create the actual data table
      await _createDataTable(db, tableName);
    }
  }
  
  Future<void> _createDataTable(Database db, String tableName) async {
    // Create a flexible table structure
    await db.execute('''
      CREATE TABLE $tableName (
        id TEXT PRIMARY KEY,
        data_json TEXT NOT NULL,
        created_at TEXT,
        updated_at TEXT,
        sync_id TEXT,
        is_deleted INTEGER DEFAULT 0
      )
    ''');
    
    // Create default schema
    final defaultSchema = {
      'columns': [
        {'name': 'model', 'type': 'text', 'label': 'Model', 'required': true},
        {'name': 'description', 'type': 'text', 'label': 'Description', 'required': false},
        {'name': 'manufacturer', 'type': 'text', 'label': 'Manufacturer', 'required': false},
        {'name': 'approval', 'type': 'text', 'label': 'Approval', 'required': false},
        {'name': 'ex_works_price', 'type': 'currency', 'label': 'Ex-Works Price', 'required': false, 'currency_symbol': '\$'},
        {'name': 'local_price', 'type': 'currency', 'label': 'Local Price', 'required': false, 'currency_symbol': '\$'},
        {'name': 'installation_price', 'type': 'currency', 'label': 'Installation Price', 'required': false, 'currency_symbol': '\$'},
      ],
      'settings': {
        'frozen_rows': 0,
        'frozen_columns': 1,
        'default_row_height': 35,
        'default_column_width': 120,
      }
    };
    
    await db.insert('table_schemas', {
      'id': _uuid.v4(),
      'table_name': tableName,
      'schema_json': jsonEncode(defaultSchema),
      'created_at': DateTime.now().toIso8601String(),
      'updated_at': DateTime.now().toIso8601String(),
    });
  }
  
  // Section Management
  Future<List<Map<String, dynamic>>> getSections() async {
    final db = await database;
    return await db.query('sections', orderBy: 'order_index ASC');
  }
  
  Future<String> createSection(String name, String? icon) async {
    final db = await database;
    final sectionId = _uuid.v4();
    
    // Get the highest order index
    final result = await db.rawQuery('SELECT MAX(order_index) as max_order FROM sections');
    final maxOrder = result.first['max_order'] as int? ?? -1;
    
    await db.insert('sections', {
      'id': sectionId,
      'name': name,
      'icon': icon ?? 'folder',
      'order_index': maxOrder + 1,
      'created_at': DateTime.now().toIso8601String(),
      'updated_at': DateTime.now().toIso8601String(),
    });
    
    return sectionId;
  }
  
  Future<void> updateSection(String sectionId, String name, String? icon) async {
    final db = await database;
    await db.update(
      'sections',
      {
        'name': name,
        'icon': icon,
        'updated_at': DateTime.now().toIso8601String(),
      },
      where: 'id = ?',
      whereArgs: [sectionId],
    );
  }
  
  Future<void> deleteSection(String sectionId) async {
    final db = await database;
    
    // Get all subsections for this section
    final subsections = await db.query(
      'subsections',
      where: 'section_id = ?',
      whereArgs: [sectionId],
    );
    
    // Drop all data tables for subsections
    for (final subsection in subsections) {
      final tableName = subsection['table_name'] as String;
      await db.execute('DROP TABLE IF EXISTS $tableName');
      await db.delete('table_schemas', where: 'table_name = ?', whereArgs: [tableName]);
    }
    
    // Delete the section (subsections will be deleted by CASCADE)
    await db.delete('sections', where: 'id = ?', whereArgs: [sectionId]);
  }
  
  Future<void> reorderSections(List<String> sectionIds) async {
    final db = await database;
    
    await db.transaction((txn) async {
      for (int i = 0; i < sectionIds.length; i++) {
        await txn.update(
          'sections',
          {'order_index': i, 'updated_at': DateTime.now().toIso8601String()},
          where: 'id = ?',
          whereArgs: [sectionIds[i]],
        );
      }
    });
  }
  
  // Subsection Management
  Future<List<Map<String, dynamic>>> getSubsections(String sectionId) async {
    final db = await database;
    return await db.query(
      'subsections',
      where: 'section_id = ?',
      whereArgs: [sectionId],
      orderBy: 'order_index ASC',
    );
  }
  
  Future<String> createSubsection(String sectionId, String name) async {
    final db = await database;
    final subsectionId = _uuid.v4();
    
    // Generate table name
    final section = await db.query('sections', where: 'id = ?', whereArgs: [sectionId]);
    final sectionName = section.first['name'] as String;
    final tableName = '${sectionName.toLowerCase().replaceAll(' ', '_')}_${name.toLowerCase().replaceAll(' ', '_')}_${DateTime.now().millisecondsSinceEpoch}';
    
    // Get the highest order index for this section
    final result = await db.rawQuery(
      'SELECT MAX(order_index) as max_order FROM subsections WHERE section_id = ?',
      [sectionId],
    );
    final maxOrder = result.first['max_order'] as int? ?? -1;
    
    await db.insert('subsections', {
      'id': subsectionId,
      'section_id': sectionId,
      'name': name,
      'table_name': tableName,
      'order_index': maxOrder + 1,
      'created_at': DateTime.now().toIso8601String(),
      'updated_at': DateTime.now().toIso8601String(),
    });
    
    // Create the data table
    await _createDataTable(db, tableName);
    
    return subsectionId;
  }
  
  Future<void> updateSubsection(String subsectionId, String name) async {
    final db = await database;
    await db.update(
      'subsections',
      {
        'name': name,
        'updated_at': DateTime.now().toIso8601String(),
      },
      where: 'id = ?',
      whereArgs: [subsectionId],
    );
  }
  
  Future<void> deleteSubsection(String subsectionId) async {
    final db = await database;
    
    // Get the subsection to find the table name
    final subsection = await db.query(
      'subsections',
      where: 'id = ?',
      whereArgs: [subsectionId],
    );
    
    if (subsection.isNotEmpty) {
      final tableName = subsection.first['table_name'] as String;
      
      // Drop the data table
      await db.execute('DROP TABLE IF EXISTS $tableName');
      
      // Delete schema
      await db.delete('table_schemas', where: 'table_name = ?', whereArgs: [tableName]);
      
      // Delete the subsection
      await db.delete('subsections', where: 'id = ?', whereArgs: [subsectionId]);
    }
  }
  
  Future<void> reorderSubsections(String sectionId, List<String> subsectionIds) async {
    final db = await database;
    
    await db.transaction((txn) async {
      for (int i = 0; i < subsectionIds.length; i++) {
        await txn.update(
          'subsections',
          {'order_index': i, 'updated_at': DateTime.now().toIso8601String()},
          where: 'id = ? AND section_id = ?',
          whereArgs: [subsectionIds[i], sectionId],
        );
      }
    });
  }
  
  // Table Schema Management
  Future<Map<String, dynamic>?> getTableSchema(String tableName) async {
    final db = await database;
    final result = await db.query(
      'table_schemas',
      where: 'table_name = ?',
      whereArgs: [tableName],
    );
    
    if (result.isNotEmpty) {
      return jsonDecode(result.first['schema_json'] as String);
    }
    return null;
  }
  
  Future<void> updateTableSchema(String tableName, Map<String, dynamic> schema) async {
    final db = await database;
    await db.update(
      'table_schemas',
      {
        'schema_json': jsonEncode(schema),
        'updated_at': DateTime.now().toIso8601String(),
      },
      where: 'table_name = ?',
      whereArgs: [tableName],
    );
  }
  
  // Data Management
  Future<List<Map<String, dynamic>>> getTableData(String tableName, {
    int? limit,
    int? offset,
    String? orderBy,
    bool ascending = true,
    Map<String, dynamic>? filters,
  }) async {
    final db = await database;
    
    String query = 'SELECT * FROM $tableName WHERE is_deleted = 0';
    List<dynamic> args = [];
    
    // Add filters
    if (filters != null && filters.isNotEmpty) {
      for (final entry in filters.entries) {
        query += ' AND JSON_EXTRACT(data_json, "\$.${entry.key}") LIKE ?';
        args.add('%${entry.value}%');
      }
    }
    
    // Add ordering
    if (orderBy != null) {
      query += ' ORDER BY JSON_EXTRACT(data_json, "\$.$orderBy") ${ascending ? 'ASC' : 'DESC'}';
    } else {
      query += ' ORDER BY created_at DESC';
    }
    
    // Add pagination
    if (limit != null) {
      query += ' LIMIT $limit';
      if (offset != null) {
        query += ' OFFSET $offset';
      }
    }
    
    final result = await db.rawQuery(query, args);
    
    return result.map((row) {
      final data = jsonDecode(row['data_json'] as String) as Map<String, dynamic>;
      return {
        'id': row['id'],
        'created_at': row['created_at'],
        'updated_at': row['updated_at'],
        'sync_id': row['sync_id'],
        ...data,
      };
    }).toList();
  }
  
  Future<String> insertRow(String tableName, Map<String, dynamic> data) async {
    final db = await database;
    final rowId = _uuid.v4();
    
    await db.insert(tableName, {
      'id': rowId,
      'data_json': jsonEncode(data),
      'created_at': DateTime.now().toIso8601String(),
      'updated_at': DateTime.now().toIso8601String(),
      'sync_id': _uuid.v4(),
      'is_deleted': 0,
    });
    
    return rowId;
  }
  
  Future<void> updateRow(String tableName, String rowId, Map<String, dynamic> data) async {
    final db = await database;
    
    await db.update(
      tableName,
      {
        'data_json': jsonEncode(data),
        'updated_at': DateTime.now().toIso8601String(),
      },
      where: 'id = ?',
      whereArgs: [rowId],
    );
  }
  
  Future<void> deleteRow(String tableName, String rowId) async {
    final db = await database;
    
    await db.update(
      tableName,
      {
        'is_deleted': 1,
        'updated_at': DateTime.now().toIso8601String(),
      },
      where: 'id = ?',
      whereArgs: [rowId],
    );
  }
  
  Future<void> permanentDeleteRow(String tableName, String rowId) async {
    final db = await database;
    await db.delete(tableName, where: 'id = ?', whereArgs: [rowId]);
  }
  
  // Excel Import/Export
  Future<Map<String, dynamic>> importFromExcel(String filePath, String tableName) async {
    final file = File(filePath);
    final bytes = await file.readAsBytes();
    final excel = Excel.decodeBytes(bytes);
    
    int totalRows = 0;
    int successfulRows = 0;
    List<String> errors = [];
    
    for (final sheetName in excel.tables.keys) {
      final sheet = excel.tables[sheetName]!;
      if (sheet.rows.isEmpty) continue;
      
      // Get headers from first row
      final headerRow = sheet.rows.first;
      final headers = headerRow.map((cell) => cell?.value?.toString() ?? '').toList();
      
      // Process data rows
      for (int i = 1; i < sheet.rows.length; i++) {
        final row = sheet.rows[i];
        totalRows++;
        
        try {
          final data = <String, dynamic>{};
          
          for (int j = 0; j < headers.length && j < row.length; j++) {
            final header = headers[j];
            final cellValue = row[j]?.value;
            
            if (header.isNotEmpty && cellValue != null) {
              data[header.toLowerCase().replaceAll(' ', '_')] = cellValue.toString();
            }
          }
          
          if (data.isNotEmpty) {
            await insertRow(tableName, data);
            successfulRows++;
          }
        } catch (e) {
          errors.add('Row ${i + 1}: ${e.toString()}');
        }
      }
    }
    
    return {
      'total_rows': totalRows,
      'successful_rows': successfulRows,
      'errors': errors,
    };
  }
  
  Future<String> exportToExcel(String tableName, String fileName) async {
    final data = await getTableData(tableName);
    final schema = await getTableSchema(tableName);
    
    final excel = Excel.createExcel();
    final sheet = excel['Sheet1'];
    
    if (data.isNotEmpty) {
      // Get column definitions from schema
      final columns = schema?['columns'] as List<dynamic>? ?? [];
      final columnNames = columns.map((col) => col['label'] ?? col['name']).toList();
      
      // Add headers
      for (int i = 0; i < columnNames.length; i++) {
        sheet.cell(CellIndex.indexByColumnRow(columnIndex: i, rowIndex: 0)).value = columnNames[i];
      }
      
      // Add data
      for (int rowIndex = 0; rowIndex < data.length; rowIndex++) {
        final row = data[rowIndex];
        for (int colIndex = 0; colIndex < columns.length; colIndex++) {
          final column = columns[colIndex];
          final columnName = column['name'];
          final value = row[columnName];
          
          sheet.cell(CellIndex.indexByColumnRow(
            columnIndex: colIndex,
            rowIndex: rowIndex + 1,
          )).value = value;
        }
      }
    }
    
    // Save file
    final directory = await getApplicationDocumentsDirectory();
    final filePath = '${directory.path}/$fileName.xlsx';
    final file = File(filePath);
    await file.writeAsBytes(excel.encode()!);
    
    return filePath;
  }
  
  // Search and Filter
  Future<List<Map<String, dynamic>>> searchTable(String tableName, String searchTerm) async {
    final db = await database;
    
    final result = await db.rawQuery('''
      SELECT * FROM $tableName 
      WHERE is_deleted = 0 
      AND data_json LIKE ?
      ORDER BY updated_at DESC
    ''', ['%$searchTerm%']);
    
    return result.map((row) {
      final data = jsonDecode(row['data_json'] as String) as Map<String, dynamic>;
      return {
        'id': row['id'],
        'created_at': row['created_at'],
        'updated_at': row['updated_at'],
        'sync_id': row['sync_id'],
        ...data,
      };
    }).toList();
  }
  
  // Backup and Restore
  Future<String> createBackup() async {
    final db = await database;
    final directory = await getApplicationDocumentsDirectory();
    final backupPath = '${directory.path}/backup_${DateTime.now().millisecondsSinceEpoch}.db';
    
    await db.close();
    
    final dbPath = await getDatabasesPath();
    final originalPath = join(dbPath, 'enhanced_excel_database.db');
    
    await File(originalPath).copy(backupPath);
    
    // Reopen database
    _database = await _initDatabase();
    
    return backupPath;
  }
  
  Future<void> restoreFromBackup(String backupPath) async {
    final db = await database;
    await db.close();
    
    final dbPath = await getDatabasesPath();
    final originalPath = join(dbPath, 'enhanced_excel_database.db');
    
    await File(backupPath).copy(originalPath);
    
    // Reopen database
    _database = await _initDatabase();
  }
  
  // Performance optimization
  Future<void> optimizeDatabase() async {
    final db = await database;
    await db.execute('VACUUM');
    await db.execute('ANALYZE');
  }
  
  // Get table statistics
  Future<Map<String, dynamic>> getTableStats(String tableName) async {
    final db = await database;
    
    final countResult = await db.rawQuery(
      'SELECT COUNT(*) as total_rows FROM $tableName WHERE is_deleted = 0'
    );
    
    final sizeResult = await db.rawQuery(
      'SELECT page_count * page_size as size FROM pragma_page_count(), pragma_page_size()'
    );
    
    return {
      'total_rows': countResult.first['total_rows'],
      'database_size': sizeResult.first['size'],
      'last_updated': DateTime.now().toIso8601String(),
    };
  }
}