import 'package:flutter/material.dart';
import '../services/ai_service.dart';
import '../services/truly_intelligent_ai.dart';
import '../services/app_context_manager.dart';
import '../widgets/ai_chat_message.dart' as chat_widgets;
import '../widgets/ai_interactive_dialog.dart';
import '../widgets/voice_input_button.dart';
import '../models/chat_message.dart';

class AIAssistantScreen extends StatefulWidget {
  const AIAssistantScreen({super.key});

  @override
  State<AIAssistantScreen> createState() => _AIAssistantScreenState();
}

class _AIAssistantScreenState extends State<AIAssistantScreen> {
  final AIService _aiService = AIService();
  final AppContextManager _contextManager = AppContextManager();
  final TrulyIntelligentAI _intelligentAI = TrulyIntelligentAI();
  final TextEditingController _messageController = TextEditingController();
  final ScrollController _scrollController = ScrollController();

  final List<ChatMessage> _messages = [];
  bool _isLoading = false;
  bool _isInitialized = false;
  Map<String, dynamic>? _lastCalculationResults;

  @override
  void initState() {
    super.initState();
    _initializeAI();
    _contextManager.updateCurrentScreen('ai_assistant');
  }

  @override
  void dispose() {
    _messageController.dispose();
    _scrollController.dispose();
    super.dispose();
  }

  Future<void> _initializeAI() async {
    final success = await _aiService.initialize(context);
    
    setState(() {
      _isInitialized = true;
    });

    // Add welcome message
    _addMessage(ChatMessage(
      role: 'assistant',
      content: _getWelcomeMessage(),
      timestamp: DateTime.now(),
    ));

    // AI is ready without models
  }

  String _getWelcomeMessage() {
    switch (_aiService.status) {
      case AIServiceStatus.ready:
        return '''🤖 **FireTool AI Assistant Ready!**

I'm your offline AI assistant for fire suppression systems. I can help you with:

• **Calculate** clean agent systems (FM200, NOVEC)
• **Estimate** project costs and pricing
• **Suggest** equipment and components
• **Explain** technical terms and fields
• **Generate** BOQ documents
• **Answer** fire safety questions

**Intelligence:** Advanced Domain Expert
**Status:** 🟢 Online and ready

Try asking: "Calculate a 65kg FM200 system" or "Explain design concentration"
''';

      case AIServiceStatus.notInitialized:
        return '''🤖 **FireTool AI Assistant Ready!**

Your intelligent assistant for fire suppression systems is ready to help you with:

• **Smart Calculations** - "80 kg FM200 supply only"
• **System Design** - "Calculate NOVEC for 5x5m room"
• **Cost Estimation** - Real pricing from your database
• **BOM Generation** - Detailed bills of materials
• **Voice Input** - Tap 🎤 to speak in English or Arabic

**Intelligence Level:** Advanced (Domain Expert)
**Memory Usage:** Low (~50MB) - No models needed!
**Auto-Learning:** ✅ Enabled

*Pure domain intelligence - no downloads required!* ✨

**Try asking:**
- "90 kg FM200"
- "Calculate clean agent for server room"
- "Show me installation options"
- "Main and reserve system for 100 kg NOVEC"
''';


    }
  }

  void _addMessage(ChatMessage message) {
    setState(() {
      _messages.add(message);
    });
    
    // Scroll to bottom
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (_scrollController.hasClients) {
        _scrollController.animateTo(
          _scrollController.position.maxScrollExtent,
          duration: const Duration(milliseconds: 300),
          curve: Curves.easeOut,
        );
      }
    });
  }

  Future<void> _sendMessage() async {
    final text = _messageController.text.trim();
    if (text.isEmpty) return;

    // Detect if Arabic text is being used
    final isArabic = _detectArabicText(text);
    if (isArabic) {
      debugPrint('🌍 AI: Arabic text detected in user input: "$text"');
    }

    // Add user message
    _addMessage(ChatMessage(
      role: 'user',
      content: text,
      timestamp: DateTime.now(),
    ));

    _messageController.clear();
    setState(() {
      _isLoading = true;
    });

    try {
      debugPrint('🧠 AI: User message: "$text"');

      // Let the intelligent AI analyze the user's intent
      debugPrint('🧠 AI: Calling TrulyIntelligentAI.analyzeUserIntent()...');
      final analysis = await _intelligentAI.analyzeUserIntent(text);

      debugPrint('🧠 AI: Analysis result: ${analysis['intent']} (confidence: ${analysis['confidence']})');

      if (analysis['intent'] == 'calculate_clean_agent') {
        // Show intelligent dialog based on AI analysis
        setState(() {
          _isLoading = false;
        });

        _addMessage(ChatMessage(
          role: 'assistant',
          content: '🧠 ${analysis['reasoning']}\n\nLet me gather the remaining information I need...',
          timestamp: DateTime.now(),
        ));

        // Show intelligent dialog
        _showIntelligentCalculationDialog(text, analysis);
      } else {
        // Process normally for other queries
        final response = await _aiService.processQuery(text, context);

        _addMessage(ChatMessage(
          role: 'assistant',
          content: response.message,
          timestamp: DateTime.now(),
          metadata: {'toolCalls': response.toolCalls},
        ));
      }
    } catch (e) {
      _addMessage(ChatMessage(
        role: 'assistant',
        content: 'Sorry, I encountered an error: $e',
        timestamp: DateTime.now(),
      ));
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  void _showIntelligentCalculationDialog(String query, Map<String, dynamic> analysis) {
    debugPrint('🧠 AI: Showing intelligent dialog based on analysis');

    // Generate options based on AI analysis
    final options = _generateOptionsFromAnalysis(analysis);

    showDialog(
      context: context,
      builder: (context) => AIInteractiveDialog(
        title: 'Clean Agent Calculator',
        message: 'Based on my analysis: ${analysis['reasoning']}\n\nI need some additional information:',
        options: options,
        extractedWeight: analysis['extracted_data']['agent_quantity']?.toDouble(),
        extractedVolume: analysis['extracted_data']['room_volume']?.toDouble(),
        extractedData: analysis['extracted_data'] as Map<String, dynamic>,
        onCalculationComplete: (results) {
          // Store results and add detailed message with cost breakdown
          setState(() {
            _lastCalculationResults = results;
          });

          // Build detailed response with cost breakdown
          String response = '✅ **Calculation Completed Successfully!**\n\n';

          // Add form inputs summary
          if (results.containsKey('form_inputs')) {
            final formInputs = results['form_inputs'] as Map<String, dynamic>;
            response += '📋 **System Configuration:**\n';
            response += '• Agent Type: ${formInputs['agent_type']}\n';
            response += '• Design Concentration: ${formInputs['design_concentration']}\n';
            response += '• Agent Quantity: ${formInputs['agent_quantity']}kg\n';
            response += '• Installation Type: ${formInputs['installation_type'] == 'supply_only' ? 'Supply Only' : 'Supply & Install'}\n';
            response += '• System Type: ${formInputs['system_type'] == 'main_only' ? 'Main System Only' : 'Main + Reserve'}\n\n';
          }

          // Add design results
          if (results.containsKey('design_results')) {
            final designResults = results['design_results'] as Map<String, dynamic>;
            response += '📊 **Design Results:**\n';
            response += '• Room Volume: ${designResults['room_volume']}m³\n';
            if (designResults.containsKey('room_dimensions')) {
              final dimensions = designResults['room_dimensions'] as Map<String, dynamic>;
              response += '• Room Dimensions: ${dimensions['length']}m × ${dimensions['width']}m × ${dimensions['height']}m\n';
            }
            if (designResults.containsKey('cylinder_configuration')) {
              final cylinder = designResults['cylinder_configuration'] as Map<String, dynamic>;
              response += '• Cylinders: ${cylinder['quantity']} × ${cylinder['type']}\n';
            }
            if (designResults.containsKey('nozzle_configuration')) {
              final nozzle = designResults['nozzle_configuration'] as Map<String, dynamic>;
              response += '• Nozzles: ${nozzle['quantity']} units\n';
            }
            response += '\n';
          }

          // Add detailed cost breakdown
          if (results.containsKey('bom_summary')) {
            final bomSummary = results['bom_summary'] as Map<String, dynamic>;
            response += '💰 **Cost Breakdown:**\n\n';

            response += '**Ex-works Costs (USD):**\n';
            response += '• Suppression System: \$${bomSummary['suppression_cost_usd']}\n';
            response += '• Alarm & Detection: \$${bomSummary['alarm_cost_usd']}\n';
            response += '• **Ex-works Total: \$${bomSummary['ex_works_total_usd']}**\n\n';

            response += '**Local Costs (SAR):**\n';
            response += '• Landed Cost: ${bomSummary['landed_cost_sar']} SAR\n';
            if (bomSummary['installation_materials_sar'] > 0) {
              response += '• Installation Materials: ${bomSummary['installation_materials_sar']} SAR\n';
            }
            if (bomSummary['installation_labor_sar'] > 0) {
              response += '• Installation Labor: ${bomSummary['installation_labor_sar']} SAR\n';
            }
            response += '\n**🎯 GRAND TOTAL: ${bomSummary['grand_total_sar']} SAR**\n\n';
          }

          response += 'Use the buttons below to view detailed BOM or calculation steps.';

          _addMessage(ChatMessage(
            role: 'assistant',
            content: response,
            timestamp: DateTime.now(),
          ));
        },
      ),
    );
  }

  List<AIOption> _analyzeQueryForOptions(String query) {
    final queryLower = query.toLowerCase();
    List<AIOption> options = [];

    // Check if agent type is specified
    bool hasAgentType = queryLower.contains('fm200') || queryLower.contains('novec');
    if (!hasAgentType) {
      options.add(const AIOption(
        key: 'agent_type',
        title: 'Agent Type',
        description: 'Select the clean agent type for your system',
        choices: [
          AIChoice(
            value: 'NOVEC',
            label: 'NOVEC 1230',
            description: '4.5% design concentration',
          ),
          AIChoice(
            value: 'FM200',
            label: 'FM-200',
            description: '7.4% design concentration',
          ),
        ],
      ));
    }

    // Check if installation type is specified
    bool hasInstallationType = queryLower.contains('supply only') ||
                              queryLower.contains('supply & install') ||
                              queryLower.contains('installation');
    if (!hasInstallationType) {
      options.add(const AIOption(
        key: 'installation_type',
        title: 'Installation Type',
        description: 'Choose the scope of work for your project',
        choices: [
          AIChoice(
            value: 'supply_only',
            label: 'Supply Only',
            description: 'Equipment supply without installation',
          ),
          AIChoice(
            value: 'supply_install',
            label: 'Supply & Install',
            description: 'Complete supply and installation service',
          ),
        ],
      ));
    }

    // Check if system type is specified
    bool hasSystemType = queryLower.contains('main only') ||
                        queryLower.contains('main and reserve') ||
                        queryLower.contains('reserve');
    if (!hasSystemType) {
      options.add(const AIOption(
        key: 'system_type',
        title: 'System Type',
        description: 'Select the system configuration',
        choices: [
          AIChoice(
            value: 'main_only',
            label: 'Main System Only',
            description: 'Single suppression system',
          ),
          AIChoice(
            value: 'main_reserve',
            label: 'Main + Reserve',
            description: 'Primary system with backup',
          ),
        ],
      ));
    }

    // Check if concentration is specified (only if agent type is known)
    bool hasConcentration = queryLower.contains('%') || queryLower.contains('concentration');
    if (!hasConcentration && hasAgentType) {
      if (queryLower.contains('novec')) {
        options.add(const AIOption(
          key: 'concentration',
          title: 'Design Concentration',
          description: 'Select the NOVEC design concentration',
          choices: [
            AIChoice(value: '4.5%', label: '4.5%', description: 'Standard concentration'),
            AIChoice(value: '4.7%', label: '4.7%', description: 'Enhanced protection'),
            AIChoice(value: '5.6%', label: '5.6%', description: 'High-risk areas'),
            AIChoice(value: '5.9%', label: '5.9%', description: 'Maximum protection'),
          ],
        ));
      } else if (queryLower.contains('fm200')) {
        options.add(const AIOption(
          key: 'concentration',
          title: 'Design Concentration',
          description: 'Select the FM-200 design concentration',
          choices: [
            AIChoice(value: '7.4%', label: '7.4%', description: 'Standard concentration'),
            AIChoice(value: '8.5%', label: '8.5%', description: 'Enhanced protection'),
            AIChoice(value: '9.0%', label: '9.0%', description: 'Maximum protection'),
          ],
        ));
      }
    }

    return options;
  }

  double? _extractWeightFromQuery(String query) {
    // Extract weight from query using regex
    final weightRegex = RegExp(r'(\d+(?:\.\d+)?)\s*kg', caseSensitive: false);
    final match = weightRegex.firstMatch(query);
    if (match != null) {
      return double.tryParse(match.group(1)!);
    }

    // Also check for just numbers followed by weight-related words
    final numberRegex = RegExp(r'(\d+(?:\.\d+)?)\s*(kilogram|kilo|kg)', caseSensitive: false);
    final numberMatch = numberRegex.firstMatch(query);
    if (numberMatch != null) {
      return double.tryParse(numberMatch.group(1)!);
    }

    return null;
  }

  /// Generate dialog options based on intelligent AI analysis
  List<AIOption> _generateOptionsFromAnalysis(Map<String, dynamic> analysis) {
    List<AIOption> options = [];
    final extractedData = analysis['extracted_data'] as Map<String, dynamic>;
    final missingRequirements = analysis['missing_requirements'] as List<String>;

    // Add agent type option if missing
    if (missingRequirements.contains('agent_type')) {
      options.add(const AIOption(
        key: 'agent_type',
        title: 'Agent Type',
        description: 'Select the clean agent type for your system',
        choices: [
          AIChoice(
            value: 'NOVEC1230',
            label: 'NOVEC 1230',
            description: 'Environmentally friendly, 4.5% design concentration',
          ),
          AIChoice(
            value: 'FM200',
            label: 'FM-200',
            description: 'Traditional clean agent, 7.4% design concentration',
          ),
        ],
      ));
    }

    // Add concentration option based on agent type
    final agentType = extractedData['agent_type'] ?? 'NOVEC1230';
    if (agentType == 'FM200') {
      options.add(const AIOption(
        key: 'concentration',
        title: 'Design Concentration',
        description: 'Select the design concentration for FM-200',
        choices: [
          AIChoice(value: '7.4%', label: '7.4%', description: 'Standard concentration'),
          AIChoice(value: '8.5%', label: '8.5%', description: 'Higher concentration'),
          AIChoice(value: '9.0%', label: '9.0%', description: 'Maximum concentration'),
        ],
      ));
    } else {
      options.add(const AIOption(
        key: 'concentration',
        title: 'Design Concentration',
        description: 'Select the design concentration for NOVEC 1230',
        choices: [
          AIChoice(value: '4.5%', label: '4.5%', description: 'Standard concentration'),
          AIChoice(value: '4.7%', label: '4.7%', description: 'Higher concentration'),
          AIChoice(value: '5.6%', label: '5.6%', description: 'High concentration'),
          AIChoice(value: '5.9%', label: '5.9%', description: 'Maximum concentration'),
        ],
      ));
    }

    // Add system type option only if not already detected
    if (!extractedData.containsKey('system_type')) {
      options.add(const AIOption(
        key: 'system_type',
        title: 'System Type',
        description: 'Select the system configuration',
        choices: [
          AIChoice(
            value: 'main_only',
            label: 'Main System Only',
            description: 'Single suppression system',
          ),
          AIChoice(
            value: 'main_reserve',
            label: 'Main + Reserve',
            description: 'Main system with backup reserve',
          ),
        ],
      ));
    }

    // Add installation type option only if not already detected
    if (!extractedData.containsKey('installation_type')) {
      options.add(const AIOption(
        key: 'installation_type',
        title: 'Installation Type',
        description: 'Select the installation scope',
        choices: [
          AIChoice(
            value: 'supply_only',
            label: 'Supply Only',
            description: 'Equipment supply without installation',
          ),
          AIChoice(
            value: 'supply_install',
            label: 'Supply & Install',
            description: 'Complete supply and installation service',
          ),
        ],
      ));
    }

    return options;
  }

  void _showBOMDialog() {
    if (_lastCalculationResults == null) return;

    showDialog(
      context: context,
      builder: (context) => chat_widgets.BOMDetailsDialog(
        calculationResults: _lastCalculationResults!,
      ),
    );
  }

  void _showCalculationDetailsDialog() {
    if (_lastCalculationResults == null) return;

    showDialog(
      context: context,
      builder: (context) => chat_widgets.CalculationDetailsDialog(
        calculationResults: _lastCalculationResults!,
      ),
    );
  }

  void _recalculate() {
    _messageController.text = 'Recalculate clean agent system';
    _sendMessage();
  }

  /// Detect if text contains Arabic characters
  bool _detectArabicText(String text) {
    if (text.trim().isEmpty) return false;

    // Check for Arabic characters
    final arabicRegex = RegExp(r'[\u0600-\u06FF\u0750-\u077F\u08A0-\u08FF\uFB50-\uFDFF\uFE70-\uFEFF]');
    final arabicCharCount = arabicRegex.allMatches(text).length;
    final totalChars = text.replaceAll(RegExp(r'\s+'), '').length;

    // If more than 20% of characters are Arabic, consider it Arabic
    if (totalChars > 0 && (arabicCharCount / totalChars) > 0.2) {
      return true;
    }

    // Check for common Arabic words
    final arabicWords = [
      'احسب', 'حساب', 'تكلفة', 'سعر', 'تقدير', 'تصميم', 'نظام',
      'عامل', 'نظيف', 'إطفاء', 'مطفئ', 'حريق', 'نار',
      'كيلو', 'متر', 'غرفة', 'مساحة', 'مرحبا', 'أهلا', 'شكرا'
    ];

    final lowerText = text.toLowerCase();
    for (final word in arabicWords) {
      if (lowerText.contains(word)) {
        return true;
      }
    }

    return false;
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Row(
          children: [
            Container(
              width: 32,
              height: 32,
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  colors: [Colors.blue.shade600, Colors.purple.shade600],
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                ),
                borderRadius: BorderRadius.circular(16),
              ),
              child: const Icon(Icons.psychology, size: 18, color: Colors.white),
            ),
            const SizedBox(width: 12),
            const Text('AI Assistant'),
            const Spacer(),
            // Simple status indicator
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
              decoration: BoxDecoration(
                color: _aiService.isReady ? Colors.green.shade100 : Colors.orange.shade100,
                borderRadius: BorderRadius.circular(12),
              ),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Icon(
                    Icons.circle,
                    color: _aiService.isReady ? Colors.green : Colors.orange,
                    size: 8,
                  ),
                  const SizedBox(width: 4),
                  Text(
                    _aiService.isReady ? 'Ready' : 'Loading',
                    style: TextStyle(
                      fontSize: 11,
                      color: _aiService.isReady ? Colors.green.shade700 : Colors.orange.shade700,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
        backgroundColor: Colors.white,
        elevation: 0,
        surfaceTintColor: Colors.transparent,
        foregroundColor: Colors.grey.shade800,
      ),
      body: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              Colors.grey.shade50,
              Colors.white,
            ],
          ),
        ),
        child: Column(
          children: [
            // Chat messages
            Expanded(
              child: _messages.isEmpty
                  ? _buildEmptyState()
                  : ListView.builder(
                      controller: _scrollController,
                      padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 16),
                      itemCount: _messages.length,
                      itemBuilder: (context, index) {
                        final message = _messages[index];
                        return _buildModernMessageBubble(message, index);
                      },
                    ),
            ),
          
          // Loading indicator
          if (_isLoading)
            const Padding(
              padding: EdgeInsets.all(8.0),
              child: Row(
                children: [
                  SizedBox(width: 16),
                  SizedBox(
                    width: 16,
                    height: 16,
                    child: CircularProgressIndicator(strokeWidth: 2),
                  ),
                  SizedBox(width: 8),
                  Text('AI is thinking...'),
                ],
              ),
            ),
          
          // Input area
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Theme.of(context).colorScheme.surface,
              border: Border(
                top: BorderSide(
                  color: Theme.of(context).dividerColor,
                  width: 1,
                ),
              ),
            ),
            child: Row(
              children: [
                Expanded(
                  child: TextField(
                    controller: _messageController,
                    decoration: const InputDecoration(
                      hintText: 'Ask about fire suppression systems...',
                      border: OutlineInputBorder(),
                      contentPadding: EdgeInsets.symmetric(
                        horizontal: 16,
                        vertical: 12,
                      ),
                    ),
                    maxLines: null,
                    textInputAction: TextInputAction.send,
                    onSubmitted: (_) => _sendMessage(),
                    enabled: _aiService.isReady && !_isLoading,
                  ),
                ),
                const SizedBox(width: 8),

                // Voice input button
                VoiceInputButton(
                  enabled: _aiService.isReady && !_isLoading,
                  onVoiceInput: (text) {
                    _messageController.text = text;
                    _sendMessage();
                  },
                  onPartialResult: (text) {
                    // Show partial results in the text field
                    _messageController.text = text;
                  },
                ),

                const SizedBox(width: 8),
                IconButton(
                  onPressed: _aiService.isReady && !_isLoading ? _sendMessage : null,
                  icon: const Icon(Icons.send),
                  style: IconButton.styleFrom(
                    backgroundColor: Theme.of(context).colorScheme.primary,
                    foregroundColor: Theme.of(context).colorScheme.onPrimary,
                  ),
                ),
              ],
            ),
            ),
          ],
        ),
      ),
    );
  }

  /// Build empty state when no messages
  Widget _buildEmptyState() {
    return Center(
      child: Container(
        padding: const EdgeInsets.all(32),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Container(
              width: 80,
              height: 80,
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  colors: [Colors.blue.shade600, Colors.purple.shade600],
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                ),
                borderRadius: BorderRadius.circular(40),
                boxShadow: [
                  BoxShadow(
                    color: Colors.blue.withAlpha(50),
                    blurRadius: 20,
                    offset: const Offset(0, 8),
                  ),
                ],
              ),
              child: const Icon(Icons.psychology, size: 40, color: Colors.white),
            ),
            const SizedBox(height: 24),
            Text(
              'FireTool AI Assistant',
              style: TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.bold,
                color: Colors.grey.shade800,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'Your intelligent assistant for fire suppression systems',
              style: TextStyle(
                fontSize: 16,
                color: Colors.grey.shade600,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 32),
            Container(
              padding: const EdgeInsets.all(20),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(16),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withAlpha(10),
                    blurRadius: 10,
                    offset: const Offset(0, 4),
                  ),
                ],
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    '💡 Try asking:',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                      color: Colors.grey.shade800,
                    ),
                  ),
                  const SizedBox(height: 12),
                  _buildSuggestionChip('80 kg FM200 supply only'),
                  _buildSuggestionChip('Calculate NOVEC for server room'),
                  _buildSuggestionChip('Main and reserve system pricing'),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// Build suggestion chip
  Widget _buildSuggestionChip(String text) {
    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      child: InkWell(
        onTap: () {
          _messageController.text = text;
          _sendMessage();
        },
        borderRadius: BorderRadius.circular(20),
        child: Container(
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
          decoration: BoxDecoration(
            color: Colors.blue.shade50,
            borderRadius: BorderRadius.circular(20),
            border: Border.all(color: Colors.blue.shade200),
          ),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(Icons.auto_awesome, size: 16, color: Colors.blue.shade600),
              const SizedBox(width: 8),
              Text(
                text,
                style: TextStyle(
                  color: Colors.blue.shade700,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// Build modern message bubble with better styling
  Widget _buildModernMessageBubble(ChatMessage message, int index) {
    final isUser = message.role == 'user';

    // Check if this is the last AI message with calculation results
    final hasCalculationResults = !isUser &&
        index == _messages.length - 1 &&
        _lastCalculationResults != null;

    return Container(
      margin: const EdgeInsets.only(bottom: 24),
      child: Row(
        mainAxisAlignment: isUser ? MainAxisAlignment.end : MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          if (!isUser) ...[
            Container(
              width: 40,
              height: 40,
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  colors: [Colors.blue.shade600, Colors.purple.shade600],
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                ),
                borderRadius: BorderRadius.circular(20),
                boxShadow: [
                  BoxShadow(
                    color: Colors.blue.withAlpha(30),
                    blurRadius: 8,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              child: const Icon(Icons.psychology, size: 20, color: Colors.white),
            ),
            const SizedBox(width: 12),
          ],

          Flexible(
            child: Container(
              constraints: BoxConstraints(
                maxWidth: MediaQuery.of(context).size.width * 0.75,
              ),
              decoration: BoxDecoration(
                color: isUser
                    ? Colors.blue.shade600
                    : Colors.white,
                borderRadius: BorderRadius.only(
                  topLeft: const Radius.circular(20),
                  topRight: const Radius.circular(20),
                  bottomLeft: Radius.circular(isUser ? 20 : 4),
                  bottomRight: Radius.circular(isUser ? 4 : 20),
                ),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withAlpha(15),
                    blurRadius: 15,
                    offset: const Offset(0, 5),
                  ),
                ],
                border: isUser ? null : Border.all(
                  color: Colors.grey.shade200,
                  width: 1,
                ),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Padding(
                    padding: const EdgeInsets.all(16),
                    child: Text(
                      message.content,
                      style: TextStyle(
                        color: isUser
                            ? Colors.white
                            : Colors.grey.shade800,
                        fontSize: 15,
                        height: 1.5,
                        fontWeight: FontWeight.w400,
                      ),
                    ),
                  ),

                  // Action buttons for AI messages with calculations
                  if (hasCalculationResults) ...[
                    Container(
                      padding: const EdgeInsets.fromLTRB(16, 0, 16, 16),
                      child: Wrap(
                        spacing: 8,
                        runSpacing: 8,
                        children: [
                          _buildActionButton(
                            'Show Calculation',
                            Icons.calculate,
                            Colors.blue,
                            _showCalculationDetailsDialog,
                          ),
                          _buildActionButton(
                            'Show BOM',
                            Icons.list_alt,
                            Colors.green,
                            _showBOMDialog,
                          ),
                          _buildActionButton(
                            'Recalculate',
                            Icons.refresh,
                            Colors.orange,
                            _recalculate,
                          ),
                        ],
                      ),
                    ),
                  ],

                  // Timestamp
                  Padding(
                    padding: const EdgeInsets.fromLTRB(16, 0, 16, 12),
                    child: Text(
                      _formatTime(message.timestamp),
                      style: TextStyle(
                        fontSize: 11,
                        color: isUser
                            ? Colors.white.withAlpha(180)
                            : Colors.grey.shade500,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),

          if (isUser) ...[
            const SizedBox(width: 12),
            Container(
              width: 32,
              height: 32,
              decoration: BoxDecoration(
                color: Colors.grey.shade300,
                borderRadius: BorderRadius.circular(16),
              ),
              child: Icon(Icons.person, size: 18, color: Colors.grey.shade600),
            ),
          ],
        ],
      ),
    );
  }

  /// Build action button for AI messages
  Widget _buildActionButton(String label, IconData icon, Color color, VoidCallback? onPressed) {
    return Material(
      color: Colors.transparent,
      child: InkWell(
        onTap: onPressed,
        borderRadius: BorderRadius.circular(20),
        child: Container(
          padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
          decoration: BoxDecoration(
            color: color.withAlpha(20),
            borderRadius: BorderRadius.circular(20),
            border: Border.all(color: color.withAlpha(100)),
          ),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(icon, size: 14, color: color),
              const SizedBox(width: 6),
              Text(
                label,
                style: TextStyle(
                  color: color,
                  fontSize: 12,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// Format timestamp
  String _formatTime(DateTime timestamp) {
    final now = DateTime.now();
    final diff = now.difference(timestamp);

    if (diff.inMinutes < 1) {
      return 'Just now';
    } else if (diff.inHours < 1) {
      return '${diff.inMinutes}m ago';
    } else if (diff.inDays < 1) {
      return '${diff.inHours}h ago';
    } else {
      return '${timestamp.day}/${timestamp.month}';
    }
  }

  Widget _buildMessageBubble(ChatMessage message) {
    final isUser = message.role == 'user';

    return Container(
      margin: const EdgeInsets.only(bottom: 20),
      child: Row(
        mainAxisAlignment: isUser ? MainAxisAlignment.end : MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          if (!isUser) ...[
            Container(
              width: 40,
              height: 40,
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  colors: [Colors.blue.shade600, Colors.purple.shade600],
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                ),
                borderRadius: BorderRadius.circular(20),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withOpacity(0.1),
                    blurRadius: 8,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              child: const Icon(Icons.psychology, size: 20, color: Colors.white),
            ),
            const SizedBox(width: 12),
          ],

          Flexible(
            child: Container(
              constraints: BoxConstraints(
                maxWidth: MediaQuery.of(context).size.width * 0.75,
              ),
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: isUser
                    ? Theme.of(context).colorScheme.primary
                    : Colors.white,
                borderRadius: BorderRadius.only(
                  topLeft: const Radius.circular(20),
                  topRight: const Radius.circular(20),
                  bottomLeft: Radius.circular(isUser ? 20 : 4),
                  bottomRight: Radius.circular(isUser ? 4 : 20),
                ),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withOpacity(0.08),
                    blurRadius: 12,
                    offset: const Offset(0, 4),
                  ),
                ],
                border: isUser ? null : Border.all(
                  color: Colors.grey.shade200,
                  width: 1,
                ),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    message.content,
                    style: TextStyle(
                      color: isUser
                          ? Theme.of(context).colorScheme.onPrimary
                          : Colors.grey.shade800,
                      fontSize: 15,
                      height: 1.4,
                    ),
                  ),
                  
                  // Tool calls indicator
                  if (message.metadata?['toolCalls'] != null && (message.metadata!['toolCalls'] as List).isNotEmpty)
                    Container(
                      margin: const EdgeInsets.only(top: 8),
                      padding: const EdgeInsets.all(8),
                      decoration: BoxDecoration(
                        color: Colors.blue.withOpacity(0.1),
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          const Row(
                            children: [
                              Icon(Icons.build, size: 16),
                              SizedBox(width: 4),
                              Text('Tools Used:', style: TextStyle(fontWeight: FontWeight.bold)),
                            ],
                          ),
                          ...(message.metadata!['toolCalls'] as List).map((tool) => Padding(
                            padding: const EdgeInsets.only(top: 4),
                            child: Text('• $tool'),
                          )),
                        ],
                      ),
                    ),
                  
                  // Timestamp
                  Padding(
                    padding: const EdgeInsets.only(top: 4),
                    child: Text(
                      '${message.timestamp.hour.toString().padLeft(2, '0')}:${message.timestamp.minute.toString().padLeft(2, '0')}',
                      style: TextStyle(
                        fontSize: 10,
                        color: isUser 
                            ? Theme.of(context).colorScheme.onPrimary.withOpacity(0.7)
                            : Theme.of(context).colorScheme.onSurfaceVariant.withOpacity(0.7),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
          
          if (isUser) ...[
            const SizedBox(width: 8),
            CircleAvatar(
              radius: 16,
              backgroundColor: Theme.of(context).colorScheme.secondary,
              child: const Icon(Icons.person, size: 16, color: Colors.white),
            ),
          ],
        ],
      ),
    );
  }

}


