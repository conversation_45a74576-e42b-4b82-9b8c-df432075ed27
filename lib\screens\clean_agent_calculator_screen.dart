import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:provider/provider.dart';

// Import React app logic (now the main logic)
import '../models/estimator_types.dart';
import '../models/clean_agent_system.dart';
import '../utils/dynamic_estimator_calculations.dart';
import '../utils/dynamic_bom_generator.dart';
import '../utils/formatters.dart';
import '../services/project_provider.dart';

class CleanAgentCalculatorScreen extends StatefulWidget {
  final double? exchangeRate;
  final double? shippingFactor;
  final double? marginFactor;
  final CleanAgentSystem? existingSystem;
  final Function(CleanAgentSystem)? onSystemSaved;

  const CleanAgentCalculatorScreen({
    super.key,
    this.exchangeRate,
    this.shippingFactor,
    this.marginFactor,
    this.existingSystem,
    this.onSystemSaved,
  });

  @override
  State<CleanAgentCalculatorScreen> createState() => _CleanAgentCalculatorScreenState();
}

class _CleanAgentCalculatorScreenState extends State<CleanAgentCalculatorScreen> {
  // Form controllers
  final _roomLengthController = TextEditingController();
  final _roomWidthController = TextEditingController();
  final _roomHeightController = TextEditingController();
  final _agentQuantityController = TextEditingController();

  // Form state
  AgentType? _selectedAgentType;
  String? _selectedConcentration;
  InputMode _inputMode = InputMode.dimensions;
  double _installationFactor = 0.15; // Default installation factor (15%)
  SystemType _systemType = SystemType.main;
  InstallationType _installationType = InstallationType.supplyOnly;

  // Calculation state
  bool _isCalculating = false;
  DesignResults? _designResults;
  List<BomItem>? _bomItems;
  BomSummary? _bomSummary;

  // Available options
  final Map<AgentType, String> _agentTypeNames = {
    AgentType.novec1230: 'NOVEC 1230',
    AgentType.fm200: 'FM-200',
  };

  final Map<AgentType, List<String>> _concentrationOptions = {
    AgentType.novec1230: ['4.5%', '4.7%', '5.6%', '5.9%'],
    AgentType.fm200: ['7.4%', '8.5%', '9.0%'],
  };

  @override
  void initState() {
    super.initState();
    _loadExistingSystem();
  }

  void _loadExistingSystem() {
    if (widget.existingSystem != null) {
      final system = widget.existingSystem!;
      setState(() {
        _selectedAgentType = system.agentType;
        _selectedConcentration = '${(system.designConcentration * 100).toStringAsFixed(1)}%';
        _roomLengthController.text = system.roomLength.toString();
        _roomWidthController.text = system.roomWidth.toString();
        _roomHeightController.text = system.roomHeight.toString();
        // Load agent quantity if it was saved
        if (system.userInputAgentQuantity != null) {
          _agentQuantityController.text = system.userInputAgentQuantity!.toString();
        }
        // Load saved user options
        _inputMode = system.inputMode;
        _systemType = system.systemType;
        _installationType = system.installationType;
        _installationFactor = system.installationFactor;

        // Load existing calculation results without recalculating
        _designResults = DesignResults(
          roomData: RoomData(
            roomLength: system.roomLength,
            roomWidth: system.roomWidth,
            roomHeight: system.roomHeight,
            roomArea: system.roomLength * system.roomWidth,
            roomVolume: system.roomVolume,
          ),
          designFactor: system.designConcentration,
          totalAgentRequired: system.agentRequired,
          systemType: 'main',
          cylinder: CylinderData(
            targetFillSingleCyl: 0.0, // Default values for display
            cylinderSizeLiters1stIter: system.cylinderSize.toDouble(),
            numCylinders1stIter: system.cylinderQty,
            qtyPerCylinder1stIter: system.actualAgent / system.cylinderQty,
            cylinderSizeLiters2ndIter: system.cylinderSize.toDouble(),
            numCylinders2ndIter: system.cylinderQty,
            qtyPerCylinder: system.actualAgent / system.cylinderQty,
            actualTotalKg: system.actualAgent,
            fillingRatio: 0.8, // Default filling ratio
          ),
          discharge: DischargeData(
            totalFlowRate: 0.0, // Default values for display
            nozzleQty1stTrial: system.nozzleQty,
            flowPerNozzle1stTrial: 0.0,
            nozzleSize1stTrial: system.nozzleSize,
            nozzleQtyFinal: system.nozzleQty,
            flowPerNozzleFinal: 0.0,
            nozzleSizeFinal: system.nozzleSize,
            manifoldPipeSize: system.manifoldPipeSize.toInt(),
            manifoldAssemblySize: 65, // Default manifold assembly size
          ),
        );

        // Load saved BOM data if available, otherwise create default
        if (system.bomSummaryData.isNotEmpty) {
          _bomSummary = BomSummary.fromJson(system.bomSummaryData);
        } else {
          _bomSummary = BomSummary(
            exWorksItemsUSD: system.suppressionCost + system.alarmCost,
            landedCostSAR: (system.suppressionCost + system.alarmCost) * 3.75 * 1.15,
            installationMaterialsSAR: system.installationMaterialsCost,
            installationLaborSAR: system.installationLaborCost,
            suppressionCost: system.suppressionCost,
            alarmCost: system.alarmCost,
            installationItemsCost: system.installationMaterialsCost,
            suppressionInstallCost: 0.0,
            alarmInstallCost: 0.0,
            installationServicesInstallCost: system.installationLaborCost,
            totalSupplyCostUSD: system.suppressionCost + system.alarmCost,
            totalSupplyCostSAR: (system.suppressionCost + system.alarmCost) * 3.75 * 1.15,
            totalInstallCostSAR: system.installationMaterialsCost + system.installationLaborCost,
            grandTotalSAR: system.totalCost,
            marginFactor: 1.0,
            marginAmountSAR: 0.0,
          );
        }

        // Load saved BOM items if available
        if (system.bomItems.isNotEmpty) {
          _bomItems = system.bomItems.map((itemData) => BomItem.fromJson(itemData)).toList();
        }
      });

      // Don't auto-calculate - just show existing results
      // User can press Calculate button if they want to recalculate
    }
  }

  @override
  void dispose() {
    _roomLengthController.dispose();
    _roomWidthController.dispose();
    _roomHeightController.dispose();
    _agentQuantityController.dispose();
    super.dispose();
  }

  List<String> get _availableConcentrations {
    if (_selectedAgentType == null) return [];
    return _concentrationOptions[_selectedAgentType!] ?? [];
  }

  Future<void> _calculateSystem() async {
    if (!_validateInputs()) return;

    setState(() {
      _isCalculating = true;
      _designResults = null;
      _bomItems = null;
      _bomSummary = null;
    });

    try {
      // Create input based on mode
      EstimatorFormValues input;
      
      if (_inputMode == InputMode.dimensions) {
        input = EstimatorFormValues(
          agentType: _selectedAgentType!,
          designConcentration: _selectedConcentration!,
          inputMode: InputMode.dimensions,
          roomLength: double.parse(_roomLengthController.text),
          roomWidth: double.parse(_roomWidthController.text),
          roomHeight: double.parse(_roomHeightController.text),
          systemType: _systemType,
          installationType: _installationType,
        );
      } else {
        input = EstimatorFormValues(
          agentType: _selectedAgentType!,
          designConcentration: _selectedConcentration!,
          inputMode: InputMode.agentQuantity,
          agentQuantity: double.parse(_agentQuantityController.text),
          roomHeight: _roomHeightController.text.isNotEmpty 
              ? double.parse(_roomHeightController.text) 
              : null,
          systemType: _systemType,
          installationType: _installationType,
        );
      }

      print('\n=== CLEAN AGENT CALCULATION STARTED ===');
      print('Agent: ${formatAgentType(input.agentType)}');
      print('Concentration: ${input.designConcentration}');
      print('Input Mode: ${formatInputMode(input.inputMode)}');

      // Perform calculation using dynamic database logic
      final designResults = await DynamicEstimatorCalculations.calculateDesign(input);

      // Generate BOM using dynamic database data with custom installation factor
      final bomData = await DynamicBomGenerator.generateBOM(designResults, input, installationFactor: _installationFactor);
      final bomItems = bomData['bom'] as List<BomItem>;
      final bomSummary = bomData['summary'] as BomSummary;

      print('\n=== CALCULATION RESULTS ===');
      print('Room Volume: ${formatNumber(designResults.roomData.roomVolume)} m³');
      print('Total Agent Required: ${formatNumber(designResults.totalAgentRequired)} kg');
      print('Actual Agent: ${formatNumber(designResults.cylinder.actualTotalKg)} kg');
      print('Cylinders: ${designResults.cylinder.numCylinders2ndIter} x ${designResults.cylinder.cylinderSizeLiters2ndIter.toInt()}L');
      print('Nozzles: ${designResults.discharge.nozzleQtyFinal} x ${designResults.discharge.nozzleSizeFinal}mm');
      print('Total Cost: ${formatCurrencySAR(bomSummary.grandTotalSAR)}');
      print('BOM Items: ${bomItems.length}');

      setState(() {
        _designResults = designResults;
        _bomItems = bomItems;
        _bomSummary = bomSummary;
      });

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Calculation completed successfully!'),
            backgroundColor: Colors.green,
          ),
        );
      }

    } catch (e) {
      print('ERROR in calculation: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Calculation error: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      setState(() {
        _isCalculating = false;
      });
    }
  }

  bool _validateInputs() {
    if (_selectedAgentType == null) {
      _showError('Please select an agent type');
      return false;
    }

    if (_selectedConcentration == null) {
      _showError('Please select a design concentration');
      return false;
    }

    if (_inputMode == InputMode.dimensions) {
      if (_roomLengthController.text.isEmpty ||
          _roomWidthController.text.isEmpty ||
          _roomHeightController.text.isEmpty) {
        _showError('Please fill in all room dimensions');
        return false;
      }

      try {
        final length = double.parse(_roomLengthController.text);
        final width = double.parse(_roomWidthController.text);
        final height = double.parse(_roomHeightController.text);

        if (length <= 0 || width <= 0 || height <= 0) {
          _showError('Room dimensions must be positive numbers');
          return false;
        }

        if (height > 10) {
          _showError('Room height cannot exceed 10 meters');
          return false;
        }
      } catch (e) {
        _showError('Please enter valid numbers for room dimensions');
        return false;
      }
    } else {
      if (_agentQuantityController.text.isEmpty) {
        _showError('Please enter agent quantity');
        return false;
      }

      try {
        final quantity = double.parse(_agentQuantityController.text);
        if (quantity <= 0) {
          _showError('Agent quantity must be a positive number');
          return false;
        }
      } catch (e) {
        _showError('Please enter a valid number for agent quantity');
        return false;
      }
    }

    return true;
  }

  void _showError(String message) {
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(message),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  void _clearForm() {
    setState(() {
      _roomLengthController.clear();
      _roomWidthController.clear();
      _roomHeightController.clear();
      _agentQuantityController.clear();
      _selectedAgentType = null;
      _selectedConcentration = null;
      _inputMode = InputMode.dimensions;
      _systemType = SystemType.main;
      _installationType = InstallationType.supplyOnly;
      _designResults = null;
      _bomItems = null;
      _bomSummary = null;
    });
  }

  void _saveSystem() async {
    if (_designResults == null || _bomSummary == null) {
      _showError('Please calculate the system first');
      return;
    }

    try {
      // Show dialog to get system name and description
      final result = await _showSaveSystemDialog();
      if (result == null) return;

      final systemName = result['name'] as String;
      final systemDescription = result['description'] as String;

      // Create input for the system
      EstimatorFormValues input;

      if (_inputMode == InputMode.dimensions) {
        input = EstimatorFormValues(
          agentType: _selectedAgentType!,
          designConcentration: _selectedConcentration!,
          inputMode: _inputMode,
          roomLength: double.parse(_roomLengthController.text),
          roomWidth: double.parse(_roomWidthController.text),
          roomHeight: double.parse(_roomHeightController.text),
          systemType: _systemType,
          installationType: _installationType,
        );
      } else {
        // Agent quantity mode - use calculated room dimensions from results
        input = EstimatorFormValues(
          agentType: _selectedAgentType!,
          designConcentration: _selectedConcentration!,
          inputMode: _inputMode,
          agentQuantity: double.parse(_agentQuantityController.text),
          roomLength: _designResults!.roomData.roomLength,
          roomWidth: _designResults!.roomData.roomWidth,
          roomHeight: _designResults!.roomData.roomHeight,
          systemType: _systemType,
          installationType: _installationType,
        );
      }

      // Parse design concentration from string (e.g., "7.4%" -> 0.074)
      final concentrationValue = double.parse(_selectedConcentration!.replaceAll('%', '')) / 100;

      // Create or update the system
      CleanAgentSystem system;
      if (widget.existingSystem != null && mounted) {
        // Update existing system with ALL current user inputs and calculation results
        system = widget.existingSystem!.copyWith(
          name: systemName,
          description: systemDescription,
          roomLength: _designResults!.roomData.roomLength,
          roomWidth: _designResults!.roomData.roomWidth,
          roomHeight: _designResults!.roomData.roomHeight,
          roomVolume: _designResults!.roomData.roomVolume,
          agentType: input.agentType,
          designConcentration: concentrationValue,
          agentRequired: _designResults!.totalAgentRequired,
          actualAgent: _designResults!.cylinder.actualTotalKg,
          cylinderQty: _designResults!.cylinder.numCylinders2ndIter,
          cylinderSize: _designResults!.cylinder.cylinderSizeLiters2ndIter.toInt(),
          nozzleQty: _designResults!.discharge.nozzleQtyFinal,
          nozzleSize: _designResults!.discharge.nozzleSizeFinal,
          suppressionCost: _bomSummary!.suppressionCost,
          alarmCost: _bomSummary!.alarmCost,
          installationMaterialsCost: _bomSummary!.installationMaterialsSAR,
          installationLaborCost: _bomSummary!.installationLaborSAR,
          totalCost: _bomSummary!.grandTotalSAR,
          bomItems: _bomItems?.map((item) => item.toJson()).toList() ?? [],
          bomSummaryData: _bomSummary!.toJson(),
          // Update ALL user input fields to remember current choices
          inputMode: _inputMode,
          systemType: _systemType,
          installationType: _installationType,
          installationFactor: _installationFactor,
          userInputAgentQuantity: _inputMode == InputMode.agentQuantity && _agentQuantityController.text.isNotEmpty
              ? double.tryParse(_agentQuantityController.text)
              : null,
          updatedAt: DateTime.now(),
        );

        // Update in project
        Provider.of<ProjectProvider>(context, listen: false).updateCleanAgentSystem(system);
      } else if (mounted) {
        // Create new system with BOM data and user options
        system = CleanAgentSystem.fromCalculation(
          name: systemName,
          description: systemDescription,
          input: input,
          results: _designResults!,
          summary: _bomSummary!,
          bomItems: _bomItems?.map((item) => item.toJson()).toList() ?? [],
          bomSummaryData: _bomSummary!.toJson(),
          inputMode: _inputMode,
          systemType: _systemType,
          installationType: _installationType,
          installationFactor: _installationFactor,
          userInputAgentQuantity: _inputMode == InputMode.agentQuantity && _agentQuantityController.text.isNotEmpty
              ? double.tryParse(_agentQuantityController.text)
              : null,
        );

        // Add to project
        Provider.of<ProjectProvider>(context, listen: false).addCleanAgentSystem(system);
      } else {
        return; // Widget was disposed
      }

      // Call callback if provided
      if (widget.onSystemSaved != null) {
        widget.onSystemSaved!(system);
      }

      // Show success message
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(widget.existingSystem != null
                ? 'System updated successfully!'
                : 'System added successfully!'),
            backgroundColor: Colors.green,
          ),
        );
      }

      // Navigate back if this was a new system
      if (widget.existingSystem == null && mounted) {
        Navigator.pop(context);
      }

    } catch (e) {
      _showError('Failed to save system: $e');
    }
  }

  Future<Map<String, String>?> _showSaveSystemDialog() async {
    // Generate default name and description based on calculation results
    String defaultName = '';
    String defaultDescription = '';

    if (_designResults != null && _selectedAgentType != null) {
      final agentName = _selectedAgentType == AgentType.fm200 ? 'FM200' : 'NOVEC';
      final agentKg = _designResults!.totalAgentRequired.toInt();
      defaultName = '$agentName ${agentKg}KG';

      // Generate detailed description
      final cylinderQty = _designResults!.cylinder.numCylinders2ndIter;
      final cylinderSize = _designResults!.cylinder.cylinderSizeLiters2ndIter.toInt();
      final nozzleQty = _designResults!.discharge.nozzleQtyFinal;
      final nozzleSize = _designResults!.discharge.nozzleSizeFinal;
      final manifoldSize = _designResults!.discharge.manifoldPipeSize;

      defaultDescription = '${agentKg}kg $agentName system with $cylinderQty × ${cylinderSize}L cylinder${cylinderQty > 1 ? 's' : ''} '
          'c/w actuators, $nozzleQty × ${nozzleSize}mm nozzle${nozzleQty > 1 ? 's' : ''}, '
          '${manifoldSize}mm manifold pipe system for ${formatNumber(_designResults!.roomData.roomVolume)}m³ room.';
    }

    // For existing systems being updated, always use the new calculated values
    final nameController = TextEditingController(
      text: defaultName.isNotEmpty ? defaultName : (widget.existingSystem?.name ?? 'Clean Agent System'),
    );
    final descriptionController = TextEditingController(
      text: defaultDescription.isNotEmpty ? defaultDescription : (widget.existingSystem?.description ?? ''),
    );

    return showDialog<Map<String, String>>(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(widget.existingSystem != null ? 'Update System' : 'Save System'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            TextField(
              controller: nameController,
              decoration: const InputDecoration(
                labelText: 'System Name',
                border: OutlineInputBorder(),
              ),
            ),
            const SizedBox(height: 16),
            TextField(
              controller: descriptionController,
              decoration: const InputDecoration(
                labelText: 'Description (Optional)',
                border: OutlineInputBorder(),
              ),
              maxLines: 3,
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              if (nameController.text.trim().isEmpty) {
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(
                    content: Text('Please enter a system name'),
                    backgroundColor: Colors.red,
                  ),
                );
                return;
              }

              Navigator.pop(context, {
                'name': nameController.text.trim(),
                'description': descriptionController.text.trim(),
              });
            },
            child: Text(widget.existingSystem != null ? 'Update' : 'Save'),
          ),
        ],
      ),
    );
  }

  Widget _buildInputForm() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'System Configuration',
              style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                fontWeight: FontWeight.bold,
                color: Colors.red.shade700,
              ),
            ),
            const SizedBox(height: 16),

            // Agent Type Selection
            DropdownButtonFormField<AgentType>(
              value: _selectedAgentType,
              decoration: const InputDecoration(
                labelText: 'Agent Type',
                border: OutlineInputBorder(),
              ),
              items: _agentTypeNames.entries.map((entry) {
                return DropdownMenuItem<AgentType>(
                  value: entry.key,
                  child: Text(entry.value),
                );
              }).toList(),
              onChanged: (value) {
                setState(() {
                  _selectedAgentType = value;
                  _selectedConcentration = null; // Reset concentration
                });
              },
            ),
            const SizedBox(height: 16),

            // Design Concentration Selection
            DropdownButtonFormField<String>(
              value: _selectedConcentration,
              decoration: const InputDecoration(
                labelText: 'Design Concentration',
                border: OutlineInputBorder(),
              ),
              items: _availableConcentrations.map((concentration) {
                return DropdownMenuItem<String>(
                  value: concentration,
                  child: Text(concentration),
                );
              }).toList(),
              onChanged: _selectedAgentType != null ? (value) {
                setState(() {
                  _selectedConcentration = value;
                });
              } : null,
            ),
            const SizedBox(height: 16),

            // Installation Factor Selection
            DropdownButtonFormField<double>(
              value: _installationFactor,
              decoration: const InputDecoration(
                labelText: 'Installation Factor',
                border: OutlineInputBorder(),
                helperText: 'Factor applied to system cost for labour installation prices',
              ),
              items: const [
                DropdownMenuItem<double>(
                  value: 0.10,
                  child: Text('10%'),
                ),
                DropdownMenuItem<double>(
                  value: 0.15,
                  child: Text('15%'),
                ),
                DropdownMenuItem<double>(
                  value: 0.20,
                  child: Text('20%'),
                ),
                DropdownMenuItem<double>(
                  value: 0.25,
                  child: Text('25%'),
                ),
                DropdownMenuItem<double>(
                  value: 0.30,
                  child: Text('30%'),
                ),
              ],
              onChanged: (value) {
                setState(() {
                  _installationFactor = value!;
                });
              },
            ),
            const SizedBox(height: 16),

            // Input Mode Selection
            Row(
              children: [
                Expanded(
                  child: RadioListTile<InputMode>(
                    title: const Text('Room Dimensions'),
                    value: InputMode.dimensions,
                    groupValue: _inputMode,
                    onChanged: (value) {
                      setState(() {
                        _inputMode = value!;
                      });
                    },
                  ),
                ),
                Expanded(
                  child: RadioListTile<InputMode>(
                    title: const Text('Agent Quantity'),
                    value: InputMode.agentQuantity,
                    groupValue: _inputMode,
                    onChanged: (value) {
                      setState(() {
                        _inputMode = value!;
                      });
                    },
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),

            // Input fields based on mode
            if (_inputMode == InputMode.dimensions) ...[
              Row(
                children: [
                  Expanded(
                    child: TextFormField(
                      controller: _roomLengthController,
                      decoration: const InputDecoration(
                        labelText: 'Room Length (m)',
                        border: OutlineInputBorder(),
                      ),
                      keyboardType: TextInputType.number,
                      inputFormatters: [
                        FilteringTextInputFormatter.allow(RegExp(r'^\d*\.?\d*')),
                      ],
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: TextFormField(
                      controller: _roomWidthController,
                      decoration: const InputDecoration(
                        labelText: 'Room Width (m)',
                        border: OutlineInputBorder(),
                      ),
                      keyboardType: TextInputType.number,
                      inputFormatters: [
                        FilteringTextInputFormatter.allow(RegExp(r'^\d*\.?\d*')),
                      ],
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: TextFormField(
                      controller: _roomHeightController,
                      decoration: const InputDecoration(
                        labelText: 'Room Height (m)',
                        border: OutlineInputBorder(),
                      ),
                      keyboardType: TextInputType.number,
                      inputFormatters: [
                        FilteringTextInputFormatter.allow(RegExp(r'^\d*\.?\d*')),
                      ],
                    ),
                  ),
                ],
              ),
            ] else ...[
              Row(
                children: [
                  Expanded(
                    child: TextFormField(
                      controller: _agentQuantityController,
                      decoration: const InputDecoration(
                        labelText: 'Agent Quantity (kg)',
                        border: OutlineInputBorder(),
                      ),
                      keyboardType: TextInputType.number,
                      inputFormatters: [
                        FilteringTextInputFormatter.allow(RegExp(r'^\d*\.?\d*')),
                      ],
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: TextFormField(
                      controller: _roomHeightController,
                      decoration: const InputDecoration(
                        labelText: 'Room Height (m) - Optional',
                        border: OutlineInputBorder(),
                      ),
                      keyboardType: TextInputType.number,
                      inputFormatters: [
                        FilteringTextInputFormatter.allow(RegExp(r'^\d*\.?\d*')),
                      ],
                    ),
                  ),
                  const Expanded(child: SizedBox()), // Spacer
                ],
              ),
            ],
            const SizedBox(height: 16),

            // System Type and Installation Type
            Row(
              children: [
                Expanded(
                  child: DropdownButtonFormField<SystemType>(
                    value: _systemType,
                    decoration: const InputDecoration(
                      labelText: 'System Type',
                      border: OutlineInputBorder(),
                    ),
                    items: SystemType.values.where((type) => type != SystemType.reserve).map((type) {
                      return DropdownMenuItem<SystemType>(
                        value: type,
                        child: Text(formatSystemType(type)),
                      );
                    }).toList(),
                    onChanged: (value) {
                      setState(() {
                        _systemType = value!;
                      });
                    },
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: DropdownButtonFormField<InstallationType>(
                    value: _installationType,
                    decoration: const InputDecoration(
                      labelText: 'Installation Type',
                      border: OutlineInputBorder(),
                    ),
                    items: InstallationType.values.map((type) {
                      return DropdownMenuItem<InstallationType>(
                        value: type,
                        child: Text(formatInstallationType(type)),
                      );
                    }).toList(),
                    onChanged: (value) {
                      setState(() {
                        _installationType = value!;
                      });
                    },
                  ),
                ),
              ],
            ),
            const SizedBox(height: 24),

            // Calculate Button
            Center(
              child: ElevatedButton(
                onPressed: _isCalculating ? null : _calculateSystem,
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.red.shade700,
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(horizontal: 32, vertical: 16),
                ),
                child: _isCalculating
                    ? const SizedBox(
                        width: 20,
                        height: 20,
                        child: CircularProgressIndicator(
                          strokeWidth: 2,
                          valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                        ),
                      )
                    : const Text('Calculate System', style: TextStyle(fontSize: 16)),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildResultsSection() {
    if (_designResults == null) return const SizedBox.shrink();

    final results = _designResults!;

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'Calculation Results',
                  style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: Colors.green.shade700,
                  ),
                ),
                ElevatedButton.icon(
                  onPressed: _saveSystem,
                  icon: const Icon(Icons.add),
                  label: Text(widget.existingSystem != null ? 'Update System' : 'Add System'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.green.shade700,
                    foregroundColor: Colors.white,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),

            // Room Information
            _buildResultRow('Room Dimensions',
                '${formatNumber(results.roomData.roomLength)} × ${formatNumber(results.roomData.roomWidth)} × ${formatNumber(results.roomData.roomHeight)} m'),
            _buildResultRow('Room Volume', '${formatNumber(results.roomData.roomVolume)} m³'),
            _buildResultRow('Room Area', '${formatNumber(results.roomData.roomArea)} m²'),

            const Divider(),

            // Agent Information
            _buildResultRow('Design Factor', formatNumber(results.designFactor, 3)),
            _buildResultRow('Total Agent Required', '${formatNumber(results.totalAgentRequired)} kg'),
            _buildResultRow('Actual Agent', '${formatNumber(results.cylinder.actualTotalKg)} kg'),

            const Divider(),

            // Cylinder Information
            _buildResultRow('Number of Cylinders', '${results.cylinder.numCylinders2ndIter}'),
            _buildResultRow('Cylinder Size', '${results.cylinder.cylinderSizeLiters2ndIter.toInt()}L'),
            _buildResultRow('Agent per Cylinder', '${formatNumber(results.cylinder.qtyPerCylinder)} kg'),
            _buildResultRow('Filling Ratio', formatPercentage(results.cylinder.fillingRatio * 100)),

            const Divider(),

            // Discharge System
            _buildResultRow('Total Flow Rate', '${formatNumber(results.discharge.totalFlowRate)} kg/s'),
            _buildResultRow('Number of Nozzles', '${results.discharge.nozzleQtyFinal}'),
            _buildResultRow('Nozzle Size', '${results.discharge.nozzleSizeFinal}mm'),
            _buildResultRow('Manifold Pipe Size', '${results.discharge.manifoldPipeSize}mm'),
          ],
        ),
      ),
    );
  }

  Widget _buildResultRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4.0),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: const TextStyle(fontWeight: FontWeight.w500),
          ),
          Text(
            value,
            style: const TextStyle(fontWeight: FontWeight.bold),
          ),
        ],
      ),
    );
  }

  Widget _buildBomSection() {
    if (_bomItems == null || _bomSummary == null) return const SizedBox.shrink();

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Bill of Materials',
              style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                fontWeight: FontWeight.bold,
                color: Colors.blue.shade700,
              ),
            ),
            const SizedBox(height: 16),

            // BOM Summary
            _buildCostSummary(),
            const SizedBox(height: 16),

            // BOM Items Table
            _buildBomTable(),
          ],
        ),
      ),
    );
  }

  Widget _buildCostSummary() {
    if (_bomSummary == null) return const SizedBox.shrink();

    final summary = _bomSummary!;

    return Container(
      padding: const EdgeInsets.all(16.0),
      decoration: BoxDecoration(
        color: Colors.grey.shade100,
        borderRadius: BorderRadius.circular(8.0),
      ),
      child: Column(
        children: [
          // Ex-works items (shown separately then summed)
          _buildSummaryRow('Suppression System', formatCurrencyUSD(summary.suppressionCost)),
          _buildSummaryRow('Alarm & Detection', formatCurrencyUSD(summary.alarmCost)),
          _buildSummaryRow('Ex-works Total', formatCurrencyUSD(summary.exWorksItemsUSD), isSubtotal: true),
          const Divider(),

          // Landed cost (ex-works converted + shipping)
          _buildSummaryRow('Landed Cost', formatCurrencySAR(summary.landedCostSAR)),

          // Installation materials (SAR)
          if (summary.installationMaterialsSAR > 0)
            _buildSummaryRow('Installation Materials', formatCurrencySAR(summary.installationMaterialsSAR)),

          // Installation labor cost (SAR)
          if (summary.installationLaborSAR > 0)
            _buildSummaryRow('Installation Labor Cost', formatCurrencySAR(summary.installationLaborSAR)),

          const Divider(thickness: 2),
          _buildSummaryRow(
            'Total',
            formatCurrencySAR(summary.grandTotalSAR),
            isTotal: true,
          ),
        ],
      ),
    );
  }

  Widget _buildSummaryRow(String label, String value, {bool isTotal = false, bool isSubtotal = false}) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4.0),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: TextStyle(
              fontWeight: isTotal ? FontWeight.bold : (isSubtotal ? FontWeight.w600 : FontWeight.w500),
              fontSize: isTotal ? 16 : 14,
              color: isSubtotal ? Colors.blue.shade600 : null,
            ),
          ),
          Text(
            value,
            style: TextStyle(
              fontWeight: FontWeight.bold,
              fontSize: isTotal ? 16 : 14,
              color: isTotal ? Colors.green.shade700 : (isSubtotal ? Colors.blue.shade600 : null),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildBomTable() {
    if (_bomItems == null) return const SizedBox.shrink();

    return SingleChildScrollView(
      scrollDirection: Axis.horizontal,
      child: DataTable(
        columns: const [
          DataColumn(label: Text('Part No.')),
          DataColumn(label: Text('Description')),
          DataColumn(label: Text('Qty')),
          DataColumn(label: Text('Unit Cost (USD)')),
          DataColumn(label: Text('Total Cost (USD)')),
          DataColumn(label: Text('Unit Cost (SAR)')),
          DataColumn(label: Text('Total Cost (SAR)')),
          DataColumn(label: Text('Category')),
        ],
        rows: _bomItems!.map((item) {
          return DataRow(
            cells: [
              DataCell(Text(item.partNo)),
              DataCell(
                SizedBox(
                  width: 200,
                  child: Text(
                    item.description,
                    overflow: TextOverflow.ellipsis,
                    maxLines: 2,
                  ),
                ),
              ),
              DataCell(Text('${item.quantity}')),
              // USD columns
              DataCell(Text(item.currency == 'USD' ? formatCurrencyUSD(item.unitCost) : '-')),
              DataCell(Text(item.currency == 'USD' ? formatCurrencyUSD(item.totalCost) : '-')),
              // SAR columns
              DataCell(Text(item.currency == 'SAR' ? 'SAR ${item.unitCost.toStringAsFixed(2)}' : '-')),
              DataCell(Text(item.currency == 'SAR' ? 'SAR ${item.totalCost.toStringAsFixed(2)}' : '-')),
              DataCell(Text(item.category)),
            ],
          );
        }).toList(),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Clean Agent Calculator'),
        backgroundColor: Colors.red.shade700,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.clear_all),
            onPressed: _clearForm,
            tooltip: 'Clear Form',
          ),
        ],
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildInputForm(),
            const SizedBox(height: 24),
            if (_designResults != null) ...[
              _buildResultsSection(),
              const SizedBox(height: 24),
            ],
            if (_bomItems != null && _bomSummary != null) ...[
              _buildBomSection(),
            ],
          ],
        ),
      ),
    );
  }
}
