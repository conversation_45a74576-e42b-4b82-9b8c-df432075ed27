import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:data_table_2/data_table_2.dart';
import '../services/enhanced_database_service.dart';
import 'dart:async';
import 'package:intl/intl.dart';

class CellPosition {
  final int row;
  final int column;
  
  CellPosition(this.row, this.column);
  
  @override
  bool operator ==(Object other) {
    return other is CellPosition && other.row == row && other.column == column;
  }
  
  @override
  int get hashCode => row.hashCode ^ column.hashCode;
}

class GridFilter {
  final String columnName;
  final String operator; // equals, contains, startsWith, endsWith, greaterThan, lessThan
  final dynamic value;
  
  GridFilter({
    required this.columnName,
    required this.operator,
    required this.value,
  });
}

class GridSort {
  final String columnName;
  final bool ascending;
  
  GridSort({
    required this.columnName,
    required this.ascending,
  });
}

class EnhancedExcelGrid extends StatefulWidget {
  final String tableName;
  final TableSchema schema;
  final VoidCallback? onDataChanged;
  final bool readOnly;
  final int pageSize;
  final bool showRowNumbers;
  final bool allowColumnResize;
  final bool allowSorting;
  final bool allowFiltering;
  final bool allowCellEditing;
  final List<String>? frozenColumns;
  final int? frozenRows;
  
  const EnhancedExcelGrid({
    super.key,
    required this.tableName,
    required this.schema,
    this.onDataChanged,
    this.readOnly = false,
    this.pageSize = 100,
    this.showRowNumbers = true,
    this.allowColumnResize = true,
    this.allowSorting = true,
    this.allowFiltering = true,
    this.allowCellEditing = true,
    this.frozenColumns,
    this.frozenRows,
  });

  @override
  State<EnhancedExcelGrid> createState() => _EnhancedExcelGridState();
}

class _EnhancedExcelGridState extends State<EnhancedExcelGrid> {
  final EnhancedDatabaseService _dbService = EnhancedDatabaseService();
  final ScrollController _horizontalController = ScrollController();
  final ScrollController _verticalController = ScrollController();
  final FocusNode _gridFocusNode = FocusNode();
  
  List<Map<String, dynamic>> _data = [];
  List<Map<String, dynamic>> _filteredData = [];
  final Map<String, double> _columnWidths = {};
  final List<GridFilter> _filters = [];
  GridSort? _currentSort;
  CellPosition? _selectedCell;
  CellPosition? _editingCell;
  final TextEditingController _cellEditController = TextEditingController();
  OverlayEntry? _cellEditorOverlay;
  
  int _currentPage = 0;
  int _totalRows = 0;
  bool _isLoading = false;
  String _searchQuery = '';
  Timer? _searchDebouncer;
  
  // Multi-selection support
  final Set<CellPosition> _selectedCells = {};
  CellPosition? _selectionStart;
  final bool _isSelecting = false;
  
  @override
  void initState() {
    super.initState();
    _initializeColumnWidths();
    _loadData();
    _gridFocusNode.requestFocus();
  }
  
  @override
  void dispose() {
    _horizontalController.dispose();
    _verticalController.dispose();
    _gridFocusNode.dispose();
    _cellEditController.dispose();
    _searchDebouncer?.cancel();
    _cellEditorOverlay?.remove();
    super.dispose();
  }
  
  void _initializeColumnWidths() {
    for (final column in widget.schema.columns) {
      _columnWidths[column.name] = _getDefaultColumnWidth(column);
    }
  }
  
  double _getDefaultColumnWidth(ColumnSchema column) {
    switch (column.type) {
      case DataType.boolean:
        return 80;
      case DataType.number:
      case DataType.currency:
        return 120;
      case DataType.date:
        return 140;
      default:
        return 150;
    }
  }
  
  Future<void> _loadData() async {
    if (_isLoading) return;
    
    setState(() {
      _isLoading = true;
    });
    
    try {
      // Get total count
      _totalRows = await _dbService.getTableRowCount(widget.tableName);
      
      // Build query with filters and sorting
      String? whereClause;
      List<dynamic>? whereArgs;
      String? orderBy;
      
      if (_filters.isNotEmpty) {
        final conditions = <String>[];
        final args = <dynamic>[];
        
        for (final filter in _filters) {
          switch (filter.operator) {
            case 'equals':
              conditions.add('${filter.columnName} = ?');
              args.add(filter.value);
              break;
            case 'contains':
              conditions.add('${filter.columnName} LIKE ?');
              args.add('%${filter.value}%');
              break;
            case 'startsWith':
              conditions.add('${filter.columnName} LIKE ?');
              args.add('${filter.value}%');
              break;
            case 'endsWith':
              conditions.add('${filter.columnName} LIKE ?');
              args.add('%${filter.value}');
              break;
            case 'greaterThan':
              conditions.add('${filter.columnName} > ?');
              args.add(filter.value);
              break;
            case 'lessThan':
              conditions.add('${filter.columnName} < ?');
              args.add(filter.value);
              break;
          }
        }
        
        if (conditions.isNotEmpty) {
          whereClause = conditions.join(' AND ');
          whereArgs = args;
        }
      }
      
      if (_currentSort != null) {
        orderBy = '${_currentSort!.columnName} ${_currentSort!.ascending ? 'ASC' : 'DESC'}';
      }
      
      // Add search query if present
      if (_searchQuery.isNotEmpty) {
        final searchResults = await _dbService.searchTable(
          widget.tableName,
          _searchQuery,
          limit: widget.pageSize,
        );
        _data = searchResults;
      } else {
        _data = await _dbService.getTableData(
          widget.tableName,
          limit: widget.pageSize,
          offset: _currentPage * widget.pageSize,
          orderBy: orderBy,
          where: whereClause,
          whereArgs: whereArgs,
        );
      }
      
      _filteredData = List.from(_data);
      
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Error loading data: $e')),
      );
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }
  
  void _addFilter(GridFilter filter) {
    setState(() {
      _filters.removeWhere((f) => f.columnName == filter.columnName);
      _filters.add(filter);
      _currentPage = 0;
    });
    _loadData();
  }
  
  void _removeFilter(String columnName) {
    setState(() {
      _filters.removeWhere((f) => f.columnName == columnName);
      _currentPage = 0;
    });
    _loadData();
  }
  
  void _sortByColumn(String columnName) {
    setState(() {
      if (_currentSort?.columnName == columnName) {
        _currentSort = GridSort(
          columnName: columnName,
          ascending: !_currentSort!.ascending,
        );
      } else {
        _currentSort = GridSort(
          columnName: columnName,
          ascending: true,
        );
      }
      _currentPage = 0;
    });
    _loadData();
  }
  
  void _onCellTap(int row, int column) {
    setState(() {
      _selectedCell = CellPosition(row, column);
      _selectedCells.clear();
      _selectedCells.add(_selectedCell!);
    });
    _gridFocusNode.requestFocus();
  }
  
  void _onCellDoubleTap(int row, int column) {
    if (!widget.allowCellEditing || widget.readOnly) return;
    
    _startCellEditing(row, column);
  }
  
  void _startCellEditing(int row, int column) {
    if (row >= _filteredData.length || column >= widget.schema.columns.length) return;
    
    final columnSchema = widget.schema.columns[column];
    final currentValue = _filteredData[row][columnSchema.name];
    
    setState(() {
      _editingCell = CellPosition(row, column);
      _cellEditController.text = _formatCellValue(currentValue, columnSchema.type);
    });
    
    _showCellEditor(row, column);
  }
  
  void _showCellEditor(int row, int column) {
    _cellEditorOverlay?.remove();
    
    final renderBox = context.findRenderObject() as RenderBox;
    final cellRect = _getCellRect(row, column);
    
    _cellEditorOverlay = OverlayEntry(
      builder: (context) => Positioned(
        left: cellRect.left,
        top: cellRect.top,
        width: cellRect.width,
        height: cellRect.height,
        child: Material(
          elevation: 4,
          child: TextField(
            controller: _cellEditController,
            autofocus: true,
            decoration: const InputDecoration(
              border: OutlineInputBorder(),
              contentPadding: EdgeInsets.all(8),
            ),
            onSubmitted: (value) => _finishCellEditing(true),
            onTapOutside: (_) => _finishCellEditing(false),
          ),
        ),
      ),
    );
    
    Overlay.of(context).insert(_cellEditorOverlay!);
  }
  
  Rect _getCellRect(int row, int column) {
    // Calculate cell position based on column widths and row height
    double left = 0;
    for (int i = 0; i < column; i++) {
      final columnName = widget.schema.columns[i].name;
      left += _columnWidths[columnName] ?? 150;
    }
    
    const double rowHeight = 48.0;
    const double headerHeight = 56.0;
    double top = headerHeight + (row * rowHeight);
    
    final columnName = widget.schema.columns[column].name;
    final width = _columnWidths[columnName] ?? 150;
    
    return Rect.fromLTWH(left, top, width, rowHeight);
  }
  
  void _finishCellEditing(bool save) {
    _cellEditorOverlay?.remove();
    _cellEditorOverlay = null;
    
    if (save && _editingCell != null) {
      _saveCellValue(_editingCell!.row, _editingCell!.column, _cellEditController.text);
    }
    
    setState(() {
      _editingCell = null;
    });
  }
  
  Future<void> _saveCellValue(int row, int column, String value) async {
    if (row >= _filteredData.length || column >= widget.schema.columns.length) return;
    
    final columnSchema = widget.schema.columns[column];
    final rowData = _filteredData[row];
    final rowId = rowData['id'];
    
    try {
      final convertedValue = _convertStringToValue(value, columnSchema.type);
      
      await _dbService.updateRow(
        widget.tableName,
        rowId,
        {columnSchema.name: convertedValue},
      );
      
      setState(() {
        _filteredData[row][columnSchema.name] = convertedValue;
      });
      
      widget.onDataChanged?.call();
      
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Error saving cell: $e')),
      );
    }
  }
  
  dynamic _convertStringToValue(String value, DataType type) {
    switch (type) {
      case DataType.number:
      case DataType.currency:
        return double.tryParse(value) ?? 0.0;
      case DataType.boolean:
        return ['true', 'yes', '1'].contains(value.toLowerCase()) ? 1 : 0;
      case DataType.date:
        try {
          return DateTime.parse(value).toIso8601String();
        } catch (e) {
          return value;
        }
      default:
        return value;
    }
  }
  
  String _formatCellValue(dynamic value, DataType type) {
    if (value == null) return '';
    
    switch (type) {
      case DataType.currency:
        final numValue = value is num ? value : double.tryParse(value.toString());
        if (numValue != null) {
          return NumberFormat.currency(symbol: '\\\$').format(numValue);
        }
        return value.toString();
      case DataType.date:
        try {
          final date = DateTime.parse(value.toString());
          return DateFormat('yyyy-MM-dd').format(date);
        } catch (e) {
          return value.toString();
        }
      case DataType.boolean:
        if (value is int) return value == 1 ? 'Yes' : 'No';
        return value.toString();
      default:
        return value.toString();
    }
  }
  
  void _handleKeyEvent(KeyEvent event) {
    if (event is! KeyDownEvent) return;
    
    if (_editingCell != null) {
      // Handle editing mode keys
      if (event.logicalKey == LogicalKeyboardKey.escape) {
        _finishCellEditing(false);
      } else if (event.logicalKey == LogicalKeyboardKey.enter) {
        _finishCellEditing(true);
      }
      return;
    }
    
    if (_selectedCell == null) return;
    
    // Handle navigation keys
    final currentRow = _selectedCell!.row;
    final currentColumn = _selectedCell!.column;
    
    switch (event.logicalKey) {
      case LogicalKeyboardKey.arrowUp:
        if (currentRow > 0) {
          _onCellTap(currentRow - 1, currentColumn);
        }
        break;
      case LogicalKeyboardKey.arrowDown:
        if (currentRow < _filteredData.length - 1) {
          _onCellTap(currentRow + 1, currentColumn);
        }
        break;
      case LogicalKeyboardKey.arrowLeft:
        if (currentColumn > 0) {
          _onCellTap(currentRow, currentColumn - 1);
        }
        break;
      case LogicalKeyboardKey.arrowRight:
        if (currentColumn < widget.schema.columns.length - 1) {
          _onCellTap(currentRow, currentColumn + 1);
        }
        break;
      case LogicalKeyboardKey.enter:
      case LogicalKeyboardKey.f2:
        _startCellEditing(currentRow, currentColumn);
        break;
      case LogicalKeyboardKey.delete:
        _deleteCellValue(currentRow, currentColumn);
        break;
    }
  }
  
  Future<void> _deleteCellValue(int row, int column) async {
    if (widget.readOnly || !widget.allowCellEditing) return;
    
    final columnSchema = widget.schema.columns[column];
    if (columnSchema.isRequired) return;
    
    await _saveCellValue(row, column, '');
  }
  
  void _onSearch(String query) {
    _searchDebouncer?.cancel();
    _searchDebouncer = Timer(const Duration(milliseconds: 300), () {
      setState(() {
        _searchQuery = query;
        _currentPage = 0;
      });
      _loadData();
    });
  }
  
  Future<void> _addNewRow() async {
    if (widget.readOnly) return;
    
    try {
      final newData = <String, dynamic>{};
      for (final column in widget.schema.columns) {
        if (column.name == 'id') continue;
        newData[column.name] = column.defaultValue;
      }
      
      await _dbService.insertRow(widget.tableName, newData);
      await _loadData();
      widget.onDataChanged?.call();
      
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Error adding row: $e')),
      );
    }
  }
  
  Future<void> _deleteSelectedRows() async {
    if (widget.readOnly || _selectedCells.isEmpty) return;
    
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete Rows'),
        content: Text('Are you sure you want to delete ${_selectedCells.length} row(s)?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () => Navigator.of(context).pop(true),
            child: const Text('Delete'),
          ),
        ],
      ),
    );
    
    if (confirmed != true) return;
    
    try {
      for (final cell in _selectedCells) {
        if (cell.row < _filteredData.length) {
          final rowData = _filteredData[cell.row];
          await _dbService.deleteRow(widget.tableName, rowData['id']);
        }
      }
      
      setState(() {
        _selectedCells.clear();
        _selectedCell = null;
      });
      
      await _loadData();
      widget.onDataChanged?.call();
      
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Error deleting rows: $e')),
      );
    }
  }
  
  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        _buildToolbar(),
        _buildFilterBar(),
        Expanded(
          child: _buildGrid(),
        ),
        _buildPagination(),
      ],
    );
  }
  
  Widget _buildToolbar() {
    return Container(
      padding: const EdgeInsets.all(8),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        border: Border(
          bottom: BorderSide(
            color: Theme.of(context).dividerColor,
          ),
        ),
      ),
      child: Row(
        children: [
          Expanded(
            child: TextField(
              decoration: const InputDecoration(
                hintText: 'Search...',
                prefixIcon: Icon(Icons.search),
                border: OutlineInputBorder(),
                contentPadding: EdgeInsets.symmetric(horizontal: 12, vertical: 8),
              ),
              onChanged: _onSearch,
            ),
          ),
          const SizedBox(width: 8),
          if (!widget.readOnly) ...[
            IconButton(
              icon: const Icon(Icons.add),
              tooltip: 'Add Row',
              onPressed: _addNewRow,
            ),
          ],
          if (!widget.readOnly && _selectedCells.isNotEmpty) ...[
            IconButton(
              icon: const Icon(Icons.delete),
              tooltip: 'Delete Selected',
              onPressed: _deleteSelectedRows,
            ),
          ],
          IconButton(
            icon: const Icon(Icons.refresh),
            tooltip: 'Refresh',
            onPressed: _loadData,
          ),
        ],
      ),
    );
  }
  
  Widget _buildFilterBar() {
    if (_filters.isEmpty) return const SizedBox.shrink();
    
    return Container(
      padding: const EdgeInsets.all(8),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surfaceContainerHighest,
        border: Border(
          bottom: BorderSide(
            color: Theme.of(context).dividerColor,
          ),
        ),
      ),
      child: Wrap(
        spacing: 8,
        children: _filters.map((filter) {
          return Chip(
            label: Text('${filter.columnName}: ${filter.value}'),
            onDeleted: () => _removeFilter(filter.columnName),
          );
        }).toList(),
      ),
    );
  }
  
  Widget _buildGrid() {
    if (_isLoading) {
      return const Center(child: CircularProgressIndicator());
    }
    
    return Focus(
      focusNode: _gridFocusNode,
      onKeyEvent: (node, event) {
        _handleKeyEvent(event);
        return KeyEventResult.handled;
      },
      child: DataTable2(
        columnSpacing: 0,
        horizontalMargin: 0,
        minWidth: _calculateMinWidth(),
        scrollController: _verticalController,
        columns: _buildColumns(),
        rows: _buildRows(),
        showCheckboxColumn: false,
        dataRowHeight: 48,
        headingRowHeight: 56,
        border: TableBorder.all(
          color: Theme.of(context).dividerColor,
          width: 1,
        ),
      ),
    );
  }
  
  double _calculateMinWidth() {
    return _columnWidths.values.fold(0.0, (sum, width) => sum + width);
  }
  
  List<DataColumn2> _buildColumns() {
    return widget.schema.columns.map((column) {
      final isFiltered = _filters.any((f) => f.columnName == column.name);
      final isSorted = _currentSort?.columnName == column.name;
      
      return DataColumn2(
        label: InkWell(
          onTap: widget.allowSorting ? () => _sortByColumn(column.name) : null,
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Expanded(
                child: Text(
                  column.displayName,
                  style: TextStyle(
                    fontWeight: FontWeight.bold,
                    color: isFiltered ? Theme.of(context).colorScheme.primary : null,
                  ),
                ),
              ),
              if (isSorted)
                Icon(
                  _currentSort!.ascending ? Icons.arrow_upward : Icons.arrow_downward,
                  size: 16,
                ),
              if (widget.allowFiltering)
                PopupMenuButton<String>(
                  icon: Icon(
                    Icons.filter_list,
                    size: 16,
                    color: isFiltered ? Theme.of(context).colorScheme.primary : null,
                  ),
                  onSelected: (value) => _showFilterDialog(column),
                  itemBuilder: (context) => [
                    const PopupMenuItem(
                      value: 'filter',
                      child: Text('Filter'),
                    ),
                    if (isFiltered)
                      PopupMenuItem(
                        value: 'clear',
                        child: const Text('Clear Filter'),
                        onTap: () => _removeFilter(column.name),
                      ),
                  ],
                ),
            ],
          ),
        ),
        size: ColumnSize.M,
        fixedWidth: _columnWidths[column.name],
      );
    }).toList();
  }
  
  List<DataRow2> _buildRows() {
    return _filteredData.asMap().entries.map((entry) {
      final rowIndex = entry.key;
      final rowData = entry.value;
      
      return DataRow2(
        cells: widget.schema.columns.asMap().entries.map((colEntry) {
          final colIndex = colEntry.key;
          final column = colEntry.value;
          final cellValue = rowData[column.name];
          final cellPosition = CellPosition(rowIndex, colIndex);
          final isSelected = _selectedCells.contains(cellPosition);
          final isEditing = _editingCell == cellPosition;
          
          return DataCell(
            Container(
              width: double.infinity,
              height: double.infinity,
              decoration: BoxDecoration(
                color: isSelected 
                    ? Theme.of(context).colorScheme.primary.withOpacity(0.1)
                    : null,
                border: isEditing
                    ? Border.all(
                        color: Theme.of(context).colorScheme.primary,
                        width: 2,
                      )
                    : null,
              ),
              padding: const EdgeInsets.all(8),
              child: Text(
                _formatCellValue(cellValue, column.type),
                overflow: TextOverflow.ellipsis,
              ),
            ),
            onTap: () => _onCellTap(rowIndex, colIndex),
            onDoubleTap: () => _onCellDoubleTap(rowIndex, colIndex),
          );
        }).toList(),
      );
    }).toList();
  }
  
  void _showFilterDialog(ColumnSchema column) {
    showDialog(
      context: context,
      builder: (context) => _FilterDialog(
        column: column,
        onApply: (filter) => _addFilter(filter),
      ),
    );
  }
  
  Widget _buildPagination() {
    final totalPages = (_totalRows / widget.pageSize).ceil();
    
    return Container(
      padding: const EdgeInsets.all(8),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        border: Border(
          top: BorderSide(
            color: Theme.of(context).dividerColor,
          ),
        ),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text('Total: $_totalRows rows'),
          Row(
            children: [
              IconButton(
                icon: const Icon(Icons.first_page),
                onPressed: _currentPage > 0 ? () => _goToPage(0) : null,
              ),
              IconButton(
                icon: const Icon(Icons.chevron_left),
                onPressed: _currentPage > 0 ? () => _goToPage(_currentPage - 1) : null,
              ),
              Text('${_currentPage + 1} / $totalPages'),
              IconButton(
                icon: const Icon(Icons.chevron_right),
                onPressed: _currentPage < totalPages - 1 ? () => _goToPage(_currentPage + 1) : null,
              ),
              IconButton(
                icon: const Icon(Icons.last_page),
                onPressed: _currentPage < totalPages - 1 ? () => _goToPage(totalPages - 1) : null,
              ),
            ],
          ),
        ],
      ),
    );
  }
  
  void _goToPage(int page) {
    setState(() {
      _currentPage = page;
    });
    _loadData();
  }
}

class _FilterDialog extends StatefulWidget {
  final ColumnSchema column;
  final Function(GridFilter) onApply;
  
  const _FilterDialog({
    required this.column,
    required this.onApply,
  });
  
  @override
  State<_FilterDialog> createState() => _FilterDialogState();
}

class _FilterDialogState extends State<_FilterDialog> {
  String _operator = 'contains';
  final TextEditingController _valueController = TextEditingController();
  
  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: Text('Filter ${widget.column.displayName}'),
      content: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          DropdownButtonFormField<String>(
            value: _operator,
            decoration: const InputDecoration(labelText: 'Operator'),
            items: _getOperatorOptions().map((op) {
              return DropdownMenuItem(
                value: op['value']!,
                child: Text(op['label']!),
              );
            }).toList(),
            onChanged: (value) {
              setState(() {
                _operator = value!;
              });
            },
          ),
          const SizedBox(height: 16),
          TextField(
            controller: _valueController,
            decoration: const InputDecoration(labelText: 'Value'),
            keyboardType: _getKeyboardType(),
          ),
        ],
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: const Text('Cancel'),
        ),
        TextButton(
          onPressed: () {
            final filter = GridFilter(
              columnName: widget.column.name,
              operator: _operator,
              value: _convertFilterValue(_valueController.text),
            );
            widget.onApply(filter);
            Navigator.of(context).pop();
          },
          child: const Text('Apply'),
        ),
      ],
    );
  }
  
  List<Map<String, String>> _getOperatorOptions() {
    switch (widget.column.type) {
      case DataType.number:
      case DataType.currency:
      case DataType.date:
        return [
          {'value': 'equals', 'label': 'Equals'},
          {'value': 'greaterThan', 'label': 'Greater than'},
          {'value': 'lessThan', 'label': 'Less than'},
        ];
      default:
        return [
          {'value': 'contains', 'label': 'Contains'},
          {'value': 'equals', 'label': 'Equals'},
          {'value': 'startsWith', 'label': 'Starts with'},
          {'value': 'endsWith', 'label': 'Ends with'},
        ];
    }
  }
  
  TextInputType _getKeyboardType() {
    switch (widget.column.type) {
      case DataType.number:
      case DataType.currency:
        return TextInputType.number;
      case DataType.date:
        return TextInputType.datetime;
      default:
        return TextInputType.text;
    }
  }
  
  dynamic _convertFilterValue(String value) {
    switch (widget.column.type) {
      case DataType.number:
      case DataType.currency:
        return double.tryParse(value) ?? value;
      case DataType.boolean:
        return ['true', 'yes', '1'].contains(value.toLowerCase()) ? 1 : 0;
      default:
        return value;
    }
  }
}