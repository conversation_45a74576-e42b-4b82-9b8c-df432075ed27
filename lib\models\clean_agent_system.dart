import 'package:json_annotation/json_annotation.dart';
import 'estimator_types.dart';

part 'clean_agent_system.g.dart';

@JsonSerializable()
class CleanAgentSystem {
  final String id;
  final String name;
  final String description;
  final int quantity;
  
  // Room dimensions
  final double roomLength;
  final double roomWidth;
  final double roomHeight;
  final double roomVolume;
  
  // Agent type and concentration
  final AgentType agentType;
  final double designConcentration;
  final double agentRequired;
  final double actualAgent;
  final double? userInputAgentQuantity; // Store user's original kg input
  
  // Cylinder configuration
  final int cylinderQty;
  final int cylinderSize;
  
  // Nozzle configuration
  final int nozzleQty;
  final int nozzleSize;
  
  // Pipe configuration
  final double nozzlePipeLength;
  final double manifoldPipeLength;
  final double nozzlePipeSize;
  final double manifoldPipeSize;
  
  // Cable configuration
  final double loopCableLength;
  final double powerCableLength;
  
  // Cost breakdown
  final double suppressionCost;
  final double alarmCost;
  final double installationMaterialsCost;
  final double installationLaborCost;
  final double totalCost;

  // BOM Data - saved from first calculation
  final List<Map<String, dynamic>> bomItems;
  final Map<String, dynamic> bomSummaryData;

  // User calculation options - to remember when editing/duplicating
  final InputMode inputMode;
  final SystemType systemType;
  final InstallationType installationType;
  final double installationFactor;

  // Timestamps
  final DateTime createdAt;
  final DateTime updatedAt;

  CleanAgentSystem({
    required this.id,
    required this.name,
    required this.description,
    this.quantity = 1,
    required this.roomLength,
    required this.roomWidth,
    required this.roomHeight,
    required this.roomVolume,
    required this.agentType,
    required this.designConcentration,
    required this.agentRequired,
    required this.actualAgent,
    this.userInputAgentQuantity,
    required this.cylinderQty,
    required this.cylinderSize,
    required this.nozzleQty,
    required this.nozzleSize,
    required this.nozzlePipeLength,
    required this.manifoldPipeLength,
    required this.nozzlePipeSize,
    required this.manifoldPipeSize,
    required this.loopCableLength,
    required this.powerCableLength,
    required this.suppressionCost,
    required this.alarmCost,
    required this.installationMaterialsCost,
    required this.installationLaborCost,
    required this.totalCost,
    this.bomItems = const [],
    this.bomSummaryData = const {},
    this.inputMode = InputMode.dimensions,
    this.systemType = SystemType.main,
    this.installationType = InstallationType.supplyOnly,
    this.installationFactor = 0.15,
    required this.createdAt,
    required this.updatedAt,
  });

  factory CleanAgentSystem.fromJson(Map<String, dynamic> json) =>
      _$CleanAgentSystemFromJson(json);

  Map<String, dynamic> toJson() => _$CleanAgentSystemToJson(this);

  CleanAgentSystem copyWith({
    String? id,
    String? name,
    String? description,
    int? quantity,
    double? roomLength,
    double? roomWidth,
    double? roomHeight,
    double? roomVolume,
    AgentType? agentType,
    double? designConcentration,
    double? agentRequired,
    double? actualAgent,
    double? userInputAgentQuantity,
    int? cylinderQty,
    int? cylinderSize,
    int? nozzleQty,
    int? nozzleSize,
    double? nozzlePipeLength,
    double? manifoldPipeLength,
    double? nozzlePipeSize,
    double? manifoldPipeSize,
    double? loopCableLength,
    double? powerCableLength,
    double? suppressionCost,
    double? alarmCost,
    double? installationMaterialsCost,
    double? installationLaborCost,
    double? totalCost,
    List<Map<String, dynamic>>? bomItems,
    Map<String, dynamic>? bomSummaryData,
    InputMode? inputMode,
    SystemType? systemType,
    InstallationType? installationType,
    double? installationFactor,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return CleanAgentSystem(
      id: id ?? this.id,
      name: name ?? this.name,
      description: description ?? this.description,
      quantity: quantity ?? this.quantity,
      roomLength: roomLength ?? this.roomLength,
      roomWidth: roomWidth ?? this.roomWidth,
      roomHeight: roomHeight ?? this.roomHeight,
      roomVolume: roomVolume ?? this.roomVolume,
      agentType: agentType ?? this.agentType,
      designConcentration: designConcentration ?? this.designConcentration,
      agentRequired: agentRequired ?? this.agentRequired,
      actualAgent: actualAgent ?? this.actualAgent,
      userInputAgentQuantity: userInputAgentQuantity ?? this.userInputAgentQuantity,
      cylinderQty: cylinderQty ?? this.cylinderQty,
      cylinderSize: cylinderSize ?? this.cylinderSize,
      nozzleQty: nozzleQty ?? this.nozzleQty,
      nozzleSize: nozzleSize ?? this.nozzleSize,
      nozzlePipeLength: nozzlePipeLength ?? this.nozzlePipeLength,
      manifoldPipeLength: manifoldPipeLength ?? this.manifoldPipeLength,
      nozzlePipeSize: nozzlePipeSize ?? this.nozzlePipeSize,
      manifoldPipeSize: manifoldPipeSize ?? this.manifoldPipeSize,
      loopCableLength: loopCableLength ?? this.loopCableLength,
      powerCableLength: powerCableLength ?? this.powerCableLength,
      suppressionCost: suppressionCost ?? this.suppressionCost,
      alarmCost: alarmCost ?? this.alarmCost,
      installationMaterialsCost: installationMaterialsCost ?? this.installationMaterialsCost,
      installationLaborCost: installationLaborCost ?? this.installationLaborCost,
      totalCost: totalCost ?? this.totalCost,
      bomItems: bomItems ?? this.bomItems,
      bomSummaryData: bomSummaryData ?? this.bomSummaryData,
      inputMode: inputMode ?? this.inputMode,
      systemType: systemType ?? this.systemType,
      installationType: installationType ?? this.installationType,
      installationFactor: installationFactor ?? this.installationFactor,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? DateTime.now(),
    );
  }

  // Create from calculation results
  factory CleanAgentSystem.fromCalculation({
    required String name,
    required String description,
    required EstimatorFormValues input,
    required DesignResults results,
    required BomSummary summary,
    List<Map<String, dynamic>>? bomItems,
    Map<String, dynamic>? bomSummaryData,
    InputMode? inputMode,
    SystemType? systemType,
    InstallationType? installationType,
    double? installationFactor,
    double? userInputAgentQuantity,
  }) {
    // Parse design concentration from string (e.g., "7.4%" -> 0.074)
    final concentrationValue = double.parse(input.designConcentration.replaceAll('%', '')) / 100;

    return CleanAgentSystem(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      name: name,
      description: description,
      roomLength: input.roomLength ?? 0.0,
      roomWidth: input.roomWidth ?? 0.0,
      roomHeight: input.roomHeight ?? 0.0,
      roomVolume: results.roomData.roomVolume,
      agentType: input.agentType,
      designConcentration: concentrationValue,
      agentRequired: results.totalAgentRequired,
      actualAgent: results.cylinder.actualTotalKg,
      userInputAgentQuantity: userInputAgentQuantity,
      cylinderQty: results.cylinder.numCylinders2ndIter,
      cylinderSize: results.cylinder.cylinderSizeLiters2ndIter.toInt(),
      nozzleQty: results.discharge.nozzleQtyFinal,
      nozzleSize: results.discharge.nozzleSizeFinal,
      nozzlePipeLength: 0, // Will be calculated from pipe lengths
      manifoldPipeLength: 0, // Will be calculated from pipe lengths
      nozzlePipeSize: 0, // Will be determined from flow calculations
      manifoldPipeSize: 0, // Will be determined from flow calculations
      loopCableLength: 0, // Will be calculated from cable lengths
      powerCableLength: 0, // Will be calculated from cable lengths
      suppressionCost: summary.suppressionCost,
      alarmCost: summary.alarmCost,
      installationMaterialsCost: summary.installationMaterialsSAR,
      installationLaborCost: summary.installationLaborSAR,
      totalCost: summary.grandTotalSAR,
      bomItems: bomItems ?? [],
      bomSummaryData: bomSummaryData ?? {},
      inputMode: inputMode ?? InputMode.dimensions,
      systemType: systemType ?? SystemType.main,
      installationType: installationType ?? InstallationType.supplyOnly,
      installationFactor: installationFactor ?? 0.15,
      createdAt: DateTime.now(),
      updatedAt: DateTime.now(),
    );
  }

  // Generate a summary description
  String get summaryDescription {
    return '${agentType.name.toUpperCase()} system for $roomLength×$roomWidth×${roomHeight}m room. '
           '$cylinderQty × ${cylinderSize}L cylinders, $nozzleQty nozzles. '
           'Total cost: ${totalCost.toStringAsFixed(2)} SAR';
  }

  // Get system type display name
  String get systemTypeDisplay {
    switch (agentType) {
      case AgentType.fm200:
        return 'FM-200';
      case AgentType.novec1230:
        return 'NOVEC 1230';
    }
  }
}
