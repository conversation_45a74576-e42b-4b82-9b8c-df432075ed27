import 'package:flutter/material.dart';
import 'package:uuid/uuid.dart';
import '../services/company_service.dart';

class CompanySettingsScreen extends StatefulWidget {
  const CompanySettingsScreen({super.key});

  @override
  State<CompanySettingsScreen> createState() => _CompanySettingsScreenState();
}

class _CompanySettingsScreenState extends State<CompanySettingsScreen> {
  final CompanyService _companyService = CompanyService();
  final _uuid = const Uuid();
  
  List<Company> _companies = [];
  Company? _currentCompany;
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _loadCompanies();
  }

  Future<void> _loadCompanies() async {
    setState(() => _isLoading = true);
    try {
      _companies = await _companyService.getCompanies();
      _currentCompany = await _companyService.getCurrentCompany();
    } catch (e) {
      _showError('Error loading companies: $e');
    } finally {
      setState(() => _isLoading = false);
    }
  }

  void _showError(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text(message), backgroundColor: Colors.red),
    );
  }

  void _showSuccess(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text(message), backgroundColor: Colors.green),
    );
  }

  Future<void> _addCompany() async {
    final result = await showDialog<Map<String, String>>(
      context: context,
      builder: (context) => const _CompanyDialog(),
    );

    if (result != null) {
      try {
        final company = Company(
          id: _uuid.v4(),
          name: result['name']!,
          supabaseUrl: result['url']!,
          supabaseAnonKey: result['key']!,
          createdAt: DateTime.now(),
        );

        await _companyService.addCompany(company);
        await _loadCompanies();
        _showSuccess('Company added successfully');
      } catch (e) {
        _showError('Error adding company: $e');
      }
    }
  }

  Future<void> _editCompany(Company company) async {
    final result = await showDialog<Map<String, String>>(
      context: context,
      builder: (context) => _CompanyDialog(company: company),
    );

    if (result != null) {
      try {
        final updatedCompany = Company(
          id: company.id,
          name: result['name']!,
          supabaseUrl: result['url']!,
          supabaseAnonKey: result['key']!,
          createdAt: company.createdAt,
        );

        await _companyService.updateCompany(updatedCompany);
        await _loadCompanies();
        _showSuccess('Company updated successfully');
      } catch (e) {
        _showError('Error updating company: $e');
      }
    }
  }

  Future<void> _deleteCompany(Company company) async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete Company'),
        content: Text('Are you sure you want to delete "${company.name}"?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.of(context).pop(true),
            style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
            child: const Text('Delete'),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      try {
        await _companyService.deleteCompany(company.id);
        await _loadCompanies();
        _showSuccess('Company deleted successfully');
      } catch (e) {
        _showError('Error deleting company: $e');
      }
    }
  }

  Future<void> _setCurrentCompany(Company company) async {
    try {
      await _companyService.setCurrentCompany(company.id);
      await _loadCompanies();
      _showSuccess('Switched to ${company.name}');
    } catch (e) {
      _showError('Error switching company: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Company Settings'),
        backgroundColor: Colors.blue,
        foregroundColor: Colors.white,
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : Column(
              children: [
                // Current company info
                if (_currentCompany != null)
                  Container(
                    width: double.infinity,
                    margin: const EdgeInsets.all(16),
                    padding: const EdgeInsets.all(16),
                    decoration: BoxDecoration(
                      color: Colors.blue.shade50,
                      borderRadius: BorderRadius.circular(8),
                      border: Border.all(color: Colors.blue.shade200),
                    ),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        const Text(
                          'Current Company',
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                            color: Colors.blue,
                          ),
                        ),
                        const SizedBox(height: 8),
                        Text(
                          _currentCompany!.name,
                          style: const TextStyle(fontSize: 18),
                        ),
                        Text(
                          'Connected to: ${_currentCompany!.supabaseUrl}',
                          style: TextStyle(
                            fontSize: 12,
                            color: Colors.grey.shade600,
                          ),
                        ),
                      ],
                    ),
                  ),

                // Companies list
                Expanded(
                  child: _companies.isEmpty
                      ? const Center(
                          child: Text(
                            'No companies configured.\nAdd a company to get started.',
                            textAlign: TextAlign.center,
                            style: TextStyle(fontSize: 16),
                          ),
                        )
                      : ListView.builder(
                          padding: const EdgeInsets.all(16),
                          itemCount: _companies.length,
                          itemBuilder: (context, index) {
                            final company = _companies[index];
                            final isCurrent = _currentCompany?.id == company.id;

                            return Card(
                              margin: const EdgeInsets.only(bottom: 8),
                              child: ListTile(
                                leading: CircleAvatar(
                                  backgroundColor: isCurrent ? Colors.green : Colors.grey,
                                  child: Icon(
                                    isCurrent ? Icons.check : Icons.business,
                                    color: Colors.white,
                                  ),
                                ),
                                title: Text(company.name),
                                subtitle: Text(company.supabaseUrl),
                                trailing: PopupMenuButton<String>(
                                  onSelected: (value) {
                                    switch (value) {
                                      case 'select':
                                        _setCurrentCompany(company);
                                        break;
                                      case 'edit':
                                        _editCompany(company);
                                        break;
                                      case 'delete':
                                        _deleteCompany(company);
                                        break;
                                    }
                                  },
                                  itemBuilder: (context) => [
                                    if (!isCurrent)
                                      const PopupMenuItem(
                                        value: 'select',
                                        child: ListTile(
                                          leading: Icon(Icons.check_circle),
                                          title: Text('Select'),
                                          dense: true,
                                        ),
                                      ),
                                    const PopupMenuItem(
                                      value: 'edit',
                                      child: ListTile(
                                        leading: Icon(Icons.edit),
                                        title: Text('Edit'),
                                        dense: true,
                                      ),
                                    ),
                                    const PopupMenuItem(
                                      value: 'delete',
                                      child: ListTile(
                                        leading: Icon(Icons.delete),
                                        title: Text('Delete'),
                                        dense: true,
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            );
                          },
                        ),
                ),
              ],
            ),
      floatingActionButton: FloatingActionButton(
        onPressed: _addCompany,
        backgroundColor: Colors.blue,
        child: const Icon(Icons.add, color: Colors.white),
      ),
    );
  }
}

class _CompanyDialog extends StatefulWidget {
  final Company? company;

  const _CompanyDialog({this.company});

  @override
  State<_CompanyDialog> createState() => _CompanyDialogState();
}

class _CompanyDialogState extends State<_CompanyDialog> {
  final _formKey = GlobalKey<FormState>();
  late final TextEditingController _nameController;
  late final TextEditingController _urlController;
  late final TextEditingController _keyController;

  @override
  void initState() {
    super.initState();
    _nameController = TextEditingController(text: widget.company?.name ?? '');
    _urlController = TextEditingController(text: widget.company?.supabaseUrl ?? '');
    _keyController = TextEditingController(text: widget.company?.supabaseAnonKey ?? '');
  }

  @override
  void dispose() {
    _nameController.dispose();
    _urlController.dispose();
    _keyController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: Text(widget.company == null ? 'Add Company' : 'Edit Company'),
      content: Form(
        key: _formKey,
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            TextFormField(
              controller: _nameController,
              decoration: const InputDecoration(
                labelText: 'Company Name',
                hintText: 'e.g., SICLI Co.',
              ),
              validator: (value) {
                if (value == null || value.trim().isEmpty) {
                  return 'Please enter a company name';
                }
                return null;
              },
            ),
            const SizedBox(height: 16),
            TextFormField(
              controller: _urlController,
              decoration: const InputDecoration(
                labelText: 'Supabase URL',
                hintText: 'https://your-project.supabase.co',
              ),
              validator: (value) {
                if (value == null || value.trim().isEmpty) {
                  return 'Please enter Supabase URL';
                }
                if (!value.startsWith('https://')) {
                  return 'URL must start with https://';
                }
                return null;
              },
            ),
            const SizedBox(height: 16),
            TextFormField(
              controller: _keyController,
              decoration: const InputDecoration(
                labelText: 'Supabase Anon Key',
                hintText: 'Your public anon key',
              ),
              validator: (value) {
                if (value == null || value.trim().isEmpty) {
                  return 'Please enter Supabase anon key';
                }
                return null;
              },
            ),
          ],
        ),
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: const Text('Cancel'),
        ),
        ElevatedButton(
          onPressed: () {
            if (_formKey.currentState!.validate()) {
              Navigator.of(context).pop({
                'name': _nameController.text.trim(),
                'url': _urlController.text.trim(),
                'key': _keyController.text.trim(),
              });
            }
          },
          child: Text(widget.company == null ? 'Add' : 'Update'),
        ),
      ],
    );
  }
}
