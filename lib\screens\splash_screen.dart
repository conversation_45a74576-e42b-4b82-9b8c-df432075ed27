import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../services/auth_service.dart';
import '../services/project_provider.dart';
import '../constants/app_constants.dart';

class SplashScreen extends StatefulWidget {
  const SplashScreen({super.key});

  @override
  State<SplashScreen> createState() => _SplashScreenState();
}

class _SplashScreenState extends State<SplashScreen> {
  @override
  void initState() {
    super.initState();
    _initialize();
  }

  Future<void> _initialize() async {
    final authService = Provider.of<AuthService>(context, listen: false);
    final projectProvider = Provider.of<ProjectProvider>(context, listen: false);

    // Simulate a delay for splash screen
    await Future.delayed(const Duration(seconds: 2));

    // Auto-login as admin
    await authService.login('<EMAIL>', 'admin123');

    // Initialize ProjectProvider to load projects from database
    await projectProvider.initialize();

    if (!mounted) return;

    // Always navigate to home screen
    Navigator.of(context).pushReplacementNamed('/home');
  }

  @override
  Widget build(BuildContext context) {
    return const Scaffold(
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            // Logo
            Icon(
              Icons.local_fire_department,
              size: 100,
              color: AppConstants.primaryColor,
            ),
            SizedBox(height: 24),

            // App name
            Text(
              'FireTool Estimator',
              style: TextStyle(
                fontSize: 28,
                fontWeight: FontWeight.bold,
              ),
            ),
            SizedBox(height: 8),

            // Tagline
            Text(
              'Create accurate estimates for fire systems',
              style: TextStyle(
                fontSize: 16,
                color: Colors.grey,
              ),
            ),
            SizedBox(height: 48),

            // Loading indicator
            CircularProgressIndicator(),
          ],
        ),
      ),
    );
  }
}
