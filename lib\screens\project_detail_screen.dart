import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:intl/intl.dart';
import '../constants/app_constants.dart';
import '../models/project.dart';
import '../services/project_provider.dart';
import '../services/export_service.dart';
import 'system_detail_screen.dart';
import 'add_system_screen.dart';
import 'clean_agent_systems_screen.dart';

class ProjectDetailScreen extends StatefulWidget {
  const ProjectDetailScreen({super.key});

  @override
  State<ProjectDetailScreen> createState() => _ProjectDetailScreenState();
}

class _ProjectDetailScreenState extends State<ProjectDetailScreen> with SingleTickerProviderStateMixin {
  late TabController _tabController;
  final ExportService _exportService = ExportService();

  // Get currency formatter with the correct symbol
  NumberFormat getCurrencyFormatter(Project project) {
    return NumberFormat.currency(symbol: project.currency);
  }

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<ProjectProvider>(
      builder: (context, projectProvider, child) {
        final project = projectProvider.currentProject;

        if (project == null) {
          return Scaffold(
            appBar: AppBar(title: const Text('Project Details')),
            body: const Center(child: Text('No project loaded')),
          );
        }

        return Scaffold(
          appBar: AppBar(
            title: Text(project.name),
            actions: [
              IconButton(
                icon: const Icon(Icons.save),
                tooltip: 'Save Project',
                onPressed: () async {
                  await projectProvider.saveCurrentProject();
                  if (!mounted) return;

                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(content: Text('Project saved')),
                  );
                },
              ),
              PopupMenuButton<String>(
                onSelected: (value) async {
                  switch (value) {
                    case 'export_pdf':
                      try {
                        final file = await _exportService.exportToPdf(project);
                        if (!mounted) return;

                        ScaffoldMessenger.of(context).showSnackBar(
                          SnackBar(content: Text('PDF exported to ${file.path}')),
                        );
                      } catch (e) {
                        if (!mounted) return;

                        ScaffoldMessenger.of(context).showSnackBar(
                          SnackBar(content: Text('Error exporting PDF: $e')),
                        );
                      }
                      break;
                    case 'export_excel':
                      try {
                        final file = await _exportService.exportToExcel(project);
                        if (!mounted) return;

                        ScaffoldMessenger.of(context).showSnackBar(
                          SnackBar(content: Text('Excel exported to ${file.path}')),
                        );
                      } catch (e) {
                        if (!mounted) return;

                        ScaffoldMessenger.of(context).showSnackBar(
                          SnackBar(content: Text('Error exporting Excel: $e')),
                        );
                      }
                      break;
                    case 'print':
                      try {
                        await _exportService.printPdf(project);
                      } catch (e) {
                        if (!mounted) return;

                        ScaffoldMessenger.of(context).showSnackBar(
                          SnackBar(content: Text('Error printing: $e')),
                        );
                      }
                      break;
                  }
                },
                itemBuilder: (BuildContext context) => <PopupMenuEntry<String>>[
                  const PopupMenuItem<String>(
                    value: 'export_pdf',
                    child: ListTile(
                      leading: Icon(Icons.picture_as_pdf),
                      title: Text('Export to PDF'),
                    ),
                  ),
                  const PopupMenuItem<String>(
                    value: 'export_excel',
                    child: ListTile(
                      leading: Icon(Icons.table_chart),
                      title: Text('Export to Excel'),
                    ),
                  ),
                  const PopupMenuItem<String>(
                    value: 'print',
                    child: ListTile(
                      leading: Icon(Icons.print),
                      title: Text('Print'),
                    ),
                  ),
                ],
              ),
            ],
            bottom: TabBar(
              controller: _tabController,
              indicatorWeight: 3,
              indicatorSize: TabBarIndicatorSize.tab,
              labelStyle: const TextStyle(
                fontWeight: FontWeight.bold,
                fontSize: 16,
              ),
              unselectedLabelStyle: const TextStyle(
                fontWeight: FontWeight.normal,
                fontSize: 16,
              ),
              tabs: const [
                Tab(
                  icon: Icon(Icons.info_outline),
                  text: 'Overview',
                ),
                Tab(
                  icon: Icon(Icons.build),
                  text: 'Systems',
                ),
                Tab(
                  icon: Icon(Icons.summarize),
                  text: 'Summary',
                ),
              ],
            ),
          ),
          body: TabBarView(
            controller: _tabController,
            children: [
              _buildOverviewTab(project),
              _buildSystemsTab(project, projectProvider),
              _buildSummaryTab(project),
            ],
          ),
          floatingActionButton: _tabController.index == 1
              ? FloatingActionButton(
                  onPressed: () {
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => const AddSystemScreen(),
                      ),
                    );
                  },
                  child: const Icon(Icons.add),
                )
              : null,
        );
      },
    );
  }

  Widget _buildOverviewTab(Project project) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Card(
            elevation: 2,
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      const Text(
                        'Project Information',
                        style: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      IconButton(
                        icon: const Icon(Icons.edit),
                        onPressed: () => _showEditProjectDialog(project),
                        tooltip: 'Edit Project Information',
                      ),
                    ],
                  ),
                  const Divider(),
                  _buildInfoRow('Project Name', project.name),
                  if (project.clientName.isNotEmpty) _buildInfoRow('Client', project.clientName),
                  if (project.projectReference.isNotEmpty) _buildInfoRow('Project Reference', project.projectReference),
                  _buildInfoRow('Created', DateFormat('MM/dd/yyyy').format(project.createdAt)),
                  _buildInfoRow('Last Updated', DateFormat('MM/dd/yyyy').format(project.updatedAt)),
                ],
              ),
            ),
          ),
          const SizedBox(height: 16),
          Card(
            elevation: 2,
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    'Project Summary',
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const Divider(),
                  _buildInfoRow('Total Systems', project.systems.length.toString()),
                  _buildInfoRow('Project Type', (project.includeInstallation ?? true) ? 'Supply & Install' : 'Supply Only'),
                  _buildInfoRow('Currency', project.currency),
                  _buildInfoRow('Exchange Rate (USD to ${project.currency})', project.exchangeRate.toString()),
                  _buildInfoRow('Ex-Works Cost (USD)', '\$${NumberFormat("#,##0.00").format(project.totalExWorksCost)}'),
                  _buildInfoRow('Local Cost (${project.currency})', '${project.currency} ${NumberFormat("#,##0.00").format(project.totalLocalCost)}'),
                  _buildInfoRow('Installation Cost (${project.currency})', '${project.currency} ${NumberFormat("#,##0.00").format(project.totalInstallationCost)}'),
                  _buildInfoRow('Total Cost (${project.currency})', '${project.currency} ${NumberFormat("#,##0.00").format(project.totalCost)}'),
                  const SizedBox(height: 8),
                  const Text(
                    'Systems:',
                    style: TextStyle(fontWeight: FontWeight.bold),
                  ),
                  const SizedBox(height: 4),
                  ...project.systems.map((system) => Padding(
                    padding: const EdgeInsets.only(left: 16.0, bottom: 4.0),
                    child: Text('• ${system.name} (${project.currency} ${NumberFormat("#,##0.00").format(system.totalCost)})'),
                  )),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSystemsTab(Project project, ProjectProvider projectProvider) {
    final cleanAgentSystems = project.cleanAgentSystems ?? [];
    final hasAnySystems = project.systems.isNotEmpty || cleanAgentSystems.isNotEmpty;

    if (!hasAnySystems) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(
              Icons.build,
              size: 80,
              color: Colors.grey,
            ),
            const SizedBox(height: 16),
            const Text(
              'No systems added yet',
              style: TextStyle(fontSize: 18, color: Colors.grey),
            ),
            const SizedBox(height: 24),
            ElevatedButton.icon(
              icon: const Icon(Icons.add),
              label: const Text('Add System'),
              onPressed: () {
                Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) => const AddSystemScreen(),
                  ),
                );
              },
            ),
          ],
        ),
      );
    }

    // Filter out empty Clean Agent systems from regular systems and handle clean agent consolidation
    final filteredSystems = project.systems.where((system) =>
      !(system.type.toLowerCase().contains('clean agent') && system.totalCost == 0.0)
    ).toList();

    final hasCleanAgentSystems = cleanAgentSystems.isNotEmpty;
    final allSystemsCount = filteredSystems.length + (hasCleanAgentSystems ? 1 : 0);

    return ListView.builder(
      itemCount: allSystemsCount,
      itemBuilder: (context, index) {
        // Show regular systems first
        if (index < filteredSystems.length) {
          final system = filteredSystems[index];
          return Card(
            margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
            child: InkWell(
              onTap: () {
                Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) => SystemDetailScreen(systemId: system.id),
                  ),
                );
              },
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Expanded(
                          child: Text(
                            system.name,
                            style: const TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                        Column(
                          crossAxisAlignment: CrossAxisAlignment.end,
                          children: [
                            Text(
                              'Ex-Works: \$${NumberFormat("#,##0.00").format(system.totalExWorksCost)}',
                              style: const TextStyle(
                                fontSize: 12,
                                color: AppConstants.secondaryTextColor,
                              ),
                            ),
                            Text(
                              'Local: ${project.currency} ${NumberFormat("#,##0.00").format(system.totalLocalCost)}',
                              style: const TextStyle(
                                fontSize: 12,
                                color: AppConstants.secondaryTextColor,
                              ),
                            ),
                            Text(
                              'Total: ${project.currency} ${NumberFormat("#,##0.00").format(system.totalCost)}',
                              style: const TextStyle(
                                fontSize: 14,
                                fontWeight: FontWeight.bold,
                                color: AppConstants.primaryColor,
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                    const SizedBox(height: 8),
                    Text('Type: ${system.type}'),
                    const SizedBox(height: 8),
                    Row(
                      children: [
                        _buildCostChip('Ex-Works', system.totalExWorksCost, prefix: '\$'),
                        const SizedBox(width: 8),
                        _buildCostChip('Local', system.totalLocalCost, prefix: project.currency),
                        const SizedBox(width: 8),
                        _buildCostChip('Install', system.totalInstallationCost, prefix: project.currency),
                      ],
                    ),
                    const SizedBox(height: 8),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.end,
                      children: [
                        TextButton.icon(
                          icon: const Icon(Icons.edit, size: 16),
                          label: const Text('Edit'),
                          onPressed: () {
                            Navigator.push(
                              context,
                              MaterialPageRoute(
                                builder: (context) => SystemDetailScreen(systemId: system.id),
                              ),
                            );
                          },
                        ),
                        const SizedBox(width: 8),
                        TextButton.icon(
                          icon: const Icon(Icons.delete, size: 16, color: Colors.red),
                          label: const Text('Delete', style: TextStyle(color: Colors.red)),
                          onPressed: () {
                            _showDeleteSystemConfirmation(context, system, projectProvider);
                          },
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),
          );
        } else {
          // Show consolidated clean agent systems entry
          final totalQuantity = cleanAgentSystems.fold<int>(0, (sum, system) => sum + system.quantity);
          final totalCost = cleanAgentSystems.fold<double>(0, (sum, system) => sum + (system.totalCost * system.quantity));
          final exWorksCost = cleanAgentSystems.fold<double>(0, (sum, system) => sum + ((system.suppressionCost + system.alarmCost) * system.quantity));
          final localCost = cleanAgentSystems.fold<double>(0, (sum, system) => sum + (system.installationMaterialsCost * system.quantity));
          final installCost = cleanAgentSystems.fold<double>(0, (sum, system) => sum + (system.installationLaborCost * system.quantity));

          return Card(
            margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
            child: InkWell(
              onTap: () {
                Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) => const CleanAgentSystemsScreen(),
                  ),
                );
              },
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      crossAxisAlignment: CrossAxisAlignment.center,
                      children: [
                        Expanded(
                          child: Row(
                            children: [
                              const Text(
                                'Clean Agent',
                                style: TextStyle(
                                  fontSize: 18,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                              const SizedBox(width: 8),
                              Container(
                                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                                decoration: BoxDecoration(
                                  color: Colors.orange.shade100,
                                  borderRadius: BorderRadius.circular(12),
                                ),
                                child: Text(
                                  'Clean Agent',
                                  style: TextStyle(
                                    fontSize: 10,
                                    color: Colors.orange.shade700,
                                    fontWeight: FontWeight.w500,
                                  ),
                                ),
                              ),
                              if (totalQuantity > 1)
                                Container(
                                  margin: const EdgeInsets.only(left: 8),
                                  padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                                  decoration: BoxDecoration(
                                    color: Colors.grey.shade200,
                                    borderRadius: BorderRadius.circular(10),
                                  ),
                                  child: Text(
                                    '$totalQuantity systems',
                                    style: const TextStyle(
                                      fontSize: 10,
                                      fontWeight: FontWeight.w500,
                                    ),
                                  ),
                                ),
                            ],
                          ),
                        ),
                        Text(
                          'Total: ${project.currency} ${NumberFormat("#,##0").format(totalCost.round())}',
                          style: TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                            color: Colors.orange.shade700,
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 8),
                    const Text('Type: Clean Agent'),
                    const SizedBox(height: 8),
                    Row(
                      children: [
                        _buildCostChip('Ex-Works', exWorksCost, prefix: '\$'),
                        const SizedBox(width: 8),
                        _buildCostChip('Local', localCost, prefix: project.currency),
                        const SizedBox(width: 8),
                        _buildCostChip('Install', installCost, prefix: project.currency),
                      ],
                    ),
                    const SizedBox(height: 8),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.end,
                      children: [
                        TextButton.icon(
                          icon: const Icon(Icons.edit, size: 16),
                          label: const Text('Edit'),
                          onPressed: () {
                            Navigator.push(
                              context,
                              MaterialPageRoute(
                                builder: (context) => const CleanAgentSystemsScreen(),
                              ),
                            );
                          },
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),
          );
        }
      },
    );
  }

  Widget _buildSummaryTab(Project project) {
    // Ex-Works costs (USD) - from regular systems
    final materialsExWorksCost = project.systems.fold(0.0, (sum, system) => sum + system.materialsExWorksCost);
    final equipmentExWorksCost = project.systems.fold(0.0, (sum, system) => sum + system.equipmentExWorksCost);

    // Clean Agent Ex-Works costs (USD)
    final cleanAgentSystems = project.cleanAgentSystems ?? [];
    final cleanAgentExWorksCost = cleanAgentSystems.fold(0.0, (sum, system) =>
        sum + ((system.suppressionCost + system.alarmCost) * system.quantity));

    final totalExWorksCost = project.totalExWorksCost + cleanAgentExWorksCost;

    // Local costs (local currency) - from regular systems
    final materialsLocalCost = project.systems.fold(0.0, (sum, system) => sum + system.materialsLocalCost);
    final equipmentLocalCost = project.systems.fold(0.0, (sum, system) => sum + system.equipmentLocalCost);

    // Clean Agent Local costs (SAR) - only installation materials (cables/pipes)
    final cleanAgentLocalCost = cleanAgentSystems.fold(0.0, (sum, system) =>
        sum + (system.installationMaterialsCost * system.quantity));

    final totalLocalCost = project.totalLocalCost + cleanAgentLocalCost;

    // Installation costs (local currency) - from regular systems
    final laborCost = project.systems.fold(0.0, (sum, system) => sum + system.laborCost);

    // Clean Agent Installation costs (SAR) - labor costs
    final cleanAgentInstallationCost = cleanAgentSystems.fold(0.0, (sum, system) =>
        sum + (system.installationLaborCost * system.quantity));

    final totalInstallationCost = project.totalInstallationCost + cleanAgentInstallationCost;

    // Total costs
    final totalCost = project.totalCost + cleanAgentSystems.fold(0.0, (sum, system) =>
        sum + (system.totalCost * system.quantity));

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Card(
            elevation: 2,
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    'Cost Summary',
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const Divider(),
                  const Text(
                    'Ex-Works Costs (USD)',
                    style: TextStyle(fontWeight: FontWeight.bold),
                  ),
                  _buildInfoRow('Materials', '\$${NumberFormat("#,##0.00").format(materialsExWorksCost)}'),
                  _buildInfoRow('Equipment', '\$${NumberFormat("#,##0.00").format(equipmentExWorksCost)}'),
                  if (cleanAgentExWorksCost > 0)
                    _buildInfoRow('Clean Agent', '\$${NumberFormat("#,##0.00").format(cleanAgentExWorksCost)}'),
                  _buildInfoRow('Subtotal', '\$${NumberFormat("#,##0.00").format(totalExWorksCost)}'),
                  const Divider(),
                  Text(
                    'Local Costs (${project.currency})',
                    style: const TextStyle(fontWeight: FontWeight.bold),
                  ),
                  _buildInfoRow('Materials', '${project.currency} ${NumberFormat("#,##0.00").format(materialsLocalCost)}'),
                  _buildInfoRow('Equipment', '${project.currency} ${NumberFormat("#,##0.00").format(equipmentLocalCost)}'),
                  if (cleanAgentLocalCost > 0)
                    _buildInfoRow('Clean Agent', '${project.currency} ${NumberFormat("#,##0.00").format(cleanAgentLocalCost)}'),
                  _buildInfoRow('Subtotal', '${project.currency} ${NumberFormat("#,##0.00").format(totalLocalCost)}'),
                  const Divider(),
                  Text(
                    'Installation Costs (${project.currency})',
                    style: const TextStyle(fontWeight: FontWeight.bold),
                  ),
                  _buildInfoRow('Labor', '${project.currency} ${NumberFormat("#,##0.00").format(laborCost)}'),
                  if (cleanAgentInstallationCost > 0)
                    _buildInfoRow('Clean Agent', '${project.currency} ${NumberFormat("#,##0.00").format(cleanAgentInstallationCost)}'),
                  _buildInfoRow('Subtotal', '${project.currency} ${NumberFormat("#,##0.00").format(totalInstallationCost)}'),
                  const Divider(),
                  _buildInfoRow('Total Cost', '${project.currency} ${NumberFormat("#,##0.00").format(totalCost)}', isBold: true),
                ],
              ),
            ),
          ),
          const SizedBox(height: 16),
          const Text(
            'Cost Breakdown by System',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 8),
          ...project.systems.map((system) => Card(
            elevation: 2,
            margin: const EdgeInsets.only(bottom: 8),
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    system.name,
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const Divider(),
                  const Text(
                    'Ex-Works Costs (USD)',
                    style: TextStyle(fontWeight: FontWeight.bold, fontSize: 14),
                  ),
                  _buildInfoRow('Materials', '\$${NumberFormat("#,##0.00").format(system.materialsExWorksCost)}'),
                  _buildInfoRow('Equipment', '\$${NumberFormat("#,##0.00").format(system.equipmentExWorksCost)}'),
                  _buildInfoRow('Subtotal', '\$${NumberFormat("#,##0.00").format(system.totalExWorksCost)}'),
                  const Divider(),
                  Text(
                    'Local Costs (${project.currency})',
                    style: const TextStyle(fontWeight: FontWeight.bold, fontSize: 14),
                  ),
                  _buildInfoRow('Materials', '${project.currency} ${NumberFormat("#,##0.00").format(system.materialsLocalCost)}'),
                  _buildInfoRow('Equipment', '${project.currency} ${NumberFormat("#,##0.00").format(system.equipmentLocalCost)}'),
                  _buildInfoRow('Subtotal', '${project.currency} ${NumberFormat("#,##0.00").format(system.totalLocalCost)}'),
                  const Divider(),
                  Text(
                    'Installation Costs (${project.currency})',
                    style: const TextStyle(fontWeight: FontWeight.bold, fontSize: 14),
                  ),
                  _buildInfoRow('Labor', '${project.currency} ${NumberFormat("#,##0.00").format(system.laborCost)}'),
                  _buildInfoRow('Subtotal', '${project.currency} ${NumberFormat("#,##0.00").format(system.totalInstallationCost)}'),
                  const Divider(),
                  _buildInfoRow('System Total', '${project.currency} ${NumberFormat("#,##0.00").format(system.totalCost)}', isBold: true),
                ],
              ),
            ),
          )),
          // Clean Agent Systems
          ...cleanAgentSystems.map((system) => Card(
            elevation: 2,
            margin: const EdgeInsets.only(bottom: 8),
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        system.name,
                        style: const TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      Container(
                        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                        decoration: BoxDecoration(
                          color: Colors.orange.shade100,
                          borderRadius: BorderRadius.circular(12),
                        ),
                        child: Text(
                          'Clean Agent',
                          style: TextStyle(
                            fontSize: 12,
                            color: Colors.orange.shade700,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ),
                    ],
                  ),
                  if (system.quantity > 1)
                    Padding(
                      padding: const EdgeInsets.only(top: 4),
                      child: Text(
                        'Quantity: ${system.quantity}',
                        style: TextStyle(
                          fontSize: 14,
                          color: Colors.grey.shade600,
                        ),
                      ),
                    ),
                  const Divider(),
                  const Text(
                    'Ex-Works Costs (USD)',
                    style: TextStyle(fontWeight: FontWeight.bold, fontSize: 14),
                  ),
                  _buildInfoRow('Suppression', '\$${NumberFormat("#,##0.00").format(system.suppressionCost * system.quantity)}'),
                  _buildInfoRow('Alarm & Detection', '\$${NumberFormat("#,##0.00").format(system.alarmCost * system.quantity)}'),
                  _buildInfoRow('Subtotal', '\$${NumberFormat("#,##0.00").format((system.suppressionCost + system.alarmCost) * system.quantity)}'),
                  const Divider(),
                  Text(
                    'Local Costs (${project.currency})',
                    style: const TextStyle(fontWeight: FontWeight.bold, fontSize: 14),
                  ),
                  _buildInfoRow('Installation Materials', '${project.currency} ${NumberFormat("#,##0.00").format(system.installationMaterialsCost * system.quantity)}'),
                  _buildInfoRow('Subtotal', '${project.currency} ${NumberFormat("#,##0.00").format(system.installationMaterialsCost * system.quantity)}'),
                  const Divider(),
                  Text(
                    'Installation Costs (${project.currency})',
                    style: const TextStyle(fontWeight: FontWeight.bold, fontSize: 14),
                  ),
                  _buildInfoRow('Labor', '${project.currency} ${NumberFormat("#,##0.00").format(system.installationLaborCost * system.quantity)}'),
                  _buildInfoRow('Subtotal', '${project.currency} ${NumberFormat("#,##0.00").format(system.installationLaborCost * system.quantity)}'),
                  const Divider(),
                  _buildInfoRow('System Total', '${project.currency} ${NumberFormat("#,##0.00").format(system.totalCost * system.quantity)}', isBold: true),
                ],
              ),
            ),
          )),
        ],
      ),
    );
  }

  Widget _buildInfoRow(String label, String value, {bool isBold = false}) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4.0),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(label),
          Text(
            value,
            style: TextStyle(
              fontWeight: isBold ? FontWeight.bold : FontWeight.normal,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildCostChip(String label, double cost, {String? prefix}) {
    // Round to nearest whole number (no decimals)
    final formattedCost = NumberFormat("#,##0").format(cost.round());
    final displayText = prefix != null
        ? '$label: $prefix $formattedCost'
        : '$label: $formattedCost';

    return Chip(
      label: Text(
        displayText,
        style: const TextStyle(fontSize: 12),
      ),
      backgroundColor: Colors.grey[200],
    );
  }

  void _showDeleteSystemConfirmation(BuildContext context, SystemEstimate system, ProjectProvider projectProvider) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete System'),
        content: Text('Are you sure you want to delete "${system.name}"? This action cannot be undone.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              projectProvider.removeSystem(system.id);
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(content: Text('System deleted')),
              );
            },
            child: const Text('Delete', style: TextStyle(color: Colors.red)),
          ),
        ],
      ),
    );
  }

  void _showEditProjectDialog(Project project) {
    final nameController = TextEditingController(text: project.name);
    final clientController = TextEditingController(text: project.clientName);
    final referenceController = TextEditingController(text: project.projectReference);
    final exchangeRateController = TextEditingController(text: project.exchangeRate.toString());
    final shippingRateController = TextEditingController(text: project.shippingRate.toString());
    final marginRateController = TextEditingController(text: project.marginRate.toString());

    String selectedCurrency = project.currency;
    bool includeInstallation = project.includeInstallation ?? true;
    final currencies = ['SAR', 'USD', 'GBP', 'EGP', 'AED'];

    showDialog(
      context: context,
      builder: (context) => StatefulBuilder(
        builder: (context, setState) => AlertDialog(
          title: const Text('Edit Project Information'),
          content: SingleChildScrollView(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                TextField(
                  controller: nameController,
                  decoration: const InputDecoration(
                    labelText: 'Project Name',
                    border: OutlineInputBorder(),
                  ),
                ),
                const SizedBox(height: 16),
                TextField(
                  controller: clientController,
                  decoration: const InputDecoration(
                    labelText: 'Client Name',
                    border: OutlineInputBorder(),
                  ),
                ),
                const SizedBox(height: 16),
                TextField(
                  controller: referenceController,
                  decoration: const InputDecoration(
                    labelText: 'Project Reference (Optional)',
                    border: OutlineInputBorder(),
                  ),
                ),
                const SizedBox(height: 16),
                DropdownButtonFormField<String>(
                  value: selectedCurrency,
                  decoration: const InputDecoration(
                    labelText: 'Currency',
                    border: OutlineInputBorder(),
                  ),
                  items: currencies.map((currency) => DropdownMenuItem(
                    value: currency,
                    child: Text(currency),
                  )).toList(),
                  onChanged: (value) {
                    if (value != null) {
                      setState(() {
                        selectedCurrency = value;
                      });
                    }
                  },
                ),
                const SizedBox(height: 16),
                TextField(
                  controller: exchangeRateController,
                  decoration: const InputDecoration(
                    labelText: 'Exchange Rate (USD to Local)',
                    border: OutlineInputBorder(),
                  ),
                  keyboardType: TextInputType.number,
                ),
                const SizedBox(height: 16),
                TextField(
                  controller: shippingRateController,
                  decoration: const InputDecoration(
                    labelText: 'Shipping Factor (e.g., 1.15 for 15%)',
                    border: OutlineInputBorder(),
                  ),
                  keyboardType: TextInputType.number,
                ),
                const SizedBox(height: 16),
                TextField(
                  controller: marginRateController,
                  decoration: const InputDecoration(
                    labelText: 'Margin Factor (e.g., 1.20 for 20%)',
                    border: OutlineInputBorder(),
                  ),
                  keyboardType: TextInputType.number,
                ),
                const SizedBox(height: 16),
                // Installation Type Toggle
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    border: Border.all(color: Colors.grey.shade300),
                    borderRadius: BorderRadius.circular(4),
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Text(
                        'Project Type',
                        style: TextStyle(fontSize: 16, fontWeight: FontWeight.w500),
                      ),
                      const SizedBox(height: 8),
                      StatefulBuilder(
                        builder: (context, setDialogState) {
                          return Column(
                            children: [
                              RadioListTile<bool>(
                                title: const Text('Supply & Install'),
                                subtitle: const Text('Include installation costs and services'),
                                value: true,
                                groupValue: includeInstallation,
                                onChanged: (value) {
                                  setDialogState(() {
                                    includeInstallation = value ?? true;
                                  });
                                },
                              ),
                              RadioListTile<bool>(
                                title: const Text('Supply Only'),
                                subtitle: const Text('Equipment supply without installation'),
                                value: false,
                                groupValue: includeInstallation,
                                onChanged: (value) {
                                  setDialogState(() {
                                    includeInstallation = value ?? false;
                                  });
                                },
                              ),
                            ],
                          );
                        },
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('Cancel'),
            ),
            ElevatedButton(
              onPressed: () {
                _updateProjectInformation(
                  project,
                  nameController.text.trim(),
                  clientController.text.trim(),
                  referenceController.text.trim(),
                  selectedCurrency,
                  double.tryParse(exchangeRateController.text) ?? project.exchangeRate,
                  double.tryParse(shippingRateController.text) ?? project.shippingRate,
                  double.tryParse(marginRateController.text) ?? project.marginRate,
                  includeInstallation,
                );
                Navigator.of(context).pop();
              },
              child: const Text('Save'),
            ),
          ],
        ),
      ),
    );
  }

  void _updateProjectInformation(
    Project project,
    String name,
    String clientName,
    String projectReference,
    String currency,
    double exchangeRate,
    double shippingRate,
    double marginRate,
    bool includeInstallation,
  ) {
    final projectProvider = Provider.of<ProjectProvider>(context, listen: false);

    // Update the project properties
    project.name = name.isNotEmpty ? name : project.name;
    project.clientName = clientName;
    project.projectReference = projectReference;
    project.currency = currency;
    project.exchangeRate = exchangeRate;
    project.shippingRate = shippingRate;
    project.marginRate = marginRate;
    project.includeInstallation = includeInstallation;
    project.updatedAt = DateTime.now();

    // Save the changes
    projectProvider.saveCurrentProject();

    // Show success message
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Project information updated successfully'),
        backgroundColor: Colors.green,
      ),
    );
  }
}
