
enum ColumnDataType {
  text,
  number,
  currency,
  percentage,
  date,
  time,
  datetime,
  boolean,
  email,
  url,
  phone,
  dropdown,
  multiselect,
}

class FlexibleDatabase {
  final String? databaseId;
  final String name;
  final String path;
  final DateTime createdAt;
  final DateTime updatedAt;

  FlexibleDatabase({
    this.databaseId,
    required this.name,
    required this.path,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) : createdAt = createdAt ?? DateTime.now(),
       updatedAt = updatedAt ?? DateTime.now();

  Map<String, dynamic> toJson() => {
    'databaseId': databaseId,
    'name': name,
    'path': path,
    'createdAt': createdAt.toIso8601String(),
    'updatedAt': updatedAt.toIso8601String(),
  };

  factory FlexibleDatabase.fromJson(Map<String, dynamic> json) => FlexibleDatabase(
    databaseId: json['databaseId'],
    name: json['name'],
    path: json['path'],
    createdAt: DateTime.parse(json['createdAt']),
    updatedAt: DateTime.parse(json['updatedAt']),
  );
}

class FlexibleTable {
  final String? tableId;
  final String name;
  final String? databaseId;
  final DateTime createdAt;
  final DateTime updatedAt;
  final List<FlexibleColumn> columns;

  FlexibleTable({
    this.tableId,
    required this.name,
    this.databaseId,
    DateTime? createdAt,
    DateTime? updatedAt,
    List<FlexibleColumn>? columns,
  }) : createdAt = createdAt ?? DateTime.now(),
       updatedAt = updatedAt ?? DateTime.now(),
       columns = columns ?? [];

  Map<String, dynamic> toJson() => {
    'tableId': tableId,
    'name': name,
    'databaseId': databaseId,
    'createdAt': createdAt.toIso8601String(),
    'updatedAt': updatedAt.toIso8601String(),
  };

  factory FlexibleTable.fromJson(Map<String, dynamic> json) => FlexibleTable(
    tableId: json['tableId'],
    name: json['name'],
    databaseId: json['databaseId'],
    createdAt: DateTime.parse(json['createdAt']),
    updatedAt: DateTime.parse(json['updatedAt']),
  );
}

class FlexibleColumn {
  final String? columnId;
  final String name;
  final String? tableId;
  final String dataType;
  final bool isRequired;
  final String? defaultValue;
  final int orderIndex;
  final DateTime createdAt;
  final DateTime updatedAt;

  FlexibleColumn({
    this.columnId,
    required this.name,
    this.tableId,
    this.dataType = 'TEXT',
    this.isRequired = false,
    this.defaultValue,
    this.orderIndex = 0,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) : createdAt = createdAt ?? DateTime.now(),
       updatedAt = updatedAt ?? DateTime.now();

  Map<String, dynamic> toJson() => {
    'columnId': columnId,
    'name': name,
    'tableId': tableId,
    'dataType': dataType,
    'isRequired': isRequired,
    'defaultValue': defaultValue,
    'orderIndex': orderIndex,
    'createdAt': createdAt.toIso8601String(),
    'updatedAt': updatedAt.toIso8601String(),
  };

  factory FlexibleColumn.fromJson(Map<String, dynamic> json) => FlexibleColumn(
    columnId: json['columnId'],
    name: json['name'],
    tableId: json['tableId'],
    dataType: json['dataType'] ?? 'TEXT',
    isRequired: json['isRequired'] ?? false,
    defaultValue: json['defaultValue'],
    orderIndex: json['orderIndex'] ?? 0,
    createdAt: DateTime.parse(json['createdAt']),
    updatedAt: DateTime.parse(json['updatedAt']),
  );
}

class FlexibleRow {
  final String? rowId;
  final String? tableId;
  final DateTime createdAt;
  final DateTime updatedAt;
  final Map<String, dynamic> data;

  FlexibleRow({
    this.rowId,
    this.tableId,
    DateTime? createdAt,
    DateTime? updatedAt,
    Map<String, dynamic>? data,
  }) : createdAt = createdAt ?? DateTime.now(),
       updatedAt = updatedAt ?? DateTime.now(),
       data = data ?? {};

  Map<String, dynamic> toJson() => {
    'rowId': rowId,
    'tableId': tableId,
    'createdAt': createdAt.toIso8601String(),
    'updatedAt': updatedAt.toIso8601String(),
    'data': data,
  };

  factory FlexibleRow.fromJson(Map<String, dynamic> json) => FlexibleRow(
    rowId: json['rowId'],
    tableId: json['tableId'],
    createdAt: DateTime.parse(json['createdAt']),
    updatedAt: DateTime.parse(json['updatedAt']),
    data: json['data'] ?? {},
  );
}

class FlexibleCell {
  final String? cellId;
  final String? rowId;
  final String? columnId;
  final dynamic value;
  final DateTime createdAt;
  final DateTime updatedAt;

  FlexibleCell({
    this.cellId,
    this.rowId,
    this.columnId,
    this.value,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) : createdAt = createdAt ?? DateTime.now(),
       updatedAt = updatedAt ?? DateTime.now();

  Map<String, dynamic> toJson() => {
    'cellId': cellId,
    'rowId': rowId,
    'columnId': columnId,
    'value': value,
    'createdAt': createdAt.toIso8601String(),
    'updatedAt': updatedAt.toIso8601String(),
  };

  factory FlexibleCell.fromJson(Map<String, dynamic> json) => FlexibleCell(
    cellId: json['cellId'],
    rowId: json['rowId'],
    columnId: json['columnId'],
    value: json['value'],
    createdAt: DateTime.parse(json['createdAt']),
    updatedAt: DateTime.parse(json['updatedAt']),
  );
}