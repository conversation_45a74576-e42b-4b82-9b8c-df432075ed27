import 'package:flutter/material.dart';
import '../models/chat_message.dart';
import '../services/modern_ai_service.dart';
import '../services/truly_intelligent_ai.dart';
import '../widgets/modern_chat_message.dart';
import '../widgets/modern_chat_input.dart';
import '../widgets/modern_ai_interactive_dialog.dart';

/// Modern AI Assistant Screen with beautiful ChatGPT-like UI
class ModernAIAssistantScreen extends StatefulWidget {
  const ModernAIAssistantScreen({super.key});

  @override
  State<ModernAIAssistantScreen> createState() => _ModernAIAssistantScreenState();
}

class _ModernAIAssistantScreenState extends State<ModernAIAssistantScreen> 
    with TickerProviderStateMixin {
  final ModernAIService _aiService = ModernAIService();
  final TrulyIntelligentAI _intelligentAI = TrulyIntelligentAI();
  final ScrollController _scrollController = ScrollController();
  final TextEditingController _messageController = TextEditingController();
  
  final List<ChatMessage> _messages = [];
  bool _isInitialized = false;
  bool _isLoading = false;
  Map<String, dynamic>? _lastCalculationResults;
  
  late AnimationController _fadeController;
  late Animation<double> _fadeAnimation;

  @override
  void initState() {
    super.initState();
    _fadeController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _fadeController, curve: Curves.easeIn),
    );
    _initializeAI();
  }

  @override
  void dispose() {
    _scrollController.dispose();
    _messageController.dispose();
    _fadeController.dispose();
    super.dispose();
  }

  Future<void> _initializeAI() async {
    final success = await _aiService.initialize(context);
    
    setState(() {
      _isInitialized = true;
    });

    if (success) {
      _fadeController.forward();
      
      // Add welcome message
      _addMessage(ChatMessage(
        role: 'assistant',
        content: _getWelcomeMessage(),
        timestamp: DateTime.now(),
      ));
    }
  }

  String _getWelcomeMessage() {
    return '''🤖 **Modern FireTool AI Assistant**

I'm your intelligent assistant for fire suppression systems. I can help you with:

• **Smart Calculations** - "80 kg FM200 supply only"
• **System Design** - "Calculate NOVEC for 5x5m room"  
• **Cost Estimation** - Real pricing from your database
• **BOM Generation** - Detailed bills of materials
• **Auto-Learning** - I learn from your app usage

**Intelligence Level:** ${_aiService.intelligenceLevel}
**Memory Usage:** ${_aiService.memoryUsage}
**Auto-Learning:** ${_aiService.canAutoLearn ? '✅ Enabled' : '❌ Disabled'}

*No models needed - I use pure domain intelligence!* ✨

**Try asking:**
- "90 kg FM200"
- "Calculate clean agent for server room"
- "Show me installation options"''';
  }

  void _addMessage(ChatMessage message) {
    setState(() {
      _messages.add(message);
    });
    
    // Smooth scroll to bottom
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (_scrollController.hasClients) {
        _scrollController.animateTo(
          _scrollController.position.maxScrollExtent,
          duration: const Duration(milliseconds: 500),
          curve: Curves.easeOutCubic,
        );
      }
    });
  }

  Future<void> _sendMessage() async {
    final text = _messageController.text.trim();
    if (text.isEmpty || _isLoading) return;

    _messageController.clear();
    
    // Add user message
    _addMessage(ChatMessage(
      role: 'user',
      content: text,
      timestamp: DateTime.now(),
    ));

    setState(() {
      _isLoading = true;
    });

    try {
      // Use intelligent AI for analysis
      debugPrint('🧠 AI: User message: "$text"');
      
      // Analyze user intent with truly intelligent AI
      final analysis = await _intelligentAI.analyzeUserIntent(text);
      
      if (analysis['intent'] == 'calculate_clean_agent') {
        // Show intelligent dialog for clean agent calculations
        setState(() {
          _isLoading = false;
        });

        _addMessage(ChatMessage(
          role: 'assistant',
          content: '🧠 ${analysis['reasoning']}\n\nLet me gather the information I need...',
          timestamp: DateTime.now(),
        ));

        // Show intelligent dialog with Main/Reserve option
        _showIntelligentCalculationDialog(text, analysis);
      } else {
        // Process with modern AI service
        final response = await _aiService.processQuery(text, context);

        _addMessage(ChatMessage(
          role: 'assistant',
          content: response.message,
          timestamp: DateTime.now(),
        ));
      }
    } catch (e) {
      _addMessage(ChatMessage(
        role: 'assistant',
        content: 'Sorry, I encountered an error: $e',
        timestamp: DateTime.now(),
      ));
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  void _showIntelligentCalculationDialog(String userInput, Map<String, dynamic> analysis) {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => ModernAIInteractiveDialog(
        userInput: userInput,
        analysis: analysis,
        onCalculationComplete: (results) {
          setState(() {
            _lastCalculationResults = results;
          });
          
          // Add completion message with results
          _addMessage(ChatMessage(
            role: 'assistant',
            content: '✅ **Calculation Complete!**\n\n${results['summary']}',
            timestamp: DateTime.now(),
          ));
        },
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey.shade50,
      appBar: _buildModernAppBar(),
      body: FadeTransition(
        opacity: _fadeAnimation,
        child: Column(
          children: [
            // Chat messages
            Expanded(
              child: _buildChatArea(),
            ),
            
            // Loading indicator
            if (_isLoading) _buildLoadingIndicator(),
            
            // Input area
            _buildInputArea(),
          ],
        ),
      ),
    );
  }

  PreferredSizeWidget _buildModernAppBar() {
    return AppBar(
      elevation: 0,
      backgroundColor: Colors.white,
      foregroundColor: Colors.black87,
      title: Row(
        children: [
          Container(
            width: 32,
            height: 32,
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: [Colors.blue.shade600, Colors.purple.shade600],
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
              ),
              borderRadius: BorderRadius.circular(8),
            ),
            child: const Icon(Icons.psychology, color: Colors.white, size: 18),
          ),
          const SizedBox(width: 12),
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const Text(
                'AI Assistant',
                style: TextStyle(fontSize: 16, fontWeight: FontWeight.w600),
              ),
              Text(
                _aiService.isReady ? 'Online • Intelligent' : 'Initializing...',
                style: TextStyle(
                  fontSize: 12,
                  color: _aiService.isReady ? Colors.green.shade600 : Colors.orange.shade600,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
          ),
        ],
      ),
      actions: [
        // Status indicator
        Container(
          margin: const EdgeInsets.only(right: 16),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Container(
                width: 8,
                height: 8,
                decoration: BoxDecoration(
                  color: _aiService.isReady ? Colors.green : Colors.orange,
                  shape: BoxShape.circle,
                ),
              ),
              const SizedBox(width: 8),
              Text(
                _aiService.isReady ? 'Ready' : 'Loading',
                style: const TextStyle(fontSize: 12, fontWeight: FontWeight.w500),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildChatArea() {
    if (_messages.isEmpty) {
      return const Center(
        child: CircularProgressIndicator(),
      );
    }

    return ListView.builder(
      controller: _scrollController,
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      itemCount: _messages.length,
      itemBuilder: (context, index) {
        final message = _messages[index];
        final isUser = message.role == 'user';
        
        // Check if this is the last AI message with calculation results
        final hasCalculationResults = !isUser &&
            index == _messages.length - 1 &&
            _lastCalculationResults != null;

        return ModernChatMessage(
          message: message,
          isUser: isUser,
          calculationResults: hasCalculationResults ? _lastCalculationResults : null,
          onShowBOM: hasCalculationResults ? _showBOMDialog : null,
          onShowCalculation: hasCalculationResults ? _showCalculationDetailsDialog : null,
        );
      },
    );
  }

  Widget _buildLoadingIndicator() {
    return Container(
      padding: const EdgeInsets.all(16),
      child: Row(
        children: [
          const SizedBox(width: 48), // Align with AI avatar
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
            decoration: BoxDecoration(
              color: Colors.grey.shade100,
              borderRadius: BorderRadius.circular(20),
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                SizedBox(
                  width: 16,
                  height: 16,
                  child: CircularProgressIndicator(
                    strokeWidth: 2,
                    valueColor: AlwaysStoppedAnimation<Color>(Colors.blue.shade600),
                  ),
                ),
                const SizedBox(width: 12),
                const Text(
                  'AI is thinking...',
                  style: TextStyle(fontSize: 14, color: Colors.black54),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildInputArea() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10,
            offset: const Offset(0, -2),
          ),
        ],
      ),
      child: ModernChatInput(
        controller: _messageController,
        onSend: _sendMessage,
        enabled: _aiService.isReady && !_isLoading,
      ),
    );
  }

  void _showBOMDialog() {
    // Implementation for BOM dialog
  }

  void _showCalculationDetailsDialog() {
    // Implementation for calculation details dialog
  }
}
