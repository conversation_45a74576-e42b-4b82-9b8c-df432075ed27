import 'package:sqflite/sqflite.dart';
import 'package:path/path.dart';
import 'dart:async';
import 'dart:math';
import 'dart:convert';

class LocalSqlService {
  static final LocalSqlService _instance = LocalSqlService._internal();
  static Database? _database;

  // Factory constructor
  factory LocalSqlService() {
    return _instance;
  }

  // Private constructor
  LocalSqlService._internal();

  // Get database instance
  Future<Database> get database async {
    if (_database != null) return _database!;
    _database = await _initDatabase();
    return _database!;
  }

  // Initialize database
  Future<Database> _initDatabase() async {
    final dbPath = await getDatabasesPath();
    final path = join(dbPath, 'firetool.db');

    return await openDatabase(
      path,
      version: 1,
      onCreate: _createDatabase,
    );
  }

  // Create database tables
  Future<void> _createDatabase(Database db, int version) async {
    // Create section and schema tables for better organization
    await db.execute('''
      CREATE TABLE sections (
        id TEXT PRIMARY KEY,
        name TEXT NOT NULL UNIQUE,
        display_name TEXT NOT NULL,
        icon TEXT,
        parent_id TEXT,
        order_index INTEGER DEFAULT 0,
        created_at TEXT NOT NULL,
        updated_at TEXT NOT NULL,
        FOREIGN KEY (parent_id) REFERENCES sections (id) ON DELETE CASCADE
      )
    ''');

    await db.execute('''
      CREATE TABLE table_schemas (
        table_name TEXT PRIMARY KEY,
        display_name TEXT NOT NULL,
        section_id TEXT NOT NULL,
        schema_json TEXT NOT NULL,
        created_at TEXT NOT NULL,
        updated_at TEXT NOT NULL,
        FOREIGN KEY (section_id) REFERENCES sections (id) ON DELETE CASCADE
      )
    ''');

    // Create tables for each system type
    await db.execute('''
      CREATE TABLE alarm (
        id TEXT PRIMARY KEY,
        section_id TEXT,
        model TEXT,
        description TEXT,
        manufacturer TEXT,
        approval TEXT,
        ex_works_price TEXT,
        local_price TEXT,
        installation_price TEXT,
        created_at TEXT,
        FOREIGN KEY (section_id) REFERENCES sections (id) ON DELETE SET NULL
      )
    ''');

    await db.execute('''
      CREATE TABLE water (
        id TEXT PRIMARY KEY,
        section_id TEXT,
        model TEXT,
        description TEXT,
        manufacturer TEXT,
        approval TEXT,
        ex_works_price TEXT,
        local_price TEXT,
        installation_price TEXT,
        created_at TEXT,
        FOREIGN KEY (section_id) REFERENCES sections (id) ON DELETE SET NULL
      )
    ''');

    await db.execute('''
      CREATE TABLE foam (
        id TEXT PRIMARY KEY,
        section_id TEXT,
        model TEXT,
        description TEXT,
        manufacturer TEXT,
        approval TEXT,
        ex_works_price TEXT,
        local_price TEXT,
        installation_price TEXT,
        created_at TEXT,
        FOREIGN KEY (section_id) REFERENCES sections (id) ON DELETE SET NULL
      )
    ''');

    await db.execute('''
      CREATE TABLE fm200 (
        id TEXT PRIMARY KEY,
        section_id TEXT,
        model TEXT,
        description TEXT,
        manufacturer TEXT,
        approval TEXT,
        ex_works_price TEXT,
        local_price TEXT,
        installation_price TEXT,
        created_at TEXT,
        FOREIGN KEY (section_id) REFERENCES sections (id) ON DELETE SET NULL
      )
    ''');

    await db.execute('''
      CREATE TABLE novec (
        id TEXT PRIMARY KEY,
        section_id TEXT,
        model TEXT,
        description TEXT,
        manufacturer TEXT,
        approval TEXT,
        ex_works_price TEXT,
        local_price TEXT,
        installation_price TEXT,
        created_at TEXT,
        FOREIGN KEY (section_id) REFERENCES sections (id) ON DELETE SET NULL
      )
    ''');

    await db.execute('''
      CREATE TABLE co2 (
        id TEXT PRIMARY KEY,
        section_id TEXT,
        model TEXT,
        description TEXT,
        manufacturer TEXT,
        approval TEXT,
        ex_works_price TEXT,
        local_price TEXT,
        installation_price TEXT,
        created_at TEXT,
        FOREIGN KEY (section_id) REFERENCES sections (id) ON DELETE SET NULL
      )
    ''');

    await db.execute('''
      CREATE TABLE materials (
        id TEXT PRIMARY KEY,
        section_id TEXT,
        model TEXT,
        description TEXT,
        manufacturer TEXT,
        approval TEXT,
        ex_works_price TEXT,
        local_price TEXT,
        installation_price TEXT,
        created_at TEXT,
        FOREIGN KEY (section_id) REFERENCES sections (id) ON DELETE SET NULL
      )
    ''');

    await db.execute('''
      CREATE TABLE projects (
        id TEXT PRIMARY KEY,
        name TEXT,
        description TEXT,
        client TEXT,
        location TEXT,
        created_at TEXT,
        updated_at TEXT,
        status TEXT,
        currency TEXT,
        exchange_rate TEXT
      )
    ''');

    await db.execute('''
      CREATE TABLE project_systems (
        id TEXT PRIMARY KEY,
        project_id TEXT,
        system_type TEXT,
        name TEXT,
        description TEXT,
        created_at TEXT,
        FOREIGN KEY (project_id) REFERENCES projects (id) ON DELETE CASCADE
      )
    ''');

    await db.execute('''
      CREATE TABLE system_items (
        id TEXT PRIMARY KEY,
        system_id TEXT,
        item_id TEXT,
        item_type TEXT,
        quantity INTEGER,
        ex_works_price TEXT,
        local_price TEXT,
        installation_price TEXT,
        created_at TEXT,
        FOREIGN KEY (system_id) REFERENCES project_systems (id) ON DELETE CASCADE
      )
    ''');

    // Insert sample data
    await _insertSampleData(db);
  }

  // Insert sample data for each system type
  Future<void> _insertSampleData(Database db) async {
    final systemTypes = ['alarm', 'water', 'foam', 'fm200', 'novec', 'co2', 'materials'];
    final random = Random();
    
    // Create default sections first
    final Map<String, String> sectionIds = {};
    final DateTime now = DateTime.now();
    
    // Create main sections
    for (var systemType in systemTypes) {
      final String displayName = systemType.substring(0, 1).toUpperCase() + systemType.substring(1);
      final String sectionId = 'section-$systemType';
      
      await db.insert('sections', {
        'id': sectionId,
        'name': systemType,
        'display_name': displayName,
        'icon': null,
        'parent_id': null,
        'order_index': systemTypes.indexOf(systemType),
        'created_at': now.toIso8601String(),
        'updated_at': now.toIso8601String(),
      });
      
      sectionIds[systemType] = sectionId;
      
      // Create default schema for this section
      await db.insert('table_schemas', {
        'table_name': systemType,
        'display_name': '$displayName Items',
        'section_id': sectionId,
        'schema_json': jsonEncode({
          'columns': [
            {'name': 'model', 'type': 'text', 'required': true},
            {'name': 'description', 'type': 'text'},
            {'name': 'manufacturer', 'type': 'text'},
            {'name': 'approval', 'type': 'text'},
            {'name': 'ex_works_price', 'type': 'currency'},
            {'name': 'local_price', 'type': 'currency'},
            {'name': 'installation_price', 'type': 'currency'},
          ]
        }),
        'created_at': now.toIso8601String(),
        'updated_at': now.toIso8601String(),
      });
      
      // Create subsections for each main section
      for (var i = 1; i <= 3; i++) {
        final String subSectionId = 'section-$systemType-sub-$i';
        await db.insert('sections', {
          'id': subSectionId,
          'name': '${systemType}_sub_$i',
          'display_name': '$displayName Subsection $i',
          'icon': null,
          'parent_id': sectionId,
          'order_index': i,
          'created_at': now.toIso8601String(),
          'updated_at': now.toIso8601String(),
        });
      }
    }
    
    // Insert sample items for each system type
    for (var systemType in systemTypes) {
      final sectionId = sectionIds[systemType];
      
      for (var i = 1; i <= 20; i++) {
        await db.insert(systemType, {
          'id': 'sample-$systemType-$i',
          'section_id': sectionId,
          'model': 'Model $i',
          'description': 'Description for $systemType item $i',
          'manufacturer': 'Manufacturer ${i % 3 + 1}',
          'approval': 'UL Listed',
          'ex_works_price': '${(i * 100) + random.nextInt(50)}',
          'local_price': '${(i * 150) + random.nextInt(75)}',
          'installation_price': '${(i * 50) + random.nextInt(25)}',
          'created_at': DateTime.now().toIso8601String(),
        });
      }
    }

    // Create sample projects
    final projectIds = <String>[];
    for (var i = 1; i <= 5; i++) {
      final projectId = 'project-$i';
      projectIds.add(projectId);
      
      await db.insert('projects', {
        'id': projectId,
        'name': 'Project $i',
        'description': 'Sample project $i description',
        'client': 'Client ${i % 3 + 1}',
        'location': 'Location $i',
        'created_at': DateTime.now().toIso8601String(),
        'updated_at': DateTime.now().toIso8601String(),
        'status': i % 3 == 0 ? 'Completed' : (i % 3 == 1 ? 'In Progress' : 'Pending'),
        'currency': 'SAR',
        'exchange_rate': '3.75',
      });
    }

    // Create sample systems for each project
    final systemIds = <String>[];
    for (var projectId in projectIds) {
      for (var i = 1; i <= 3; i++) {
        final systemType = systemTypes[random.nextInt(systemTypes.length)];
        final systemId = 'system-$projectId-$i';
        systemIds.add(systemId);
        
        await db.insert('project_systems', {
          'id': systemId,
          'project_id': projectId,
          'system_type': systemType,
          'name': '$systemType System $i',
          'description': 'Sample $systemType system $i for project $projectId',
          'created_at': DateTime.now().toIso8601String(),
        });
      }
    }

    // Create sample items for each system
    for (var systemId in systemIds) {
      final systemType = (await db.query(
        'project_systems',
        columns: ['system_type'],
        where: 'id = ?',
        whereArgs: [systemId],
      )).first['system_type'] as String;
      
      // Get random items from the corresponding system type
      final items = await db.query(
        systemType,
        limit: 5 + random.nextInt(5),
      );
      
      for (var item in items) {
        await db.insert('system_items', {
          'id': 'item-$systemId-${item['id']}',
          'system_id': systemId,
          'item_id': item['id'] as String,
          'item_type': systemType,
          'quantity': 1 + random.nextInt(10),
          'ex_works_price': item['ex_works_price'],
          'local_price': item['local_price'],
          'installation_price': item['installation_price'],
          'created_at': DateTime.now().toIso8601String(),
        });
      }
    }
  }

  // CRUD Operations

  // Get all items from a collection
  Future<List<Map<String, dynamic>>> getItems(String collectionPath) async {
    final db = await database;
    return await db.query(collectionPath);
  }

  // Get item by ID
  Future<Map<String, dynamic>?> getItemById(String collectionPath, String id) async {
    final db = await database;
    final results = await db.query(
      collectionPath,
      where: 'id = ?',
      whereArgs: [id],
      limit: 1,
    );
    
    return results.isNotEmpty ? results.first : null;
  }

  // Add a new item to a collection
  Future<String> addItem(String collectionPath, Map<String, dynamic> item) async {
    final db = await database;
    
    // Generate a unique ID if not provided
    if (!item.containsKey('id') || item['id'] == null) {
      item['id'] = 'item-${DateTime.now().millisecondsSinceEpoch}';
    }
    
    // Add created_at timestamp if not provided
    if (!item.containsKey('created_at') || item['created_at'] == null) {
      item['created_at'] = DateTime.now().toIso8601String();
    }
    
    await db.insert(collectionPath, item);
    return item['id'];
  }

  // Update an item in a collection
  Future<void> updateItem(String collectionPath, String itemId, Map<String, dynamic> item) async {
    final db = await database;
    await db.update(
      collectionPath,
      item,
      where: 'id = ?',
      whereArgs: [itemId],
    );
  }

  // Update a specific field in an item
  Future<void> updateField(String collectionPath, String itemId, String field, dynamic value) async {
    final db = await database;
    await db.update(
      collectionPath,
      {field: value},
      where: 'id = ?',
      whereArgs: [itemId],
    );
  }

  // Delete an item from a collection
  Future<void> deleteItem(String collectionPath, String itemId) async {
    final db = await database;
    await db.delete(
      collectionPath,
      where: 'id = ?',
      whereArgs: [itemId],
    );
  }

  // Close the database
  Future<void> close() async {
    final db = await database;
    db.close();
  }
  
  // Section Management Methods
  
  // Get all sections
  Future<List<Map<String, dynamic>>> getSections() async {
    final db = await database;
    return await db.query('sections', orderBy: 'order_index ASC');
  }
  
  // Get section by ID
  Future<Map<String, dynamic>?> getSectionById(String id) async {
    final db = await database;
    final results = await db.query(
      'sections',
      where: 'id = ?',
      whereArgs: [id],
      limit: 1,
    );
    
    return results.isNotEmpty ? results.first : null;
  }
  
  // Get subsections for a parent section
  Future<List<Map<String, dynamic>>> getSubsections(String parentId) async {
    final db = await database;
    return await db.query(
      'sections',
      where: 'parent_id = ?',
      whereArgs: [parentId],
      orderBy: 'order_index ASC',
    );
  }
  
  // Add a new section
  Future<String> addSection(Map<String, dynamic> section) async {
    final db = await database;
    
    // Generate a unique ID if not provided
    if (!section.containsKey('id') || section['id'] == null) {
      section['id'] = 'section-${DateTime.now().millisecondsSinceEpoch}';
    }
    
    // Add timestamps
    final now = DateTime.now().toIso8601String();
    section['created_at'] = now;
    section['updated_at'] = now;
    
    await db.insert('sections', section);
    return section['id'];
  }
  
  // Update a section
  Future<void> updateSection(String sectionId, Map<String, dynamic> section) async {
    final db = await database;
    
    // Update timestamp
    section['updated_at'] = DateTime.now().toIso8601String();
    
    await db.update(
      'sections',
      section,
      where: 'id = ?',
      whereArgs: [sectionId],
    );
  }
  
  // Delete a section and all its subsections
  Future<void> deleteSection(String sectionId) async {
    final db = await database;
    
    // First delete all subsections
    await db.delete(
      'sections',
      where: 'parent_id = ?',
      whereArgs: [sectionId],
    );
    
    // Then delete the section itself
    await db.delete(
      'sections',
      where: 'id = ?',
      whereArgs: [sectionId],
    );
  }
  
  // Schema Management Methods
  
  // Get schema for a table
  Future<Map<String, dynamic>?> getTableSchema(String tableName) async {
    final db = await database;
    final results = await db.query(
      'table_schemas',
      where: 'table_name = ?',
      whereArgs: [tableName],
      limit: 1,
    );
    
    return results.isNotEmpty ? results.first : null;
  }
  
  // Get all schemas for a section
  Future<List<Map<String, dynamic>>> getSchemasBySection(String sectionId) async {
    final db = await database;
    return await db.query(
      'table_schemas',
      where: 'section_id = ?',
      whereArgs: [sectionId],
    );
  }
  
  // Create or update a table schema
  Future<void> saveTableSchema(Map<String, dynamic> schema) async {
    final db = await database;
    final now = DateTime.now().toIso8601String();
    
    // Check if schema already exists
    final existing = await getTableSchema(schema['table_name']);
    
    if (existing != null) {
      // Update existing schema
      schema['updated_at'] = now;
      await db.update(
        'table_schemas',
        schema,
        where: 'table_name = ?',
        whereArgs: [schema['table_name']],
      );
    } else {
      // Create new schema
      schema['created_at'] = now;
      schema['updated_at'] = now;
      await db.insert('table_schemas', schema);
    }
  }
  
  // Delete a table schema
  Future<void> deleteTableSchema(String tableName) async {
    final db = await database;
    await db.delete(
      'table_schemas',
      where: 'table_name = ?',
      whereArgs: [tableName],
    );
  }
  
  // Get items by section
  Future<List<Map<String, dynamic>>> getItemsBySection(String collectionPath, String sectionId) async {
    final db = await database;
    return await db.query(
      collectionPath,
      where: 'section_id = ?',
      whereArgs: [sectionId],
    );
  }
  
  // Move items to a different section
  Future<void> moveItemsToSection(String collectionPath, List<String> itemIds, String sectionId) async {
    final db = await database;
    
    for (final itemId in itemIds) {
      await db.update(
        collectionPath,
        {'section_id': sectionId},
        where: 'id = ?',
        whereArgs: [itemId],
      );
    }
  }
}
