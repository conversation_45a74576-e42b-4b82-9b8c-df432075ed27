import 'dart:async';
import 'package:flutter/foundation.dart';

/// Service for managing performance optimizations in SuperDatabase
class PerformanceService {
  static final PerformanceService _instance = PerformanceService._internal();
  factory PerformanceService() => _instance;
  PerformanceService._internal();

  // Cache for frequently accessed data
  final Map<String, CacheEntry> _cache = {};
  final int _maxCacheSize = 100;
  final Duration _cacheExpiry = const Duration(minutes: 5);

  // Background processing queue
  final List<BackgroundTask> _taskQueue = [];
  bool _isProcessingTasks = false;

  /// Cache data with expiry
  void cacheData(String key, dynamic data) {
    // Remove oldest entries if cache is full
    if (_cache.length >= _maxCacheSize) {
      final oldestKey = _cache.keys.first;
      _cache.remove(oldestKey);
    }

    _cache[key] = CacheEntry(
      data: data,
      timestamp: DateTime.now(),
    );

    if (kDebugMode) {
      print('Cached data for key: $key');
    }
  }

  /// Get cached data if not expired
  T? getCachedData<T>(String key) {
    final entry = _cache[key];
    if (entry == null) return null;

    // Check if expired
    if (DateTime.now().difference(entry.timestamp) > _cacheExpiry) {
      _cache.remove(key);
      return null;
    }

    return entry.data as T?;
  }

  /// Clear cache
  void clearCache() {
    _cache.clear();
    if (kDebugMode) {
      print('Cache cleared');
    }
  }

  /// Clear expired cache entries
  void cleanupCache() {
    final now = DateTime.now();
    final expiredKeys = _cache.entries
        .where((entry) => now.difference(entry.value.timestamp) > _cacheExpiry)
        .map((entry) => entry.key)
        .toList();

    for (final key in expiredKeys) {
      _cache.remove(key);
    }

    if (kDebugMode && expiredKeys.isNotEmpty) {
      print('Cleaned up ${expiredKeys.length} expired cache entries');
    }
  }

  /// Add background task to queue
  void addBackgroundTask(BackgroundTask task) {
    _taskQueue.add(task);
    _processTaskQueue();
  }

  /// Process background tasks
  Future<void> _processTaskQueue() async {
    if (_isProcessingTasks || _taskQueue.isEmpty) return;

    _isProcessingTasks = true;

    while (_taskQueue.isNotEmpty) {
      final task = _taskQueue.removeAt(0);
      
      try {
        await task.execute();
        if (kDebugMode) {
          print('Completed background task: ${task.name}');
        }
      } catch (e) {
        if (kDebugMode) {
          print('Background task failed: ${task.name} - $e');
        }
      }
    }

    _isProcessingTasks = false;
  }

  /// Debounce function calls
  Timer? _debounceTimer;
  void debounce(Duration delay, VoidCallback callback) {
    _debounceTimer?.cancel();
    _debounceTimer = Timer(delay, callback);
  }

  /// Throttle function calls
  DateTime? _lastThrottleTime;
  void throttle(Duration interval, VoidCallback callback) {
    final now = DateTime.now();
    if (_lastThrottleTime == null || 
        now.difference(_lastThrottleTime!) >= interval) {
      _lastThrottleTime = now;
      callback();
    }
  }

  /// Batch operations for better performance
  Future<List<T>> batchOperation<T>(
    List<Future<T> Function()> operations, {
    int batchSize = 10,
    Duration delay = const Duration(milliseconds: 10),
  }) async {
    final results = <T>[];
    
    for (int i = 0; i < operations.length; i += batchSize) {
      final batch = operations.skip(i).take(batchSize);
      final batchResults = await Future.wait(batch.map((op) => op()));
      results.addAll(batchResults);
      
      // Small delay between batches to prevent blocking UI
      if (i + batchSize < operations.length) {
        await Future.delayed(delay);
      }
    }
    
    return results;
  }

  /// Memory usage monitoring
  void logMemoryUsage(String context) {
    if (kDebugMode) {
      print('Memory usage at $context: Cache size: ${_cache.length}');
    }
  }

  /// Dispose resources
  void dispose() {
    _debounceTimer?.cancel();
    _cache.clear();
    _taskQueue.clear();
  }
}

/// Cache entry with timestamp
class CacheEntry {
  final dynamic data;
  final DateTime timestamp;

  CacheEntry({
    required this.data,
    required this.timestamp,
  });
}

/// Background task definition
abstract class BackgroundTask {
  final String name;
  final DateTime createdAt;

  BackgroundTask({
    required this.name,
  }) : createdAt = DateTime.now();

  Future<void> execute();
}

/// Data preloading task
class DataPreloadTask extends BackgroundTask {
  final Future<void> Function() preloadFunction;

  DataPreloadTask({
    required super.name,
    required this.preloadFunction,
  });

  @override
  Future<void> execute() async {
    await preloadFunction();
  }
}

/// Cache cleanup task
class CacheCleanupTask extends BackgroundTask {
  final PerformanceService performanceService;

  CacheCleanupTask({
    required this.performanceService,
  }) : super(name: 'Cache Cleanup');

  @override
  Future<void> execute() async {
    performanceService.cleanupCache();
  }
}

/// Data export task
class DataExportTask extends BackgroundTask {
  final Future<void> Function() exportFunction;

  DataExportTask({
    required super.name,
    required this.exportFunction,
  });

  @override
  Future<void> execute() async {
    await exportFunction();
  }
}

/// Virtual scrolling helper
class VirtualScrollHelper {
  final int itemHeight;
  final int viewportHeight;
  final int totalItems;

  VirtualScrollHelper({
    required this.itemHeight,
    required this.viewportHeight,
    required this.totalItems,
  });

  /// Calculate visible range based on scroll offset
  VisibleRange calculateVisibleRange(double scrollOffset) {
    final startIndex = (scrollOffset / itemHeight).floor();
    final endIndex = ((scrollOffset + viewportHeight) / itemHeight).ceil();
    
    // Add buffer for smooth scrolling
    const buffer = 5;
    final bufferedStart = (startIndex - buffer).clamp(0, totalItems);
    final bufferedEnd = (endIndex + buffer).clamp(0, totalItems);

    return VisibleRange(
      startIndex: bufferedStart,
      endIndex: bufferedEnd,
      visibleCount: bufferedEnd - bufferedStart,
    );
  }

  /// Calculate total scroll height
  double get totalHeight => totalItems * itemHeight.toDouble();
}

/// Visible range for virtual scrolling
class VisibleRange {
  final int startIndex;
  final int endIndex;
  final int visibleCount;

  VisibleRange({
    required this.startIndex,
    required this.endIndex,
    required this.visibleCount,
  });

  @override
  String toString() {
    return 'VisibleRange(start: $startIndex, end: $endIndex, count: $visibleCount)';
  }
}

/// Data pagination helper
class PaginationHelper {
  final int pageSize;
  final int totalItems;

  PaginationHelper({
    required this.pageSize,
    required this.totalItems,
  });

  int get totalPages => (totalItems / pageSize).ceil();

  bool isValidPage(int page) => page >= 1 && page <= totalPages;

  int getOffset(int page) => (page - 1) * pageSize;

  int getLimit(int page) {
    if (!isValidPage(page)) return 0;
    
    final offset = getOffset(page);
    final remaining = totalItems - offset;
    return remaining < pageSize ? remaining : pageSize;
  }

  PageInfo getPageInfo(int currentPage) {
    return PageInfo(
      currentPage: currentPage,
      totalPages: totalPages,
      totalItems: totalItems,
      pageSize: pageSize,
      hasNext: currentPage < totalPages,
      hasPrevious: currentPage > 1,
      startItem: getOffset(currentPage) + 1,
      endItem: getOffset(currentPage) + getLimit(currentPage),
    );
  }
}

/// Page information
class PageInfo {
  final int currentPage;
  final int totalPages;
  final int totalItems;
  final int pageSize;
  final bool hasNext;
  final bool hasPrevious;
  final int startItem;
  final int endItem;

  PageInfo({
    required this.currentPage,
    required this.totalPages,
    required this.totalItems,
    required this.pageSize,
    required this.hasNext,
    required this.hasPrevious,
    required this.startItem,
    required this.endItem,
  });

  @override
  String toString() {
    return 'Page $currentPage of $totalPages ($startItem-$endItem of $totalItems items)';
  }
}
