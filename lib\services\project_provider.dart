import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:uuid/uuid.dart';
import '../models/project.dart';
import '../models/clean_agent_system.dart';
import '../models/isar_models.dart' as isar_models;
import 'isar_service.dart';

class ProjectProvider with ChangeNotifier {
  final IsarService _isarService = IsarService.instance;

  Project? _currentProject;
  List<Project> _projects = [];
  bool _isLoading = false;
  String? _error;

  // Always return true for admin access
  bool get isAdmin => true;

  // Update auth service reference
  void updateAuth(dynamic auth) {
    // No-op for now
  }

  // Getters
  Project? get currentProject => _currentProject;
  List<Project> get projects => _projects;
  bool get isLoading => _isLoading;
  String? get error => _error;

  // Initialize provider
  Future<void> initialize() async {
    _setLoading(true);
    try {
      await _loadProjects();
    } catch (e) {
      print('Error in initialize: $e');
      _setError('Failed to initialize: ${e.toString()}');
    } finally {
      _setLoading(false);
    }
  }

  // Load all projects
  Future<void> _loadProjects() async {
    try {
      final isar = await _isarService.database;
      // Get all projects - use a working approach
      final List<isar_models.Project> isarProjects = [];

      // Try to get projects by checking a reasonable range of IDs
      for (int i = 1; i <= 1000; i++) {
        final project = await isar.projects.get(i);
        if (project != null) {
          isarProjects.add(project);
        }
      }

      // Convert ISAR projects to regular Project objects
      _projects = isarProjects.map((isarProject) {
        // Load systems from JSON
        List<SystemEstimate> systems = [];
        if (isarProject.systemsJson != null && isarProject.systemsJson!.isNotEmpty) {
          try {
            final List<dynamic> systemsJsonList = json.decode(isarProject.systemsJson!);
            systems = systemsJsonList
                .map((systemJson) => SystemEstimate.fromMap(systemJson))
                .toList();
          } catch (e) {
            debugPrint('Error loading systems for project ${isarProject.name}: $e');
          }
        }

        // Load clean agent systems from JSON
        List<CleanAgentSystem> cleanAgentSystems = [];
        if (isarProject.cleanAgentSystemsJson != null && isarProject.cleanAgentSystemsJson!.isNotEmpty) {
          try {
            final List<dynamic> cleanAgentSystemsJsonList = json.decode(isarProject.cleanAgentSystemsJson!);
            cleanAgentSystems = cleanAgentSystemsJsonList
                .map((systemJson) => CleanAgentSystem.fromJson(systemJson))
                .toList();
          } catch (e) {
            debugPrint('Error loading clean agent systems for project ${isarProject.name}: $e');
          }
        }

        return Project(
          id: isarProject.projectId ?? const Uuid().v4(),
          name: isarProject.name ?? 'Unnamed Project',
          clientName: isarProject.clientName ?? '',
          projectReference: isarProject.projectReference ?? '',
          createdAt: isarProject.createdAt ?? DateTime.now(),
          updatedAt: isarProject.updatedAt ?? DateTime.now(),
          currency: isarProject.currency ?? 'SAR',
          exchangeRate: isarProject.exchangeRate ?? 3.75,
          shippingRate: isarProject.shippingRate ?? 1.15,
          marginRate: isarProject.marginRate ?? 1.0,
          includeInstallation: isarProject.includeInstallation ?? true,
          systems: systems,
          cleanAgentSystems: cleanAgentSystems,
        );
      }).toList();

      print('Loaded ${_projects.length} projects');

      // Clean up empty clean agent systems if current project is loaded
      if (_currentProject != null) {
        removeEmptyCleanAgentSystems();
      }

      notifyListeners();
    } catch (e) {
      print('Error in _loadProjects: $e');
      _setError('Failed to load projects: ${e.toString()}');
    }
  }

  // Create a new project
  Future<void> createProject({
    required String name,
    String clientName = '',
    String projectReference = '',
    String? currency,
    double? exchangeRate,
    double? shippingRate,
    double? marginRate,
    bool? includeInstallation,
  }) async {
    _setLoading(true);
    try {
      final projectId = const Uuid().v4();
      final newProject = Project(
        id: projectId,
        name: name,
        clientName: clientName,
        projectReference: projectReference,
        currency: currency ?? 'SAR',
        exchangeRate: exchangeRate ?? 3.75,
        shippingRate: shippingRate ?? 1.15,
        marginRate: marginRate ?? 1.0,
        includeInstallation: includeInstallation ?? true,
      );

      // Create ISAR project
      final isarProject = isar_models.Project.create(
        projectId: projectId,
        name: name,
        clientName: clientName,
        projectReference: projectReference,
        currency: currency ?? 'SAR',
        exchangeRate: exchangeRate ?? 3.75,
        shippingRate: shippingRate ?? 1.15,
        marginRate: marginRate ?? 1.0,
        includeInstallation: includeInstallation ?? true,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );

      final isar = await _isarService.database;
      await isar.writeTxn(() async {
        await isar.projects.put(isarProject);
      });

      _currentProject = newProject;
      await _loadProjects();
    } catch (e) {
      _setError('Failed to create project: ${e.toString()}');
    } finally {
      _setLoading(false);
    }
  }

  // Load a project
  Future<void> loadProject(String projectId) async {
    _setLoading(true);
    try {
      final isar = await _isarService.database;
      final isarProject = await isar.projects.getByProjectId(projectId);

      if (isarProject != null) {
        // Load systems from JSON
        List<SystemEstimate> systems = [];
        if (isarProject.systemsJson != null && isarProject.systemsJson!.isNotEmpty) {
          try {
            final List<dynamic> systemsJsonList = json.decode(isarProject.systemsJson!);
            systems = systemsJsonList
                .map((systemJson) => SystemEstimate.fromMap(systemJson))
                .toList();
            debugPrint('Loaded ${systems.length} systems from database');
          } catch (e) {
            debugPrint('Error loading systems: $e');
          }
        }

        // Load clean agent systems from JSON
        List<CleanAgentSystem> cleanAgentSystems = [];
        if (isarProject.cleanAgentSystemsJson != null && isarProject.cleanAgentSystemsJson!.isNotEmpty) {
          try {
            final List<dynamic> cleanAgentSystemsJsonList = json.decode(isarProject.cleanAgentSystemsJson!);
            cleanAgentSystems = cleanAgentSystemsJsonList
                .map((systemJson) => CleanAgentSystem.fromJson(systemJson))
                .toList();
            debugPrint('Loaded ${cleanAgentSystems.length} clean agent systems from database');
          } catch (e) {
            debugPrint('Error loading clean agent systems: $e');
          }
        }

        _currentProject = Project(
          id: isarProject.projectId ?? projectId,
          name: isarProject.name ?? 'Unnamed Project',
          clientName: isarProject.clientName ?? '',
          projectReference: isarProject.projectReference ?? '',
          createdAt: isarProject.createdAt ?? DateTime.now(),
          updatedAt: isarProject.updatedAt ?? DateTime.now(),
          currency: isarProject.currency ?? 'SAR',
          exchangeRate: isarProject.exchangeRate ?? 3.75,
          shippingRate: isarProject.shippingRate ?? 1.15,
          marginRate: isarProject.marginRate ?? 1.0,
          includeInstallation: isarProject.includeInstallation ?? true,
          systems: systems,
          cleanAgentSystems: cleanAgentSystems,
        );
        debugPrint('Loaded project: ${_currentProject?.name}');

        // Clean up empty clean agent systems after loading
        removeEmptyCleanAgentSystems();
      } else {
        _setError('Project not found');
      }

      notifyListeners();
    } catch (e) {
      debugPrint('Error in loadProject: $e');
      _setError('Failed to load project: ${e.toString()}');
    } finally {
      _setLoading(false);
    }
  }

  // Save current project
  Future<void> saveCurrentProject() async {
    if (_currentProject == null) {
      _setError('No project to save');
      return;
    }

    _setLoading(true);
    try {
      _currentProject!.updatedAt = DateTime.now();

      // Update ISAR project
      final isar = await _isarService.database;
      final isarProject = await isar.projects.getByProjectId(_currentProject!.id);

      if (isarProject != null) {
        isarProject.name = _currentProject!.name;
        isarProject.clientName = _currentProject!.clientName;
        isarProject.projectReference = _currentProject!.projectReference;
        isarProject.currency = _currentProject!.currency;
        isarProject.exchangeRate = _currentProject!.exchangeRate;
        isarProject.shippingRate = _currentProject!.shippingRate;
        isarProject.marginRate = _currentProject!.marginRate;
        isarProject.includeInstallation = _currentProject!.includeInstallation;
        isarProject.updatedAt = _currentProject!.updatedAt;

        // Save systems as JSON
        if (_currentProject!.systems.isNotEmpty) {
          isarProject.systemsJson = json.encode(
            _currentProject!.systems.map((system) => system.toMap()).toList()
          );
          debugPrint('Saving ${_currentProject!.systems.length} systems');
        } else {
          isarProject.systemsJson = null;
        }

        // Save clean agent systems as JSON
        if (_currentProject!.cleanAgentSystems.isNotEmpty) {
          isarProject.cleanAgentSystemsJson = json.encode(
            _currentProject!.cleanAgentSystems.map((system) => system.toJson()).toList()
          );
          debugPrint('Saving ${_currentProject!.cleanAgentSystems.length} clean agent systems');
        } else {
          isarProject.cleanAgentSystemsJson = null;
        }

        await isar.writeTxn(() async {
          await isar.projects.put(isarProject);
        });
      }

      await _loadProjects();
    } catch (e) {
      _setError('Failed to save project: ${e.toString()}');
    } finally {
      _setLoading(false);
    }
  }

  // Delete a project
  Future<void> deleteProject(String projectId) async {
    _setLoading(true);
    try {
      final isar = await _isarService.database;
      await isar.writeTxn(() async {
        final project = await isar.projects.getByProjectId(projectId);
        if (project != null) {
          await isar.projects.delete(project.id);
        }
      });

      if (_currentProject?.id == projectId) {
        _currentProject = null;
      }
      await _loadProjects();
    } catch (e) {
      _setError('Failed to delete project: ${e.toString()}');
    } finally {
      _setLoading(false);
    }
  }

  // Add a system to the current project
  void addSystem(SystemEstimate system) {
    if (_currentProject == null) {
      _setError('No active project');
      return;
    }

    _currentProject!.systems.add(system);
    notifyListeners();
  }

  // Update a system in the current project
  void updateSystem(SystemEstimate updatedSystem) {
    if (_currentProject == null) {
      _setError('No active project');
      return;
    }

    final index = _currentProject!.systems.indexWhere((s) => s.id == updatedSystem.id);
    if (index != -1) {
      _currentProject!.systems[index] = updatedSystem;
      notifyListeners();
    } else {
      _setError('System not found');
    }
  }

  // Remove a system from the current project
  void removeSystem(String systemId) {
    if (_currentProject == null) {
      _setError('No active project');
      return;
    }

    _currentProject!.systems.removeWhere((s) => s.id == systemId);
    notifyListeners();
  }

  // Add a material to a system
  void addMaterial(String systemId, MaterialItem material) {
    if (_currentProject == null) {
      _setError('No active project');
      return;
    }

    final systemIndex = _currentProject!.systems.indexWhere((s) => s.id == systemId);
    if (systemIndex != -1) {
      _currentProject!.systems[systemIndex].materials.add(material);
      notifyListeners();
    } else {
      _setError('System not found');
    }
  }

  // Update a material in a system
  void updateMaterial(String systemId, MaterialItem updatedMaterial) {
    if (_currentProject == null) {
      _setError('No active project');
      return;
    }

    final systemIndex = _currentProject!.systems.indexWhere((s) => s.id == systemId);
    if (systemIndex != -1) {
      final materialIndex = _currentProject!.systems[systemIndex].materials
          .indexWhere((m) => m.id == updatedMaterial.id);

      if (materialIndex != -1) {
        _currentProject!.systems[systemIndex].materials[materialIndex] = updatedMaterial;
        notifyListeners();
      } else {
        _setError('Material not found');
      }
    } else {
      _setError('System not found');
    }
  }

  // Remove a material from a system
  void removeMaterial(String systemId, String materialId) {
    if (_currentProject == null) {
      _setError('No active project');
      return;
    }

    final systemIndex = _currentProject!.systems.indexWhere((s) => s.id == systemId);
    if (systemIndex != -1) {
      _currentProject!.systems[systemIndex].materials.removeWhere((m) => m.id == materialId);
      notifyListeners();
    } else {
      _setError('System not found');
    }
  }

  // Equipment CRUD operations
  void addEquipment(String systemId, EquipmentItem equipment) {
    if (_currentProject == null) {
      _setError('No active project');
      return;
    }

    final systemIndex = _currentProject!.systems.indexWhere((s) => s.id == systemId);
    if (systemIndex != -1) {
      _currentProject!.systems[systemIndex].equipment.add(equipment);
      notifyListeners();
    } else {
      _setError('System not found');
    }
  }

  void updateEquipment(String systemId, EquipmentItem updatedEquipment) {
    if (_currentProject == null) {
      _setError('No active project');
      return;
    }

    final systemIndex = _currentProject!.systems.indexWhere((s) => s.id == systemId);
    if (systemIndex != -1) {
      final equipmentIndex = _currentProject!.systems[systemIndex].equipment
          .indexWhere((e) => e.id == updatedEquipment.id);

      if (equipmentIndex != -1) {
        _currentProject!.systems[systemIndex].equipment[equipmentIndex] = updatedEquipment;
        notifyListeners();
      } else {
        _setError('Equipment not found');
      }
    } else {
      _setError('System not found');
    }
  }

  void removeEquipment(String systemId, String equipmentId) {
    if (_currentProject == null) {
      _setError('No active project');
      return;
    }

    final systemIndex = _currentProject!.systems.indexWhere((s) => s.id == systemId);
    if (systemIndex != -1) {
      _currentProject!.systems[systemIndex].equipment.removeWhere((e) => e.id == equipmentId);
      notifyListeners();
    } else {
      _setError('System not found');
    }
  }

  // Service CRUD operations
  void addService(String systemId, ServiceItem service) {
    if (_currentProject == null) {
      _setError('No active project');
      return;
    }

    final systemIndex = _currentProject!.systems.indexWhere((s) => s.id == systemId);
    if (systemIndex != -1) {
      _currentProject!.systems[systemIndex].services.add(service);
      notifyListeners();
    } else {
      _setError('System not found');
    }
  }

  void updateService(String systemId, ServiceItem updatedService) {
    if (_currentProject == null) {
      _setError('No active project');
      return;
    }

    final systemIndex = _currentProject!.systems.indexWhere((s) => s.id == systemId);
    if (systemIndex != -1) {
      final serviceIndex = _currentProject!.systems[systemIndex].services
          .indexWhere((s) => s.id == updatedService.id);

      if (serviceIndex != -1) {
        _currentProject!.systems[systemIndex].services[serviceIndex] = updatedService;
        notifyListeners();
      } else {
        _setError('Service item not found');
      }
    } else {
      _setError('System not found');
    }
  }

  void removeService(String systemId, String serviceId) {
    if (_currentProject == null) {
      _setError('No active project');
      return;
    }

    final systemIndex = _currentProject!.systems.indexWhere((s) => s.id == systemId);
    if (systemIndex != -1) {
      _currentProject!.systems[systemIndex].services.removeWhere((s) => s.id == serviceId);
      notifyListeners();
    } else {
      _setError('System not found');
    }
  }

  // For backward compatibility
  void addLabor(String systemId, LaborItem labor) {
    addService(systemId, labor);
  }

  void updateLabor(String systemId, LaborItem updatedLabor) {
    updateService(systemId, updatedLabor);
  }

  void removeLabor(String systemId, String laborId) {
    removeService(systemId, laborId);
  }

  // Helper methods
  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  void _setError(String? errorMessage) {
    _error = errorMessage;
    notifyListeners();
  }

  void clearError() {
    _error = null;
    notifyListeners();
  }

  // Clean Agent System management methods
  void addCleanAgentSystem(CleanAgentSystem system) {
    if (_currentProject == null) {
      _setError('No active project');
      return;
    }

    _currentProject!.cleanAgentSystems.add(system);
    notifyListeners();
  }

  void updateCleanAgentSystem(CleanAgentSystem updatedSystem) {
    if (_currentProject == null) {
      _setError('No active project');
      return;
    }

    final index = _currentProject!.cleanAgentSystems.indexWhere((s) => s.id == updatedSystem.id);
    if (index != -1) {
      _currentProject!.cleanAgentSystems[index] = updatedSystem;
      notifyListeners();
    } else {
      _setError('Clean agent system not found');
    }
  }

  void removeCleanAgentSystem(String systemId) {
    if (_currentProject == null) {
      _setError('No active project');
      return;
    }

    _currentProject!.cleanAgentSystems.removeWhere((s) => s.id == systemId);
    notifyListeners();
  }

  // Remove empty clean agent systems (systems with $0 cost)
  void removeEmptyCleanAgentSystems() {
    if (_currentProject == null) return;

    _currentProject!.cleanAgentSystems.removeWhere((system) =>
      system.totalCost == 0.0 ||
      system.suppressionCost == 0.0 && system.alarmCost == 0.0
    );
    notifyListeners();
  }
}
