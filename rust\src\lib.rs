use flutter_rust_bridge::frb;
use std::collections::HashMap;
use serde::{Deserialize, Serialize};

pub mod ai_engine;
pub mod tool_calling;
pub mod context;
pub mod models;

use ai_engine::AIEngine;
use tool_calling::{Tool<PERSON><PERSON>, ToolResult};
use context::AppContext;

/// Main AI Agent struct that manages the local LLM
pub struct FireToolAIAgent {
    engine: Option<AIEngine>,
    context: AppContext,
    conversation_history: Vec<ChatMessage>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ChatMessage {
    pub role: String, // "user", "assistant", "system"
    pub content: String,
    pub timestamp: i64,
    pub tool_calls: Option<Vec<ToolCall>>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AIResponse {
    pub message: String,
    pub tool_calls: Vec<ToolCall>,
    pub context_used: Vec<String>,
    pub processing_time_ms: u64,
}

#[derive(<PERSON>bug, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, Deserialize)]
pub struct ModelInfo {
    pub name: String,
    pub size_mb: u64,
    pub quantization: String,
    pub is_loaded: bool,
    pub supports_tools: bool,
}

impl FireToolAIAgent {
    /// Initialize the AI agent
    #[frb(sync)]
    pub fn new() -> Self {
        Self {
            engine: None,
            context: AppContext::new(),
            conversation_history: Vec::new(),
        }
    }

    /// Load a local LLM model
    #[frb(sync)]
    pub fn load_model(&mut self, model_path: String) -> Result<bool, String> {
        match AIEngine::new(&model_path) {
            Ok(engine) => {
                self.engine = Some(engine);
                Ok(true)
            }
            Err(e) => Err(format!("Failed to load model: {}", e)),
        }
    }

    /// Check if model is loaded and ready
    #[frb(sync)]
    pub fn is_ready(&self) -> bool {
        self.engine.is_some()
    }

    /// Get available models information
    #[frb(sync)]
    pub fn get_available_models() -> Vec<ModelInfo> {
        vec![
            ModelInfo {
                name: "Phi-2 Q4".to_string(),
                size_mb: 1600,
                quantization: "Q4_K_M".to_string(),
                is_loaded: false,
                supports_tools: true,
            },
            ModelInfo {
                name: "TinyLlama Q4".to_string(),
                size_mb: 800,
                quantization: "Q4_K_M".to_string(),
                is_loaded: false,
                supports_tools: true,
            },
        ]
    }

    /// Update app context with current state
    #[frb(sync)]
    pub fn update_context(&mut self, context_json: String) -> Result<(), String> {
        match serde_json::from_str(&context_json) {
            Ok(ctx) => {
                self.context = ctx;
                Ok(())
            }
            Err(e) => Err(format!("Invalid context JSON: {}", e)),
        }
    }

    /// Process user query with AI
    pub async fn process_query(&mut self, query: String) -> Result<AIResponse, String> {
        let engine = self.engine.as_mut()
            .ok_or("AI model not loaded")?;

        let start_time = std::time::Instant::now();

        // Add user message to history
        self.conversation_history.push(ChatMessage {
            role: "user".to_string(),
            content: query.clone(),
            timestamp: chrono::Utc::now().timestamp(),
            tool_calls: None,
        });

        // Build system prompt with context
        let system_prompt = self.build_system_prompt();
        
        // Generate response
        let response = engine.generate_response(&system_prompt, &query, &self.context).await?;
        
        // Parse tool calls if any
        let tool_calls = self.parse_tool_calls(&response)?;
        
        // Execute tool calls
        let executed_tools = self.execute_tool_calls(tool_calls).await?;

        // Add assistant message to history
        self.conversation_history.push(ChatMessage {
            role: "assistant".to_string(),
            content: response.clone(),
            timestamp: chrono::Utc::now().timestamp(),
            tool_calls: Some(executed_tools.clone()),
        });

        let processing_time = start_time.elapsed().as_millis() as u64;

        Ok(AIResponse {
            message: response,
            tool_calls: executed_tools,
            context_used: self.context.get_active_context_keys(),
            processing_time_ms: processing_time,
        })
    }

    /// Clear conversation history
    #[frb(sync)]
    pub fn clear_history(&mut self) {
        self.conversation_history.clear();
    }

    /// Get conversation history
    #[frb(sync)]
    pub fn get_history(&self) -> Vec<ChatMessage> {
        self.conversation_history.clone()
    }

    // Private helper methods
    fn build_system_prompt(&self) -> String {
        format!(
            r#"You are FireTool AI, an expert assistant for fire suppression system design and pricing.

Current Context:
- Screen: {}
- Project: {}
- Available Data: {}

You can use these tools:
- calculate_clean_agent: Calculate clean agent system requirements
- estimate_cost: Estimate project costs
- suggest_items: Suggest equipment items
- explain_field: Explain field meanings
- generate_boq: Generate Bill of Quantities

Always respond in a helpful, professional manner. Use tools when calculations or data access is needed."#,
            self.context.current_screen.as_deref().unwrap_or("Unknown"),
            self.context.project_name.as_deref().unwrap_or("No project"),
            self.context.available_data.join(", ")
        )
    }

    fn parse_tool_calls(&self, response: &str) -> Result<Vec<ToolCall>, String> {
        // Parse tool calls from LLM response
        // This will be implemented based on the specific format the model uses
        Ok(Vec::new())
    }

    async fn execute_tool_calls(&self, tool_calls: Vec<ToolCall>) -> Result<Vec<ToolCall>, String> {
        // Execute each tool call and return results
        // This will interface with your Flutter app's functions
        Ok(tool_calls)
    }
}

/// Initialize the AI agent system
#[frb(init)]
pub fn init_app() {
    flutter_rust_bridge::setup_default_user_utils();
}
