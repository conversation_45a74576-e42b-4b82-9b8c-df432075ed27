import 'dart:io';
import 'package:pdf/pdf.dart';
import 'package:pdf/widgets.dart' as pw;
import 'package:printing/printing.dart';
import 'package:excel/excel.dart';
import 'package:path_provider/path_provider.dart';
import 'package:intl/intl.dart';
import '../models/project.dart';

class ExportService {
  final currencyFormatter = NumberFormat.currency(symbol: '\$');

  // Export project to PDF
  Future<File> exportToPdf(Project project) async {
    final pdf = pw.Document();

    // Add title page
    pdf.addPage(
      pw.Page(
        pageFormat: PdfPageFormat.a4,
        build: (pw.Context context) {
          return pw.Center(
            child: pw.Column(
              mainAxisAlignment: pw.MainAxisAlignment.center,
              children: [
                pw.Text('Fire System Estimate', style: pw.TextStyle(fontSize: 24, fontWeight: pw.FontWeight.bold)),
                pw.SizedBox(height: 20),
                pw.Text(project.name, style: const pw.TextStyle(fontSize: 18)),
                pw.SizedBox(height: 10),
                if (project.clientName.isNotEmpty) pw.Text('Client: ${project.clientName}'),
                pw.Text('Date: ${DateFormat('MM/dd/yyyy').format(project.updatedAt)}'),
                pw.SizedBox(height: 40),
                pw.Text('Total Estimate: ${currencyFormatter.format(project.totalCost)}',
                  style: pw.TextStyle(fontSize: 16, fontWeight: pw.FontWeight.bold)),
              ],
            ),
          );
        },
      ),
    );

    // Add summary page
    pdf.addPage(
      pw.Page(
        pageFormat: PdfPageFormat.a4,
        build: (pw.Context context) {
          return pw.Column(
            crossAxisAlignment: pw.CrossAxisAlignment.start,
            children: [
              pw.Header(level: 0, text: 'Estimate Summary'),
              pw.SizedBox(height: 10),
              pw.Table.fromTextArray(
                context: context,
                data: <List<String>>[
                  ['System', 'Materials', 'Equipment', 'Labor', 'Total'],
                  ...project.systems.map((system) => [
                    system.name,
                    currencyFormatter.format(system.materialsCost),
                    currencyFormatter.format(system.equipmentCost),
                    currencyFormatter.format(system.laborCost),
                    currencyFormatter.format(system.totalCost),
                  ]),
                  [
                    'TOTAL',
                    currencyFormatter.format(project.systems.fold(0.0, (sum, system) => sum + system.materialsCost)),
                    currencyFormatter.format(project.systems.fold(0.0, (sum, system) => sum + system.equipmentCost)),
                    currencyFormatter.format(project.systems.fold(0.0, (sum, system) => sum + system.laborCost)),
                    currencyFormatter.format(project.totalCost),
                  ],
                ],
              ),
            ],
          );
        },
      ),
    );

    // Add detailed pages for each system
    for (var system in project.systems) {
      pdf.addPage(
        pw.Page(
          pageFormat: PdfPageFormat.a4,
          build: (pw.Context context) {
            return pw.Column(
              crossAxisAlignment: pw.CrossAxisAlignment.start,
              children: [
                pw.Header(level: 0, text: system.name),
                pw.SizedBox(height: 10),

                // Materials section
                pw.Header(level: 1, text: 'Materials'),
                pw.Table.fromTextArray(
                  context: context,
                  data: <List<String>>[
                    ['Item', 'Quantity', 'Unit', 'Unit Cost', 'Total'],
                    ...system.materials.map((item) => [
                      item.name,
                      item.quantity.toString(),
                      item.unit,
                      currencyFormatter.format(item.exWorksUnitCost),
                      currencyFormatter.format(item.totalCost),
                    ]),
                  ],
                ),
                pw.SizedBox(height: 10),

                // Equipment section
                pw.Header(level: 1, text: 'Equipment'),
                pw.Table.fromTextArray(
                  context: context,
                  data: <List<String>>[
                    ['Item', 'Quantity', 'Unit Cost', 'Total'],
                    ...system.equipment.map((item) => [
                      item.name,
                      item.quantity.toString(),
                      currencyFormatter.format(item.exWorksUnitCost),
                      currencyFormatter.format(item.totalCost),
                    ]),
                  ],
                ),
                pw.SizedBox(height: 10),

                // Services section
                pw.Header(level: 1, text: 'Services'),
                pw.Table.fromTextArray(
                  context: context,
                  data: <List<String>>[
                    ['Description', 'Category', 'Quantity', 'Unit', 'Rate', 'Total'],
                    ...system.services.map((item) => [
                      item.description,
                      item.category,
                      item.quantity.toString(),
                      item.unit,
                      currencyFormatter.format(item.unitRate),
                      currencyFormatter.format(item.totalCost),
                    ]),
                  ],
                ),

                pw.SizedBox(height: 20),
                pw.Text('System Total: ${currencyFormatter.format(system.totalCost)}',
                  style: pw.TextStyle(fontWeight: pw.FontWeight.bold)),
              ],
            );
          },
        ),
      );
    }

    // Save the PDF file
    final output = await getTemporaryDirectory();
    final file = File('${output.path}/${project.name.replaceAll(' ', '_')}_estimate.pdf');
    await file.writeAsBytes(await pdf.save());

    return file;
  }

  // Export project to Excel
  Future<File> exportToExcel(Project project) async {
    final excel = Excel.createExcel();

    // Remove the default sheet
    excel.delete('Sheet1');

    // Create summary sheet
    final summarySheet = excel['Summary'];

    // Add title and project info
    summarySheet.cell(CellIndex.indexByColumnRow(columnIndex: 0, rowIndex: 0)).value = TextCellValue('Fire System Estimate');
    summarySheet.cell(CellIndex.indexByColumnRow(columnIndex: 0, rowIndex: 1)).value = TextCellValue(project.name);
    if (project.clientName.isNotEmpty) {
      summarySheet.cell(CellIndex.indexByColumnRow(columnIndex: 0, rowIndex: 2)).value = TextCellValue('Client: ${project.clientName}');
    }
    summarySheet.cell(CellIndex.indexByColumnRow(columnIndex: 0, rowIndex: 3)).value = TextCellValue('Date: ${DateFormat('MM/dd/yyyy').format(project.updatedAt)}');

    // Add summary table headers
    summarySheet.cell(CellIndex.indexByColumnRow(columnIndex: 0, rowIndex: 6)).value = TextCellValue('System');
    summarySheet.cell(CellIndex.indexByColumnRow(columnIndex: 1, rowIndex: 6)).value = TextCellValue('Materials');
    summarySheet.cell(CellIndex.indexByColumnRow(columnIndex: 2, rowIndex: 6)).value = TextCellValue('Equipment');
    summarySheet.cell(CellIndex.indexByColumnRow(columnIndex: 3, rowIndex: 6)).value = TextCellValue('Services');
    summarySheet.cell(CellIndex.indexByColumnRow(columnIndex: 4, rowIndex: 6)).value = TextCellValue('Total');

    // Add system data
    int rowIndex = 7;
    for (var system in project.systems) {
      summarySheet.cell(CellIndex.indexByColumnRow(columnIndex: 0, rowIndex: rowIndex)).value = TextCellValue(system.name);
      summarySheet.cell(CellIndex.indexByColumnRow(columnIndex: 1, rowIndex: rowIndex)).value = DoubleCellValue(system.materialsCost);
      summarySheet.cell(CellIndex.indexByColumnRow(columnIndex: 2, rowIndex: rowIndex)).value = DoubleCellValue(system.equipmentCost);
      summarySheet.cell(CellIndex.indexByColumnRow(columnIndex: 3, rowIndex: rowIndex)).value = DoubleCellValue(system.servicesCost);
      summarySheet.cell(CellIndex.indexByColumnRow(columnIndex: 4, rowIndex: rowIndex)).value = DoubleCellValue(system.totalCost);
      rowIndex++;

      // Create a sheet for each system
      final systemSheet = excel[system.name];

      // Materials section
      systemSheet.cell(CellIndex.indexByColumnRow(columnIndex: 0, rowIndex: 0)).value = TextCellValue('Materials');
      systemSheet.cell(CellIndex.indexByColumnRow(columnIndex: 0, rowIndex: 1)).value = TextCellValue('Item');
      systemSheet.cell(CellIndex.indexByColumnRow(columnIndex: 1, rowIndex: 1)).value = TextCellValue('Quantity');
      systemSheet.cell(CellIndex.indexByColumnRow(columnIndex: 2, rowIndex: 1)).value = TextCellValue('Unit');
      systemSheet.cell(CellIndex.indexByColumnRow(columnIndex: 3, rowIndex: 1)).value = TextCellValue('Unit Cost');
      systemSheet.cell(CellIndex.indexByColumnRow(columnIndex: 4, rowIndex: 1)).value = TextCellValue('Total');

      int materialRowIndex = 2;
      for (var item in system.materials) {
        systemSheet.cell(CellIndex.indexByColumnRow(columnIndex: 0, rowIndex: materialRowIndex)).value = TextCellValue(item.name);
        systemSheet.cell(CellIndex.indexByColumnRow(columnIndex: 1, rowIndex: materialRowIndex)).value = DoubleCellValue(item.quantity);
        systemSheet.cell(CellIndex.indexByColumnRow(columnIndex: 2, rowIndex: materialRowIndex)).value = TextCellValue(item.unit);
        systemSheet.cell(CellIndex.indexByColumnRow(columnIndex: 3, rowIndex: materialRowIndex)).value = DoubleCellValue(item.exWorksUnitCost);
        systemSheet.cell(CellIndex.indexByColumnRow(columnIndex: 4, rowIndex: materialRowIndex)).value = DoubleCellValue(item.totalCost);
        materialRowIndex++;
      }

      // Equipment section
      systemSheet.cell(CellIndex.indexByColumnRow(columnIndex: 0, rowIndex: materialRowIndex + 1)).value = TextCellValue('Equipment');
      systemSheet.cell(CellIndex.indexByColumnRow(columnIndex: 0, rowIndex: materialRowIndex + 2)).value = TextCellValue('Item');
      systemSheet.cell(CellIndex.indexByColumnRow(columnIndex: 1, rowIndex: materialRowIndex + 2)).value = TextCellValue('Quantity');
      systemSheet.cell(CellIndex.indexByColumnRow(columnIndex: 2, rowIndex: materialRowIndex + 2)).value = TextCellValue('Unit Cost');
      systemSheet.cell(CellIndex.indexByColumnRow(columnIndex: 3, rowIndex: materialRowIndex + 2)).value = TextCellValue('Total');

      int equipmentRowIndex = materialRowIndex + 3;
      for (var item in system.equipment) {
        systemSheet.cell(CellIndex.indexByColumnRow(columnIndex: 0, rowIndex: equipmentRowIndex)).value = TextCellValue(item.name);
        systemSheet.cell(CellIndex.indexByColumnRow(columnIndex: 1, rowIndex: equipmentRowIndex)).value = DoubleCellValue(item.quantity);
        systemSheet.cell(CellIndex.indexByColumnRow(columnIndex: 2, rowIndex: equipmentRowIndex)).value = DoubleCellValue(item.exWorksUnitCost);
        systemSheet.cell(CellIndex.indexByColumnRow(columnIndex: 3, rowIndex: equipmentRowIndex)).value = DoubleCellValue(item.totalCost);
        equipmentRowIndex++;
      }

      // Services section
      systemSheet.cell(CellIndex.indexByColumnRow(columnIndex: 0, rowIndex: equipmentRowIndex + 1)).value = TextCellValue('Services');
      systemSheet.cell(CellIndex.indexByColumnRow(columnIndex: 0, rowIndex: equipmentRowIndex + 2)).value = TextCellValue('Description');
      systemSheet.cell(CellIndex.indexByColumnRow(columnIndex: 1, rowIndex: equipmentRowIndex + 2)).value = TextCellValue('Category');
      systemSheet.cell(CellIndex.indexByColumnRow(columnIndex: 2, rowIndex: equipmentRowIndex + 2)).value = TextCellValue('Quantity');
      systemSheet.cell(CellIndex.indexByColumnRow(columnIndex: 3, rowIndex: equipmentRowIndex + 2)).value = TextCellValue('Unit');
      systemSheet.cell(CellIndex.indexByColumnRow(columnIndex: 4, rowIndex: equipmentRowIndex + 2)).value = TextCellValue('Rate');
      systemSheet.cell(CellIndex.indexByColumnRow(columnIndex: 5, rowIndex: equipmentRowIndex + 2)).value = TextCellValue('Total');

      int servicesRowIndex = equipmentRowIndex + 3;
      for (var item in system.services) {
        systemSheet.cell(CellIndex.indexByColumnRow(columnIndex: 0, rowIndex: servicesRowIndex)).value = TextCellValue(item.description);
        systemSheet.cell(CellIndex.indexByColumnRow(columnIndex: 1, rowIndex: servicesRowIndex)).value = TextCellValue(item.category);
        systemSheet.cell(CellIndex.indexByColumnRow(columnIndex: 2, rowIndex: servicesRowIndex)).value = DoubleCellValue(item.quantity);
        systemSheet.cell(CellIndex.indexByColumnRow(columnIndex: 3, rowIndex: servicesRowIndex)).value = TextCellValue(item.unit);
        systemSheet.cell(CellIndex.indexByColumnRow(columnIndex: 4, rowIndex: servicesRowIndex)).value = DoubleCellValue(item.unitRate);
        systemSheet.cell(CellIndex.indexByColumnRow(columnIndex: 5, rowIndex: servicesRowIndex)).value = DoubleCellValue(item.totalCost);
        servicesRowIndex++;
      }

      // System total
      systemSheet.cell(CellIndex.indexByColumnRow(columnIndex: 0, rowIndex: servicesRowIndex + 1)).value = TextCellValue('System Total');
      systemSheet.cell(CellIndex.indexByColumnRow(columnIndex: 5, rowIndex: servicesRowIndex + 1)).value = DoubleCellValue(system.totalCost);
    }

    // Add total row
    summarySheet.cell(CellIndex.indexByColumnRow(columnIndex: 0, rowIndex: rowIndex)).value = TextCellValue('TOTAL');

    double totalMaterialsCost = project.systems.fold(0.0, (sum, system) => sum + system.materialsCost);
    double totalEquipmentCost = project.systems.fold(0.0, (sum, system) => sum + system.equipmentCost);
    double totalServicesCost = project.systems.fold(0.0, (sum, system) => sum + system.servicesCost);

    summarySheet.cell(CellIndex.indexByColumnRow(columnIndex: 1, rowIndex: rowIndex)).value = DoubleCellValue(totalMaterialsCost);
    summarySheet.cell(CellIndex.indexByColumnRow(columnIndex: 2, rowIndex: rowIndex)).value = DoubleCellValue(totalEquipmentCost);
    summarySheet.cell(CellIndex.indexByColumnRow(columnIndex: 3, rowIndex: rowIndex)).value = DoubleCellValue(totalServicesCost);
    summarySheet.cell(CellIndex.indexByColumnRow(columnIndex: 4, rowIndex: rowIndex)).value = DoubleCellValue(project.totalCost);

    // Save the Excel file
    final output = await getTemporaryDirectory();
    final file = File('${output.path}/${project.name.replaceAll(' ', '_')}_estimate.xlsx');
    await file.writeAsBytes(excel.encode()!);

    return file;
  }

  // Print PDF directly
  Future<void> printPdf(Project project) async {
    final pdf = await exportToPdf(project);
    await Printing.layoutPdf(
      onLayout: (_) => pdf.readAsBytes(),
      name: '${project.name} Estimate',
    );
  }
}
