import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:isar/isar.dart';
import 'constants/app_constants.dart';
import 'services/project_provider.dart';
import 'services/auth_service.dart';
import 'services/isar_service.dart';
import 'services/company_service.dart';
import 'services/flexible_database_service.dart';
import 'providers/sidebar_provider.dart';
import 'screens/home_screen.dart';
import 'screens/project_list_screen.dart';
import 'screens/new_project_screen.dart';
import 'screens/project_detail_screen.dart';
import 'screens/login_screen.dart';
import 'screens/splash_screen.dart';
import 'screens/admin_dashboard_main_screen.dart';
import 'screens/ai_assistant_screen.dart';
import 'models/isar_models.dart';
import 'repositories/clean_agent_repository.dart';

// Debug function to examine clean agent database
Future<void> _debugCleanAgentDatabase(Isar isar) async {
  try {
    print('\n=== DEBUGGING CLEAN AGENT DATABASE ===');

    // Find clean agent section
    final cleanAgentSection = await isar.sections
        .filter()
        .systemTypeEqualTo(SystemType.cleanAgent)
        .findFirst();

    if (cleanAgentSection != null) {
      print('Clean Agent Section found: ${cleanAgentSection.name} (sectionId: ${cleanAgentSection.sectionId})');

      // Find all flexible tables in clean agent section
      final cleanAgentTables = await isar.flexibleTables
          .filter()
          .sectionIdEqualTo(cleanAgentSection.sectionId)
          .findAll();

      print('Found ${cleanAgentTables.length} flexible tables in clean agent section:');
      for (final table in cleanAgentTables) {
        print('  - Table: ${table.name} (tableId: ${table.tableId})');

        // Get table columns
        final tableColumns = await isar.flexibleColumns
            .filter()
            .tableIdEqualTo(table.tableId)
            .sortByOrderIndex()
            .findAll();

        print('    Columns (${tableColumns.length}):');
        for (final column in tableColumns) {
          print('      - ${column.name} (${column.dataType}) [order: ${column.orderIndex}]');
        }

        // Get table data (rows)
        final tableRows = await isar.flexibleRows
            .filter()
            .tableIdEqualTo(table.tableId)
            .findAll();

        print('    Data rows (${tableRows.length}):');
        for (int i = 0; i < (tableRows.length > 5 ? 5 : tableRows.length); i++) {
          final row = tableRows[i];
          print('      Row ${i + 1}: ${row.data}');
        }
        if (tableRows.length > 5) {
          print('      ... and ${tableRows.length - 5} more rows');
        }
        print('');
      }
    }

    // Also list ALL sections and their tables
    final allSections = await isar.sections.where().findAll();
    print('\n=== ALL SECTIONS AND THEIR TABLES ===');
    for (final section in allSections) {
      print('Section: ${section.name} (${section.systemType}) - sectionId: ${section.sectionId}');

      final sectionTables = await isar.flexibleTables
          .filter()
          .sectionIdEqualTo(section.sectionId)
          .findAll();

      print('  Tables (${sectionTables.length}):');
      for (final table in sectionTables) {
        print('    - ${table.name} (${table.tableId})');
      }
      print('');
    }

    print('=== END DEBUG ===\n');
  } catch (e) {
    print('Error in debug function: $e');
  }
}

// Debug function to examine FlexibleDatabaseService (the real database!)
Future<void> _debugFlexibleDatabase() async {
  try {
    print('\n=== DEBUGGING FLEXIBLE DATABASE SERVICE ===');

    final flexibleService = FlexibleDatabaseService();

    // Get all databases
    final databases = await flexibleService.getAllDatabases();
    print('Found ${databases.length} databases:');

    for (final database in databases) {
      print('  Database: ${database.name} (ID: ${database.databaseId})');

      if (database.databaseId != null) {
        // Get tables for this database
        final tables = await flexibleService.getTablesForDatabase(database.databaseId!);
        print('    Tables (${tables.length}):');

        for (final table in tables) {
          print('      - ${table.name} (ID: ${table.tableId})');

          // Check if this is a clean agent related table
          if (table.name.toLowerCase().contains('clean') ||
              table.name.toLowerCase().contains('agent') ||
              table.name.toLowerCase().contains('fm200') ||
              table.name.toLowerCase().contains('novec') ||
              table.name.toLowerCase().contains('filling') ||
              table.name.toLowerCase().contains('concentration') ||
              table.name.toLowerCase().contains('pipes') ||
              table.name.toLowerCase().contains('alarm')) {
            print('        *** CLEAN AGENT TABLE FOUND! ***');
          }
        }
      }
      print('');
    }

    print('=== END FLEXIBLE DATABASE DEBUG ===\n');
  } catch (e) {
    print('Error debugging FlexibleDatabase: $e');
  }
}

// Debug function to search ALL tables in ISAR database
Future<void> _debugAllIsarTables(Isar isar) async {
  try {
    print('\n=== SEARCHING ALL ISAR TABLES ===');

    // Get ALL flexible tables regardless of section
    final allTables = await isar.flexibleTables.where().findAll();
    print('Found ${allTables.length} total flexible tables in ISAR:');

    for (final table in allTables) {
      print('  Table: ${table.name} (sectionId: ${table.sectionId}, tableId: ${table.tableId})');

      // Check if this matches your table names
      final tableName = table.name?.toLowerCase() ?? '';
      if (tableName.contains('min') || tableName.contains('max') || tableName.contains('filling') ||
          tableName.contains('design') || tableName.contains('concentration') ||
          tableName.contains('pipes') || tableName.contains('flow') || tableName.contains('price') ||
          tableName.contains('fm200') || tableName.contains('novec') ||
          tableName.contains('alarm') || tableName.contains('instal')) {
        print('    *** POTENTIAL CLEAN AGENT TABLE MATCH! ***');

        // Get columns for this table
        final columns = await isar.flexibleColumns
            .filter()
            .tableIdEqualTo(table.tableId)
            .findAll();
        print('    Columns (${columns.length}):');
        for (final column in columns) {
          print('      - ${column.name} (${column.dataType})');
        }

        // Get rows for this table
        final rows = await isar.flexibleRows
            .filter()
            .tableIdEqualTo(table.tableId)
            .findAll();
        print('    Rows: ${rows.length}');
      }
    }

    print('=== END ALL ISAR TABLES SEARCH ===\n');
  } catch (e) {
    print('Error searching all ISAR tables: $e');
  }
}

// Test function to verify CleanAgentRepository can access the real tables
Future<void> _testCleanAgentRepository(Isar isar) async {
  try {
    print('\n=== TESTING CLEAN AGENT REPOSITORY ===');

    final repository = CleanAgentRepository(isar);

    // Test getting agent types (this will trigger the debug output)
    final agentTypes = await repository.getAgentTypes();
    print('Repository returned ${agentTypes.length} agent types');

    // Test getting design concentrations
    final designConcentrations = await repository.getDesignConcentrations();
    print('Repository returned ${designConcentrations.length} design concentrations');

    print('=== END CLEAN AGENT REPOSITORY TEST ===\n');
  } catch (e) {
    print('Error testing CleanAgentRepository: $e');
  }
}

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // Initialize ISAR database
  try {
    final isarService = IsarService.instance;
    final isar = await isarService.database;
    debugPrint("ISAR database initialized successfully");

    // Debug clean agent database tables (commented out for production)
    // await _debugCleanAgentDatabase(isar);
    // await _debugAllIsarTables(isar);
    // await _testCleanAgentRepository(isar);
    // await _debugFlexibleDatabase();
  } catch (e) {
    debugPrint("Error initializing ISAR database: $e");
  }

  // Initialize company service and load current company configuration
  try {
    final companyService = CompanyService();
    await companyService.initializeCurrentCompany();
    debugPrint("Company service initialized successfully");
  } catch (e) {
    debugPrint("Error initializing company service: $e");
  }

  runApp(const MyApp());
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MultiProvider(
      providers: [
        // Auth service
        ChangeNotifierProvider(create: (context) => AuthService()),

        // ISAR service
        Provider(create: (context) => IsarService.instance),

        // Project provider
        ChangeNotifierProxyProvider<AuthService, ProjectProvider>(
          create: (context) => ProjectProvider(),
          update: (context, auth, previousProjectProvider) =>
            previousProjectProvider!..updateAuth(auth),
        ),

        // Sidebar provider
        ChangeNotifierProvider(create: (context) => SidebarProvider()),
      ],
      child: Consumer<AuthService>(
        builder: (context, authService, _) {
          return MaterialApp(
            title: AppConstants.appName,
            theme: AppConstants.getTheme(),
            initialRoute: '/',
            routes: {
              '/': (context) => const SplashScreen(),
              '/login': (context) => const LoginScreen(),
              '/home': (context) => const HomeScreen(),
              '/projects': (context) => const ProjectListScreen(),
              '/new_project': (context) => const NewProjectScreen(),
              '/project_detail': (context) => const ProjectDetailScreen(),
              '/unified_dashboard': (context) => const AdminDashboardMainScreen(),
              '/ai_assistant': (context) => const AIAssistantScreen(),
            },
          );
        },
      ),
    );
  }
}
