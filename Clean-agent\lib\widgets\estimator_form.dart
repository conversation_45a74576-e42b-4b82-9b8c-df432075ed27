import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../models/estimator_types.dart';
import '../providers/estimator_provider.dart';
import '../data/estimator_config.dart';

class EstimatorForm extends StatefulWidget {
  final EstimatorFormValues? defaultValues;

  const EstimatorForm({
    super.key,
    this.defaultValues,
  });

  @override
  State<EstimatorForm> createState() => _EstimatorFormState();
}

class _EstimatorFormState extends State<EstimatorForm> {
  final _formKey = GlobalKey<FormState>();
  
  // Form state
  AgentType _agentType = AgentType.novec1230;
  String _designConcentration = '';
  InputMode _inputMode = InputMode.dimensions;
  double _roomLength = 0;
  double _roomWidth = 0;
  double _roomHeight = 0;
  double _agentQuantity = 0;
  SystemType _systemType = SystemType.main;
  InstallationType _installationType = InstallationType.supplyOnly;

  @override
  void initState() {
    super.initState();
    _initializeFromDefaults();
  }

  void _initializeFromDefaults() {
    if (widget.defaultValues != null) {
      final defaults = widget.defaultValues!;
      _agentType = defaults.agentType;
      _designConcentration = defaults.designConcentration;
      _inputMode = defaults.inputMode;
      _roomLength = defaults.roomLength ?? 0;
      _roomWidth = defaults.roomWidth ?? 0;
      _roomHeight = defaults.roomHeight ?? 0;
      _agentQuantity = defaults.agentQuantity ?? 0;
      _systemType = defaults.systemType;
      _installationType = defaults.installationType;
    }
    
    // Set default design concentration if not set
    if (_designConcentration.isEmpty) {
      _setDefaultDesignConcentration();
    }
  }

  void _setDefaultDesignConcentration() {
    final agentKey = _agentType == AgentType.novec1230 ? 'NOVEC1230' : 'FM200';
    final factors = DesignFactors.factors[agentKey];
    if (factors != null) {
      // Find the default concentration
      try {
        final defaultEntry = factors.entries.where((entry) => entry.value['default'] == true).first;
        _designConcentration = defaultEntry.key;
      } catch (e) {
        if (factors.isNotEmpty) {
          _designConcentration = factors.keys.first;
        }
      }
    }
  }

  List<String> _getAvailableConcentrations() {
    final agentKey = _agentType == AgentType.novec1230 ? 'NOVEC1230' : 'FM200';
    return DesignFactors.factors[agentKey]?.keys.toList() ?? [];
  }

  void _handleSubmit() {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    // Validate inputs based on input mode
    if (_inputMode == InputMode.dimensions) {
      if (_roomLength <= 0 || _roomWidth <= 0 || _roomHeight <= 0) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Please enter valid room dimensions.'),
            backgroundColor: Colors.red,
          ),
        );
        return;
      }
    } else {
      if (_agentQuantity <= 0) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Please enter a valid agent quantity.'),
            backgroundColor: Colors.red,
          ),
        );
        return;
      }
    }

    // Create form data
    final formData = EstimatorFormValues(
      agentType: _agentType,
      designConcentration: _designConcentration,
      inputMode: _inputMode,
      roomLength: _inputMode == InputMode.dimensions ? _roomLength : null,
      roomWidth: _inputMode == InputMode.dimensions ? _roomWidth : null,
      roomHeight: _inputMode == InputMode.dimensions ? _roomHeight : null,
      agentQuantity: _inputMode == InputMode.agentQuantity ? _agentQuantity : null,
      systemType: _systemType,
      installationType: _installationType,
    );

    // Submit to provider
    context.read<EstimatorProvider>().calculateSystem(formData);
  }

  @override
  Widget build(BuildContext context) {
    return Form(
      key: _formKey,
      child: SingleChildScrollView(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
          Text(
            'System Configuration',
            style: Theme.of(context).textTheme.headlineSmall?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 16),
          
          // Agent Type
          DropdownButtonFormField<AgentType>(
            value: _agentType,
            decoration: const InputDecoration(
              labelText: 'Agent Type',
              border: OutlineInputBorder(),
            ),
            items: AgentType.values.map((type) {
              return DropdownMenuItem(
                value: type,
                child: Text(type == AgentType.novec1230 ? 'NOVEC 1230' : 'FM-200'),
              );
            }).toList(),
            onChanged: (value) {
              if (value != null) {
                setState(() {
                  _agentType = value;
                  _setDefaultDesignConcentration();
                });
              }
            },
          ),
          const SizedBox(height: 16),

          // Design Concentration
          DropdownButtonFormField<String>(
            value: _designConcentration.isNotEmpty ? _designConcentration : null,
            decoration: const InputDecoration(
              labelText: 'Design Concentration',
              border: OutlineInputBorder(),
            ),
            items: _getAvailableConcentrations().map((concentration) {
              return DropdownMenuItem(
                value: concentration,
                child: Text(concentration),
              );
            }).toList(),
            onChanged: (value) {
              if (value != null) {
                setState(() {
                  _designConcentration = value;
                });
              }
            },
            validator: (value) {
              if (value == null || value.isEmpty) {
                return 'Please select a design concentration';
              }
              return null;
            },
          ),
          const SizedBox(height: 16),

          // Input Mode
          Text(
            'Input Mode',
            style: Theme.of(context).textTheme.titleMedium,
          ),
          const SizedBox(height: 8),
          Row(
            children: [
              Expanded(
                child: RadioListTile<InputMode>(
                  title: const Text('Room Dimensions'),
                  value: InputMode.dimensions,
                  groupValue: _inputMode,
                  onChanged: (value) {
                    if (value != null) {
                      setState(() {
                        _inputMode = value;
                      });
                    }
                  },
                ),
              ),
              Expanded(
                child: RadioListTile<InputMode>(
                  title: const Text('Agent Quantity'),
                  value: InputMode.agentQuantity,
                  groupValue: _inputMode,
                  onChanged: (value) {
                    if (value != null) {
                      setState(() {
                        _inputMode = value;
                      });
                    }
                  },
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),

          // Input fields based on mode
          if (_inputMode == InputMode.dimensions) ...[
            Row(
              children: [
                Expanded(
                  child: TextFormField(
                    decoration: const InputDecoration(
                      labelText: 'Room Length (m)',
                      border: OutlineInputBorder(),
                    ),
                    keyboardType: TextInputType.number,
                    initialValue: _roomLength > 0 ? _roomLength.toString() : '',
                    onChanged: (value) {
                      _roomLength = double.tryParse(value) ?? 0;
                    },
                  ),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: TextFormField(
                    decoration: const InputDecoration(
                      labelText: 'Room Width (m)',
                      border: OutlineInputBorder(),
                    ),
                    keyboardType: TextInputType.number,
                    initialValue: _roomWidth > 0 ? _roomWidth.toString() : '',
                    onChanged: (value) {
                      _roomWidth = double.tryParse(value) ?? 0;
                    },
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            TextFormField(
              decoration: const InputDecoration(
                labelText: 'Room Height (m)',
                border: OutlineInputBorder(),
              ),
              keyboardType: TextInputType.number,
              initialValue: _roomHeight > 0 ? _roomHeight.toString() : '',
              onChanged: (value) {
                _roomHeight = double.tryParse(value) ?? 0;
              },
            ),
          ] else ...[
            TextFormField(
              decoration: const InputDecoration(
                labelText: 'Agent Quantity (kg)',
                border: OutlineInputBorder(),
              ),
              keyboardType: TextInputType.number,
              initialValue: _agentQuantity > 0 ? _agentQuantity.toString() : '',
              onChanged: (value) {
                _agentQuantity = double.tryParse(value) ?? 0;
              },
            ),
          ],
          const SizedBox(height: 16),

          // System Type
          Text(
            'System Type',
            style: Theme.of(context).textTheme.titleMedium,
          ),
          const SizedBox(height: 8),
          Column(
            children: [
              RadioListTile<SystemType>(
                title: const Text('Main System Only'),
                value: SystemType.main,
                groupValue: _systemType,
                onChanged: (value) {
                  if (value != null) {
                    setState(() {
                      _systemType = value;
                    });
                  }
                },
              ),
              RadioListTile<SystemType>(
                title: const Text('Main + Reserve System'),
                value: SystemType.mainAndReserve,
                groupValue: _systemType,
                onChanged: (value) {
                  if (value != null) {
                    setState(() {
                      _systemType = value;
                    });
                  }
                },
              ),
            ],
          ),
          const SizedBox(height: 16),

          // Installation Type
          Text(
            'Installation Type',
            style: Theme.of(context).textTheme.titleMedium,
          ),
          const SizedBox(height: 8),
          Column(
            children: [
              RadioListTile<InstallationType>(
                title: const Text('Supply Only'),
                value: InstallationType.supplyOnly,
                groupValue: _installationType,
                onChanged: (value) {
                  if (value != null) {
                    setState(() {
                      _installationType = value;
                    });
                  }
                },
              ),
              RadioListTile<InstallationType>(
                title: const Text('Supply & Installation'),
                value: InstallationType.supplyAndInstall,
                groupValue: _installationType,
                onChanged: (value) {
                  if (value != null) {
                    setState(() {
                      _installationType = value;
                    });
                  }
                },
              ),
            ],
          ),
          const SizedBox(height: 24),

          // Calculate button
          Consumer<EstimatorProvider>(
            builder: (context, provider, child) {
              return ElevatedButton(
                onPressed: provider.isLoading ? null : _handleSubmit,
                style: ElevatedButton.styleFrom(
                  padding: const EdgeInsets.symmetric(vertical: 16),
                ),
                child: provider.isLoading
                    ? const Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          SizedBox(
                            width: 20,
                            height: 20,
                            child: CircularProgressIndicator(strokeWidth: 2),
                          ),
                          SizedBox(width: 8),
                          Text('Calculating...'),
                        ],
                      )
                    : const Text(
                        'Calculate',
                        style: TextStyle(fontSize: 16),
                      ),
              );
            },
          ),
        ],
        ),
      ),
    );
  }
}
