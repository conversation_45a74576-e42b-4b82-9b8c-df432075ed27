// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'clean_agent_system.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

CleanAgentSystem _$CleanAgentSystemFromJson(Map<String, dynamic> json) =>
    CleanAgentSystem(
      id: json['id'] as String,
      name: json['name'] as String,
      description: json['description'] as String,
      quantity: (json['quantity'] as num?)?.toInt() ?? 1,
      roomLength: (json['roomLength'] as num).toDouble(),
      roomWidth: (json['roomWidth'] as num).toDouble(),
      roomHeight: (json['roomHeight'] as num).toDouble(),
      roomVolume: (json['roomVolume'] as num).toDouble(),
      agentType: $enumDecode(_$AgentTypeEnumMap, json['agentType']),
      designConcentration: (json['designConcentration'] as num).toDouble(),
      agentRequired: (json['agentRequired'] as num).toDouble(),
      actualAgent: (json['actualAgent'] as num).toDouble(),
      userInputAgentQuantity:
          (json['userInputAgentQuantity'] as num?)?.toDouble(),
      cylinderQty: (json['cylinderQty'] as num).toInt(),
      cylinderSize: (json['cylinderSize'] as num).toInt(),
      nozzleQty: (json['nozzleQty'] as num).toInt(),
      nozzleSize: (json['nozzleSize'] as num).toInt(),
      nozzlePipeLength: (json['nozzlePipeLength'] as num).toDouble(),
      manifoldPipeLength: (json['manifoldPipeLength'] as num).toDouble(),
      nozzlePipeSize: (json['nozzlePipeSize'] as num).toDouble(),
      manifoldPipeSize: (json['manifoldPipeSize'] as num).toDouble(),
      loopCableLength: (json['loopCableLength'] as num).toDouble(),
      powerCableLength: (json['powerCableLength'] as num).toDouble(),
      suppressionCost: (json['suppressionCost'] as num).toDouble(),
      alarmCost: (json['alarmCost'] as num).toDouble(),
      installationMaterialsCost:
          (json['installationMaterialsCost'] as num).toDouble(),
      installationLaborCost: (json['installationLaborCost'] as num).toDouble(),
      totalCost: (json['totalCost'] as num).toDouble(),
      bomItems: (json['bomItems'] as List<dynamic>?)
              ?.map((e) => e as Map<String, dynamic>)
              .toList() ??
          const [],
      bomSummaryData:
          json['bomSummaryData'] as Map<String, dynamic>? ?? const {},
      inputMode: $enumDecodeNullable(_$InputModeEnumMap, json['inputMode']) ??
          InputMode.dimensions,
      systemType:
          $enumDecodeNullable(_$SystemTypeEnumMap, json['systemType']) ??
              SystemType.main,
      installationType: $enumDecodeNullable(
              _$InstallationTypeEnumMap, json['installationType']) ??
          InstallationType.supplyOnly,
      installationFactor:
          (json['installationFactor'] as num?)?.toDouble() ?? 0.15,
      createdAt: DateTime.parse(json['createdAt'] as String),
      updatedAt: DateTime.parse(json['updatedAt'] as String),
    );

Map<String, dynamic> _$CleanAgentSystemToJson(CleanAgentSystem instance) =>
    <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'description': instance.description,
      'quantity': instance.quantity,
      'roomLength': instance.roomLength,
      'roomWidth': instance.roomWidth,
      'roomHeight': instance.roomHeight,
      'roomVolume': instance.roomVolume,
      'agentType': _$AgentTypeEnumMap[instance.agentType]!,
      'designConcentration': instance.designConcentration,
      'agentRequired': instance.agentRequired,
      'actualAgent': instance.actualAgent,
      'userInputAgentQuantity': instance.userInputAgentQuantity,
      'cylinderQty': instance.cylinderQty,
      'cylinderSize': instance.cylinderSize,
      'nozzleQty': instance.nozzleQty,
      'nozzleSize': instance.nozzleSize,
      'nozzlePipeLength': instance.nozzlePipeLength,
      'manifoldPipeLength': instance.manifoldPipeLength,
      'nozzlePipeSize': instance.nozzlePipeSize,
      'manifoldPipeSize': instance.manifoldPipeSize,
      'loopCableLength': instance.loopCableLength,
      'powerCableLength': instance.powerCableLength,
      'suppressionCost': instance.suppressionCost,
      'alarmCost': instance.alarmCost,
      'installationMaterialsCost': instance.installationMaterialsCost,
      'installationLaborCost': instance.installationLaborCost,
      'totalCost': instance.totalCost,
      'bomItems': instance.bomItems,
      'bomSummaryData': instance.bomSummaryData,
      'inputMode': _$InputModeEnumMap[instance.inputMode]!,
      'systemType': _$SystemTypeEnumMap[instance.systemType]!,
      'installationType': _$InstallationTypeEnumMap[instance.installationType]!,
      'installationFactor': instance.installationFactor,
      'createdAt': instance.createdAt.toIso8601String(),
      'updatedAt': instance.updatedAt.toIso8601String(),
    };

const _$AgentTypeEnumMap = {
  AgentType.novec1230: 'NOVEC1230',
  AgentType.fm200: 'FM200',
};

const _$InputModeEnumMap = {
  InputMode.dimensions: 'dimensions',
  InputMode.agentQuantity: 'agentQuantity',
};

const _$SystemTypeEnumMap = {
  SystemType.main: 'main',
  SystemType.reserve: 'reserve',
  SystemType.mainAndReserve: 'mainAndReserve',
};

const _$InstallationTypeEnumMap = {
  InstallationType.supplyOnly: 'supplyOnly',
  InstallationType.supplyAndInstall: 'supplyAndInstall',
};
