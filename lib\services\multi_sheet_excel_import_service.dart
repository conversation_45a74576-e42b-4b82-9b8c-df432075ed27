import 'dart:io';
import 'package:excel/excel.dart';
import '../models/isar_models.dart';
import 'dynamic_schema_service.dart';

class MultiSheetImportResult {
  final bool success;
  final String sectionName;
  final int totalTablesImported;
  final int totalRowsImported;
  final List<SheetImportInfo> importedSheets;
  final List<String> errors;

  MultiSheetImportResult({
    required this.success,
    required this.sectionName,
    required this.totalTablesImported,
    required this.totalRowsImported,
    required this.importedSheets,
    required this.errors,
  });
}

class SheetImportInfo {
  final String sheetName;
  final String subsectionName;
  final int rowsImported;

  SheetImportInfo({
    required this.sheetName,
    required this.subsectionName,
    required this.rowsImported,
  });
}

class MultiSheetExcelImportService {
  final DynamicSchemaService _schemaService = DynamicSchemaService.instance;

  Future<MultiSheetImportResult> importMultipleTablesFromWorkbook({
    required String filePath,
    required String sectionName,
    Map<String, String>? sheetToSubsectionMapping,
    bool createSubsectionsFromSheets = true,
    bool overwriteExisting = false,
  }) async {
    final errors = <String>[];
    final importedSheets = <SheetImportInfo>[];
    int totalRowsImported = 0;

    try {
      // Read Excel file
      final bytes = await File(filePath).readAsBytes();
      final excel = Excel.decodeBytes(bytes);

      if (excel.tables.isEmpty) {
        return MultiSheetImportResult(
          success: false,
          sectionName: sectionName,
          totalTablesImported: 0,
          totalRowsImported: 0,
          importedSheets: [],
          errors: ['No sheets found in Excel file'],
        );
      }

      // Get or create a section for the imported tables
      final sections = await _schemaService.getAllSections();
      SidebarSection? targetSection = sections.where((s) => s.name == sectionName).firstOrNull;

      if (targetSection == null) {
        // Create a new section
        final sectionId = await _schemaService.createSection(
          name: sectionName,
          icon: 'table_chart',
          systemType: SystemType.custom,
        );
        // Get the created section
        final allSections = await _schemaService.getAllSections();
        targetSection = allSections.where((s) => s.sectionId == sectionId).firstOrNull;
      }

      if (targetSection == null) {
        return MultiSheetImportResult(
          success: false,
          sectionName: sectionName,
          totalTablesImported: 0,
          totalRowsImported: 0,
          importedSheets: [],
          errors: ['Failed to create or find section'],
        );
      }

      // Process each sheet as a separate table
      for (final sheetName in excel.tables.keys) {
        try {
          final sheet = excel.tables[sheetName];
          if (sheet == null || sheet.rows.isEmpty) {
            errors.add('Sheet "$sheetName" is empty');
            continue;
          }

          // Import sheet data directly as a table
          final rowsImported = await _importSheetAsTable(
            targetSection.sectionId!,
            sheet,
            sheetName,
            overwriteExisting,
          );

          importedSheets.add(SheetImportInfo(
            sheetName: sheetName,
            subsectionName: sheetName,
            rowsImported: rowsImported,
          ));

          totalRowsImported += rowsImported;

        } catch (e) {
          errors.add('Error importing sheet "$sheetName": $e');
        }
      }

      return MultiSheetImportResult(
        success: importedSheets.isNotEmpty,
        sectionName: sectionName,
        totalTablesImported: importedSheets.length,
        totalRowsImported: totalRowsImported,
        importedSheets: importedSheets,
        errors: errors,
      );

    } catch (e) {
      return MultiSheetImportResult(
        success: false,
        sectionName: sectionName,
        totalTablesImported: 0,
        totalRowsImported: 0,
        importedSheets: [],
        errors: ['Failed to import workbook: $e'],
      );
    }
  }

  Future<int> _importSheetAsTable(
    String sectionId,
    Sheet sheet,
    String tableName,
    bool overwriteExisting,
  ) async {
    if (sheet.rows.isEmpty) return 0;

    // Get headers from first row
    final headerRow = sheet.rows.first;
    final headers = <String>[];

    for (final cell in headerRow) {
      final value = cell?.value?.toString() ?? '';
      if (value.isNotEmpty) {
        headers.add(value.trim());
      }
    }

    if (headers.isEmpty) return 0;

    // Check if table with this name already exists in this section
    final existingTables = await _schemaService.getTablesForSection(sectionId);
    FlexibleTable? existingTable = existingTables.where((table) => table.name == tableName).firstOrNull;

    String tableId;
    if (existingTable != null && overwriteExisting) {
      // Delete existing table
      await _schemaService.deleteTable(existingTable.tableId!);
      existingTable = null;
    }

    if (existingTable == null) {
      // Create new table - following the exact same pattern as working single import
      tableId = await _schemaService.createTable(
        name: tableName,
        sectionId: sectionId,
      );

      // Create columns - following the exact same pattern as working single import
      for (final header in headers) {
        await _schemaService.createColumn(
          name: header,
          tableId: tableId,
          dataType: ColumnDataType.text,
        );
      }
    } else {
      tableId = existingTable.tableId!;
    }

    // Import data rows (skip header row) - following the exact same pattern as working single import
    int rowsImported = 0;
    final dataRows = sheet.rows.skip(1).toList();

    for (int rowIndex = 0; rowIndex < dataRows.length; rowIndex++) {
      final row = dataRows[rowIndex];

      // Check if row has any data
      bool hasData = false;
      for (final cell in row) {
        if (cell?.value != null && cell!.value.toString().trim().isNotEmpty) {
          hasData = true;
          break;
        }
      }

      if (!hasData) continue;

      // Create a new row - following the exact same pattern as working single import
      final rowId = await _schemaService.createRow(tableId);

      // Prepare row data - following the exact same pattern as working single import
      final rowData = <String, dynamic>{};

      // Get columns for this table
      final columns = await _schemaService.getColumnsForTable(tableId);

      // Map data to columns - following the exact same pattern as working single import
      for (int colIndex = 0; colIndex < headers.length && colIndex < row.length; colIndex++) {
        final cell = row[colIndex];
        // Fix: Access cell.value to get calculated formula values, same as single import
        final value = cell?.value?.toString().trim() ?? '';
        final header = headers[colIndex];

        // Find the column for this header
        final column = columns.where((col) => col.name == header).firstOrNull;

        if (column != null && value.isNotEmpty) {
          rowData[column.columnId!] = value;
        }
      }

      // Update row data - following the exact same pattern as working single import
      await _schemaService.updateRowData(rowId, rowData);
      rowsImported++;
    }

    return rowsImported;
  }

}
