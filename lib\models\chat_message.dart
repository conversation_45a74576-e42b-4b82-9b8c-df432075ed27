/// Chat message model for AI assistant
class ChatMessage {
  final String role;
  final String content;
  final DateTime timestamp;
  final Map<String, dynamic>? metadata;

  ChatMessage({
    required this.role,
    required this.content,
    required this.timestamp,
    this.metadata,
  });

  /// Create a user message
  factory ChatMessage.user(String content) {
    return ChatMessage(
      role: 'user',
      content: content,
      timestamp: DateTime.now(),
    );
  }

  /// Create an assistant message
  factory ChatMessage.assistant(String content, {Map<String, dynamic>? metadata}) {
    return ChatMessage(
      role: 'assistant',
      content: content,
      timestamp: DateTime.now(),
      metadata: metadata,
    );
  }

  /// Create a system message
  factory ChatMessage.system(String content) {
    return ChatMessage(
      role: 'system',
      content: content,
      timestamp: DateTime.now(),
    );
  }

  /// Check if this is a user message
  bool get isUser => role == 'user';

  /// Check if this is an assistant message
  bool get isAssistant => role == 'assistant';

  /// Check if this is a system message
  bool get isSystem => role == 'system';

  /// Convert to JSON
  Map<String, dynamic> toJson() {
    return {
      'role': role,
      'content': content,
      'timestamp': timestamp.toIso8601String(),
      'metadata': metadata,
    };
  }

  /// Create from JSON
  factory ChatMessage.fromJson(Map<String, dynamic> json) {
    return ChatMessage(
      role: json['role'] as String,
      content: json['content'] as String,
      timestamp: DateTime.parse(json['timestamp'] as String),
      metadata: json['metadata'] as Map<String, dynamic>?,
    );
  }

  @override
  String toString() {
    return 'ChatMessage(role: $role, content: ${content.length > 50 ? '${content.substring(0, 50)}...' : content}, timestamp: $timestamp)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is ChatMessage &&
        other.role == role &&
        other.content == content &&
        other.timestamp == timestamp;
  }

  @override
  int get hashCode {
    return role.hashCode ^ content.hashCode ^ timestamp.hashCode;
  }
}
