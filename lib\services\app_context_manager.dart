import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'project_provider.dart';
import '../services/isar_service.dart';
import 'offline_ai_service.dart';

/// Manages app context for AI agent
class AppContextManager {
  static final AppContextManager _instance = AppContextManager._internal();
  factory AppContextManager() => _instance;
  AppContextManager._internal();

  String? _currentScreen;
  final Map<String, String> _userInput = {};
  Map<String, dynamic> _screenData = {};
  final List<String> _navigationHistory = [];

  /// Update current screen context
  void updateCurrentScreen(String screenName, {Map<String, dynamic>? data}) {
    _currentScreen = screenName;
    _screenData = data ?? {};
    
    // Add to navigation history
    if (_navigationHistory.isEmpty || _navigationHistory.last != screenName) {
      _navigationHistory.add(screenName);
      
      // Keep only last 10 screens
      if (_navigationHistory.length > 10) {
        _navigationHistory.removeAt(0);
      }
    }
  }

  /// Add user input context
  void addUserInput(String field, String value) {
    _userInput[field] = value;
    
    // Keep only last 20 inputs
    if (_userInput.length > 20) {
      final keys = _userInput.keys.toList();
      _userInput.remove(keys.first);
    }
  }

  /// Clear user input context
  void clearUserInput() {
    _userInput.clear();
  }

  /// Build complete app context for AI
  Future<AppContextData> buildContext(BuildContext context) async {
    final projectProvider = Provider.of<ProjectProvider>(context, listen: false);
    final isarService = Provider.of<IsarService>(context, listen: false);

    // Get database state
    final databaseState = await _buildDatabaseState(isarService);
    
    // Get calculation context
    final calculationContext = await _buildCalculationContext(projectProvider);

    return AppContextData(
      currentScreen: _currentScreen,
      projectName: projectProvider.currentProject?.name,
      projectCurrency: projectProvider.currentProject?.currency,
      availableData: _getAvailableDataSources(),
      userInput: Map.from(_userInput),
      databaseState: databaseState,
      calculationContext: calculationContext,
    );
  }

  /// Build database state context
  Future<DatabaseStateData> _buildDatabaseState(IsarService isarService) async {
    try {
      // Get sections and tables information
      // This would interface with your actual database structure
      
      return DatabaseStateData(
        sections: [
          'Clean Agent',
          'Fire Alarm',
          'Sprinkler',
          'Foam',
          'CO2',
        ],
        tables: [
          'FM200 Items',
          'NOVEC Items',
          'Alarm Components',
          'Pipes & Fittings',
          'Installation',
        ],
        recentItems: [
          'FM200 Cylinder 106L',
          'Discharge Nozzle',
          'Control Panel',
        ],
        totalRecords: 1250,
      );
    } catch (e) {
      return DatabaseStateData();
    }
  }

  /// Build calculation context
  Future<CalculationContextData> _buildCalculationContext(ProjectProvider projectProvider) async {
    try {
      final currentProject = projectProvider.currentProject;
      if (currentProject == null) {
        return CalculationContextData();
      }

      // Get recent calculations from clean agent systems
      final recentResults = currentProject.cleanAgentSystems
          .take(5)
          .map((system) => CalculationResultData(
                calculationType: 'clean_agent',
                inputs: {
                  'agent_weight': system.actualAgent,
                  'room_volume': system.roomVolume ?? 0.0,
                },
                result: system.totalCost,
                timestamp: DateTime.now().millisecondsSinceEpoch ~/ 1000,
              ))
          .toList();

      return CalculationContextData(
        lastCalculationType: recentResults.isNotEmpty ? 'clean_agent' : null,
        currentValues: _extractCurrentValues(),
        availableFormulas: [
          'Clean Agent Weight Calculation',
          'Nozzle Spacing',
          'Pipe Sizing',
          'Cost Estimation',
        ],
        recentResults: recentResults,
      );
    } catch (e) {
      return CalculationContextData();
    }
  }

  /// Extract current values from screen data and user input
  Map<String, double> _extractCurrentValues() {
    Map<String, double> values = {};

    // Extract numeric values from user input
    _userInput.forEach((key, value) {
      final numValue = double.tryParse(value);
      if (numValue != null) {
        values[key] = numValue;
      }
    });

    // Extract numeric values from screen data
    _screenData.forEach((key, value) {
      if (value is num) {
        values[key] = value.toDouble();
      } else if (value is String) {
        final numValue = double.tryParse(value);
        if (numValue != null) {
          values[key] = numValue;
        }
      }
    });

    return values;
  }

  /// Get available data sources
  List<String> _getAvailableDataSources() {
    List<String> sources = [];

    if (_currentScreen != null) {
      sources.add('current_screen');
    }

    if (_userInput.isNotEmpty) {
      sources.add('user_input');
    }

    if (_screenData.isNotEmpty) {
      sources.add('screen_data');
    }

    if (_navigationHistory.isNotEmpty) {
      sources.add('navigation_history');
    }

    return sources;
  }

  /// Get context summary for debugging
  String getContextSummary() {
    return '''
Current Screen: ${_currentScreen ?? 'Unknown'}
User Inputs: ${_userInput.length} fields
Screen Data: ${_screenData.length} items
Navigation: ${_navigationHistory.length} screens
Recent Screens: ${_navigationHistory.take(3).join(' → ')}
''';
  }

  /// Update AI service with current context
  Future<void> updateAIContext(BuildContext context) async {
    try {
      final contextData = await buildContext(context);
      await OfflineAIService().updateContext(contextData);
    } catch (e) {
      debugPrint('❌ Failed to update AI context: $e');
    }
  }

  /// Screen-specific context builders
  
  /// Build context for calculator screens
  Map<String, dynamic> buildCalculatorContext({
    required String calculatorType,
    required Map<String, dynamic> inputs,
    Map<String, dynamic>? results,
  }) {
    return {
      'screen_type': 'calculator',
      'calculator_type': calculatorType,
      'inputs': inputs,
      'results': results,
      'timestamp': DateTime.now().toIso8601String(),
    };
  }

  /// Build context for database screens
  Map<String, dynamic> buildDatabaseContext({
    required String section,
    required String table,
    List<Map<String, dynamic>>? selectedItems,
    String? searchQuery,
  }) {
    return {
      'screen_type': 'database',
      'section': section,
      'table': table,
      'selected_items': selectedItems,
      'search_query': searchQuery,
      'timestamp': DateTime.now().toIso8601String(),
    };
  }

  /// Build context for project screens
  Map<String, dynamic> buildProjectContext({
    required String projectName,
    required String currency,
    Map<String, int>? systemCounts,
    double? totalCost,
  }) {
    return {
      'screen_type': 'project',
      'project_name': projectName,
      'currency': currency,
      'system_counts': systemCounts,
      'total_cost': totalCost,
      'timestamp': DateTime.now().toIso8601String(),
    };
  }

  /// Build context for system screens
  Map<String, dynamic> buildSystemContext({
    required String systemType,
    required List<Map<String, dynamic>> systems,
    Map<String, dynamic>? selectedSystem,
  }) {
    return {
      'screen_type': 'system',
      'system_type': systemType,
      'systems': systems,
      'selected_system': selectedSystem,
      'systems_count': systems.length,
      'timestamp': DateTime.now().toIso8601String(),
    };
  }

  /// Getters for current state
  String? get currentScreen => _currentScreen;
  Map<String, String> get userInput => Map.from(_userInput);
  Map<String, dynamic> get screenData => Map.from(_screenData);
  List<String> get navigationHistory => List.from(_navigationHistory);

  /// Check if we're in a specific screen type
  bool isInCalculator() => _currentScreen?.contains('calculator') ?? false;
  bool isInDatabase() => _currentScreen?.contains('database') ?? false;
  bool isInProject() => _currentScreen?.contains('project') ?? false;
  bool isInSystem() => _currentScreen?.contains('system') ?? false;

  /// Get relevant context for current screen
  String getRelevantContext() {
    if (isInCalculator()) {
      return 'User is currently in a calculator screen. Focus on calculations, formulas, and technical assistance.';
    } else if (isInDatabase()) {
      return 'User is browsing database tables. Focus on data queries, item suggestions, and explanations.';
    } else if (isInProject()) {
      return 'User is managing project information. Focus on project overview, costs, and system summaries.';
    } else if (isInSystem()) {
      return 'User is working with fire suppression systems. Focus on system configuration and recommendations.';
    } else {
      return 'User is navigating the FireTool application. Provide general assistance and guidance.';
    }
  }
}
