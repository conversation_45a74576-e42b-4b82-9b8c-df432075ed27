import 'package:json_annotation/json_annotation.dart';

part 'estimator_types.g.dart';

enum AgentType {
  @JsonValue('NOVEC1230')
  novec1230,
  @JsonValue('FM200')
  fm200,
}

enum SystemType {
  @JsonValue('main')
  main,
  @JsonValue('reserve')
  reserve,
  @JsonValue('mainAndReserve')
  mainAndReserve,
}

enum InstallationType {
  @JsonValue('supplyOnly')
  supplyOnly,
  @JsonValue('supplyAndInstall')
  supplyAndInstall,
}

enum InputMode {
  @JsonValue('dimensions')
  dimensions,
  @JsonValue('agentQuantity')
  agentQuantity,
}

@JsonSerializable()
class EstimatorFormValues {
  final AgentType agentType;
  final String designConcentration;
  final InputMode inputMode;
  final double? roomLength;
  final double? roomWidth;
  final double? roomHeight;
  final double? agentQuantity;
  final SystemType systemType;
  final InstallationType installationType;

  EstimatorFormValues({
    required this.agentType,
    required this.designConcentration,
    required this.inputMode,
    this.roomLength,
    this.roomWidth,
    this.roomHeight,
    this.agentQuantity,
    required this.systemType,
    required this.installationType,
  });

  factory EstimatorFormValues.fromJson(Map<String, dynamic> json) =>
      _$EstimatorFormValuesFromJson(json);

  Map<String, dynamic> toJson() => _$EstimatorFormValuesToJson(this);
}

@JsonSerializable()
class RoomData {
  final double roomLength;
  final double roomWidth;
  final double roomHeight;
  final double roomArea;
  final double roomVolume;

  RoomData({
    required this.roomLength,
    required this.roomWidth,
    required this.roomHeight,
    required this.roomArea,
    required this.roomVolume,
  });

  factory RoomData.fromJson(Map<String, dynamic> json) =>
      _$RoomDataFromJson(json);

  Map<String, dynamic> toJson() => _$RoomDataToJson(this);
}

@JsonSerializable()
class CylinderData {
  final double targetFillSingleCyl;
  final double cylinderSizeLiters1stIter;
  final int numCylinders1stIter;
  final double qtyPerCylinder1stIter;
  final double cylinderSizeLiters2ndIter;
  final int numCylinders2ndIter;
  final double qtyPerCylinder;
  final double actualTotalKg;
  final double fillingRatio;

  CylinderData({
    required this.targetFillSingleCyl,
    required this.cylinderSizeLiters1stIter,
    required this.numCylinders1stIter,
    required this.qtyPerCylinder1stIter,
    required this.cylinderSizeLiters2ndIter,
    required this.numCylinders2ndIter,
    required this.qtyPerCylinder,
    required this.actualTotalKg,
    required this.fillingRatio,
  });

  factory CylinderData.fromJson(Map<String, dynamic> json) =>
      _$CylinderDataFromJson(json);

  Map<String, dynamic> toJson() => _$CylinderDataToJson(this);
}

@JsonSerializable()
class DischargeData {
  final double totalFlowRate;
  final int nozzleQty1stTrial;
  final double flowPerNozzle1stTrial;
  final int nozzleSize1stTrial;
  final int nozzleQtyFinal;
  final double flowPerNozzleFinal;
  final int nozzleSizeFinal;
  final int manifoldPipeSize;
  final int manifoldAssemblySize;

  DischargeData({
    required this.totalFlowRate,
    required this.nozzleQty1stTrial,
    required this.flowPerNozzle1stTrial,
    required this.nozzleSize1stTrial,
    required this.nozzleQtyFinal,
    required this.flowPerNozzleFinal,
    required this.nozzleSizeFinal,
    required this.manifoldPipeSize,
    required this.manifoldAssemblySize,
  });

  factory DischargeData.fromJson(Map<String, dynamic> json) =>
      _$DischargeDataFromJson(json);

  Map<String, dynamic> toJson() => _$DischargeDataToJson(this);
}

@JsonSerializable()
class DesignResults {
  final RoomData roomData;
  final double designFactor;
  final double totalAgentRequired;
  final CylinderData cylinder;
  final DischargeData discharge;
  final String systemType;

  DesignResults({
    required this.roomData,
    required this.designFactor,
    required this.totalAgentRequired,
    required this.cylinder,
    required this.discharge,
    required this.systemType,
  });

  factory DesignResults.fromJson(Map<String, dynamic> json) =>
      _$DesignResultsFromJson(json);

  Map<String, dynamic> toJson() => _$DesignResultsToJson(this);
}

@JsonSerializable()
class BomItem {
  final String partNo;
  final String description;
  final int quantity;
  final double unitCost;
  final double totalCost;
  final String manufacturer;
  final String category;
  final String? subcategory;
  final String? currency;

  BomItem({
    required this.partNo,
    required this.description,
    required this.quantity,
    required this.unitCost,
    required this.totalCost,
    required this.manufacturer,
    required this.category,
    this.subcategory,
    this.currency,
  });

  factory BomItem.fromJson(Map<String, dynamic> json) =>
      _$BomItemFromJson(json);

  Map<String, dynamic> toJson() => _$BomItemToJson(this);
}

@JsonSerializable()
class BomSummary {
  // Ex-works costs (USD)
  final double exWorksItemsUSD;

  // Landed costs (SAR) - ex-works converted + shipping
  final double landedCostSAR;

  // Installation materials (SAR) - pipes, cables, etc.
  final double installationMaterialsSAR;

  // Installation labor costs (SAR) - 15% FF + 20% FA
  final double installationLaborSAR;

  // Legacy fields for backward compatibility
  final double suppressionCost;
  final double alarmCost;
  final double installationItemsCost;
  final double suppressionInstallCost;
  final double alarmInstallCost;
  final double installationServicesInstallCost;
  final double totalSupplyCostUSD;
  final double totalSupplyCostSAR;
  final double totalInstallCostSAR;
  final double grandTotalSAR;
  final double marginFactor;
  final double marginAmountSAR;

  BomSummary({
    required this.exWorksItemsUSD,
    required this.landedCostSAR,
    required this.installationMaterialsSAR,
    required this.installationLaborSAR,
    required this.suppressionCost,
    required this.alarmCost,
    required this.installationItemsCost,
    required this.suppressionInstallCost,
    required this.alarmInstallCost,
    required this.installationServicesInstallCost,
    required this.totalSupplyCostUSD,
    required this.totalSupplyCostSAR,
    required this.totalInstallCostSAR,
    required this.grandTotalSAR,
    required this.marginFactor,
    required this.marginAmountSAR,
  });

  factory BomSummary.fromJson(Map<String, dynamic> json) =>
      _$BomSummaryFromJson(json);

  Map<String, dynamic> toJson() => _$BomSummaryToJson(this);
}

@JsonSerializable()
class QuotedSystem {
  final String id;
  final String name;
  final String date;
  final EstimatorFormValues inputData;
  final DesignResults designResults;
  final List<BomItem> bom;
  final BomSummary summary;

  QuotedSystem({
    required this.id,
    required this.name,
    required this.date,
    required this.inputData,
    required this.designResults,
    required this.bom,
    required this.summary,
  });

  factory QuotedSystem.fromJson(Map<String, dynamic> json) =>
      _$QuotedSystemFromJson(json);

  Map<String, dynamic> toJson() => _$QuotedSystemToJson(this);
}
