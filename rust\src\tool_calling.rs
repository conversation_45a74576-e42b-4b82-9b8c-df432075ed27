use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use anyhow::Result;

/// Tool call definition for LLM function calling
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ToolCall {
    pub id: String,
    pub function_name: String,
    pub parameters: HashMap<String, serde_json::Value>,
    pub result: Option<ToolResult>,
}

/// Result of a tool call execution
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ToolResult {
    pub success: bool,
    pub data: serde_json::Value,
    pub message: String,
    pub execution_time_ms: u64,
}

/// Available tools for the AI agent
pub struct ToolRegistry {
    tools: HashMap<String, ToolDefinition>,
}

#[derive(Debug, Clone)]
pub struct ToolDefinition {
    pub name: String,
    pub description: String,
    pub parameters: Vec<ToolParameter>,
    pub handler: fn(&HashMap<String, serde_json::Value>) -> Result<ToolResult>,
}

#[derive(Debug, <PERSON><PERSON>)]
pub struct ToolParameter {
    pub name: String,
    pub param_type: String,
    pub description: String,
    pub required: bool,
}

impl ToolRegistry {
    pub fn new() -> Self {
        let mut registry = Self {
            tools: HashMap::new(),
        };
        
        registry.register_default_tools();
        registry
    }

    /// Register all default FireTool AI tools
    fn register_default_tools(&mut self) {
        // Clean Agent Calculator
        self.register_tool(ToolDefinition {
            name: "calculate_clean_agent".to_string(),
            description: "Calculate clean agent system requirements (FM200, NOVEC)".to_string(),
            parameters: vec![
                ToolParameter {
                    name: "agent_type".to_string(),
                    param_type: "string".to_string(),
                    description: "Type of clean agent (FM200 or NOVEC)".to_string(),
                    required: true,
                },
                ToolParameter {
                    name: "room_volume".to_string(),
                    param_type: "number".to_string(),
                    description: "Room volume in cubic meters".to_string(),
                    required: true,
                },
                ToolParameter {
                    name: "design_concentration".to_string(),
                    param_type: "number".to_string(),
                    description: "Design concentration percentage".to_string(),
                    required: false,
                },
            ],
            handler: handle_calculate_clean_agent,
        });

        // Cost Estimation
        self.register_tool(ToolDefinition {
            name: "estimate_cost".to_string(),
            description: "Estimate project or system costs".to_string(),
            parameters: vec![
                ToolParameter {
                    name: "system_type".to_string(),
                    param_type: "string".to_string(),
                    description: "Type of system (clean_agent, fire_alarm, etc.)".to_string(),
                    required: true,
                },
                ToolParameter {
                    name: "components".to_string(),
                    param_type: "array".to_string(),
                    description: "List of components to estimate".to_string(),
                    required: true,
                },
                ToolParameter {
                    name: "currency".to_string(),
                    param_type: "string".to_string(),
                    description: "Currency for estimation (USD, SAR, etc.)".to_string(),
                    required: false,
                },
            ],
            handler: handle_estimate_cost,
        });

        // Item Suggestions
        self.register_tool(ToolDefinition {
            name: "suggest_items".to_string(),
            description: "Suggest equipment items based on requirements".to_string(),
            parameters: vec![
                ToolParameter {
                    name: "category".to_string(),
                    param_type: "string".to_string(),
                    description: "Equipment category".to_string(),
                    required: true,
                },
                ToolParameter {
                    name: "specifications".to_string(),
                    param_type: "object".to_string(),
                    description: "Technical specifications".to_string(),
                    required: false,
                },
            ],
            handler: handle_suggest_items,
        });

        // Field Explanations
        self.register_tool(ToolDefinition {
            name: "explain_field".to_string(),
            description: "Explain the meaning and purpose of form fields".to_string(),
            parameters: vec![
                ToolParameter {
                    name: "field_name".to_string(),
                    param_type: "string".to_string(),
                    description: "Name of the field to explain".to_string(),
                    required: true,
                },
                ToolParameter {
                    name: "context".to_string(),
                    param_type: "string".to_string(),
                    description: "Context where the field appears".to_string(),
                    required: false,
                },
            ],
            handler: handle_explain_field,
        });

        // BOQ Generation
        self.register_tool(ToolDefinition {
            name: "generate_boq".to_string(),
            description: "Generate Bill of Quantities for a system".to_string(),
            parameters: vec![
                ToolParameter {
                    name: "system_data".to_string(),
                    param_type: "object".to_string(),
                    description: "System configuration data".to_string(),
                    required: true,
                },
                ToolParameter {
                    name: "include_installation".to_string(),
                    param_type: "boolean".to_string(),
                    description: "Include installation costs".to_string(),
                    required: false,
                },
            ],
            handler: handle_generate_boq,
        });
    }

    /// Register a new tool
    pub fn register_tool(&mut self, tool: ToolDefinition) {
        self.tools.insert(tool.name.clone(), tool);
    }

    /// Get tool definition
    pub fn get_tool(&self, name: &str) -> Option<&ToolDefinition> {
        self.tools.get(name)
    }

    /// Execute a tool call
    pub async fn execute_tool(&self, tool_call: &mut ToolCall) -> Result<()> {
        let start_time = std::time::Instant::now();

        if let Some(tool) = self.tools.get(&tool_call.function_name) {
            let result = (tool.handler)(&tool_call.parameters)?;
            tool_call.result = Some(result);
        } else {
            tool_call.result = Some(ToolResult {
                success: false,
                data: serde_json::Value::Null,
                message: format!("Unknown tool: {}", tool_call.function_name),
                execution_time_ms: 0,
            });
        }

        // Update execution time
        if let Some(ref mut result) = tool_call.result {
            result.execution_time_ms = start_time.elapsed().as_millis() as u64;
        }

        Ok(())
    }

    /// Get all available tools for LLM prompt
    pub fn get_tools_schema(&self) -> String {
        let mut schema = String::from("Available tools:\n");
        
        for tool in self.tools.values() {
            schema.push_str(&format!("- {}: {}\n", tool.name, tool.description));
            for param in &tool.parameters {
                let required = if param.required { " (required)" } else { "" };
                schema.push_str(&format!("  - {}: {} - {}{}\n", 
                    param.name, param.param_type, param.description, required));
            }
            schema.push('\n');
        }

        schema
    }
}

// Tool handler functions (these will interface with Flutter/Dart code)

fn handle_calculate_clean_agent(params: &HashMap<String, serde_json::Value>) -> Result<ToolResult> {
    // This will call back to Flutter/Dart calculation functions
    Ok(ToolResult {
        success: true,
        data: serde_json::json!({
            "agent_weight": 65.0,
            "cylinder_size": "106L",
            "nozzles_required": 4,
            "estimated_cost": 15000.0
        }),
        message: "Clean agent calculation completed".to_string(),
        execution_time_ms: 0,
    })
}

fn handle_estimate_cost(params: &HashMap<String, serde_json::Value>) -> Result<ToolResult> {
    // This will interface with your pricing database
    Ok(ToolResult {
        success: true,
        data: serde_json::json!({
            "total_cost": 25000.0,
            "breakdown": {
                "equipment": 20000.0,
                "installation": 5000.0
            }
        }),
        message: "Cost estimation completed".to_string(),
        execution_time_ms: 0,
    })
}

fn handle_suggest_items(params: &HashMap<String, serde_json::Value>) -> Result<ToolResult> {
    // This will query your equipment database
    Ok(ToolResult {
        success: true,
        data: serde_json::json!({
            "suggestions": [
                {"name": "FM200 Cylinder 106L", "price": 2500.0},
                {"name": "Discharge Nozzle", "price": 150.0}
            ]
        }),
        message: "Item suggestions generated".to_string(),
        execution_time_ms: 0,
    })
}

fn handle_explain_field(params: &HashMap<String, serde_json::Value>) -> Result<ToolResult> {
    // This will provide field explanations
    Ok(ToolResult {
        success: true,
        data: serde_json::json!({
            "explanation": "This field represents the design concentration percentage for clean agent systems."
        }),
        message: "Field explanation provided".to_string(),
        execution_time_ms: 0,
    })
}

fn handle_generate_boq(params: &HashMap<String, serde_json::Value>) -> Result<ToolResult> {
    // This will generate BOQ using your existing logic
    Ok(ToolResult {
        success: true,
        data: serde_json::json!({
            "boq_items": [
                {"description": "FM200 System 65kg", "quantity": 1, "unit_price": 15000.0}
            ],
            "total": 15000.0
        }),
        message: "BOQ generated successfully".to_string(),
        execution_time_ms: 0,
    })
}
