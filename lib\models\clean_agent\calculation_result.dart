import 'design_concentration.dart';
import 'bom_item.dart';

class CalculationResult {
  final double agentWeight;
  final int cylinderQuantity;
  final int cylinderSize;
  final double flowRate;
  final double pipeSize;
  final int nozzleQuantity;
  final List<BomItem> bomItems;
  final double totalCost;
  final DesignConcentration designConcentration;

  CalculationResult({
    required this.agentWeight,
    required this.cylinderQuantity,
    required this.cylinderSize,
    required this.flowRate,
    required this.pipeSize,
    required this.nozzleQuantity,
    required this.bomItems,
    required this.totalCost,
    required this.designConcentration,
  });

  @override
  String toString() {
    return '''
CalculationResult:
  Agent Weight: ${agentWeight.toStringAsFixed(2)} kg
  Cylinders: $cylinderQuantity x ${cylinderSize}L
  Flow Rate: ${flowRate.toStringAsFixed(2)} kg/sec
  Pipe Size: ${pipeSize.toStringAsFixed(0)}mm
  Nozzles: $nozzleQuantity
  Total Cost: \$${totalCost.toStringAsFixed(2)}
  BOM Items: ${bomItems.length}
''';
  }
}
