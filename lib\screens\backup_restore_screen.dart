import 'package:flutter/material.dart';
import 'package:file_picker/file_picker.dart';
import '../constants/app_constants.dart';
import '../services/backup_service.dart';
import '../services/company_service.dart';
import '../services/supabase_sync_service.dart';
import '../services/dynamic_schema_service.dart';
import '../widgets/multi_sheet_excel_import_dialog.dart';

class BackupRestoreScreen extends StatefulWidget {
  const BackupRestoreScreen({super.key});

  @override
  State<BackupRestoreScreen> createState() => _BackupRestoreScreenState();
}

class _BackupRestoreScreenState extends State<BackupRestoreScreen> {
  final CompanyService _companyService = CompanyService();
  late final BackupService _backupService;
  late final SupabaseSyncService _supabaseService;
  bool _isLoading = false;
  String? _statusMessage;

  @override
  void initState() {
    super.initState();
    _backupService = BackupService();
    _supabaseService = SupabaseSyncService(DynamicSchemaService.instance);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Backup & Restore'),
        backgroundColor: AppConstants.primaryColor,
        foregroundColor: Colors.white,
        elevation: 2,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () => Navigator.of(context).pop(),
        ),
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(24.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header
            Row(
              children: [
                const Icon(
                  Icons.backup,
                  color: AppConstants.primaryColor,
                  size: 32,
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Text(
                        'Backup & Restore',
                        style: TextStyle(
                          fontSize: 24,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      Text(
                        'Manage your application data backups and Supabase synchronization',
                        style: TextStyle(
                          color: Colors.grey.shade600,
                          fontSize: 14,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
            
            const SizedBox(height: 32),
            
            // Status message
            if (_statusMessage != null)
              Container(
                padding: const EdgeInsets.all(16),
                margin: const EdgeInsets.only(bottom: 24),
                decoration: BoxDecoration(
                  color: _statusMessage!.contains('Error') 
                    ? Colors.red.shade50 
                    : Colors.green.shade50,
                  border: Border.all(
                    color: _statusMessage!.contains('Error') 
                      ? Colors.red.shade200 
                      : Colors.green.shade200,
                  ),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Row(
                  children: [
                    Icon(
                      _statusMessage!.contains('Error') 
                        ? Icons.error_outline 
                        : Icons.check_circle_outline,
                      color: _statusMessage!.contains('Error') 
                        ? Colors.red.shade700 
                        : Colors.green.shade700,
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: Text(
                        _statusMessage!,
                        style: TextStyle(
                          color: _statusMessage!.contains('Error') 
                            ? Colors.red.shade700 
                            : Colors.green.shade700,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            
            // Loading indicator
            if (_isLoading)
              const LinearProgressIndicator(),
            
            const SizedBox(height: 24),
            
            // Responsive layout based on screen size
            LayoutBuilder(
              builder: (context, constraints) {
                final isWideScreen = constraints.maxWidth > 800;

                if (isWideScreen) {
                  // Two-column layout for wide screens
                  return Row(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Left column - Local Backup
                      Expanded(
                        child: _buildSection(
                          title: 'Local Backup',
                          icon: Icons.save_alt,
                          children: [
                            _buildActionCard(
                              title: 'Create Full Backup',
                              subtitle: 'Export all application data to a backup file',
                              icon: Icons.download,
                              onTap: _createBackup,
                              color: Colors.blue,
                            ),
                            const SizedBox(height: 16),
                            _buildActionCard(
                              title: 'Restore from Backup',
                              subtitle: 'Import data from a previously created backup file',
                              icon: Icons.upload,
                              onTap: _restoreBackup,
                              color: Colors.orange,
                            ),
                          ],
                        ),
                      ),

                      const SizedBox(width: 32),

                      // Right column - Supabase sync
                      Expanded(
                        child: _buildSection(
                          title: 'Database Tables Synchronization',
                          icon: Icons.cloud_sync,
                          children: [
                            Container(
                              padding: const EdgeInsets.all(16),
                              margin: const EdgeInsets.only(bottom: 16),
                              decoration: BoxDecoration(
                                color: Colors.blue.shade50,
                                border: Border.all(color: Colors.blue.shade200),
                                borderRadius: BorderRadius.circular(8),
                              ),
                              child: Row(
                                children: [
                                  Icon(
                                    Icons.info_outline,
                                    color: Colors.blue.shade700,
                                    size: 20,
                                  ),
                                  const SizedBox(width: 12),
                                  Expanded(
                                    child: Text(
                                      'These operations sync your database tables (sections, tables, columns, and row data) with Supabase. Make sure you have configured your company Supabase settings first.',
                                      style: TextStyle(
                                        color: Colors.blue.shade700,
                                        fontSize: 14,
                                      ),
                                    ),
                                  ),
                                ],
                              ),
                            ),
                            _buildActionCard(
                              title: 'Upload All Database Tables to Supabase',
                              subtitle: 'Sync all local tables and data to your Supabase database',
                              icon: Icons.cloud_upload,
                              onTap: _uploadAllToSupabase,
                              color: Colors.green,
                            ),
                            const SizedBox(height: 16),
                            _buildActionCard(
                              title: 'Download All Database Tables from Supabase',
                              subtitle: 'Sync all tables and data from Supabase to local database',
                              icon: Icons.cloud_download,
                              onTap: _downloadAllFromSupabase,
                              color: Colors.purple,
                            ),
                          ],
                        ),
                      ),
                    ],
                  );
                } else {
                  // Single column layout for narrow screens
                  return Column(
                    children: [
                      // Local Backup section
                      _buildSection(
                        title: 'Local Backup',
                        icon: Icons.save_alt,
                        children: [
                          _buildActionCard(
                            title: 'Create Full Backup',
                            subtitle: 'Export all application data to a backup file',
                            icon: Icons.download,
                            onTap: _createBackup,
                            color: Colors.blue,
                          ),
                          const SizedBox(height: 16),
                          _buildActionCard(
                            title: 'Restore from Backup',
                            subtitle: 'Import data from a previously created backup file',
                            icon: Icons.upload,
                            onTap: _restoreBackup,
                            color: Colors.orange,
                          ),
                          const SizedBox(height: 16),
                          _buildActionCard(
                            title: 'Import Excel Workbook',
                            subtitle: 'Import multiple tables from Excel sheets as sections/subsections',
                            icon: Icons.table_chart,
                            onTap: _showMultiSheetImportDialog,
                            color: Colors.green,
                          ),
                          const SizedBox(height: 16),
                          _buildActionCard(
                            title: 'Import Excel Workbook',
                            subtitle: 'Import multiple tables from Excel sheets as sections/subsections',
                            icon: Icons.table_chart,
                            onTap: _showMultiSheetImportDialog,
                            color: Colors.green,
                          ),
                        ],
                      ),

                      const SizedBox(height: 32),

                      // Supabase sync section
                      _buildSection(
                        title: 'Database Tables Synchronization',
                        icon: Icons.cloud_sync,
                        children: [
                          Container(
                            padding: const EdgeInsets.all(16),
                            margin: const EdgeInsets.only(bottom: 16),
                            decoration: BoxDecoration(
                              color: Colors.blue.shade50,
                              border: Border.all(color: Colors.blue.shade200),
                              borderRadius: BorderRadius.circular(8),
                            ),
                            child: Row(
                              children: [
                                Icon(
                                  Icons.info_outline,
                                  color: Colors.blue.shade700,
                                  size: 20,
                                ),
                                const SizedBox(width: 12),
                                Expanded(
                                  child: Text(
                                    'These operations sync your database tables (sections, tables, columns, and row data) with Supabase. Make sure you have configured your company Supabase settings first.',
                                    style: TextStyle(
                                      color: Colors.blue.shade700,
                                      fontSize: 14,
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          ),
                          _buildActionCard(
                            title: 'Upload All Database Tables to Supabase',
                            subtitle: 'Sync all local tables and data to your Supabase database',
                            icon: Icons.cloud_upload,
                            onTap: _uploadAllToSupabase,
                            color: Colors.green,
                          ),
                          const SizedBox(height: 16),
                          _buildActionCard(
                            title: 'Download All Database Tables from Supabase',
                            subtitle: 'Sync all tables and data from Supabase to local database',
                            icon: Icons.cloud_download,
                            onTap: _downloadAllFromSupabase,
                            color: Colors.purple,
                          ),
                        ],
                      ),
                    ],
                  );
                }
              },
            ),

            // Add some bottom spacing
            const SizedBox(height: 32),
          ],
        ),
      ),
    );
  }

  Widget _buildSection({
    required String title,
    required IconData icon,
    required List<Widget> children,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Icon(icon, color: AppConstants.primaryColor),
            const SizedBox(width: 12),
            Text(
              title,
              style: const TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
              ),
            ),
          ],
        ),
        const SizedBox(height: 16),
        ...children,
      ],
    );
  }

  Widget _buildActionCard({
    required String title,
    required String subtitle,
    required IconData icon,
    required VoidCallback onTap,
    required Color color,
  }) {
    return Card(
      elevation: 2,
      child: InkWell(
        onTap: _isLoading ? null : onTap,
        borderRadius: BorderRadius.circular(8),
        child: Padding(
          padding: const EdgeInsets.all(20),
          child: Row(
            children: [
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: color.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(
                  icon,
                  color: color,
                  size: 24,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      title,
                      style: const TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      subtitle,
                      style: TextStyle(
                        color: Colors.grey.shade600,
                        fontSize: 14,
                      ),
                    ),
                  ],
                ),
              ),
              Icon(
                Icons.arrow_forward_ios,
                color: Colors.grey.shade400,
                size: 16,
              ),
            ],
          ),
        ),
      ),
    );
  }

  Future<void> _createBackup() async {
    setState(() {
      _isLoading = true;
      _statusMessage = null;
    });

    try {
      final result = await _backupService.createFullBackup();
      setState(() {
        _statusMessage = 'Backup created successfully: ${result['filename']}';
      });
    } catch (e) {
      setState(() {
        _statusMessage = 'Error creating backup: $e';
      });
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _restoreBackup() async {
    final result = await FilePicker.platform.pickFiles(
      type: FileType.custom,
      allowedExtensions: ['json'],
    );

    if (result != null && result.files.single.path != null) {
      setState(() {
        _isLoading = true;
        _statusMessage = null;
      });

      try {
        await _backupService.restoreFromBackup(result.files.single.path!);
        setState(() {
          _statusMessage = 'Backup restored successfully!';
        });
      } catch (e) {
        setState(() {
          _statusMessage = 'Error restoring backup: $e';
        });
      } finally {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  Future<void> _uploadAllToSupabase() async {
    setState(() {
      _isLoading = true;
      _statusMessage = null;
    });

    try {
      final result = await _backupService.uploadAllToSupabase();
      setState(() {
        _statusMessage = 'All database tables uploaded to Supabase successfully! ${result['tables_uploaded']} of ${result['total_tables']} tables uploaded.';
      });
    } catch (e) {
      setState(() {
        _statusMessage = 'Error uploading database tables to Supabase: $e';
      });
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _downloadAllFromSupabase() async {
    setState(() {
      _isLoading = true;
      _statusMessage = null;
    });

    try {
      final result = await _backupService.downloadAllFromSupabase();
      setState(() {
        _statusMessage = 'All database tables downloaded from Supabase successfully! ${result['tables_downloaded']} of ${result['total_tables']} tables downloaded.';
      });
    } catch (e) {
      setState(() {
        _statusMessage = 'Error downloading database tables from Supabase: $e';
      });
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  void _showMultiSheetImportDialog() {
    showDialog(
      context: context,
      builder: (context) => const MultiSheetExcelImportDialog(),
    );
  }
}
