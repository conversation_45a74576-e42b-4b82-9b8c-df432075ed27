import 'package:uuid/uuid.dart';

enum UserRole {
  user,
  admin,
}

class User {
  final String id;
  final String username;
  final String passwordHash; // In a real app, use proper password hashing
  final UserRole role;
  final DateTime createdAt;
  final DateTime lastLoginAt;

  User({
    String? id,
    required this.username,
    required this.passwordHash,
    this.role = UserRole.user,
    DateTime? createdAt,
    DateTime? lastLoginAt,
  }) : 
    id = id ?? const Uuid().v4(),
    createdAt = createdAt ?? DateTime.now(),
    lastLoginAt = lastLoginAt ?? DateTime.now();

  bool get isAdmin => role == UserRole.admin;

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'username': username,
      'passwordHash': passwordHash,
      'role': role.toString(),
      'createdAt': createdAt.toIso8601String(),
      'lastLoginAt': lastLoginAt.toIso8601String(),
    };
  }

  factory User.fromMap(Map<String, dynamic> map) {
    return User(
      id: map['id'],
      username: map['username'],
      passwordHash: map['passwordHash'],
      role: map['role'] == UserRole.admin.toString() ? UserRole.admin : UserRole.user,
      createdAt: DateTime.parse(map['createdAt']),
      lastLoginAt: DateTime.parse(map['lastLoginAt']),
    );
  }

  User copyWith({
    String? id,
    String? username,
    String? passwordHash,
    UserRole? role,
    DateTime? createdAt,
    DateTime? lastLoginAt,
  }) {
    return User(
      id: id ?? this.id,
      username: username ?? this.username,
      passwordHash: passwordHash ?? this.passwordHash,
      role: role ?? this.role,
      createdAt: createdAt ?? this.createdAt,
      lastLoginAt: lastLoginAt ?? this.lastLoginAt,
    );
  }
}
