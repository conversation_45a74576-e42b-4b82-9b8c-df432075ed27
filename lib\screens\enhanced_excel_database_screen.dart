import 'package:flutter/material.dart';
import 'package:file_picker/file_picker.dart';
import 'package:file_selector/file_selector.dart';
import '../services/enhanced_database_service.dart';
import '../widgets/enhanced_excel_grid.dart';
import '../widgets/multi_sheet_excel_import_dialog.dart';
import '../services/auth_service.dart';
import 'dart:io';

class EnhancedExcelDatabaseScreen extends StatefulWidget {
  const EnhancedExcelDatabaseScreen({super.key});

  @override
  State<EnhancedExcelDatabaseScreen> createState() => _EnhancedExcelDatabaseScreenState();
}

class _EnhancedExcelDatabaseScreenState extends State<EnhancedExcelDatabaseScreen>
    with TickerProviderStateMixin {
  final EnhancedDatabaseService _dbService = EnhancedDatabaseService();
  final AuthService _authService = AuthService();
  
  List<DatabaseSection> _sections = [];
  DatabaseSection? _selectedSection;
  List<TableSchema> _currentTables = [];
  TableSchema? _selectedTable;
  TabController? _tabController;
  
  bool _isLoading = false;
  bool _hasUnsavedChanges = false;
  
  @override
  void initState() {
    super.initState();
    _loadSections();
  }
  
  @override
  void dispose() {
    _tabController?.dispose();
    super.dispose();
  }
  
  Future<void> _loadSections() async {
    setState(() {
      _isLoading = true;
    });
    
    try {
      _sections = await _dbService.getAllSections();
      if (_sections.isNotEmpty && _selectedSection == null) {
        _selectedSection = _sections.first;
        await _loadTablesForSection(_selectedSection!);
      }
    } catch (e) {
      _showErrorSnackBar('Error loading sections: $e');
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }
  
  Future<void> _loadTablesForSection(DatabaseSection section) async {
    try {
      _currentTables = await _dbService.getTablesForSection(section.id);
      
      _tabController?.dispose();
      _tabController = TabController(
        length: _currentTables.length,
        vsync: this,
      );
      
      if (_currentTables.isNotEmpty) {
        _selectedTable = _currentTables.first;
      } else {
        _selectedTable = null;
      }
      
      setState(() {});
    } catch (e) {
      _showErrorSnackBar('Error loading tables: $e');
    }
  }
  
  void _showErrorSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.red,
      ),
    );
  }
  
  void _showSuccessSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.green,
      ),
    );
  }
  
  Future<void> _createNewSection() async {
    final result = await showDialog<Map<String, String>>(
      context: context,
      builder: (context) => _CreateSectionDialog(),
    );
    
    if (result != null) {
      try {
        await _dbService.createSection(
          name: result['name']!,
          displayName: result['displayName']!,
          icon: result['icon'],
        );
        
        await _loadSections();
        _showSuccessSnackBar('Section created successfully');
      } catch (e) {
        _showErrorSnackBar('Error creating section: $e');
      }
    }
  }
  
  Future<void> _deleteSection(DatabaseSection section) async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete Section'),
        content: Text(
          'Are you sure you want to delete "${section.displayName}"? '
          'This will also delete all tables in this section.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () => Navigator.of(context).pop(true),
            style: TextButton.styleFrom(foregroundColor: Colors.red),
            child: const Text('Delete'),
          ),
        ],
      ),
    );
    
    if (confirmed == true) {
      try {
        await _dbService.deleteSection(section.id);
        await _loadSections();
        _showSuccessSnackBar('Section deleted successfully');
      } catch (e) {
        _showErrorSnackBar('Error deleting section: $e');
      }
    }
  }
  
  Future<void> _createNewTable() async {
    if (_selectedSection == null) return;
    
    final result = await showDialog<Map<String, dynamic>>(
      context: context,
      builder: (context) => _CreateTableDialog(),
    );
    
    if (result != null) {
      try {
        await _dbService.createTable(
          name: result['name'],
          displayName: result['displayName'],
          columns: result['columns'],
          description: result['description'],
          sectionId: _selectedSection!.id,
        );
        
        await _loadTablesForSection(_selectedSection!);
        _showSuccessSnackBar('Table created successfully');
      } catch (e) {
        _showErrorSnackBar('Error creating table: $e');
      }
    }
  }
  
  Future<void> _importFromExcel() async {
    if (_selectedSection == null) return;
    
    try {
      final result = await FilePicker.platform.pickFiles(
        type: FileType.custom,
        allowedExtensions: ['xlsx', 'xls', 'csv'],
        allowMultiple: false,
      );
      
      if (result != null && result.files.isNotEmpty) {
        final file = result.files.first;
        if (file.path == null) return;
        
        final importResult = await showDialog<Map<String, String>>(
          context: context,
          builder: (context) => _ImportExcelDialog(
            fileName: file.name,
          ),
        );
        
        if (importResult != null) {
          setState(() {
            _isLoading = true;
          });
          
          try {
            await _dbService.importFromExcel(
              filePath: file.path!,
              tableName: importResult['tableName']!,
              displayName: importResult['displayName']!,
              sectionId: _selectedSection!.id,
              sheetName: importResult['sheetName'],
            );
            
            await _loadTablesForSection(_selectedSection!);
            _showSuccessSnackBar('Excel file imported successfully');
          } catch (e) {
            _showErrorSnackBar('Error importing Excel: $e');
          } finally {
            setState(() {
              _isLoading = false;
            });
          }
        }
      }
    } catch (e) {
      _showErrorSnackBar('Error selecting file: $e');
    }
  }
  
  Future<void> _exportToExcel() async {
    if (_selectedTable == null) return;
    
    try {
      final directoryPath = await getDirectoryPath();
      if (directoryPath == null) return;
      
      setState(() {
        _isLoading = true;
      });
      
      final file = await _dbService.exportToExcel(
        tableName: _selectedTable!.name,
        fileName: _selectedTable!.displayName,
        directoryPath: directoryPath,
      );
      
      _showSuccessSnackBar('Exported to: ${file.path}');
    } catch (e) {
      _showErrorSnackBar('Error exporting: $e');
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }
  
  Future<void> _deleteTable(TableSchema table) async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete Table'),
        content: Text(
          'Are you sure you want to delete "${table.displayName}"? '
          'This action cannot be undone.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () => Navigator.of(context).pop(true),
            style: TextButton.styleFrom(foregroundColor: Colors.red),
            child: const Text('Delete'),
          ),
        ],
      ),
    );
    
    if (confirmed == true) {
      try {
        await _dbService.deleteTable(table.name);
        await _loadTablesForSection(_selectedSection!);
        _showSuccessSnackBar('Table deleted successfully');
      } catch (e) {
        _showErrorSnackBar('Error deleting table: $e');
      }
    }
  }
  
  void _onDataChanged() {
    setState(() {
      _hasUnsavedChanges = true;
    });
  }
  
  @override
  Widget build(BuildContext context) {
    if (!_authService.isAdmin) {
      return Scaffold(
        appBar: AppBar(
          title: const Text('Enhanced Database'),
        ),
        body: const Center(
          child: Text(
            'Admin access required',
            style: TextStyle(fontSize: 18),
          ),
        ),
      );
    }
    
    return Scaffold(
      appBar: AppBar(
        title: const Text('Enhanced Database Management'),
        actions: [
          if (_selectedTable != null) ...[
            IconButton(
              icon: const Icon(Icons.file_download),
              tooltip: 'Export to Excel',
              onPressed: _exportToExcel,
            ),
          ],
          IconButton(
            icon: const Icon(Icons.file_upload),
            tooltip: 'Import from Excel',
            onPressed: _importFromExcel,
          ),
          if (_hasUnsavedChanges)
            Container(
              margin: const EdgeInsets.only(right: 8),
              child: const Icon(
                Icons.circle,
                color: Colors.orange,
                size: 12,
              ),
            ),
        ],
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : Row(
              children: [
                _buildSectionSidebar(),
                Expanded(
                  child: _buildMainContent(),
                ),
              ],
            ),
    );
  }
  
  Widget _buildSectionSidebar() {
    return Container(
      width: 250,
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        border: Border(
          right: BorderSide(
            color: Theme.of(context).dividerColor,
          ),
        ),
      ),
      child: Column(
        children: [
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Theme.of(context).colorScheme.surfaceContainerHighest,
              border: Border(
                bottom: BorderSide(
                  color: Theme.of(context).dividerColor,
                ),
              ),
            ),
            child: Row(
              children: [
                const Expanded(
                  child: Text(
                    'Sections',
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
                IconButton(
                  icon: const Icon(Icons.add),
                  tooltip: 'Add Section',
                  onPressed: _createNewSection,
                ),
              ],
            ),
          ),
          Expanded(
            child: ListView.builder(
              itemCount: _sections.length,
              itemBuilder: (context, index) {
                final section = _sections[index];
                final isSelected = _selectedSection?.id == section.id;
                
                return ListTile(
                  leading: Icon(
                    _getIconData(section.icon),
                    color: isSelected
                        ? Theme.of(context).colorScheme.primary
                        : null,
                  ),
                  title: Text(
                    section.displayName,
                    style: TextStyle(
                      fontWeight: isSelected ? FontWeight.bold : null,
                      color: isSelected
                          ? Theme.of(context).colorScheme.primary
                          : null,
                    ),
                  ),
                  subtitle: Text('${section.tableNames.length} tables'),
                  selected: isSelected,
                  onTap: () async {
                    setState(() {
                      _selectedSection = section;
                    });
                    await _loadTablesForSection(section);
                  },
                  trailing: PopupMenuButton<String>(
                    onSelected: (value) {
                      switch (value) {
                        case 'delete':
                          _deleteSection(section);
                          break;
                      }
                    },
                    itemBuilder: (context) => [
                      const PopupMenuItem(
                        value: 'delete',
                        child: ListTile(
                          leading: Icon(Icons.delete, color: Colors.red),
                          title: Text('Delete'),
                          contentPadding: EdgeInsets.zero,
                        ),
                      ),
                    ],
                  ),
                );
              },
            ),
          ),
        ],
      ),
    );
  }
  
  IconData _getIconData(String? iconName) {
    switch (iconName) {
      case 'alarm':
        return Icons.alarm;
      case 'water_drop':
        return Icons.water_drop;
      case 'air':
        return Icons.air;
      case 'bubble_chart':
        return Icons.bubble_chart;
      case 'cloud':
        return Icons.cloud;
      case 'settings':
        return Icons.settings;
      case 'construction':
        return Icons.construction;
      default:
        return Icons.folder;
    }
  }
  
  Widget _buildMainContent() {
    if (_selectedSection == null) {
      return const Center(
        child: Text(
          'Select a section to view tables',
          style: TextStyle(fontSize: 18),
        ),
      );
    }
    
    return Column(
      children: [
        _buildTabBar(),
        Expanded(
          child: _buildGridContent(),
        ),
      ],
    );
  }
  
  Widget _buildTabBar() {
    if (_currentTables.isEmpty) {
      return Container(
        height: 60,
        decoration: BoxDecoration(
          color: Theme.of(context).colorScheme.surface,
          border: Border(
            bottom: BorderSide(
              color: Theme.of(context).dividerColor,
            ),
          ),
        ),
        child: Row(
          children: [
            const Expanded(
              child: Center(
                child: Text('No tables in this section'),
              ),
            ),
            IconButton(
              icon: const Icon(Icons.add),
              tooltip: 'Create Table',
              onPressed: _createNewTable,
            ),
          ],
        ),
      );
    }
    
    return Container(
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        border: Border(
          bottom: BorderSide(
            color: Theme.of(context).dividerColor,
          ),
        ),
      ),
      child: Row(
        children: [
          Expanded(
            child: TabBar(
              controller: _tabController,
              isScrollable: true,
              onTap: (index) {
                setState(() {
                  _selectedTable = _currentTables[index];
                });
              },
              tabs: _currentTables.map((table) {
                return Tab(
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Text(table.displayName),
                      if (_hasUnsavedChanges)
                        Container(
                          margin: const EdgeInsets.only(left: 4),
                          child: const Icon(
                            Icons.circle,
                            size: 8,
                            color: Colors.orange,
                          ),
                        ),
                      PopupMenuButton<String>(
                        icon: const Icon(Icons.more_vert, size: 16),
                        onSelected: (value) {
                          switch (value) {
                            case 'delete':
                              _deleteTable(table);
                              break;
                          }
                        },
                        itemBuilder: (context) => [
                          const PopupMenuItem(
                            value: 'delete',
                            child: ListTile(
                              leading: Icon(Icons.delete, color: Colors.red),
                              title: Text('Delete'),
                              contentPadding: EdgeInsets.zero,
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                );
              }).toList(),
            ),
          ),
          IconButton(
            icon: const Icon(Icons.add),
            tooltip: 'Create Table',
            onPressed: _createNewTable,
          ),
        ],
      ),
    );
  }
  
  Widget _buildGridContent() {
    if (_selectedTable == null) {
      return const Center(
        child: Text(
          'No table selected',
          style: TextStyle(fontSize: 18),
        ),
      );
    }
    
    return EnhancedExcelGrid(
      key: ValueKey(_selectedTable!.name),
      tableName: _selectedTable!.name,
      schema: _selectedTable!,
      onDataChanged: _onDataChanged,
      allowCellEditing: true,
      allowColumnResize: true,
      allowSorting: true,
      allowFiltering: true,
      showRowNumbers: true,
      pageSize: 100,
    );
  }
}

class _CreateSectionDialog extends StatefulWidget {
  @override
  State<_CreateSectionDialog> createState() => _CreateSectionDialogState();
}

class _CreateSectionDialogState extends State<_CreateSectionDialog> {
  final _nameController = TextEditingController();
  final _displayNameController = TextEditingController();
  String? _selectedIcon;
  
  final List<Map<String, String>> _iconOptions = [
    {'value': 'alarm', 'label': 'Alarm'},
    {'value': 'water_drop', 'label': 'Water Drop'},
    {'value': 'air', 'label': 'Air'},
    {'value': 'bubble_chart', 'label': 'Bubble Chart'},
    {'value': 'cloud', 'label': 'Cloud'},
    {'value': 'settings', 'label': 'Settings'},
    {'value': 'construction', 'label': 'Construction'},
    {'value': 'folder', 'label': 'Folder'},
  ];
  
  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: const Text('Create New Section'),
      content: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          TextField(
            controller: _nameController,
            decoration: const InputDecoration(
              labelText: 'Section Name (internal)',
              hintText: 'e.g., fire_alarm',
            ),
          ),
          const SizedBox(height: 16),
          TextField(
            controller: _displayNameController,
            decoration: const InputDecoration(
              labelText: 'Display Name',
              hintText: 'e.g., Fire Alarm',
            ),
          ),
          const SizedBox(height: 16),
          DropdownButtonFormField<String>(
            value: _selectedIcon,
            decoration: const InputDecoration(labelText: 'Icon'),
            items: _iconOptions.map((icon) {
              return DropdownMenuItem(
                value: icon['value']!,
                child: Text(icon['label']!),
              );
            }).toList(),
            onChanged: (value) {
              setState(() {
                _selectedIcon = value;
              });
            },
          ),
        ],
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: const Text('Cancel'),
        ),
        TextButton(
          onPressed: () {
            if (_nameController.text.isNotEmpty &&
                _displayNameController.text.isNotEmpty) {
              Navigator.of(context).pop({
                'name': _nameController.text.toLowerCase().replaceAll(' ', '_'),
                'displayName': _displayNameController.text,
                'icon': _selectedIcon,
              });
            }
          },
          child: const Text('Create'),
        ),
      ],
    );
  }
}

class _CreateTableDialog extends StatefulWidget {
  @override
  State<_CreateTableDialog> createState() => _CreateTableDialogState();
}

class _CreateTableDialogState extends State<_CreateTableDialog> {
  final _nameController = TextEditingController();
  final _displayNameController = TextEditingController();
  final _descriptionController = TextEditingController();
  final List<ColumnSchema> _columns = [];
  
  @override
  void initState() {
    super.initState();
    // Add default columns
    _columns.addAll([
      ColumnSchema(
        name: 'name',
        type: DataType.text,
        displayName: 'Name',
        isRequired: true,
      ),
      ColumnSchema(
        name: 'description',
        type: DataType.text,
        displayName: 'Description',
      ),
      ColumnSchema(
        name: 'price',
        type: DataType.currency,
        displayName: 'Price',
        currencySymbol: '\$',
      ),
    ]);
  }
  
  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: const Text('Create New Table'),
      content: SizedBox(
        width: 500,
        height: 400,
        child: Column(
          children: [
            TextField(
              controller: _nameController,
              decoration: const InputDecoration(
                labelText: 'Table Name (internal)',
                hintText: 'e.g., notifier_devices',
              ),
            ),
            const SizedBox(height: 8),
            TextField(
              controller: _displayNameController,
              decoration: const InputDecoration(
                labelText: 'Display Name',
                hintText: 'e.g., Notifier Devices',
              ),
            ),
            const SizedBox(height: 8),
            TextField(
              controller: _descriptionController,
              decoration: const InputDecoration(
                labelText: 'Description (optional)',
              ),
            ),
            const SizedBox(height: 16),
            const Text(
              'Columns:',
              style: TextStyle(fontWeight: FontWeight.bold),
            ),
            Expanded(
              child: ListView.builder(
                itemCount: _columns.length,
                itemBuilder: (context, index) {
                  final column = _columns[index];
                  return ListTile(
                    title: Text(column.displayName),
                    subtitle: Text(column.type.toString().split('.').last),
                    trailing: IconButton(
                      icon: const Icon(Icons.delete),
                      onPressed: () {
                        setState(() {
                          _columns.removeAt(index);
                        });
                      },
                    ),
                  );
                },
              ),
            ),
          ],
        ),
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: const Text('Cancel'),
        ),
        TextButton(
          onPressed: () {
            if (_nameController.text.isNotEmpty &&
                _displayNameController.text.isNotEmpty) {
              Navigator.of(context).pop({
                'name': _nameController.text.toLowerCase().replaceAll(' ', '_'),
                'displayName': _displayNameController.text,
                'description': _descriptionController.text.isEmpty
                    ? null
                    : _descriptionController.text,
                'columns': _columns,
              });
            }
          },
          child: const Text('Create'),
        ),
      ],
    );
  }
}

class _ImportExcelDialog extends StatefulWidget {
  final String fileName;
  
  const _ImportExcelDialog({required this.fileName});
  
  @override
  State<_ImportExcelDialog> createState() => _ImportExcelDialogState();
}

class _ImportExcelDialogState extends State<_ImportExcelDialog> {
  final _tableNameController = TextEditingController();
  final _displayNameController = TextEditingController();
  final _sheetNameController = TextEditingController();
  
  @override
  void initState() {
    super.initState();
    final baseName = widget.fileName.split('.').first;
    _tableNameController.text = baseName.toLowerCase().replaceAll(' ', '_');
    _displayNameController.text = baseName;
  }
  
  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: const Text('Import Excel File'),
      content: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Text('File: ${widget.fileName}'),
          const SizedBox(height: 16),
          TextField(
            controller: _tableNameController,
            decoration: const InputDecoration(
              labelText: 'Table Name (internal)',
            ),
          ),
          const SizedBox(height: 8),
          TextField(
            controller: _displayNameController,
            decoration: const InputDecoration(
              labelText: 'Display Name',
            ),
          ),
          const SizedBox(height: 8),
          TextField(
            controller: _sheetNameController,
            decoration: const InputDecoration(
              labelText: 'Sheet Name (optional)',
              hintText: 'Leave empty for first sheet',
            ),
          ),
        ],
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: const Text('Cancel'),
        ),
        TextButton(
          onPressed: () {
            if (_tableNameController.text.isNotEmpty &&
                _displayNameController.text.isNotEmpty) {
              Navigator.of(context).pop({
                'tableName': _tableNameController.text,
                'displayName': _displayNameController.text,
                'sheetName': _sheetNameController.text.isEmpty
                    ? null
                    : _sheetNameController.text,
              });
            }
          },
          child: const Text('Import'),
        ),
      ],
    );
  }
}