class CalculationInput {
  final String agentType;
  final double designConcentration; // percentage (e.g., 7.4 for 7.4%)
  final String inputMode; // 'dimensions' or 'agentQuantity'
  final double? protectedVolume; // m³ (calculated or provided)
  final double? protectedArea; // m² (calculated or provided)
  final double? roomLength; // m (for dimensions mode)
  final double? roomWidth; // m (for dimensions mode)
  final double? roomHeight; // m
  final double? agentQuantity; // kg (for agentQuantity mode)
  final double dischargeTime; // seconds (typically 10)
  final double? pipeLength; // meters (optional)
  final double altitude; // meters above sea level
  final double temperature; // °C
  final String systemType; // 'main' or 'reserve'
  final String installationType; // 'supplyOnly' or 'supplyAndInstall'

  CalculationInput({
    required this.agentType,
    required this.designConcentration,
    required this.inputMode,
    this.protectedVolume,
    this.protectedArea,
    this.roomLength,
    this.roomWidth,
    this.roomHeight,
    this.agentQuantity,
    this.dischargeTime = 10.0,
    this.pipeLength,
    this.altitude = 0.0,
    this.temperature = 20.0,
    this.systemType = 'main',
    this.installationType = 'supplyOnly',
  });

  factory CalculationInput.fromDimensions({
    required String agentType,
    required double designConcentration,
    required double roomLength,
    required double roomWidth,
    required double roomHeight,
    double dischargeTime = 10.0,
    double? pipeLength,
    double altitude = 0.0,
    double temperature = 20.0,
    String systemType = 'main',
    String installationType = 'supplyOnly',
  }) {
    final volume = roomLength * roomWidth * roomHeight;
    final area = roomLength * roomWidth;

    return CalculationInput(
      agentType: agentType,
      designConcentration: designConcentration,
      inputMode: 'dimensions',
      protectedVolume: volume,
      protectedArea: area,
      roomLength: roomLength,
      roomWidth: roomWidth,
      roomHeight: roomHeight,
      dischargeTime: dischargeTime,
      pipeLength: pipeLength,
      altitude: altitude,
      temperature: temperature,
      systemType: systemType,
      installationType: installationType,
    );
  }

  factory CalculationInput.fromAgentQuantity({
    required String agentType,
    required double designConcentration,
    required double agentQuantity,
    double? roomHeight,
    double dischargeTime = 10.0,
    double? pipeLength,
    double altitude = 0.0,
    double temperature = 20.0,
    String systemType = 'main',
    String installationType = 'supplyOnly',
  }) {
    return CalculationInput(
      agentType: agentType,
      designConcentration: designConcentration,
      inputMode: 'agentQuantity',
      agentQuantity: agentQuantity,
      roomHeight: roomHeight,
      dischargeTime: dischargeTime,
      pipeLength: pipeLength,
      altitude: altitude,
      temperature: temperature,
      systemType: systemType,
      installationType: installationType,
    );
  }

  @override
  String toString() {
    return '''
CalculationInput:
  Agent Type: $agentType
  Design Concentration: $designConcentration%
  Input Mode: $inputMode
  Protected Volume: ${protectedVolume?.toStringAsFixed(2) ?? 'N/A'} m³
  Protected Area: ${protectedArea?.toStringAsFixed(2) ?? 'N/A'} m²
  Room Dimensions: ${roomLength?.toStringAsFixed(2) ?? 'N/A'}m x ${roomWidth?.toStringAsFixed(2) ?? 'N/A'}m x ${roomHeight?.toStringAsFixed(2) ?? 'N/A'}m
  Agent Quantity: ${agentQuantity?.toStringAsFixed(2) ?? 'N/A'} kg
  Discharge Time: ${dischargeTime.toStringAsFixed(1)} sec
  System Type: $systemType
  Installation Type: $installationType
''';
  }
}
