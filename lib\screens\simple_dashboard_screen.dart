import 'package:flutter/material.dart';

class SimpleDashboardScreen extends StatelessWidget {
  const SimpleDashboardScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Firetool Dashboard'),
        backgroundColor: Colors.red[800],
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Welcome to Firetool Estimator',
              style: TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 20),
            const Text(
              'Fire Protection Systems',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 10),
            GridView.count(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              crossAxisCount: 3,
              crossAxisSpacing: 16,
              mainAxisSpacing: 16,
              children: [
                _buildSystemCard(
                  context,
                  'Water Systems',
                  Icons.water_drop,
                  Colors.blue,
                ),
                _buildSystemCard(
                  context,
                  'Foam Systems',
                  Icons.bubble_chart,
                  Colors.amber,
                ),
                _buildSystemCard(
                  context,
                  'CO2 Systems',
                  Icons.cloud,
                  Colors.grey,
                ),
                _buildSystemCard(
                  context,
                  'FM200 Systems',
                  Icons.air,
                  Colors.green,
                ),
                _buildSystemCard(
                  context,
                  'Novec Systems',
                  Icons.science,
                  Colors.purple,
                ),
                _buildSystemCard(
                  context,
                  'Fire Alarm',
                  Icons.notifications_active,
                  Colors.red,
                ),
              ],
            ),
            const SizedBox(height: 20),
            const Text(
              'Recent Projects',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 10),
            _buildProjectCard(
              'Hospital Fire Protection',
              'Client: Medical Center',
              'Systems: Water, Foam, Fire Alarm',
              '250,000 SAR',
            ),
            _buildProjectCard(
              'Data Center Protection',
              'Client: Tech Solutions Inc.',
              'Systems: FM200, Fire Alarm',
              '180,000 SAR',
            ),
            _buildProjectCard(
              'Office Building',
              'Client: Corporate Spaces',
              'Systems: Water, Fire Alarm',
              '120,000 SAR',
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSystemCard(
    BuildContext context,
    String title,
    IconData icon,
    Color color,
  ) {
    return Card(
      elevation: 4,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: InkWell(
        onTap: () {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('$title selected'),
              duration: const Duration(seconds: 1),
            ),
          );
        },
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                icon,
                size: 48,
                color: color,
              ),
              const SizedBox(height: 8),
              Text(
                title,
                textAlign: TextAlign.center,
                style: const TextStyle(
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildProjectCard(
    String title,
    String client,
    String systems,
    String value,
  ) {
    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              title,
              style: const TextStyle(
                fontWeight: FontWeight.bold,
                fontSize: 16,
              ),
            ),
            const SizedBox(height: 8),
            Text(client),
            Text(systems),
            const SizedBox(height: 8),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                const Text('Total Value:'),
                Text(
                  value,
                  style: const TextStyle(
                    fontWeight: FontWeight.bold,
                    color: Colors.green,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}
