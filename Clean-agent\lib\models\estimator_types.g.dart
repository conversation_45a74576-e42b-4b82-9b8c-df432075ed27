// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'estimator_types.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

EstimatorFormValues _$EstimatorFormValuesFromJson(Map<String, dynamic> json) =>
    EstimatorFormValues(
      agentType: $enumDecode(_$AgentTypeEnumMap, json['agentType']),
      designConcentration: json['designConcentration'] as String,
      inputMode: $enumDecode(_$InputModeEnumMap, json['inputMode']),
      roomLength: (json['roomLength'] as num?)?.toDouble(),
      roomWidth: (json['roomWidth'] as num?)?.toDouble(),
      roomHeight: (json['roomHeight'] as num?)?.toDouble(),
      agentQuantity: (json['agentQuantity'] as num?)?.toDouble(),
      systemType: $enumDecode(_$SystemTypeEnumMap, json['systemType']),
      installationType:
          $enumDecode(_$InstallationTypeEnumMap, json['installationType']),
    );

Map<String, dynamic> _$EstimatorFormValuesToJson(
        EstimatorFormValues instance) =>
    <String, dynamic>{
      'agentType': _$AgentTypeEnumMap[instance.agentType]!,
      'designConcentration': instance.designConcentration,
      'inputMode': _$InputModeEnumMap[instance.inputMode]!,
      'roomLength': instance.roomLength,
      'roomWidth': instance.roomWidth,
      'roomHeight': instance.roomHeight,
      'agentQuantity': instance.agentQuantity,
      'systemType': _$SystemTypeEnumMap[instance.systemType]!,
      'installationType': _$InstallationTypeEnumMap[instance.installationType]!,
    };

const _$AgentTypeEnumMap = {
  AgentType.novec1230: 'NOVEC1230',
  AgentType.fm200: 'FM200',
};

const _$InputModeEnumMap = {
  InputMode.dimensions: 'dimensions',
  InputMode.agentQuantity: 'agentQuantity',
};

const _$SystemTypeEnumMap = {
  SystemType.main: 'main',
  SystemType.reserve: 'reserve',
  SystemType.mainAndReserve: 'mainAndReserve',
};

const _$InstallationTypeEnumMap = {
  InstallationType.supplyOnly: 'supplyOnly',
  InstallationType.supplyAndInstall: 'supplyAndInstall',
};

RoomData _$RoomDataFromJson(Map<String, dynamic> json) => RoomData(
      roomLength: (json['roomLength'] as num).toDouble(),
      roomWidth: (json['roomWidth'] as num).toDouble(),
      roomHeight: (json['roomHeight'] as num).toDouble(),
      roomArea: (json['roomArea'] as num).toDouble(),
      roomVolume: (json['roomVolume'] as num).toDouble(),
    );

Map<String, dynamic> _$RoomDataToJson(RoomData instance) => <String, dynamic>{
      'roomLength': instance.roomLength,
      'roomWidth': instance.roomWidth,
      'roomHeight': instance.roomHeight,
      'roomArea': instance.roomArea,
      'roomVolume': instance.roomVolume,
    };

CylinderData _$CylinderDataFromJson(Map<String, dynamic> json) => CylinderData(
      targetFillSingleCyl: (json['targetFillSingleCyl'] as num).toDouble(),
      cylinderSizeLiters1stIter:
          (json['cylinderSizeLiters1stIter'] as num).toDouble(),
      numCylinders1stIter: (json['numCylinders1stIter'] as num).toInt(),
      qtyPerCylinder1stIter: (json['qtyPerCylinder1stIter'] as num).toDouble(),
      cylinderSizeLiters2ndIter:
          (json['cylinderSizeLiters2ndIter'] as num).toDouble(),
      numCylinders2ndIter: (json['numCylinders2ndIter'] as num).toInt(),
      qtyPerCylinder: (json['qtyPerCylinder'] as num).toDouble(),
      actualTotalKg: (json['actualTotalKg'] as num).toDouble(),
      fillingRatio: (json['fillingRatio'] as num).toDouble(),
    );

Map<String, dynamic> _$CylinderDataToJson(CylinderData instance) =>
    <String, dynamic>{
      'targetFillSingleCyl': instance.targetFillSingleCyl,
      'cylinderSizeLiters1stIter': instance.cylinderSizeLiters1stIter,
      'numCylinders1stIter': instance.numCylinders1stIter,
      'qtyPerCylinder1stIter': instance.qtyPerCylinder1stIter,
      'cylinderSizeLiters2ndIter': instance.cylinderSizeLiters2ndIter,
      'numCylinders2ndIter': instance.numCylinders2ndIter,
      'qtyPerCylinder': instance.qtyPerCylinder,
      'actualTotalKg': instance.actualTotalKg,
      'fillingRatio': instance.fillingRatio,
    };

DischargeData _$DischargeDataFromJson(Map<String, dynamic> json) =>
    DischargeData(
      totalFlowRate: (json['totalFlowRate'] as num).toDouble(),
      nozzleQty1stTrial: (json['nozzleQty1stTrial'] as num).toInt(),
      flowPerNozzle1stTrial: (json['flowPerNozzle1stTrial'] as num).toDouble(),
      nozzleSize1stTrial: (json['nozzleSize1stTrial'] as num).toInt(),
      nozzleQtyFinal: (json['nozzleQtyFinal'] as num).toInt(),
      flowPerNozzleFinal: (json['flowPerNozzleFinal'] as num).toDouble(),
      nozzleSizeFinal: (json['nozzleSizeFinal'] as num).toInt(),
      manifoldPipeSize: (json['manifoldPipeSize'] as num).toInt(),
      manifoldAssemblySize: (json['manifoldAssemblySize'] as num).toInt(),
    );

Map<String, dynamic> _$DischargeDataToJson(DischargeData instance) =>
    <String, dynamic>{
      'totalFlowRate': instance.totalFlowRate,
      'nozzleQty1stTrial': instance.nozzleQty1stTrial,
      'flowPerNozzle1stTrial': instance.flowPerNozzle1stTrial,
      'nozzleSize1stTrial': instance.nozzleSize1stTrial,
      'nozzleQtyFinal': instance.nozzleQtyFinal,
      'flowPerNozzleFinal': instance.flowPerNozzleFinal,
      'nozzleSizeFinal': instance.nozzleSizeFinal,
      'manifoldPipeSize': instance.manifoldPipeSize,
      'manifoldAssemblySize': instance.manifoldAssemblySize,
    };

DesignResults _$DesignResultsFromJson(Map<String, dynamic> json) =>
    DesignResults(
      roomData: RoomData.fromJson(json['roomData'] as Map<String, dynamic>),
      designFactor: (json['designFactor'] as num).toDouble(),
      totalAgentRequired: (json['totalAgentRequired'] as num).toDouble(),
      cylinder: CylinderData.fromJson(json['cylinder'] as Map<String, dynamic>),
      discharge:
          DischargeData.fromJson(json['discharge'] as Map<String, dynamic>),
      systemType: json['systemType'] as String,
    );

Map<String, dynamic> _$DesignResultsToJson(DesignResults instance) =>
    <String, dynamic>{
      'roomData': instance.roomData,
      'designFactor': instance.designFactor,
      'totalAgentRequired': instance.totalAgentRequired,
      'cylinder': instance.cylinder,
      'discharge': instance.discharge,
      'systemType': instance.systemType,
    };

BomItem _$BomItemFromJson(Map<String, dynamic> json) => BomItem(
      partNo: json['partNo'] as String,
      description: json['description'] as String,
      quantity: (json['quantity'] as num).toInt(),
      unitCost: (json['unitCost'] as num).toDouble(),
      totalCost: (json['totalCost'] as num).toDouble(),
      manufacturer: json['manufacturer'] as String,
      category: json['category'] as String,
      subcategory: json['subcategory'] as String?,
      currency: json['currency'] as String?,
    );

Map<String, dynamic> _$BomItemToJson(BomItem instance) => <String, dynamic>{
      'partNo': instance.partNo,
      'description': instance.description,
      'quantity': instance.quantity,
      'unitCost': instance.unitCost,
      'totalCost': instance.totalCost,
      'manufacturer': instance.manufacturer,
      'category': instance.category,
      'subcategory': instance.subcategory,
      'currency': instance.currency,
    };

BomSummary _$BomSummaryFromJson(Map<String, dynamic> json) => BomSummary(
      suppressionCost: (json['suppressionCost'] as num).toDouble(),
      alarmCost: (json['alarmCost'] as num).toDouble(),
      installationItemsCost: (json['installationItemsCost'] as num).toDouble(),
      suppressionInstallCost:
          (json['suppressionInstallCost'] as num).toDouble(),
      alarmInstallCost: (json['alarmInstallCost'] as num).toDouble(),
      installationServicesInstallCost:
          (json['installationServicesInstallCost'] as num).toDouble(),
      totalSupplyCostUSD: (json['totalSupplyCostUSD'] as num).toDouble(),
      totalSupplyCostSAR: (json['totalSupplyCostSAR'] as num).toDouble(),
      totalInstallCostSAR: (json['totalInstallCostSAR'] as num).toDouble(),
      grandTotalSAR: (json['grandTotalSAR'] as num).toDouble(),
      marginFactor: (json['marginFactor'] as num).toDouble(),
      marginAmountSAR: (json['marginAmountSAR'] as num).toDouble(),
    );

Map<String, dynamic> _$BomSummaryToJson(BomSummary instance) =>
    <String, dynamic>{
      'suppressionCost': instance.suppressionCost,
      'alarmCost': instance.alarmCost,
      'installationItemsCost': instance.installationItemsCost,
      'suppressionInstallCost': instance.suppressionInstallCost,
      'alarmInstallCost': instance.alarmInstallCost,
      'installationServicesInstallCost':
          instance.installationServicesInstallCost,
      'totalSupplyCostUSD': instance.totalSupplyCostUSD,
      'totalSupplyCostSAR': instance.totalSupplyCostSAR,
      'totalInstallCostSAR': instance.totalInstallCostSAR,
      'grandTotalSAR': instance.grandTotalSAR,
      'marginFactor': instance.marginFactor,
      'marginAmountSAR': instance.marginAmountSAR,
    };

QuotedSystem _$QuotedSystemFromJson(Map<String, dynamic> json) => QuotedSystem(
      id: json['id'] as String,
      name: json['name'] as String,
      date: json['date'] as String,
      inputData: EstimatorFormValues.fromJson(
          json['inputData'] as Map<String, dynamic>),
      designResults:
          DesignResults.fromJson(json['designResults'] as Map<String, dynamic>),
      bom: (json['bom'] as List<dynamic>)
          .map((e) => BomItem.fromJson(e as Map<String, dynamic>))
          .toList(),
      summary: BomSummary.fromJson(json['summary'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$QuotedSystemToJson(QuotedSystem instance) =>
    <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'date': instance.date,
      'inputData': instance.inputData,
      'designResults': instance.designResults,
      'bom': instance.bom,
      'summary': instance.summary,
    };
