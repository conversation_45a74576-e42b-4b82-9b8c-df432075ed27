use flutter_rust_bridge_codegen::{
    config_parse, frb_codegen, get_symbols_if_no_duplicates, RawOpts,
};

const RUST_INPUT: &str = "src/lib.rs";
const DART_OUTPUT: &str = "../lib/bridge_generated.dart";

fn main() {
    // Generate Flutter-Rust bridge code
    let raw_opts = RawOpts {
        rust_input: vec![RUST_INPUT.to_string()],
        dart_output: vec![DART_OUTPUT.to_string()],
        c_output: Some(vec!["../ios/Runner/bridge_generated.h".to_string()]),
        rust_crate_dir: Some(".".to_string()),
        dart_format_line_length: Some(120),
        ..Default::default()
    };

    let config = config_parse(raw_opts);
    let all_symbols = get_symbols_if_no_duplicates(&config).unwrap();
    frb_codegen(&config, all_symbols).unwrap();

    // Tell Cargo to rerun this build script if the input files change
    println!("cargo:rerun-if-changed=src/");
    println!("cargo:rerun-if-changed=build.rs");
}
